/**
 * @Author: yezy
 * @Email: <EMAIL>
 * @Date: 2019-06-18 17:27:23
 * @LastEditors: yezy
 * @LastEditTime: 2019-06-18 17:27:23
 * @Description: 默认在vue上添加的原型方法
 */

import Vue from 'vue'
import * as api from './axios/api'
import * as echarts from 'echarts'
import store from './store'
import moment from 'moment'
import * as utils from '@/util/common';

Vue.prototype.$api = api;
Vue.prototype.$echarts = echarts;
Vue.prototype.$utils = utils;
//认证登入用户
Vue.prototype.currentUser = function (user) {
    return user === store.state.main.company
}
//鉴权
Vue.prototype.hasPermission = function (permission) {
    //本地开发提供所有权限
    // if (process.env.NODE_ENV === 'development') return true;
    return !!store.state.auth.userPermission[permission]
}
Vue.prototype.TypeForName = (type) => {
    let current = store.state.dictionary.alarm[type]
    return current && current.name? current.name : '未知报警'
}
Vue.prototype.TypeForClass = (type) => {
    let current = store.state.dictionary.alarm[type]
    return current && current.className ? current.className : 'alarm_other'
}
Vue.prototype.TypeForDetail = (type) => {
    let current = store.state.dictionary.alarm[type]
    return current && current.introduce ? current.introduce : '无'
}
// 2021 01 25 发现此东西并未有使用的记录，暂时注销
// Vue.prototype.TypeForIcon = (type) => {
//     let className = store.state.dictionary.alarm[type] ?
//         store.state.dictionary.alarm[type].className : 'OTHER';
//     return './static/img/alarm/' + className + '.png'
// }

// 时间戳格式化
Vue.prototype.TimeFormat = (time, ...args) => {
    return time ? moment(time, ...args).format('YYYY-MM-DD HH:mm:ss') : ''
}
Vue.prototype.DateFormat = (date, ...args) => {
    return date ? moment(date, ...args).format('YYYY-MM-DD') : ''
}
// 毫秒转时分秒格式
Vue.prototype.MillisecondFormat = (millisecond) => {
    if(!millisecond) return ''
    var value = millisecond/1000
    var theTime = parseInt(value);// 秒
	var middle= 0;// 分
	var hour= 0;// 小时
	if(theTime > 60) {
	    middle= parseInt(theTime/60);
	    theTime = parseInt(theTime%60);
	    if(middle> 60) {
	        hour= parseInt(middle/60);
	        middle= parseInt(middle%60);
	    }
	}
	var result = ""+parseInt(theTime)+"秒";
	if(middle > 0) {
	    result = ""+parseInt(middle)+"分"+result;
	}
	if(hour> 0) {
	    result = ""+parseInt(hour)+"时"+result;
	}
	return result;
}
// 毫秒转时分秒格式
Vue.prototype.MillisecondDayFormat = (millisecond) => {
    if(!millisecond)return
    var value = millisecond/1000
    var theTime = parseInt(value);// 秒
    var day = 0
	var middle= 0;// 分
	var hour= 0;// 小时
	if(theTime > 60) {
	    middle= parseInt(theTime/60);
	    theTime = parseInt(theTime%60);
	    if(middle> 60) {
	        hour= parseInt(middle/60);
	        middle= parseInt(middle%60);
            if(hour > 24){
                day = parseInt(hour/24)
                hour = parseInt(hour%24);
            }
	    }
	}
    var result = ""
    if(day==0){
	 result = ""+parseInt(theTime)+"秒";
    }
	if(middle > 0) {
	    result = ""+parseInt(middle)+"分"+result;
	}
	if(hour> 0) {
	    result = ""+parseInt(hour)+"时"+result;
	}
    if(day> 0) {
	    result = ""+parseInt(day)+"天"+result;
	}
	return result;
}
//时间戳》》00:00:00格式
Vue.prototype.SecondFormat = (millisecond) => {
    let time = millisecond/1000
    let timeStr = null;
        let hour = 0;
        let minute = 0;
        let second = 0;
        if (time <= 0)
            return "00:00:00";
        else {
            second = parseInt(time);
            minute = parseInt(second / 60);
            if (second < 60) {
                timeStr = "00:00:" + unitFormat(second);
            } else if (minute < 60) {
                second = parseInt(second % 60);
                timeStr = "00:" + unitFormat(minute) + ":" + unitFormat(second);
            } else {// 数字>=3600 000的时候
                hour = parseInt(minute / 60);
                minute = parseInt(minute % 60);
                second = second - parseInt(hour * 3600) - parseInt(minute * 60);
                timeStr = unitFormat(hour) + ":" + unitFormat(minute) + ":" + unitFormat(second);
            }
        }
        return timeStr
}
const unitFormat = (i)=>{
    let retStr = null;
        if (i >= 0 && i < 10)
            retStr = "0" + i;
        else
            retStr = "" + i;
        return retStr;
}
/**
     * 时间转为毫秒
     * @param time 时间(00:00:00)
     * @returns {string} 时间戳（单位：毫秒）
     */
Vue.prototype.TimeToSec = function (time) {
    var s = '';
    var hour = time.split(':')[0];
    var min = time.split(':')[1];
    var sec = time.split(':')[2];
    s = Number(hour*3600) + Number(min*60) + Number(sec);
    return s*1000;
};


Vue.prototype.MileFormat = (mile) => {
    return mile ? (mile / 1000) >= 10000 ? (mile / 10000000).toFixed(2) + '万km' : (mile / 1000) + 'km' : '无'
}
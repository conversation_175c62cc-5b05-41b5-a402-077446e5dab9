<template>
    <Layout :has-color="true" :contentLoading="table.loading" class="vehicleCommandList">

        <template slot="aside">
            <div class="query-top">
                <ElementTree ref="vehicleTree" :checkMode="true" type="vehicle" @check="onCheckVehicle"></ElementTree>
            </div>
            <div class="query-bottom bg bg--lighter">
                <div class="query-item">
                    <el-button size="mini" type="primary" style="width: 100%;" @click="search">查询</el-button>
                </div>
            </div>
        </template>

        <template slot="query">
            <div class="query-item">
                <el-button size="mini" type="primary" @click="exportTable">导出</el-button>
            </div>
            <div class="break-item"></div>
            <div class="query-item">
                <el-pagination small background :current-page.sync="table.page" :page-size="table.size" layout="prev, pager, next, total" :total="table.total">
                </el-pagination>
            </div>
        </template>

        <template slot="content">
            <el-table ref="table" class="el-table--ellipsis el-table--radius" border stripe highlight-current-row size="mini" :data="formatList" height="100%" style="width: 100%">
                <el-table-column type="index" align="center" :index="(index) => index + 1 + pageStart" label="序号" width="60"></el-table-column>
                <el-table-column prop="company" label="部门" width="170"></el-table-column>
                <el-table-column prop="plateNo" label="车牌号" width="130"></el-table-column>
                <el-table-column prop="simNo" label="终端手机号" width="180"></el-table-column>
                <el-table-column prop="terminalType" label="终端类型" width="220">
                    <template slot-scope="{row}">
                        <el-select v-model="row.terminalType" :clearable="false" multiple collapse-tags style="width: 190px" placeholder="请选择">
                            <el-option v-for="(item,index) in row.terminalType" :key="index" :label="item" :value="item" :disabled="true">
                            </el-option>
                        </el-select>
                    </template>
                </el-table-column>
                <el-table-column prop="firmwareVer" label="当前终端固件版本号" width="200"></el-table-column>
                <el-table-column prop="firmwareVerHistory" label="历史终端固件版本号" width="300">
                    <template slot-scope="{row}">
                        <el-select v-model="row.firmwareVerHistory" :clearable="false" multiple collapse-tags style="width: 280px" placeholder="请选择">
                            <el-option v-for="(item,index) in row.firmwareVerHistory" :key="index" :label="item" :value="item" :disabled="true">
                            </el-option>
                        </el-select>
                    </template>
                </el-table-column>
                <el-table-column prop="updateTime" label="更新时间" width="150"></el-table-column>
                <el-table-column prop="mfrsId" label="制造商ID"></el-table-column>
                <el-table-column prop="terminalModel" label="终端型号" width="100"></el-table-column>
                <el-table-column prop="terminalId" label="终端ID"></el-table-column>
                <el-table-column prop="deviceId" label="终端ID(平台)" width="150" show-overflow-tooltip></el-table-column>
                <el-table-column prop="iccid" label="终端SIM卡 ICCID" width="180"></el-table-column>
                <el-table-column prop="hardwareVer" label="终端硬件版本号" width="120"></el-table-column>
                <el-table-column prop="gnss" label="GNSS 模块属性" width="240">
                    <template slot-scope="{row}">
                        <el-select v-model="row.gnss" :clearable="false" multiple collapse-tags style="width: 200px" placeholder="请选择">
                            <el-option v-for="(item,index) in row.gnss" :key="index" :label="item" :value="item" :disabled="true">
                            </el-option>
                        </el-select>
                    </template>
                </el-table-column>
                <el-table-column prop="radio" label="通信模块属性" width="240">
                    <template slot-scope="{row}">
                        <el-select v-model="row.radio" :clearable="false" multiple collapse-tags style="width: 200px" placeholder="请选择">
                            <el-option v-for="(item,index) in row.radio" :key="index" :label="item" :value="item" :disabled="true">
                            </el-option>
                        </el-select>
                    </template>
                </el-table-column>
            </el-table>
        </template>
    </Layout>
</template>

<script>
const ExportJsonExcel = require('js-export-excel')

export default {
    name: "VehicleCommandList",
    data() {
        return {
            vehicleIdList: [],
            table: {
                loading: false,
                page: 1,
                size: 30,
                data: [],
                total: 0
            },
            options: [],
            value: '',
            // 终端类型
            terminalType: {
                0: ['不适用客运车辆', '适用客运车辆'],
                1: ['不适用危险品车辆', '适用危险品车辆'],
                2: ['不适用普通货运车辆', '适用普通货运车辆'],
                3: ['不适用出租车辆', '适用出租车辆'],
                6: ['不支持硬盘录像', '支持硬盘录像'],
                7: ['一体机', '分体机']
            },
            GNSS: {
                0: ['不支持 GPS 定位', '支持 GPS 定位'],
                1: ['不支持北斗定位', '支持北斗定位'],
                2: ['不支持 GLONASS 定位', '支持 GLONASS 定位'],
                3: ['不支持 Galileo 定位', '支持 Galileo 定位'],
            },

            communication: {
                0: ['不支持GPRS通信', '支持GPRS通信'],
                1: ['不支持CDMA通信', '支持CDMA通信'],
                2: ['不支持TD-SCDMA通信', '支持TD-SCDMA通信'],
                3: ['不支持WCDMA通信', '支持WCDMA通信'],
                4: ['不支持CDMA2000通信', '支持CDMA2000通信'],
                5: ['不支持TD-LTE通信', '支持TD-LTE通信'],
                7: ['不支持其他通信方式', '支持其他通信方式'],
            }


        }
    },
    computed: {
        pageStart() {
            return (this.table.page - 1) * this.table.size
        },
        formatList() {
            return this.table.data.slice(this.pageStart, this.pageStart + this.table.size)
        },
    },
    methods: {
        // 树点击事件
        onCheckVehicle(data, { checkedNodes }) {
            let nodes = checkedNodes.filter(item => item.type == 4);
            this.vehicleIdList = nodes.map(item => Number(item.id));
        },
        // 十进制转二进制
        convertToBinary(num) {
            return num.toString(2).padStart(8, '0')
        },
        // 转二进制匹配数据
        getList(number, data) {
            let list = []
            switch (data) {
                case 'terminalType':
                    data = this.terminalType;
                    break
                case 'GNSS':
                    data = this.GNSS;
                    break
                case 'communication':
                    data = this.communication;
                    break
            }
            let str = this.convertToBinary(number).toString().split('').reverse().join('')
            for (let i = 0; i < 8; i++) {
                let num = Object.keys(data)
                for (let j = 0; j < num.length; j++) {
                    if (Number(num[j]) === i) {
                        if (Number(str[i]) === 1) {
                            list.push(data[i][1])
                        } else {
                            list.push(data[i][0])
                        }
                    }
                }
            }
            return list
        },
        async search() {
            if (this.vehicleIdList.length === 0) {
                return this.$message.warning('请选择车辆！')
            }
            this.table.page = 1
            this.table.loading = true
            const res = await this.$api.getTerminalHWInfo({
                vehicleIdList: this.vehicleIdList
            })
            this.table.loading = false
            if (res.status !== 200) {
                return this.$message.warning('查询错误！')
            }
            this.table.total = res.data.length
            res.data.forEach((item) => {
                item.terminalType = this.getList(item.terminalType, 'terminalType')
                item.gnss = this.getList(item.gnss, 'GNSS')
                item.radio = this.getList(item.radio, 'communication')
                item.updateTime = moment(item.updateTime).format('YYYY-MM-DD HH:mm:ss')

                let arr = item.firmwareVerHistory.split(';').reverse()
                // 去除相邻重复值
                for (let i = 0; i < arr.length; i++) {
                    if (arr[i] === arr[i - 1]) {
                        arr.splice(i, 1)
                    }
                }
                // 截取前4个
                if (arr.length > 5) {
                    arr = arr.slice(0, 5)
                }
                item.firmwareVerHistory = arr
            })

            this.table.data = res.data
            this.$nextTick(() => {
                this.$refs['table'].doLayout()
            })
        },
        // 导出
        exportTable() {
            if (this.vehicleIdList.length === 0) {
                this.$message.warning('没有数据可以导出！')
                return
            }

            let excelBody = []
            this.table.data.forEach((item, index) => {
                let array = []
                array.push(
                    index + 1,
                    item.company,
                    item.plateNo,
                    item.simNo,
                    item.terminalType,
                    item.firmwareVer,
                    item.firmwareVerHistory,
                    item.updateTime,
                    item.mfrsId,
                    item.terminalModel,
                    item.terminalId,
                    item.iccid,
                    item.hardwareVer,
                    item.gnss,
                    item.radio,
                )
                excelBody.push(array)
            })
            let options = {
                fileName: '终端属性',
                datas: [
                    {
                        sheetName: "终端属性",
                        sheetData: excelBody,
                        sheetHeader: ['序号', '部门', '车牌号', '终端手机号', '终端类型', '当前终端固件版本号', '历史终端固件版本号', '更新时间', '制造商ID', '终端型号', '终端ID', '终端SIM卡 ICCID', '终端硬件版本号', '模块属性', '通信模块属性'],
                    }
                ]
            }
            ExportJsonExcel(options).saveExcel();
        },
    }
}
</script>

<style lang='scss' scoped>
/deep/ .el-icon-close {
    display: none;
}

.vehicleCommandList {
    .query-top {
        height: calc(100% - 65px);
    }

    .query-bottom {
        margin-top: 5px;
        padding: 10px;

        .query-item {
            display: flex;
            height: 40px;
            line-height: 40px;
            justify-content: space-between;
            align-items: center;
        }
    }
}
</style>
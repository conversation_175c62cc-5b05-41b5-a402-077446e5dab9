<template>
    <Layout :has-color="true" :contentLoading="table.loading">
        
        <template slot="query" >
            <div class="query-item">
                <span>开始时间：</span>
                <el-date-picker 
                    @change="dateStartChange"
                    :picker-options="startDatePickerOptions"
                    value-format="timestamp" 
                    type="datetime" placeholder="选择日期时间"
                    v-model="queryList.start_time" >
                </el-date-picker>
            </div>
            <div class="query-item">
                <span>结束时间：</span>
                <el-date-picker 
                    :picker-options="endDatePickerOptions"
                    value-format="timestamp" 
                    type="datetime" placeholder="选择日期时间"
                    v-model="queryList.end_time">
                </el-date-picker>
            </div>
            <div class="query-item">
                <el-button type="primary" @click.prevent="handleSearchEvent">查询</el-button>
            </div>
            <div class="break-item"></div>
            <el-pagination background small
                :pager-count="5"  
                layout="prev, pager, next, total" 
                :current-page.sync="table.page" 
                :page-size="table.size" 
                :total="table.data.length">
            </el-pagination>
        </template>

        <template slot="content">
            <el-table 
                class="el-table--ellipsis el-table--radius"
                highlight-current-row stripe border size="mini" 
                :data="formatTableList" 
                height="100%" style="width: 100%;">
                <el-table-column type="index" :index="(index) => index + 1 + pageStart" label="序号" width="80"></el-table-column>
                <el-table-column prop="plate_no" label="车牌号" min-width="140"></el-table-column>
                <el-table-column prop="terminal_no" label="终端号" min-width="140"></el-table-column>
                <el-table-column prop="driver_index_code" label="驾驶员数值唯一标识" min-width="140"></el-table-column>
                <el-table-column prop="face_image_update_time" label="驾驶员人脸图片更新时间" min-width="150"></el-table-column>
                <el-table-column prop="driver_allow_sync_time" label="驾驶员人脸同步时间" min-width="150"></el-table-column>
                <el-table-column prop="build_err_time" label="建模异常时间" min-width="150"></el-table-column>
                <el-table-column prop="build_err_desc" label="建模异常描述" min-width="200" show-overflow-tooltip></el-table-column>
                <el-table-column prop="driver_face_image_url" label="驾驶员人脸图片地址" min-width="200" show-overflow-tooltip></el-table-column>
            </el-table>
        </template>

    </Layout>
</template>

<script>

export default {
    name: 'HikFaceError',
    components: {  },
    data () {
        return {
            queryList: {
                start_time: moment().subtract(1, 'day').startOf('day').valueOf(),
                end_time: moment().endOf('day').valueOf(),
                page: 0,
                size: 30,
                version: 2
            },

            table: {
                loading: false,
                data: [],
                page: 1,
                size: 30
            },

            startDatePickerOptions: {
                disabledDate: function (date) {
                    return (date - moment().endOf('day').toDate()) > 0
                }
            },

        };
    },

    computed: {
        pageStart() {
            return (this.table.page - 1) * this.table.size
        },
        formatTableList() {
            return this.table.data.slice(this.pageStart, this.pageStart + this.table.size)
        },
        endDatePickerOptions: function () {
            return {
                disabledDate: (date) => {
                    return (date - moment().endOf('day').toDate()) > 0 ||
                        date - this.queryList.start_time < 0;
                }
            }
        },
    },

    mounted() {

    },

    methods: {
        dateStartChange(date) {
            if (this.queryList.end_time - date < 0) {
                this.queryList.end_time = moment(date).endOf('day');
            }
        },

        clearUp() {
            this.table.page = 1
            this.table.data = []
            this.table.loading = false
        },

        async handleSearchEvent() {
            let result = await this.$api.queryFaceBuildErr(JSON.parse(JSON.stringify(this.queryList)))
            this.table.loading = true
            if(!result) {
                this.clearUp()
                this.$error('查询出错')
                return
            }
            if(result.status != 200) {
                this.clearUp()
                this.$warning(result.message)
                return
            }
            if(!result.data.page_data.length) {
                this.clearUp()
                this.$warning('未查询到数据!')
                return
            }
            this.table.data = result.data.page_data
            this.table.loading = false
        },  
    }
}

</script>

<style lang='scss' scoped>

</style>

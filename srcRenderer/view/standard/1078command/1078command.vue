<!--
 * @Description:
 * @Version: 2.0
 * @Autor: wuchuang
 * @Date: 2019-10-28 16:27:34
 * @LastEditors: wuchuang
 * @LastEditTime: 2019-10-31 19:55:32
 -->
<template>
    <div
        class="vehicle-command"
        v-loading="source.loading"
        element-loading-text="指令处理中···· 请稍后"
    >
        <Layout :has-color="true">
            <template slot="aside">
                <ElementTree
                    type="vehicle"
                    :state="true"
                    ref="tree"
                    :checkMode="true"
                    @check="nodeCheck"
                >
                </ElementTree>
            </template>

            <div slot="query" class="dfb" style="width: 100%">
                <div class="dfb">
                    <el-select
                        size="mini"
                        class="query-item"
                        v-model="currentCommandType"
                        @change="clearCurrentCommand"
                    >
                        <el-option
                            v-for="(item, index) in commandList"
                            :key="index"
                            :value="item.index"
                            :label="item.name"
                        >
                        </el-option>
                    </el-select>
                    <el-badge
                        :is-dot="fileBack.comingNew"
                        style="margin-left: 10px"
                    >
                        <el-button
                            @click="fileBack.show = true"
                            size="mini"
                            type="primary"
                            >附件返回结果
                        </el-button>
                    </el-badge>
                </div>

                <el-pagination
                    background
                    small
                    :pager-count="5"
                    layout="prev, pager, next, total"
                    :current-page.sync="table.page"
                    :page-size="table.size"
                    :total="table.list.length"
                >
                </el-pagination>
            </div>

            <div class="command-content" slot="content">
                <el-table
                    ref="table"
                    class="table-item el-table--radius"
                    highlight-current-row
                    stripe
                    border
                    size="mini"
                    :data="formatTableList"
                    height="100%"
                    style="width: 100%"
                >
                    <el-table-column
                        align="center"
                        type="index"
                        :index="(index) => index + 1 + pageStart"
                        label="序号"
                    ></el-table-column>
                    <el-table-column
                        header-align="center"
                        align="left"
                        prop="company_name"
                        label="单位"
                    ></el-table-column>
                    <el-table-column
                        align="center"
                        prop="vehicle_name"
                        label="车牌号码"
                    ></el-table-column>
                    <el-table-column
                        align="center"
                        prop="state"
                        label="执行状态"
                    >
                        <template slot-scope="scope">
                            {{ commandStateDesc[scope.row.state] }}
                        </template>
                    </el-table-column>
                    <el-table-column
                        align="center"
                        prop="message"
                        label="终端回应结果"
                    ></el-table-column>
                    <el-table-column align="center" label="回应数据">
                        <template slot-scope="scope">
                            <el-button
                                v-if="scope.row.respond"
                                type="text"
                                size="mini"
                                @click="returnRespond(scope.row)"
                            >
                                <i class="pony-iconv2 pony-sousuo"></i>
                            </el-button>
                            <span v-else>无返回数据</span>
                        </template>
                    </el-table-column>
                </el-table>

                <div class="tool-item">
                    <AudioAndVideoCommand
                        ref="commandAudio"
                        @sendCommand="sendCommand"
                        v-if="currentCommandType == 14"
                    ></AudioAndVideoCommand>
                    <ImageInformationAlarmCommand
                        ref="ImageInformationAlarmCommand"
                        @sendCommand="sendCommand"
                        v-if="currentCommandType == 17"
                    ></ImageInformationAlarmCommand>
                    <TerminalSleepWakeupCommand
                        ref="TerminalSleepWakeupCommand"
                        @sendCommand="sendCommand"
                        v-if="currentCommandType == 18"
                    ></TerminalSleepWakeupCommand>
                    <QueryTerminalAudioAndVideoCommand
                        ref="QueryTerminalAudioAndVideoCommand"
                        @sendCommand="sendCommand"
                        v-if="currentCommandType == 19"
                    ></QueryTerminalAudioAndVideoCommand>
                    <TerminalWakeupCommand
                        ref="TerminalWakeupCommand"
                        @sendCommand="sendCommand"
                        v-if="currentCommandType == 20"
                    ></TerminalWakeupCommand>
                    <AudioAndVideoChannelListCommand
                        ref="audioAndVideoChannelListCommand"
                        @sendCommand="sendCommand"
                        v-if="currentCommandType == 21"
                    ></AudioAndVideoChannelListCommand>
                    <SingleVideoChannelCommand
                        ref="singleVideoChannelCommand"
                        @sendCommand="sendCommand"
                        v-if="currentCommandType == 22"
                    ></SingleVideoChannelCommand>
                    <SpecialAlarmVideo
                        ref="specialAlarmVideo"
                        @sendCommand="sendCommand"
                        v-if="currentCommandType == 23"
                    ></SpecialAlarmVideo>
                    <VideoRelatedAlarm
                        ref="videoRelatedAlarm"
                        @sendCommand="sendCommand"
                        v-if="currentCommandType == 24"
                    ></VideoRelatedAlarm>
                </div>
            </div>

            <DialogDrawer
                title="终端回复文件列表"
                v-model="fileBack.show"
                @close="clearFileCurrent"
            >
                <LayoutCard
                    :active="fileBack.current == item.terminalNo"
                    v-for="(item, index) in fileBack.list"
                    :key="index"
                    style="margin-bottom: 20px"
                >
                    <div slot="header" class="dfb" style="width: 100%">
                        <span>{{ item.vehicle_name }}</span>
                        <a
                            class="el-icon-download fon--d"
                            :href="item.url"
                            title="下载"
                            download
                        ></a>
                    </div>
                    <el-image
                        v-if="!item.mediaType"
                        :src="item.url"
                        fit="scale-down"
                    ></el-image>
                    <div class="dfa" v-else>
                        {{ item.mediaType == 1 ? "音频文件" : "视频文件" }}
                        请下载查看
                    </div>
                </LayoutCard>
            </DialogDrawer>
        </Layout>
    </div>
</template>

<script>
import DialogDrawer from "@/components/common/DialogDrawer";
import LayoutCard from "@/components/layout/LayoutCard";
import { mapState } from "vuex";

import AudioAndVideoCommand from "./components/AudioAndVideoCommand";
import ImageInformationAlarmCommand from "./components/ImageInformationAlarmCommand";
import TerminalSleepWakeupCommand from "./components/TerminalSleepWakeupCommand";
import QueryTerminalAudioAndVideoCommand from "./components/QueryTerminalAudioAndVideoCommand";
import TerminalWakeupCommand from "./components/TerminalWakeupCommand";
import AudioAndVideoChannelListCommand from "./components/AudioAndVideoChannelListCommand";
import SingleVideoChannelCommand from "./components/SingleVideoChannelCommand";
import SpecialAlarmVideo from "./components/SpecialAlarmVideo";
import VideoRelatedAlarm from "./components/VideoRelatedAlarm";
import {
    TaskCmdResponse,
    MediaCommand,
    AskAnswer,
    TerminalInfo,
    DataDriverInfo,
    MediaProperty,
} from "@/service/wsService";

let TIMER = null;
let INTERVAL = null;

Array.prototype.remove = function (val) {
    var index = this.indexOf(val);
    if (index > -1) {
        this.splice(index, 1);
    }
};

export default {
    name: "Command1078",
    components: {
        DialogDrawer,
        LayoutCard,

        AudioAndVideoCommand,
        ImageInformationAlarmCommand,
        TerminalSleepWakeupCommand,
        QueryTerminalAudioAndVideoCommand,
        TerminalWakeupCommand,
        AudioAndVideoChannelListCommand,
        SingleVideoChannelCommand,
        SpecialAlarmVideo,
        VideoRelatedAlarm,
    },
    data() {
        return {
            commandList: [
                { index: 14, name: "音视频参数设置" },
                { index: 17, name: "图像分析报警参数设置" },
                { index: 18, name: "终端休眠唤醒指令" },
                { index: 19, name: "查询终端音视频属性" },
                { index: 20, name: "终端唤醒指令" },
                { index: 21, name: "音视频通道列表设置" },
                { index: 22, name: "单独视频通道参数设置" },
                { index: 23, name: "特殊报警录像参数设置" },
                { index: 24, name: "视频相关报警屏蔽字" },
            ],

            additionalReturnIndex: [19], //  具有终端回复的指令对应码

            commandStateDesc: {
                0: "等待发送",
                1: "发送失败",
                2: "发送成功",
                3: "终端已回复",
                4: "获取超时",
            },

            callBackCommandList: [14, 17, 18, 20, 21, 22, 23, 24], //  直接返回对应码

            currentCommandType: 14,

            table: {
                page: 1,
                size: 30,
                list: [],
                terminalList: [],
            },

            source: {
                timeOut: 12,
                loading: false,
                currentTaskId: "",
                subscriptList: [],
                basicTaskLsit: [],
                additionalList: [],
            },

            fileBack: {
                current: "233333",
                list: [],
                comingNew: false,
                show: false,
            },
        };
    },

    computed: {
        ...mapState("vehicle", ["basicByVehicleId"]),

        pageStart() {
            return (this.table.page - 1) * this.table.size;
        },
        formatTableList() {
            return this.table.list.slice(
                this.pageStart,
                this.pageStart + this.table.size
            );
        },
    },

    mounted() {
        this.source.subscriptList.push(
            TaskCmdResponse.subscribe((msg) =>
                this.source.basicTaskLsit.push(msg)
            ),
            TerminalInfo.subscribe((msg) =>
                this.source.additionalList.push(msg)
            ),
            AskAnswer.subscribe((msg) => this.source.additionalList.push(msg)),
            MediaProperty.subscribe((msg) =>
                this.source.additionalList.push(msg)
            ),
            //  因为媒体文件返回太慢 所以独立出来避免超时 并且添加到附件列表
            MediaCommand.subscribe((msg) => {
                if (!this.table.list.length) return;
                let current = this.table.list.find(
                    (vehicle) => vehicle.terminal_no == msg.terminalNo
                );
                let result = {};

                // 预防第二次偷袭
                if (current && current.state != 0) {
                    current.respond = msg;
                    result = JSON.parse(JSON.stringify(current));
                }
                this.fileBack.list.unshift(Object.assign(msg, result));
                this.fileBack.list = this.fileBack.list.splice(0, 50);
                this.fileBack.comingNew = true;
            }),
            DataDriverInfo.subscribe((msg) =>
                this.source.additionalList.push(msg)
            )
        );
    },

    methods: {
        async sendCommand(obj) {
            if (!this.table.terminalList.length) {
                this.$message({
                    showClose: true,
                    message: "请先选择下发设备!",
                    type: "warning",
                });
                return;
            }
            this.source.loading = true;
            let pramas = Object.assign(
                { vehicle_terminal_list: this.table.terminalList },
                JSON.parse(JSON.stringify(obj))
            );
            let result = await this.$api.sendMiniStandardCommon(pramas);

            if (!result || result.status != 200) {
                this.table.list.forEach((item) => {
                    item.state = 1;
                    item.message = "失败";
                });
                this.$message({
                    showClose: true,
                    message: result.message,
                    type: "warning",
                });
                this.source.loading = false;
                return;
            }
            this.source.currentTaskId = result.data;
            this.table.list.forEach((item) => {
                item.state = 2;
                item.message = "等待终端返回···";
            });
            this.startCheckedInterVal();
        },

        startCheckedInterVal() {
            TIMER = setTimeout(() => {
                if (INTERVAL) {
                    clearInterval(INTERVAL);
                    INTERVAL = null;
                }
                this.table.list.forEach((item) => {
                    if (item.state < 3) {
                        // 未收到回复的变成超时
                        item.state = 4;
                        item.message = "获取超时";
                    }
                });
                this.source.loading = false;
            }, 1000 * this.source.timeOut);

            INTERVAL = setInterval(() => {
                this.table.list.forEach((vehicle) => {
                    this.handleBasicResult(vehicle);
                    this.handleTerminalResult(vehicle);
                });
                let index = this.table.list.findIndex(
                    (vehicle) => vehicle.state < 3
                ); // 检查还有没有没收到回复的

                if (index == -1) {
                    // 关闭loading  但是照样在循环 为了那些具有额外返回的备用
                    this.source.basicTaskLsit = [];
                    // 如果并没有指定返回的可以关闭定时器
                    if (
                        !this.additionalReturnIndex.includes(
                            this.currentCommandType
                        )
                    ) {
                        clearInterval(INTERVAL);
                        INTERVAL = null;
                        clearTimeout(TIMER);
                        TIMER = null;
                        this.source.loading = false;
                    }
                }
            }, 500);
        },

        handleBasicResult(vehicle) {
            // 通用应答
            if (vehicle.state > 2 || !this.source.basicTaskLsit.length) return;
            let currentResult = this.source.basicTaskLsit.find(
                (result) =>
                    result.terminal_no == vehicle.terminal_no &&
                    this.source.currentTaskId == result.task_id
            );

            if (!currentResult) return;
            let message = "";
            switch (currentResult.task_state) {
                case 0:
                    message = "操作成功";
                    break;
                case 1:
                    message = "操作失败," + currentResult.message;
                    break;
                case 2:
                    message = "消息错误";
                    break;
                case 3:
                    message = "不支持";
                    break;
            }

            // 只有终端参数才会伴随通用应答回复结果 针对处理
            if (
                currentResult.result &&
                this.callBackCommandList.includes(this.currentCommandType)
            ) {
                vehicle.respond = currentResult.result;
            }
            vehicle.state = 3;
            vehicle.message = message;
        },

        async handleTerminalResult(vehicle) {
            // 具有返回数据的应答
            if (!this.additionalReturnIndex.includes(this.currentCommandType))
                return;
            if (!this.source.additionalList.length) return;
            let currentResult = this.source.additionalList.find(
                (result) => result.terminalNo == vehicle.terminal_no
            );
            if (!currentResult) {
                return;
            } else {
                this.table.list.forEach((item) => {
                    item.state = 3;
                    item.message = "终端已回复";
                });
            }
            vehicle.respond = currentResult;
            this.source.additionalList.remove(currentResult);
            let index = this.table.list.findIndex(
                (vehicle) => vehicle.state < 3
            );
            if (index == -1) {
                this.source.basicTaskLsit = [];
                clearInterval(INTERVAL);
                INTERVAL = null;
                clearTimeout(TIMER);
                TIMER = null;
                this.source.loading = false;
            }
        },

        returnRespond(result) {
            if (!result) return;
            switch (this.currentCommandType) {
                case 7:
                case 8:
                    this.fileBack.show = true;
                    this.fileBack.comingNew = false;
                    this.fileBack.current =
                        result.respond.terminalNo || "2333333";
                    break;
                case 14:
                    this.$refs["commandAudio"].handleCommandReturn(result);
                    break;
                case 17:
                    this.$refs[
                        "ImageInformationAlarmCommand"
                    ].handleCommandReturn(result);
                    break;
                case 18:
                    this.$refs[
                        "TerminalSleepWakeupCommand"
                    ].handleCommandReturn(result);
                    break;
                case 19:
                    this.$refs[
                        "QueryTerminalAudioAndVideoCommand"
                    ].handleCommandReturn(result);
                    break;
                case 20:
                    this.$refs["TerminalWakeupCommand"].handleCommandReturn(
                        result
                    );
                    break;
                case 21:
                    this.$refs[
                        "audioAndVideoChannelListCommand"
                    ].handleCommandReturn(result);
                    break;
                case 22:
                    this.$refs["singleVideoChannelCommand"].handleCommandReturn(
                        result
                    );
                    break;
                case 23:
                    this.$refs["specialAlarmVideo"].handleCommandReturn(result);
                    break;
                case 24:
                    this.$refs["videoRelatedAlarm"].handleCommandReturn(result);
                    break;
            }
        },
        nodeCheck(data, { checkedNodes }) {
            if (!data) return;
            this.table.list = [];
            this.table.terminalList = [];
            let checkNode = checkedNodes.filter((item) => item.type == 4);
            if (!checkNode.length) return;
            checkNode.forEach((item) => {
                this.table.terminalList.push({
                    terminal_no: item.terminalNo,
                    vehicle_id: item.id,
                });
                this.table.list.push({
                    terminal_no: item.terminalNo,
                    vehicle_id: item.id,
                    vehicle_name: item.name,
                    company_name: this.basicByVehicleId[item.id].companyName,
                    state: 0,
                    message: "等待发送",
                    respond: null,
                });
            });
            this.$nextTick(() => {
                this.$refs.table.doLayout();
            });
        },
        clearFileCurrent() {
            this.fileBack.show = false;
            this.fileBack.current == "233333";
            this.fileBack.comingNew = false;
        },

        clearCurrentCommand() {
            this.fileBack.list = [];
            this.fileBack.show = false;
            this.fileBack.comingNew = false;

            this.source.basicTaskLsit = [];
            this.source.additionalList = [];

            this.table.list.forEach((item) => {
                item.state = 0;
                item.message = "等待发送";
                item.respond = null;
            });
        },
    },

    beforeDestroy() {
        if (this.source.subscriptList.length) {
            this.source.subscriptList.forEach((item) => item.unsubscribe());
            this.source.subscriptList = [];
        }
        if (INTERVAL) {
            clearInterval(INTERVAL);
            INTERVAL = null;
        }
        if (TIMER) {
            clearTimeout(TIMER);
            TIMER = null;
        }
    },
};
</script>

<style lang="scss" scoped>
.vehicle-command {
    width: 100%;
    height: 100%;

    .command-content {
        height: 100%;
        display: flex;
        flex-direction: column;

        .table-item {
            flex-flow: 1;
            height: 55%;
        }

        .tool-item {
            height: max-content;
            max-height: 45%;
        }
    }
}
</style>

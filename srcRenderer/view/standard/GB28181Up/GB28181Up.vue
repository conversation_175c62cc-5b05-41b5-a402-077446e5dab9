<template>
  <Layout :content-loading="loading">
    <template slot="query">
      <div class="query-item">
        <el-button size="mini" type="primary" @click="getAllList()">查询</el-button>
      </div>
      <div class="query-item">
        <el-button size="mini" type="primary" @click="refreshTerminal()" v-if="hasPermission('GB28181Up:refreshTerminal')">刷新</el-button>
      </div>
      <div class="query-item">
        <el-button size="mini" type="primary" @click="addConfig()">新增</el-button>
      </div>
      <div class="query-item">
        <el-button size="mini" type="primary" @click="exportDataInfo()" :loading="exportLoading">导出</el-button>
      </div>

      <div class="break-item"></div>
      <div class="query-item">
        <el-pagination
          small
          background
          :current-page.sync="table.page"
          :page-size="table.size"
          :total="table.data.length"
          layout="prev, pager, next, total"
        >
        </el-pagination>
      </div>
    </template>
    <template slot="content">
      <el-table
        class="el-table--radius"
        stripe
        :data="formatList"
        height="100%"
        border
        ref="table"
        highlight-current-row
        style="width: 100%"
      >
        <el-table-column type="index" :index="(index) => index + 1 + pageStart" label="序号" min-width="45"> </el-table-column>
        <el-table-column label="操作" min-width="95">
          <template slot-scope="{ row }">
            <el-button type="text" title="修改" size="mini">
              <i class="pony-iconv2 pony-xiugai" @click="modifyItem(row)"></i>
            </el-button>
            <el-button type="text" :title="row.status == -1 ? '开始' : '暂停'" size="mini">
              <i :class="['pony-iconv2', row.status == -1 ? 'pony-bofang' : 'pony-zanting']" @click="pauseItem(row)"></i>
            </el-button>
            <el-button type="text" title="删除" size="mini">
              <i class="pony-iconv2 pony-shanchu" @click="removeItem(row)"></i>
            </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="上级平台名称" min-width="140" :show-overflow-tooltip="true"> </el-table-column>
        <el-table-column prop="serverGbId" label="上级平台编码ID" min-width="160" :show-overflow-tooltip="true">
        </el-table-column>
        <el-table-column prop="serverIp" label="上级平台IP" min-width="100" show-overflow-tooltip> </el-table-column>
        <el-table-column prop="serverPort" label="上级平台端口" min-width="140"> </el-table-column>
        <el-table-column prop="deviceGbId" label="本地平台编码ID" min-width="160" show-overflow-tooltip> </el-table-column>
        <el-table-column prop="username" label="上级平台连接用户名" min-width="160" show-overflow-tooltip> </el-table-column>
        <el-table-column prop="password" label="上级平台连接密码" min-width="160" show-overflow-tooltip> </el-table-column>
        <el-table-column prop="registerTime" label="上次注册时间" min-width="140"> </el-table-column>
        <el-table-column prop="keepaliveTime" label="上次发送心跳时间" min-width="140"> </el-table-column>
        <el-table-column prop="errors" label="上次通讯错误信息" min-width="120" show-overflow-tooltip> </el-table-column>
        <el-table-column prop="deviceNumber" label="设备数量" min-width="80"> </el-table-column>
        <el-table-column prop="createBy" label="创建人" min-width="90"> </el-table-column>
        <el-table-column prop="createTime" label="创建时间" min-width="140" show-overflow-tooltip> </el-table-column>
        <el-table-column prop="updateBy" label="更新人" min-width="90"> </el-table-column>
        <el-table-column prop="updateTime" label="更新时间" min-width="140" show-overflow-tooltip> </el-table-column>
        <el-table-column prop="remark" label="备注" min-width="160" show-overflow-tooltip> </el-table-column>
      </el-table>
    </template>
    <AddConfigModel ref="addConfigModel" @refresh="getAllList"></AddConfigModel>
  </Layout>
</template>

<script>
import AddConfigModel from "./components/AddConfigModel.vue";
export default {
  name: "gB28181Up",
  components: {
    AddConfigModel,
  },
  data() {
    return {
      loading: false,
      exportLoading: false,
      table: {
        data: [],
        page: 1,
        size: 30,
      },
      tableDataList: [
        { name: "上级平台名称", key: "name", size: "140", align: "center", type: 0, sort: false },
        { name: "上级平台编码ID", key: "serverGbId", size: "140", align: "center", type: 0, sort: false },
        { name: "上级平台IP", key: "serverIp", size: "100", align: "center", type: 0, sort: false },
        { name: "上级平台端口", key: "serverPort", size: "140", align: "center", type: 0 },
        { name: "本地平台编码ID", key: "deviceGbId", size: "140", align: "center", type: 0 },
        { name: "上级平台连接用户名", key: "username", size: "160", align: "center", type: 0 },
        { name: "上级平台连接密码", key: "password", size: "160", align: "center", type: 0 },
        { name: "上次注册时间", key: "registerTime", size: "140", align: "center", type: 0 },
        { name: "上次发送心跳时间", key: "keepaliveTime", size: "140", align: "center", type: 0 },
        { name: "上次通讯错误信息", key: "errors", size: "120", align: "center", type: 0 },
        { name: "设备数量", key: "deviceNumber", size: "80", align: "center", type: 0 },
        { name: "创建人", key: "createBy", size: "90", align: "center", type: 0 },
        { name: "创建时间", key: "createTime", size: "140", align: "center", type: 0 },
        { name: "更新人", key: "updateBy", size: "90", align: "center", type: 0 },
        { name: "更新时间", key: "updateTime", size: "140", align: "center", type: 0, sort: false },
        { name: "备注", key: "remark", size: "160", align: "center", type: 0 },
      ],
    };
  },
  computed: {
    pageStart() {
      return (this.table.page - 1) * this.table.size;
    },

    formatList() {
      return this.table.data.slice(this.pageStart, this.pageStart + this.table.size);
    },
  },
  mounted() {
    this.getAllList();
  },

  methods: {
    async getAllList() {
      this.loading = true;
      let result = await this.$api.terminalGBPlatform({
        operateType: 2,
      });

      if (!result) {
        this.$error("查询出错");
        return;
      }
      if (result.status != 200) {
        this.$warning(result.message || "查询出错");
        return;
      }
      if (!result.data.length) {
        this.$warning("未查询到数据!");
        this.loading = false;
        return;
      }
      this.loading = false;
      this.table.data = result.data;
    },
    async refreshTerminal() {
      this.loading = true;
      let result = await this.$api.terminalGBPlatform({
        // 固定标识102，同步场地终端的变更（场地加终端，改终端，删终端）
        operateType: 102,
      });

      if (!result) {
        this.$error("更新出错");
        return;
      }
      if (result.status != 200) {
        this.$warning(result.message || "更新出错");
        return;
      }
      this.loading = false;
      this.$success(result.data);
    },
    addConfig() {
      this.$refs.addConfigModel.showModel(null);
    },
    modifyItem(row) {
      this.$refs.addConfigModel.showModel(row);
    },
    async pauseItem(row) {
      this.loading = true;
      let params = {
        operateType: 101,
        id: row.id,
        enable: row.status == -1 ? true : false,
      };
      let res = await this.$api.terminalGBPlatform(params);
      this.loading = false;
      if (res && res.status == 200) {
        this.$success("操作成功");
        row.status = row.status == -1 ? 0 : -1;
      } else {
        this.$error("操作失败" + res.message);
      }
    },
    async removeItem(row) {
      await this.$confirm("确认要删除此配置?", "确认删除", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "error",
      });
      let res = await this.$api.terminalGBPlatform({
        operateType: -2,
        ids: [row.id],
      });
      if (res.status === 200) {
        this.$success("删除成功");
        this.table.data = this.table.data.filter((item) => item.id !== row.id);
      } else {
        this.$error("删除失败" + res.message);
      }
    },
    //导出表格
    async exportDataInfo() {
      if (this.table.data.length == 0) {
        this.$warning("没有数据可以导出");
        return;
      }
      let excelBody = this.table.data;
      let params = {};
      let paramsList = [];
      let sheetName = [];
      this.tableDataList.forEach((item) => sheetName.push(`${item.name}@${item.key}@10000@000000`));
      params = {
        sheetName: "28181上级平台配置",
        title: "28181上级平台配置",
        headers: sheetName,
        dataList: excelBody,
      };
      paramsList.push(params);
      this.exportLoading = true;
      await this.$utils.jsExcelExport(JSON.stringify(paramsList), "28181上级平台配置" + ".xlsx");
      this.exportLoading = false;
    },
  },
};
</script>

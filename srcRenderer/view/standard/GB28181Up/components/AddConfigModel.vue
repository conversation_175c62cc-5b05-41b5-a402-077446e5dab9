<template>
  <PonyDialog
    width="1000"
    :title="mode == 'add' ? '新增' : '修改'"
    v-model="show"
    :loading="loading"
    contentStyle="height:450px;padding:20px"
    @confirm="submit"
    @close="closeModel"
    hasMask
  >
    <div class="tree">
      <ElementTree
        :type="treeType"
        :alwaysNew="true"
        ref="tree"
        v-if="treeType"
        :checkMode="true"
        @check="selectNodes"
        :elementTypeList="elementTypeList"
        :elementType="true"
        @change="treeChange"
        nodeKeyId="id"
      ></ElementTree>
    </div>
    <div class="formWarp">
      <el-form class="form" :model="data" :rules="rules" ref="form" label-width="155px">
        <el-form-item label="上级平台名称:" prop="name">
          <el-input v-model="data.name" placeholder="请输入内容"></el-input>
        </el-form-item>
      </el-form>
      <div style="width: 100%; margin: 18px 0; display: flex; justify-content: center; align-items: center">
        <div style="width: 20%; border-bottom: 1px solid #ccc"></div>
        <span style="padding: 0 20px">上级平台</span>
        <div style="width: 20%; border-bottom: 1px solid #ccc"></div>
      </div>

      <div style="width: 100%; margin: 0 0; display: flex; justify-content: center; align-items: center">
        <el-form style="width: 50%" class="form" :model="data" :rules="rules" ref="form" label-width="155px">
          <el-form-item label="上级平台IP:" prop="serverIp">
            <el-input v-model="data.serverIp" placeholder="请输入内容"></el-input>
          </el-form-item>
        </el-form>
        <el-form style="width: 50%" class="form" :model="data" :rules="rules" ref="form" label-width="155px">
          <el-form-item label="上级平台端口:" prop="serverPort">
            <el-input v-model="data.serverPort" placeholder="请输入内容"></el-input>
          </el-form-item>
        </el-form>
      </div>
      <div style="width: 100%; margin: 0 0; display: flex; justify-content: center; align-items: center">
        <el-form style="width: 50%" class="form" :model="data" :rules="rules" ref="form" label-width="155px">
          <el-form-item label="上级平台连接用户名:" prop="username">
            <el-input v-model="data.username" placeholder="请输入内容"></el-input>
          </el-form-item>
        </el-form>
        <el-form style="width: 50%" class="form" :model="data" :rules="rules" ref="form" label-width="155px">
          <el-form-item label="上级平台连接密码:" prop="password">
            <el-input v-model="data.password" placeholder="请输入内容"></el-input>
          </el-form-item>
        </el-form>
      </div>

      <el-form class="form" :model="data" :rules="rules" ref="form" label-width="155px">
        <el-form-item label="上级平台编码ID:" prop="serverGbId">
          <el-input v-model="data.serverGbId" placeholder="请输入内容"></el-input>
        </el-form-item>
      </el-form>
      <div style="width: 100%; margin: 18px 0; display: flex; justify-content: center; align-items: center">
        <div style="width: 20%; border-bottom: 1px solid #ccc"></div>
        <span style="padding: 0 20px">下级平台</span>
        <div style="width: 20%; border-bottom: 1px solid #ccc"></div>
      </div>
      <el-form class="form" :model="data" :rules="rules" ref="form" label-width="155px">
        <el-form-item label="本地平台编码ID:" prop="deviceGbId">
          <el-input v-model="data.deviceGbId" placeholder="请输入内容"></el-input>
        </el-form-item>
        <el-form-item label="备注:" prop="remark">
          <el-input v-model="data.remark" type="textarea" :rows="4"></el-input>
        </el-form-item>
      </el-form>
    </div>
  </PonyDialog>
</template>

<script>
import { set } from "vue";
export default {
  name: "addConfigModel",
  data() {
    const defaultData = {
      name: "",
      serverGbId: "",
      serverIp: "",
      serverPort: "",
      username: "",
      deviceGbId: "",
      password: "",
      remark: "",
    };
    return {
      show: false,
      loading: false,
      mode: "add",
      treeType: "workSiteVideo",
      data: JSON.parse(JSON.stringify(defaultData)),
      defaultData,
      rowId: null,
      currentIdList: [],
      rules: {
        name: [{ required: true, message: "请输入内容", trigger: "blur" }],
        serverGbId: [{ required: true, message: "请输入内容", trigger: "blur" }],
        serverIp: [{ required: true, message: "请输入内容", trigger: "blur" }],
        serverPort: [{ required: true, message: "请输入内容", trigger: "blur" }],
        password: [{ required: true, message: "请输入内容", trigger: "blur" }],
        deviceGbId: [{ required: true, message: "请输入内容", trigger: "blur" }],
        // username : [
        //     { required: true, message: '请输入内容', trigger: 'blur' }
        // ],
      },
      renderDataType: 5,
      elementTypeList: [
        {
          type: "workSiteVideo",
          value: 0,
          name: "工地",
          renderDataType: 5,
        },
        {
          type: "xiaoNaVideo",
          value: 1,
          name: "消纳场",
          renderDataType: 5,
        },
        {
          type: "pointTerminalWorksite",
          value: 2,
          name: "收集点",
          renderDataType: 6,
        },
        {
          type: "stopTerminalWorksite",
          value: 3,
          name: "停车场",
          renderDataType: 6,
        },
      ],
    };
  },

  methods: {
    async showModel(row) {
      this.show = true;
      this.data = JSON.parse(JSON.stringify(this.defaultData));
      this.rowId = null;
      this.currentIdList = [];
      await this.$nextTick();
      await this.$refs["tree"].waitForInit;
      if (row) {
        this.mode = "modify";
        this.$utils.assign(this.data, row);
        this.rowId = row.id;
        let deviceIds = row.deviceIds ? JSON.parse(JSON.stringify(row.deviceIds)) : [];
        this.currentIdList = deviceIds;
        if (!deviceIds.length) return;
        //4个树循环找节点,节点在哪个树上就选哪个树(只选第一个,其他的等手动切换树时再渲染,若第一个没有,则选第二个,以此类推)
        for (let i = 1; i <= this.elementTypeList.length; i++) {
          let isReturn = this.treeChecked();
          if (isReturn) {
            return;
          } else {
            this.$refs["tree"].query.elementType = i;
            await this.treeChange(i, false);
          }
        }
      } else {
        this.mode = "add";
      }
    },
    submit() {
      this.$refs["form"].validate(async (valid) => {
        if (!valid) return;
        if (this.mode == "add") {
          this.add();
        } else {
          this.update();
        }
      });
    },
    async closeModel() {
      // this.$refs['tree'].query.elementType = 0
      this.treeChange(0, false);
      this.show = false;
    },
    treeChecked() {
      if (!this.currentIdList.length) return false;
      //获取所有树节点
      let allTreeData = this.$refs["tree"].$refs.tree.store
        ._getAllNodes()
        .filter((item) => item.data.iconSkin == "terminal")
        .map((item) => item.data);
      //筛选出所有选中的节点data
      let selectDataList = allTreeData.filter((item) => this.currentIdList.includes(item.hrefName));
      if (selectDataList.length) {
        //若这个树有节点,选中
        this.$refs["tree"].$refs.tree.setCheckedKeys(selectDataList.map((item) => item.id));
        this.$refs["tree"].$refs.tree.setCurrentKey(selectDataList.map((item) => item.id));
        selectDataList.forEach((item) => {
          let node = this.$refs["tree"].$refs.tree.getNode(item);
          node?.expand(null, true);
        });
        return true;
      }
      return false;
    },
    async add() {
      try {
        const params = {
          ...this.data,
          deviceIds: this.currentIdList,
        };
        this.loading = true;

        let res = await this.$api.terminalGBPlatform({
          operateType: 0,
          ...params,
        });
        if (res.status === 200) {
          this.$success("新增成功");
          this.closeModel();
          this.$emit("refresh");
        } else {
          throw new Error("新增失败" + res.message);
        }
      } catch (e) {
        this.$error(e);
      } finally {
        this.loading = false;
      }
    },
    async update() {
      try {
        const params = {
          ...this.data,
          deviceIds: this.currentIdList,
          id: this.rowId,
        };
        this.loading = true;
        let res = await this.$api.terminalGBPlatform({
          operateType: 1,
          ...params,
        });
        if (res.status === 200) {
          this.$success("修改成功");
          this.closeModel();

          this.$emit("refresh");
        } else {
          throw new Error("修改失败" + res.message);
        }
      } catch (e) {
        this.$error(e);
        return false;
      } finally {
        this.loading = false;
      }
    },
    selectNodes(data, { checkedNodes }) {
      // let currentIds = checkedNodes.filter(item=>{
      //   return item.type === (this.renderDataType+1)
      //  }).map(item => item.hrefName)
      // this.$refs['tree'].$refs.tree.setCheckedKeys(checkedNodes.map(item=>item.id))

      let allTreeData = this.$refs["tree"].$refs.tree.store._getAllNodes().filter((item) => item.data.iconSkin == "terminal");
      this.currentIdList = allTreeData.filter((item) => item.checked).map((item) => item.data.hrefName);
    },
    async treeChange(val, check = true) {
      this.$refs["tree"].loading = true;
      this.treeType = this.elementTypeList[val].type;
      this.renderDataType = this.elementTypeList[val].renderDataType;
      await this.$refs["tree"].initTree(this.treeType);
      if (check) this.treeChecked();
    },
  },
};
</script>
<style lang="scss" scoped>
.tree {
  float: left;
  width: 40%;
  height: 100%;
  // overflow: auto;
}
.formWarp {
  float: left;
  width: 60%;
  height: 100%;
  // overflow: auto;
}
</style>

<template>
    <CompareLayout
        ref="comparelayout"
        @sortChange="generateCompareData">

        <div class="queryList dfaw" :slot="index" 
            v-for="(item, index) in queryList" :key="item.vehicle">
            <div class="queryList__card" @click="readyAddNewSortObj(index)">{{ item.name }}</div>
            <div class="queryList__card timeLayer" v-show="item.start_time">
                {{ DateFormat(item.start_time) }} &nbsp; ~ &nbsp; {{ DateFormat(item.end_time)}}
            </div>
        </div>

        <PonyDialog :title="$ct('name')" width="600"
            v-model="modal.show"
            contentStyle="height: 400px;"
            @confirm="changeSortSource">
            <el-row :gutter="20">
                <el-col :span="11">
                    <!-- <ZtreeMatics v-show="modal.data.type == 1" treeType="vehicle" ref="vehicleZtree" :checkMode="false" style="height: 350px"></ZtreeMatics> -->
                    <ElementTree type="vehicle" ref="vehicleZtree" v-show="modal.data.type == 1"  :checkMode="false" @node-click="selectNodes" style="height: 350px">
                    </ElementTree>
                    <!-- <ZtreeMatics v-show="modal.data.type == 2" treeType="driverTree" ref="driverZtree" :checkMode="false" style="height: 350px"></ZtreeMatics> -->
                    <ElementTree  v-show="modal.data.type == 2" type="driverTree" ref="driverTree" :checkMode="false" @node-click="selectNodes1" style="height: 350px">
                    </ElementTree>
                    
                    <ElementTree  v-show="modal.data.type == 0" type="department" ref="department" :checkMode="false" @node-click="selectNodes2" style="height: 350px">
                    </ElementTree>
                </el-col>
                <el-col :span="13">
                    <el-form class="dfbw" style="height: 140px" ref="form" 
                        :model="modal.data" 
                        label-width="80px">
                        <el-form-item :label="$ct('searchObj')" prop="start_time">
                            <el-radio-group v-model="modal.data.type">
                                <el-radio-button :label="1">{{ $ct(`searchValue[${1}]`) }}</el-radio-button>
                                <el-radio-button :label="2">{{ $ct(`searchValue[${2}]`) }}</el-radio-button>
                                <el-radio-button :label="0">{{ $ct(`searchValue[${0}]`) }}</el-radio-button>
                            </el-radio-group>
                        </el-form-item>
                            
                        <!-- <el-form-item :label="$ct('startDate')" prop="start_time">
                            <el-date-picker
                                v-model="modal.data.start_time"
                                @change="dateStartChange"
                                type="date"
                                value-format="timestamp"
                                :clearable="false"
                                :picker-options="pickerOptions">
                            </el-date-picker>
                        </el-form-item>
                        <el-form-item :label="$ct('endDate')" prop="end_time">
                            <el-date-picker
                                v-model="modal.data.end_time"
                                type="date"
                                :clearable="false"
                                value-format="timestamp"
                                :picker-options="endDatePickerOptions">
                            </el-date-picker>
                        </el-form-item> -->
                    </el-form>
                    <StartEndTime
          itemHeight="35"
          v-model="selectStartEndTime"
          valueFormat="timestamp"
          timeType="date"
          :isLimit="true"
        >
        </StartEndTime>
                </el-col>
            </el-row>
        </PonyDialog>

    </CompareLayout>
</template>

<script>
import CompareLayout from './components/CompareLayout'
import { getContrastSetting } from './components/CompareSetting'
import StartEndTime from "@/components/common/StartEedTime";
export default {
    name: 'scoreCompare',
    components: { CompareLayout,StartEndTime },
    data () {
        return {
            queryList: [
                { name: this.$ct('click'), end_time: '', start_time: '', response: {} },
                { name: this.$ct('click'), end_time: '', start_time: '', response: {} },
                { name: this.$ct('click'), end_time: '', start_time: '', response: {} },
                { name: this.$ct('click'), end_time: '', start_time: '', response: {} },
            ],
            
            sortInstaranceResult: null,

            modal: {
                show: false,
                addIndex: -1,
                
                data: {
                    type: 1,
                    start_time: moment().subtract(1, "days").startOf('day').valueOf(), 
                    end_time: moment().subtract(1, "days").endOf('day').valueOf(),
                }
            },

            pickerOptions: {
                disabledDate: function (date) {
                    return (date - moment().endOf('day').toDate()) > 0
                }
            },
            vehicleId:null,
            driverId:null,
            departId:null,
            selectStartEndTime: [
        moment().startOf("day").subtract(1, 'days').valueOf(),
        moment().endOf("day").subtract(1, 'days').valueOf(),
      ],
        };
    },

    computed: {
        endDatePickerOptions: function () {
            return {
                disabledDate: (date) => {
                    return (date - moment().endOf('day').toDate()) > 0 ||
                        date - this.modal.data.start_time < 0;
                }
            }
        },
    },
    watch: {
        selectStartEndTime: function (newVal, oldVal) {
      this.modal.data.start_time = newVal[0]
      this.modal.data.end_time = newVal[1]
    }
    },
    mounted() {
        this.readyCompareSetting() 
    },
    methods: {
        readyCompareSetting() {
            getContrastSetting.score.forEach(item => {
                item.name = this.$ct(`${ item.sign }.name`)
                item.children.forEach(rule => {
                    rule.name = this.$ct(`${ item.sign }.${ rule.sign }`)
                })
            })
            this.$refs['comparelayout'].readyDataInfoObj(getContrastSetting.score)
        },

        readyAddNewSortObj(index) {
            this.modal.show = true
            this.modal.addIndex = index
        },
        selectNodes(current) {
                if(current.type==4){
                    this.vehicleId = current;
                }
        },
        selectNodes1(current) {
                if(current.type==4){
                    this.driverId = current;
                }
        },
        selectNodes2(current) {
            if(current.type==3){
            this.departId = current;

            }
        },
        async changeSortSource() {
            let current
            switch (this.modal.data.type) {
                case 0:
                    current = { target_id: this.departId.id, name: this.departId.name }
                    break;
                case 1:
                    current = { target_id: this.vehicleId.id, name: this.vehicleId.name } 
                    break;
                case 2:
                    current ={ target_id: this.driverId.id, name: this.driverId.name } 
                    break;
            }

            let existence = this.queryList.find(query => 
                query.target_id == current.target_id &&
                query.start_time == this.modal.data.start_time &&
                query.end_time == this.modal.data.end_time
            )

            if(existence) {
                this.$message({ showClose: true, message: this.$ct('simpleParmas'), type: 'warning' })
                return
            }

            if(!current) {
                this.$message({ showClose: true, message: this.$ct('noQuery'), type: 'warning' })
                return
            }

            let obj = {
                target_type: this.modal.data.type,
                target_id: current.target_id,
                name: current.name,
                start_time: this.modal.data.start_time,
                end_time: this.modal.data.end_time
            }
            let currentQuery = this.queryList[this.modal.addIndex]
            Object.assign(currentQuery, JSON.parse(JSON.stringify(obj)))
            let result = await this.$api.queryCommonCompare(JSON.parse(JSON.stringify(currentQuery)))

            if(!result || result.status != 200) {
                this.$message({ showClose: true, message: result.message || this.$ct('queryError'), type: 'warning' })
                return
            }
            currentQuery.response = result.data
            this.handleSortResult()
            this.modal.show = false

        },

        handleSortResult() {
            this.queryList.forEach((item, index) => {
                let current = JSON.parse(JSON.stringify(item.response))
                let keyList = Object.keys(current)
                keyList.forEach(key => {
                    if(!this.sortInstaranceResult[key]) return
                    let result = current[key] == -1?'-':current[key]
                    this.sortInstaranceResult[key].result.splice(index, 1, result || '-')
                })
            })
            Object.values(this.sortInstaranceResult).forEach(item => {
                let sortList = item.result.filter(num => +num >= 0 && isFinite(+num))
                item.min = Math.min(...sortList);
                item.max = Math.max(...sortList);
            })
            this.$refs['comparelayout'].resortCompare(this.sortInstaranceResult)
        },

        generateCompareData(result, index) {
            this.sortInstaranceResult = JSON.parse(JSON.stringify(result)) 
            if(!index) return

            if(index == -1) {
                this.queryList.push({ name: this.$ct('click'), end_time: '', start_time: '', response: {} })
            } else {
                this.queryList.splice(index, 1)
            }
        },

        dateStartChange(date) {
            if (this.modal.data.end_time - date < 0) {
                this.modal.data.end_time = moment(date).endOf('day').valueOf();
            }
        },
    }
}

</script>

<style lang='scss' scoped>
.queryList {
    height: 100%;
    width: 100%;
    padding: 10px 0;

    &__card {
        width: 100%;
        text-align: center;
        font-size: 12px;

        &:first-child {
            cursor: pointer;
        }

        &.timeLayer {
            background-color: var(--background-color-stripe);
            line-height: 30px;
            border-radius: 50px;
            width: max-content;
            padding: 0 10px;
        }
    }
}
</style>
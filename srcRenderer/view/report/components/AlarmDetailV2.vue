<template>
    <PonyDialog v-model="show"
                class="alarm-detail-v2" width="750" extra-width="350"
                content-style="width:400px;min-height:320px;padding:10px 10px 0 10px"
                @close="show = false" :loading="loading"
                :has-mask="false"
    >
        <template slot="title">
            <span class="pointer text text--link" :title="$ct('messageInfo.5')"
                  @mousedown.stop="copy">{{ detailList.plate_no }}</span>
            <span :title="$ct('label.driver')" style="margin-left: 5px">{{ detailList.driver_name }}</span>
            <span> - <i class="pony-iconv2 pony-jietu1" @click="readyExportPDF" style="cursor: pointer;" title="截图"></i></span>
            <el-button type="primary" @click="alarmConfirm" v-if="needConfirm.includes(detailList.alarm_type_code)">报警确认
            </el-button>
        </template>
        <div class="left-wrap">
            <ul class="alarm-info-list">
                <li class="info-item"
                    style="max-width:220px;text-overflow:ellipsis;overflow: hidden;white-space: nowrap;">
                    <span class="title">报警类型：</span>
                    <span>{{ detailList.alarm_type }}</span>
                </li>
                <li class="info-item short">
                    <span class="title">报警级别：</span>
                    <span>{{ detailList.alarm_level || '' }}</span>
                </li>
                <li class="info-item long">
                    <span class="title">{{ $ct('label.company') }}：</span>
                    <span>{{ (detailList.company_name + '>>' + detailList.fleet_name) || $ct('label.notSet') }}</span>
                </li>
                <li class="info-item long">
                    <span class="title">驾驶员：</span>
                    <span>{{ detailList.driver_name || '' }}</span>
                </li>
                <li class="info-item">
                    <span class="title">{{ $ct('label.startTime') }}：</span>
                    <span>{{ detailList.alarm_time }}</span>
                </li>
                <li class="info-item short">
                    <span class="title">持续时间：</span>
                    <span>{{ detailList.duration | formatTime }}</span>
                </li>
                <li class="info-item">
                    <span class="title"> {{ $ct('label.endTime') }}：</span>
                    <span>{{ detailList.alarm_time_end || $ct('label.noEnd') }}</span>
                </li>
                <li class="info-item short">
                    <span class="title">车速：</span>
                    <span>{{
                            (detailList.gps_speed || detailList.gps_speed == 0) ? detailList.gps_speed + 'km/h' : '-'
                        }}</span>
                </li>
                <li class="info-item" v-if="hasPermission('lockMap:detail')">
                    <span class="title">辖区：</span>
                    <span>{{ detailList.jurisdiction_name || '' }}</span>
                </li>
                <li class="info-item short">
                    <span class="title">持续里程：</span>
                    <span>{{ mile || '-' }}km</span>
                </li>
                <li class="info-item long">
                    <span class="title">开始位置：</span>
                    <span>{{ detailList.location || '' }}</span>
                </li>
                <li class="info-item long">
                    <span class="title">结束位置：</span>
                    <span>{{ detailList.location_end || '' }}</span>
                </li>
                <div class="detail-title">详细信息</div>
                <li class="info-item long" v-if="detailList.alarm_type_code==278">
                    <div>
                        <el-button type="primary" size="mini" @click="jumpOnPRFID">查看详情</el-button>
                    </div>
                </li>
            </ul>
            <div class="alarm-card-list detail" v-if="detailList.fenseSli258Param" style="font-size:12px;padding:10px">
                    <div v-if="detailList.fenseSli258Param">{{detailList.fenseSli258Param}}</div>
            </div>
            <div class="alarm-card-list" v-else-if="detailList.alarm_param && detailList.alarm_param.length">
		        <!-- <div v-if="detailList.roadSpeedLimit" class="info-item long"><span>限速值： </span><span>{{ detailList.roadSpeedLimit }}km/h</span></div> -->
                <div class="info-item long" v-for="(item,index) in detailList.alarm_param" :key="index">
                    <span>{{ item.name }}：</span>
                    <span>{{ item.value }}</span>
                    <span v-if="!isNaN(item.value)">{{ item.unit }}</span>
                </div>
            </div>
            <div class="alarm-card-list detail" v-else-if="detailList.platfromOverSpeedParam"
                 style="font-size:12px;padding:10px">
                <div>{{ detailList.platfromOverSpeedParam }}</div>
            </div>
            <div class="alarm-card-list detail" v-else-if="detailList.detail" style="font-size:12px;padding:10px">
                <div v-if="detailList.roadSpeedLimit" class="info-item long"><span>限速值： </span><span>{{ detailList.roadSpeedLimit }}km/h</span></div>
                {{ detailList.detail }}
            </div>
            <div class="alarm-card-list detail" v-else-if="detailList.roadSpeedLimit"
                 style="font-size:12px;padding:10px">
                <div v-if="detailList.roadSpeedLimit">限速值： {{ detailList.roadSpeedLimit }}km/h</div>
            </div>
            <div class="alarm-card-list detail" v-else-if="detailList.alarm_type_code == 271 || detailList.alarm_type_code == 255"
                 style="font-size:12px;padding:10px">
                <div v-if="detailList.collectStartTime">开始计算时间： {{ detailList.collectStartTime }}</div>
            </div>
            <div v-else style="font-size:12px;padding:10px">
                无
            </div>
            <div class="deal-box">
                <div class="top">
                    <el-select v-model="dealType"
                               :disabled="!canDealType.includes(detailList.deal_type)">
                        <el-option v-for="(item,key) in dealTypeMap" :value="parseInt(key)" :disabled="key ==0"
                                   :label="item"
                                   :key="key"></el-option>
                    </el-select>
                    <el-select v-model="templateId" @change="setMessageTemplate"
                               v-if="dealType===8"
                               :disabled="!canDealType.includes(detailList.deal_type)">
                        <el-option v-for="item in templateList" :key="item.value" :value="item.value"
                                   :label="item.label"></el-option>
                    </el-select>
                    <div class="dealFunction">
                        <span><i class="pony-iconv2 pony-baojingjiechu" title="报警解除" @click="sendCommand('clear')"></i></span>
                        <span><i class="pony-iconv2 pony-paizhao" title="拍照"
                                 @click="sendCommand('photo')"></i></span>
                        <span><i class="pony-iconv2 pony-dianhua" title="电话" @click="sendCommand('phone')"></i></span>
                        <span><i class="pony-iconv2 pony-xinxi" title="文本" @click="sendCommand('text')"></i></span>
                    </div>
                </div>
                <el-input type="textarea" :rows="2" v-model="remark" @keydown.native="templateId = 0"
                          :disabled="!canDealType.includes(detailList.deal_type)">
                </el-input>
                <el-input placeholder="请输入备注" style="margin-top: 5px" v-if="detailList.deal_type !==0"
                          v-model="remarkAgain"></el-input>
            </div>
        </div>
        <div class="right-wrap" slot="extra">
            <div class="map-wrap" @mousedown.stop>
                <SimpleMap class="map" ref="map" mapConfig="justmap" :mapLittleTool="['scale']"></SimpleMap>
                <div class="play-black-btn" @click="linkToPlayBack"></div>
                <!-- <div class="show-can-run">
                    <el-tooltip class="item" effect="dark" content="显示可通行路段" placement="bottom">
                        <el-switch  @change="showCanRunLine" v-model="canRun.switch"></el-switch>
                    </el-tooltip>
                </div> -->
            </div>
        </div>
        <player ref="player" @played="changeBlue"></player>
        <template slot="footer">
            <slot name="extra-button">
            </slot>
            <div style="flex-grow:1"></div>
            <el-button type="primary" @click="commit"
                       :disabled="dealType === 0 || (detailList.deal_type !== 0 && remarkAgain == '')">
                {{ $ct('label.deal') }}
            </el-button>
            <!--            <el-button size="mini" type="text" v-else>{{ $ct('label.hasDeal') }}</el-button>-->
            <el-button type="border" @click="show = false">{{ $ct('label.cancel') }}</el-button>
        </template>
        <div class="pdfViewBackground" ref="pdfview"></div>
        <!--    拍照弹窗-->
        <PonyDialog
         v-model="photo.show"
         @close="photo.show = false"
         :loading="photo.loading"
         title="拍照"
         :width="500"
         class="photo"
         @confirm="sendPhotoCommand"
        >
            <el-form :model="photoModal.data"
                     label-width="75px"
                     ref="photoForm"
            >
                <el-form-item label="通道">
                    <el-select v-model="photoModal.data.channel_no">
                        <el-option v-for="item in 8" :label="`通道${item}`" :value="item" :key="item"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="拍照方式">
                    <el-select v-model="photoModal.data.operate_cmd">
                        <el-option :value="0" label="停止拍摄"></el-option>
                        <el-option :value="-1" label="开始录像"></el-option>
                        <el-option :value="1" label="开始拍照"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="拍照张数">
                    <el-input v-model="photoModal.data.photoNum"></el-input>
                </el-form-item>
                <el-form-item label="分辨率">
                    <el-select v-model="photoModal.data.pixel">
                        <el-option
                         v-for="item in photoModal.pixelRatio"
                         :label="item.label"
                         :value="item.value"
                         :key="item.value">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="图像质量">
                    <el-select v-model="photoModal.data.quality" style="width:calc(100% - 30px)">
                        <el-option v-for="item in 10" :label="item" :value="item" :key="item"></el-option>
                    </el-select>
                    <el-tooltip class="item" effect="dark" placement="bottom-end">
                        <div slot="content">数值越小，图像质量越好，图片体积越大</div>
                        <i class="pony-iconv2 pony-bangzhu"></i>
                    </el-tooltip>
                </el-form-item>
                <el-form-item label="时间间隔">
                    <el-input-number v-model="photoModal.data.interval" :min="0"
                                     style="width:calc(100% - 30px)"></el-input-number>
                    <el-tooltip class="item" effect="dark" placement="bottom-end">
                        <div slot="content">拍照间隔 / 录像时长; 0 表示按最小间隔拍照或一直录像</div>
                        <i class="pony-iconv2 pony-bangzhu"></i>
                    </el-tooltip>
                </el-form-item>
                <el-form-item label="对比度">
                    <el-input-number v-model="photoModal.data.contrast" :min="0" :max="127"></el-input-number>
                </el-form-item>
                <el-form-item label="饱和度">
                    <el-input-number v-model="photoModal.data.saturation" :min="0" :max="127"></el-input-number>
                </el-form-item>
                <el-form-item label="亮度">
                    <el-input-number v-model="photoModal.data.light" :min="0" :max="255"></el-input-number>
                </el-form-item>
                <el-form-item label="色度">
                    <el-input-number v-model="photoModal.data.chroma" :min="0" :max="127"></el-input-number>
                </el-form-item>
                <el-form-item label="保存标志">
                    <el-select v-model="photoModal.data.save_flag">
                        <el-option label="保存" :value="1"></el-option>
                        <el-option label="实时上传" :value="0"></el-option>
                    </el-select>
                </el-form-item>
            </el-form>
        </PonyDialog>
        <!--    监听弹窗-->
        <PonyDialog
         v-model="phone.show"
         @close="phone.show = false"
         :loading="phone.loading"
         title="监听"
         :width="350"
         class="phone"
         @confirm="sendPhoneCommand"
        >
            <el-form :model="phoneModal"
                     :rules="phoneRules"
                     label-width="85px"
                     ref="phoneForm"
            >
                <el-form-item label="类型">
                    <el-radio-group v-model="phoneModal.type">
                        <el-radio :label="0">普通通话</el-radio>
                        <el-radio :label="1">监听</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="电话号码" prop="tel">
                    <el-input v-model="phoneModal.tel"></el-input>
                </el-form-item>
            
            </el-form>
        </PonyDialog>
        <!--    文本弹窗-->
        <PonyDialog
         v-model="text.show"
         @close="text.show = false"
         :loading="text.loading"
         title="文本下发"
         :width="450"
         class="text"
         
         @confirm="sendTextCommand"
        >
            <el-form :model="messageModal.data"
                     :rules="messageModal.rules"
                     label-width="85px"
                     ref="textForm"
                     label-position="left"
            >
                <el-form-item label="标志">
                    <el-select v-model="messageModal.data.urgent" placeholder="请选择"
                               style="width: 80px; margin-right: 10px;">
                        <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
                        </el-option>
                    </el-select>
                    <el-checkbox style="margin-left:60px;margin-bottom: 20px;" v-model="messageModal.data.add_show">
                        广告屏显示
                    </el-checkbox>
                    <el-checkbox v-model="messageModal.data.terminal_monitor">终端显示器显示</el-checkbox>
                    <el-checkbox v-model="messageModal.data.terminal_tts">终端TTS播报</el-checkbox>
                </el-form-item>
                <el-form-item label="文本类型">
                    <el-radio v-model="messageModal.data.usage" :label="1">
                        通知
                    </el-radio>
                    <el-radio style="margin-left:12px" v-model="messageModal.data.usage" :label="2">
                        服务
                    </el-radio>
                </el-form-item>
                <el-form-item label="信息类型">
                    <el-radio v-model="messageModal.data.center" :label="0">
                        中心导航信息
                    </el-radio>
                    <el-radio style="margin-left:12px" v-model="messageModal.data.center" :label="1">
                        CAN故障码信息
                    </el-radio>
                </el-form-item>
                <el-form-item label="TTS模板">
                    <el-select v-model="ttsmodelresult" @change="textContentChange">
                        <el-option v-for="(item, index) in ttsmodelist" :key="index" :label="item.configDesc"
                                   :value="item.configValue"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="下发内容" prop="content">
                    <el-input type="textarea" :rows="5" v-model="messageModal.data.content"></el-input>
                </el-form-item>
            </el-form>
        </PonyDialog>
    </PonyDialog>
</template>

<script>
/**
 * @Author: yezy
 * @Email: <EMAIL>
 * @Date: 2020/4/3 15:51
 * @LastEditors: yezy
 * @LastEditTime: 2020/4/3 15:51
 * @Description:
 */
import L from '@/assets/lib/leaflet-bmap'
import {getAlarmMapIcon} from '@/view/monitor/util/monitorUtil'
import SimpleMap from '@/view/monitor/leaflet/leafletMap/MaticsMapV2'
import BstandardUtil from '@/util/bstandard'
// import RealtimePlayer from "@/view/videoPlay/component/videoPlayerV2/RealtimePlayer";
import CheckSimOverFlow from "@/view/videoPlay/component/videoPlayerV2/component/CheckSimOverFlow";
import {generatePonylineFenseLayer} from '@/view/monitor/util/generateUtil'
import html2canvas from 'html2canvas'
import Base64 from 'base-64';


const startIcon = L.icon({
    iconUrl: require('../../../../static/img/map/start.png'),
    iconSize: [26, 26],
    className: 'ml_start_point'
})
const endIcon = L.icon({
    iconUrl: require('../../../../static/img/map/end1.png'),
    iconSize: [26, 26],
    className: 'ml_start_point'
})

export default {
    name: "alarmDetailV2",
    _i18Name: 'alarmDetail',
    components: {CheckSimOverFlow, SimpleMap},
    data() {
        return {
            //拍照，监听
            photo: {
                show: false,
                loading: false,
            },
            phone: {
                show: false,
                loading: false,
            },
            text: {
                show: false,
                loading: false,
            },
            photoModal: {
                pixelRatio: [
                    {value: 0, label: '最小分辨率'},
                    {value: 1, label: '320 * 240'},
                    {value: 2, label: '640 * 480'},
                    {value: 3, label: '800 * 600'},
                    {value: 4, label: '1024 * 768'},
                    {value: 5, label: '176 * 144'},
                    {value: 6, label: '352 * 288'},
                    {value: 7, label: '704 * 288'},
                    {value: 8, label: '704 * 576'},
                    {value: 255, label: '最大分辨率'},
                
                ],
                data: {
                    photoNum: 1,
                    channel_no: 1,
                    operate_cmd: 1,
                    interval: 2,
                    pixel: 1,
                    quality: 5,
                    light: 127,
                    contrast: 64,
                    chroma: 64,
                    saturation: 127,
                    save_flag: 1
                },
            },
            phoneModal: {
                type: 1,
                tel: ''
            },
            phoneRules: {
                tel: [
                    {required: true, message: '请输入电话号码！', trigger: 'blur'}
                ],
                
            },
            options: [{
                value: '01',
                label: '服务'
            }, {
                value: '10',
                label: '紧急'
            }, {
                value: '11',
                label: '通知'
            }],
            // 需要确认报警的报警类型
            needConfirm: [201, 204, 221, 223, 228, 229, 222],
            ttsmodelist: [],
            ttsmodelresult: '',
            show: false,
            loading: false,
            params: {
                vehicleId: '',
                eventId: '',
                version: 2,
            },
            detailList: {},
            canDealType: [0, -1, -2],
            dealType: 1,
            dealTypeMap: {},
            remark: '',
            templateId: 0,
            templateList: [
                {label: this.$ct('label.custom'), value: 0, template: ''},
            ],
            gpsList: null,
            resultP: {
                resolve: null,
                reject: null,
            },
            mapInitialized: false,
            channelNo: 1,
            chn: 1,
            autoPlay: false,
            autoClose: true,
            remainingTime: 0,
            stopMouseMoveOptions: {
                target: document,
                time: 300,
                handler: this.stopVideo,
                stateChange: this.onRemainingTime
            },
            
            canRun: {
                switch: false,
                _layer: null
            },
            ttsmodal: {
                modellist: [],
                
                show: false,
                _reject: null,
                _resolve: null,
                data: {
                    type: '',
                    text: ''
                },
                rules: {
                    text: [
                        {required: true, message: '请输入消息内容', trigger: 'blur'},
                    ]
                }
            },
            messageModal: {
                data: {
                    urgent: "01",
                    terminal_monitor: true,
                    terminal_tts: true,
                    add_show: false,
                    center: 0,
                    content: '',
                    usage: 1,
                },
                rules: {
                    content: [
                        {required: true, message: '请输入消息内容', trigger: 'blur'},
                    ]
                }
            },
            // TBOX报警类型,详细信息
            alarm: {
                a: ['持续里程', 'm'],
                h6: ['最大转速值', '转/分钟'],
                h5: ['最小转速值', '转/分钟'],
                h2: ['最大角速度', ''],
                h1: ['最小角速度', ''],
                // maxOverSpeed: ['最大超速值', ''],
                // minOverSpeed: ['最小超速值', ''],
                h4: ['最大速度', 'km/h'],
                h3: ['最低车速', 'km/h'],
                h7: ['减速度值', ''],
                h8: ['侧翻车速', 'km/h'],
                j5: ['报警通道', ''],
                h9: ['侧翻角度', ''],
                j6: ['报警阈值', '℃'],
                j7: ['报警温度', '℃'],// ÷10
                j8: ['平均温度', '℃'],//转码并÷10
                j9: ['最高温度', '℃'],//转码并÷10
                j1: ['设备温度', '℃'],
                j3: ['压缩机状态', ''],//(0关,1开)
                j4: ['开关门状态', ''],//(0关,1开)
                j2: ['发动机状态', ''],//(0关,1开)
                m1: ['加速度', ''],
                m2: ['转弯超速', ''],
                n:['电子锁事件',''],
                d:['',''],
                h10:['故障码',''],
                k2:['轮胎位置',''],
                k3:['轮胎压力','kPa'],
                k4:['轮胎温度','℃'],
                r1:['目标纵向距离','m'],
                r2:['目标相对速度','km/h'],
                r3:['目标类型',''],
                r4:['刹车等级',''],
                r5:['转向灯状态',''],
                r6:['刹车踏板',''],
                r7:['BSD目标ID',''],
                r8:['BSD目标类型',''],
                r9:['BSD目标ttc','ms'],
                r10:['BSD横向相对速度','km/h'],
                r11:['BSD纵向相对速度','km/h'],
                r12:['BSD目标置信度',''],
                l2:['围栏名称','']
            },
            remarkPrompt: {
                0: '已下发报警解除',
                1: '已拍照',
                2: '已监听',
                3: '已下发TTS语音'
            },
            remarkAgain: '',
            mile: ''
        }
    },
    computed:{
        detailMessage(){

        }
    },
    filters: {
        formatTime(ms) {
            if (ms) {
                let fmt = '';
                let minute = parseInt(ms, 10);
                let second = 0;
                
                if (minute <= 60) {
                    fmt = minute < 10 ? `0${minute}s` : `${minute}s`;
                } else {
                    second = Math.floor(minute / 60);
                    second = second < 10 ? `0${second}` : second;
                    minute = Math.floor(minute % 60);
                    minute = minute < 10 ? `0${minute}` : minute;
                    fmt = `${second}m${minute}s`;
                }
                return fmt;
            } else {
                return '- m - s'
            }
        },
    },
    methods: {
        jumpOnPRFID() {
            let obj = {
                vehicleId: this.detailList.vehicle_id,
                startTime: moment(this.detailList.alarm_time).valueOf(),
                endTime: moment(this.detailList.alarm_time).valueOf()
            }
            this.$router.push({
                path: '/home/<USER>',
                query: obj
            })
        },
        textContentChange() {
            if (this.ttsmodelresult.includes('{x}')) {
                let str = `${this.detailList.today_type_count}`
                this.messageModal.data.content = this.ttsmodelresult.replace(/{x}/g, str)
                
            } else {
                this.messageModal.data.content = this.ttsmodelresult
            }
        },
        // 点击报警确认
        async alarmConfirm() {
            this.loading = true
            let type = await this.$api.checkConfirmAlarmType({
                alarmType: this.detailList.alarm_type_code
            })
            if (!type || type.status != 200) {
                this.$error('报警类型查询出错！')
                this.loading = false
                return
            }
            BstandardUtil.init();
            let res = await this.$api.sendMiniStandardCommon({
                vehicle_terminal_list: [{
                    vehicle_id: this.detailList.vehicle_id,
                    terminal_no: (await this.$store.dispatch('mediaPlatform/getVehicleTerminalInfo', {
                        id: this.detailList.vehicle_id,
                    })).data,
                }],
                operate_key: 'ConfirmAlarm',
                cmd_confirm_alarm: type.data,
                
            })
            if (!res || res.status != 200) {
                this.loading = false
                this.$warning(res.message)
                return
            }
            let wsRes = await BstandardUtil.waitForResponse(res.data);
            if (wsRes && wsRes.task_state === 0) {
                this.$message({showClose: true, message: '指令下发成功', type: 'success'})
            } else {
                this.$message({showClose: true, message: `指令下发失败，${wsRes.errormsg || '超时'}`, type: 'warning'})
            }
            this.loading = false
            
        },
        // 点击处理图标
        sendCommand(type) {
            switch (type) {
                case 'clear':
                    this.clear()
                    break;
                case 'phone':
                    this.phone.show = true
                    break;
                case 'photo':
                    this.photo.show = true
                    break;
                case 'text':
                    this.text.show = true
                    break;
                default:
                    break;
            }
        },
        // 报警清除指令下发
        async clear() {
            BstandardUtil.init();
            this.loading = true
            let res = await this.$api.sendMiniStandardCommon({
                vehicle_terminal_list: [{
                    vehicle_id: this.detailList.vehicle_id,
                    terminal_no: (await this.$store.dispatch('mediaPlatform/getVehicleTerminalInfo', {
                        id: this.detailList.vehicle_id,
                    })).data,
                }],
                operate_key: 'ConfirmAlarm',
                cmd_confirm_alarm: this.detailList.alarm_type_code
            })
            if (!res || res.status != 200) {
                this.loading = false
                this.$warning(res.message)
                return
            }
            let wsRes = await BstandardUtil.waitForResponse(res.data);
            if (wsRes && wsRes.task_state === 0) {
                this.$message({showClose: true, message: '指令下发成功', type: 'success'})
                this.phone.show = false
                this.changeRemark(0)
            } else {
                this.$message({showClose: true, message: `指令下发失败，${wsRes.errormsg || '超时'}`, type: 'warning'})
            }
            this.loading = false
        },
        // 文本指令下发
        async sendTextCommand() {
            BstandardUtil.init();
            this.text.loading = true
            let one = this.messageModal.data.urgent === '01' ? 1 : this.messageModal.data.urgent === '10' ? 2 : this.messageModal.data.urgent === '11' ? 3 : 0;
            let two = this.messageModal.data.terminal_monitor ? 4 : 0;
            let three = this.messageModal.data.terminal_tts ? 8 : 0;
            let four = this.messageModal.data.add_show ? 16 : 0;
            let five = this.messageModal.data.center ? 32 : 0;
            let res = await this.$api.sendMiniStandardCommon({
                vehicle_terminal_list: [{
                    vehicle_id: this.detailList.vehicle_id,
                    terminal_no: (await this.$store.dispatch('mediaPlatform/getVehicleTerminalInfo', {
                        id: this.detailList.vehicle_id,
                    })).data,
                }],
                operate_key: 'SendSMS',
                cmd_sms: {
                    type: one + two + three + four + five,
                    text: this.messageModal.data.content,
                    usage: this.messageModal.data.usage
                }
            })
            if (!res || res.status != 200) {
                this.text.loading = false
                this.$warning(res.message)
                return
            }
            let wsRes = await BstandardUtil.waitForResponse(res.data);
            if (wsRes && wsRes.task_state === 0) {
                this.$message({showClose: true, message: '指令下发成功', type: 'success'})
                this.text.show = false
                this.changeRemark(3)
            } else {
                this.$message({showClose: true, message: `指令下发失败，${wsRes.errormsg || '超时'}`, type: 'warning'})
            }
            this.text.loading = false
        },
        // 监听指令下发
        async sendPhoneCommand() {
            BstandardUtil.init();
            this.phone.loading = true
            let res = await this.$api.sendMiniStandardCommon({
                vehicle_terminal_list: [{
                    vehicle_id: this.detailList.vehicle_id,
                    terminal_no: (await this.$store.dispatch('mediaPlatform/getVehicleTerminalInfo', {
                        id: this.detailList.vehicle_id,
                    })).data,
                }],
                operate_key: 'CallPhone',
                cmd_call_phone: {
                    type: this.phoneModal.type,
                    phone_no: this.phoneModal.tel
                }
            })
            if (!res || res.status != 200) {
                this.phone.loading = false
                this.$warning(res.message)
                return
            }
            let wsRes = await BstandardUtil.waitForResponse(res.data);
            if (wsRes && wsRes.task_state === 0) {
                this.$message({showClose: true, message: '指令下发成功', type: 'success'})
                this.phone.show = false
                this.changeRemark(2)
            } else {
                this.$message({showClose: true, message: `指令下发失败，${wsRes.errormsg || '超时'}`, type: 'warning'})
            }
            this.phone.loading = false
        },
        //   监听验证，监听指令下发
        sendTerminalMsgPhone() {
            this.$refs.phoneForm.validate(async (res) => {
                if (!res) return
                this.sendPhoneCommand()
            })
        },
        // 拍照指令下发
        async sendPhotoCommand() {
            BstandardUtil.init();
            this.photo.loading = true
            let modal = this.photoModal;
            let res = await this.$api.sendMiniStandardCommon({
                vehicle_terminal_list: [{
                    vehicle_id: this.detailList.vehicle_id,
                    terminal_no: (await this.$store.dispatch('mediaPlatform/getVehicleTerminalInfo', {
                        id: this.detailList.vehicle_id,
                    })).data,
                }],
                operate_key: 'RecordMedia',
                cmd_record_media: Object.assign(modal.data, {
                    operate_cmd: modal.data.operate_cmd == 1 ? +modal.data.photoNum : modal.data.operate_cmd,
                })
            })
            if (!res || res.status != 200) {
                this.photo.loading = false
                this.$warning(res.message)
                return
            }
            let wsRes = await BstandardUtil.waitForResponse(res.data);
            // modal.loading = false;
            if (wsRes && wsRes.task_state === 0) {
                this.$message({showClose: true, message: '指令下发成功~ 请前往多媒体查询结果！', type: 'success'})
                this.photo.show = false
                this.changeRemark(1)
            } else {
                this.$message({showClose: true, message: `指令下发失败，${wsRes.errormsg || '超时'}`, type: 'warning'})
            }
            this.photo.loading = false
        },
        // 改变备注框中的值
        changeRemark(type) {
            if (this.detailList.deal_type === 0) {
                if (this.remark == null || this.remark.length === 0) {
                    this.remark = `${this.remarkPrompt[type]}(${this.messageModal.data.content})`
                } else {
                    this.remark = this.remark + '|' + `${this.remarkPrompt[type]}(${this.messageModal.data.content})`
                }
            } else {
                if (this.remarkAgain.length === 0) {
                    this.remarkAgain = `${this.remarkPrompt[type]}(${this.messageModal.data.content})`
                } else {
                    this.remarkAgain = this.remarkAgain + '|' + `${this.remarkPrompt[type]}(${this.messageModal.data.content})`
                }
            }
        },
        // 暂时导出图片。听说导出PDF 有问题  【手动滑稽】
        async readyExportPDF() {
            this.loading = true
            const targetDom = document.querySelector('.pony-dialog')
            // 画布的宽高
            let canvas = await html2canvas(targetDom, {
                allowTaint: false,
                useCORS: true,
                height: targetDom.clientHeight,
                width: targetDom.clientWidth,
            });
            await this.$nextTick()
            const container = this.$refs['pdfview']
            while (container.hasChildNodes()) {
                container.removeChild(container.firstChild)
            }
            const dataImg = new Image()
            dataImg.src = canvas.toDataURL('image/png')
            container.appendChild(dataImg)
            const alink = document.createElement("a");
            alink.href = dataImg.src;
            alink.download = `${this.detailList.plate_no}(${this.detailList.driver_name}).jpg`;
            alink.click();
            
            this.loading = false
        },
        copy() {
            const target = document.createElement('div');
            target.id = 'tempTarget';
            target.style.opacity = '0';
            target.innerText = this.detailList.plate_no;
            document.body.appendChild(target);
            
            try {
                let range = document.createRange();
                range.selectNode(target);
                window.getSelection().removeAllRanges();
                window.getSelection().addRange(range);
                document.execCommand('copy');
                window.getSelection().removeAllRanges();
                this.$info(this.$ct('messageInfo.0'))
            } catch (e) {
            }
            target.parentElement.removeChild(target);
        },
        // 绘制地图
        async getDetail() {
            try {
                this.loading = true;
                await this.showCanRunLine()
                await this.initData();
            } catch (e) {
                this.$error(e)
            } finally {
                this.loading = false;
            }
        },
        // alarmPlay(item) {
        //     if (!item.is_file) return
        //     this.$refs['player'].play({
        //         label: this.detailList.plate_no,
        //         timestamp: item.alarm_time,
        //         alarmId: item.id
        //     })
        // },
        // 开始结束时间相同时，不要轨迹，显示一个报警图标。不同时，不要报警图标，显示轨迹起始点。
        async initData() {
            // let alarmList = this.detailList.alarmList;
            // let startTime = moment(this.detailList.alarm_time).valueOf()-30000;
            let startTime = moment(this.detailList.alarm_time).valueOf();
            // let endTime = moment(this.detailList.alarm_time_end).valueOf()+30000;
            let endTime = moment(this.detailList.alarm_time_end).valueOf();
            if (startTime == endTime) {
                if(!this.detailList.lat || !this.detailList.lng){
                    this.clearMap()
                    return
                }
                this.initMarker([{
                    gps_time: moment(this.detailList.alarm_time),
                    is_over_speed: false,
                    lat: this.detailList.lat,
                    lng: this.detailList.lng
                }], true);
            } else {
                let res = await this.$api.getCustomGPSRecord({
                    startTime,
                    endTime,
                    vehicleId: this.params.vehicleId,
                    // value: this.detailList.event_param,
                });
                if (res.status === 200) {
                    // alarmList.forEach(item => {
                    //     item.alarm_time = moment(item.alarm_time).valueOf();
                    // })
                    this.gpsList = res.data;
                    if (res.data.length) {
                        this.initMarker(res.data, false);
                    } else {
                        if(!this.detailList.lat || !this.detailList.lng){
                            this.clearMap()
                            return
                        }

                        this.initMarker([{
                            gps_time: moment(this.detailList.alarm_time),
                            is_over_speed: false,
                            lat: this.detailList.lat,
                            lng: this.detailList.lng
                        }], true);
                    }
                } else {
                    this.$message({type: 'error', showClose: true, message: this.$ct('messageInfo.1')})
                }
            }
        },
        clearMap(){
            let $simplemap = this.$refs['map']._map;
            $simplemap.hasLayer(this.featureGroup) && $simplemap.removeLayer(this.featureGroup);
            $simplemap.hasLayer(this.markerCluster) && $simplemap.removeLayer(this.markerCluster);
        },
        initMarker(gpsList = [], showIcon) {
            // console.log(gpsList);
            let $simplemap = this.$refs['map']._map;
            $simplemap.hasLayer(this.featureGroup) && $simplemap.removeLayer(this.featureGroup);
            $simplemap.hasLayer(this.markerCluster) && $simplemap.removeLayer(this.markerCluster);
            //有时候轨迹的第一个点的时间会晚于报警的第一个点时间，需要特殊处理 ↓
            //最后一个点则会晚于，也要处理

            let alarmList
            if (showIcon) {
                alarmList = [
                    {
                        alarm_type: this.detailList.alarm_type_code,
                        lat: this.detailList.lat,
                        lng: this.detailList.lng
                    }
                ]
            } else {
                alarmList = []
            }
            // console.log(alarmList,'alarmList');
            // if (alarmList.length > 0) {
            //     if (moment(gpsList[0].gps_time).valueOf() > alarmList[0].alarm_time) {
            //         gpsList.unshift({
            //             lat: alarmList[0].lat,
            //             lng: alarmList[0].lng,
            //             is_over_speed: false,
            //         })
            //     }
            //     if (moment(gpsList[gpsList.length - 1].gps_time).valueOf() < alarmList[alarmList.length - 1].alarm_time) {
            //         gpsList.push({
            //             lat: alarmList[alarmList.length - 1].lat,
            //             lng: alarmList[alarmList.length - 1].lng,
            //             is_over_speed: false,
            //         })
            //     }
            // }
            // 特殊处理 ↑
            //报警点↓
            let markerArray = []
            //经纬度转换
            
            alarmList.forEach(item => {
                item.lng = +item.lng.toFixed(4);
                item.lat = +item.lat.toFixed(4);
                let icon = getAlarmMapIcon(item.alarm_type)
                let markers = L.marker([item.lat, item.lng], {icon: icon});
                markerArray.push(markers)
            });
            //轨迹
            let linePaths = gpsList.map(item => {
                item.lng = +item.lng.toFixed(4);
                item.lat = +item.lat.toFixed(4);
                return {lat: item.lat, lng: item.lng, overSpeed: item.is_over_speed}
            });
            let flag;
            let stepPaths = linePaths.reduce((acc, cur) => {
                if (cur.overSpeed !== flag) {
                    flag = cur.overSpeed;
                    acc[acc.length - 1] && acc[acc.length - 1].path.push([cur.lat, cur.lng])
                    acc.push({overSpeed: flag, path: []});
                }
                acc[acc.length - 1].path.push([cur.lat, cur.lng])
                return acc;
            }, []);
            let paths = [];
            stepPaths.forEach(step => {
                paths.push(L.polyline(step.path, {
                    stroke: true,
                    color: step.overSpeed ? '#f5675d' : '#3388FF',
                    weight: 4
                }))
            })
            this.featureGroup = L.featureGroup(paths, {snakingPause: 100}).addTo($simplemap);
            if (!showIcon) {
                //开始点
                markerArray.push(L.marker(linePaths[0], {
                    icon: startIcon,
                    zIndexOffset: 4000,
                    rotationAngle: 0
                }))
                //结束点
                markerArray.push(L.marker(linePaths[linePaths.length - 1], {
                    icon: endIcon,
                    zIndexOffset: 4000,
                    rotationAngle: 0
                }))
                
            }
            this.markerCluster = new L.MarkerClusterGroup({showCoverageOnHover: true}).addTo($simplemap);
            this.markerCluster.addLayers(markerArray);
            this.$nextTick(() => {
                $simplemap.fitBounds(this.featureGroup.getBounds(), {paddingBottomRight: this.paddingBottomRight})
            })
            if (showIcon) {
                this.markerCluster = new L.MarkerClusterGroup({showCoverageOnHover: true}).addTo($simplemap);
                this.markerCluster.addLayers(markerArray);
            }
        },
        viewMarker(item) {
            switch (item) {
                case 'start':
                    item = this.gpsList[0];
                    break;
                case 'end':
                    item = this.gpsList[this.gpsList.length - 1];
                    break;
                default:
            }
            this.$refs['map']._map.setView([item.lat, item.lng], 19)
        },
        linkToPlayBack() {
            let startTime = this.detailList.alarm_time;
            let endTime = this.detailList.alarm_time_end
            // if (this.detailList.alarm_time == this.detailList.alarm_time_end) {
            //     let time = Date.parse(this.detailList.alarm_time);
            //     startTime = this.TimeFormat(+time - 15 * 60 * 1000)
            //     endTime = this.TimeFormat(+time + 1 * 60 * 1000)
            // }
            //跳轨迹回放时间:报警开始前10分钟,结束后10分钟
            let needList
            if (this.detailList.duration && this.detailList.duration != 0) {
                needList = {
                    vehicleId: this.params.vehicleId,
                    startTime,
                    endTime,
                    alarmTypeCode: this.detailList.alarm_type_code,
                    checkAllAlarm:0
                }
            } else {
                needList = {
                    vehicleId: this.params.vehicleId,
                    startTime: moment(startTime, 'YYYY-MM-DD HH:mm:ss').subtract(10, 'm').format('YYYY-MM-DD HH:mm:ss'),
                    endTime: moment(endTime, 'YYYY-MM-DD HH:mm:ss').add(10, 'm').format('YYYY-MM-DD HH:mm:ss'),
                    alarmTypeCode: this.detailList.alarm_type_code,
                    checkAllAlarm:0
                }
            }
            this.$router.push({
                path: '/home/<USER>',
                query: needList
            })
        },
        async showCanRunLine() {
            if (this.canRun._layer) {
                this.canRun._layer.clearLayers()
                this.canRun._layer = null
                return
            }
            if (!this.canRun.switch) return
            let $simplemap = this.$refs['map']._map;
            this.canRun._layer = new L.FeatureGroup().addTo($simplemap)
            let result = await this.$api.queryAccessRoutes({
                vehicle_id: this.params.vehicleId,
                start_time: this.detailList.start_time,
                jurisdiction: this.detailList.fenseId
            })
            if (!result || result.status != 200) {
                this.$warning(result.message || '查询失败')
                return
            }
            if (!result.data.length) {
                this.$warning('未查询围栏信息')
                return
            }
            result.data.forEach(item => {
                let list = item.fense_point_list.map(item => {
                    let wgs84 = item.split(',')
                    return [wgs84[1], wgs84[0]]
                })
                let currentList = generatePonylineFenseLayer(list, {
                    type: 2,
                    option: {weight: 6, color: '#7BD012', opacity: 0.3}
                })
                if (currentList.length) {
                    this.canRun._layer.addLayer(currentList[0])
                }
            })
        },
        setMessageTemplate(val) {
            this.remark = (this.templateList.find(item => item.value === val)).template;
        },
        // 点击处理
        async commit() {
            try {
                this.loading = true;
                let arr = {
                    list: [{
                        alarm_id: this.detailList.alarm_id,
                        vehicle_id: this.detailList.vehicle_id,
                        alarm_type_code: this.detailList.alarm_type_code,
                        alarm_type_class: this.detailList.alarm_type_class,
                        alarm_date: this.detailList.alarm_date,
                    }],
                    deal_type: this.dealType,
                    deal_desc: this.remark,
                }
                if (this.detailList.deal_type !== 0) {
                    Object.assign(arr, {
                        remark: this.remarkAgain
                    })
                }
                let res = await this.$api.handleAlarmDetail(arr)
                // console.log(res,'res');
                if (res.status !== 200) {
                    this.$meassage.error(res.message)
                    return;
                }
                this.$success(res.message)
                this.$emit('change', {
                    deal_desc: this.remark,
                    deal_type: this.dealType,
                    deal_name: this.dealTypeMap[this.dealType],
                    alarm_id: this.detailList.alarm_id
                })
                // this.detailList.deal_type = this.dealType;
                if (this.autoClose) {
                    this.show = false;
                }
                this.resultP.resolve({
                    dealType: this.dealType
                })
            } finally {
                this.loading = false;
            }
        },
        changeBlue(alarmId) {
            if (!alarmId) return
            const row = this.detailList.alarmList.find(alarm => alarm.id === alarmId)
            if (row) {
                row.is_file = 2
            }
        },
        async stopVideo() {
            if (this.show) {
                await this.$refs['video'].stop()
            }
        },
        setChn(chn) {
            this.chn = chn;
            // console.log(this.chn);
            this.$refs['video'].reload()
        },
        onRemainingTime(isStop, time) {
            this.remainingTime = isStop ? time : 0;
        },
        hexToString(hexCharCodeStr) {
            if(!hexCharCodeStr){
                return '' 
            }
            var trimedStr = hexCharCodeStr.trim();
            var rawStr =
                trimedStr.substr(0, 2).toLowerCase() === "0x"
                    ?
                    trimedStr.substr(2)
                    :
                    trimedStr;
            var len = rawStr.length;
            if (len % 2 !== 0) {
                return "";
            }
            var curCharCode;
            var resultStr = [];
            for (var i = 0; i < len; i = i + 2) {
                curCharCode = parseInt(rawStr.substr(i, 2), 16); // ASCII Code Value
                resultStr.push(String.fromCharCode(curCharCode));
            }
            return resultStr.join("");
        },
        // 事件id 车辆id 自动播放实时视频 处理后是否自动关闭
        async showModal(row) {
            this.remarkAgain = ''
            this.detailList = JSON.parse(JSON.stringify(row))
            // 处理类型
            this.dealType = this.detailList.deal_type
            // 备注
            this.remark = this.detailList.deal_desc
            // 详细信息数据
            if (this.detailList.alarm_param) {
                let param = JSON.parse(this.detailList.alarm_param)
                let arr = []
                for (let j in this.alarm) {
                    for (let i in param) {
                        if (i == j) {
                            let obj
                            if (i == 'j3' || i == 'j4' || i == 'j2') {
                                //(0关,1开)
                                obj = {
                                    name: this.alarm[i][0],
                                    value: param[i] == 0 ? '关' : '开',
                                    unit: this.alarm[i][1]
                                }
                            } else if (i == 'j8' || i == 'j9' || i == 'j6') {
                                //÷10
                                obj = {
                                    name: this.alarm[i][0],
                                    value: param[i] / 10,
                                    unit: this.alarm[i][1]
                                }
                            } else if (i == 'j7') {
                                //÷10  有异常值
                                let val = param[i] == '65533' ? '传感器正在初始化' : param[i] == '65534' ? '传感器故障' : param[i] == '65535' ? '无传感器' : param[i] / 10
                                obj = {
                                    name: this.alarm[i][0],
                                    value: val,
                                    unit: this.alarm[i][1]
                                }
                            } else if (i == 'm1') {
                                
                                obj = {
                                    name: this.alarm[i][0],
                                    value: this.detailList.addition,
                                    unit: this.alarm[i][1]
                                }
                                
                            } else if (i == 'm2') {
                                
                                obj = {
                                    name: this.alarm[i][0],
                                    value: this.detailList.addition,
                                    unit: this.alarm[i][1]
                                }
                            } else if(i == 'd'){
                                if(this.detailList.alarm_type.includes('超速') || this.detailList.alarm_type.includes('限速')){
                                    obj = {
                                            name: '限速值',
                                            value: param[i],
                                            unit: 'km/h'
                                        }
                                }else if(this.detailList.alarm_type.includes('超高') || this.detailList.alarm_type.includes('限高')){
                                    obj = {
                                            name: '限高值',
                                            value: param[i],
                                            unit: ''
                                        }
                                }else if(this.detailList.alarm_type.includes('超重') || this.detailList.alarm_type.includes('限重')){
                                    obj = {
                                            name: '限重值',
                                            value: param[i],
                                            unit: ''
                                        }
                                }

                            } else if(i == 'k2'){
                                let lunName = ['左前','右前','左后','右后']
                                obj = {
                                    name: this.alarm[i][0],
                                    value: lunName[param[i]],
                                    unit: this.alarm[i][1]
                                }
                                
                            } else if(i == 'r3'){
                              let lunName = ['不存在','毫米波反射(雷达)','行人(视觉)','小汽车(视觉)','大巴(视觉)','卡车(视觉)','二轮车(视觉)',
                              '三轮车(视觉)','一般障碍物(视觉)']
                                obj = {
                                    name: this.alarm[i][0],
                                    value: lunName[param[i]] ? lunName[param[i]] :'未知',
                                    unit: this.alarm[i][1]
                                }
                            }else if(i == 'r5'){
                              let lunName = ['不闪','左转','右转','双闪']
                                obj = {
                                    name: this.alarm[i][0],
                                    value: lunName[param[i]] ? lunName[param[i]] :param[i],
                                    unit: this.alarm[i][1]
                                }
                            }else if(i == 'r6'){
                              let lunName = ['未踩下','踩下']
                                obj = {
                                    name: this.alarm[i][0],
                                    value: lunName[param[i]] ? lunName[param[i]] :param[i],
                                    unit: this.alarm[i][1]
                                }
                            }else if(i == 'r7'){
                              let lunName = ['右前','右后','左前','左后','正前','正后']
                                obj = {
                                    name: this.alarm[i][0],
                                    value: lunName[param[i]] ? lunName[param[i]] :param[i],
                                    unit: this.alarm[i][1]
                                }
                            }else if(i == 'r8'){
                              let lunName = ['无目标','行人','骑手','车辆']
                                obj = {
                                    name: this.alarm[i][0],
                                    value: lunName[param[i]] ? lunName[param[i]] :param[i],
                                    unit: this.alarm[i][1]
                                }
                            }
                            else {
                                obj = {
                                    name: this.alarm[i][0],
                                    value: param[i],
                                    unit: this.alarm[i][1]
                                }
                            }
                            arr.push(obj)
                        }
                    }
                }
                this.detailList.alarm_param = arr
            }
            
            let channelNo = (await this.$store.dispatch('mediaPlatform/getVehicleChannelNo', {
                id: row.vehicle_id
            })).data;
            this.channelNo = channelNo;
            try {
                let vehicleInfo = await this.$store.dispatch('vehicle/getVehicleInfo', {value: row.vehicle_id});
                this.params.terminalCode = vehicleInfo.code;
            } catch (e) {
                this.params.terminalCode = '';
            }
            Object.assign(this.params, {
                vehicleId: row.vehicle_id,
            })
            this.getDetail();
            this.getMileDetail(row.alarm_id, row.alarm_time, row.vehicle_id)
            this.show = true;
            await this.$nextTick();
            return new Promise((resolve, reject) => {
                this.resultP.resolve = resolve;
                this.resultP.reject = reject;
            })
        },
        // 查询里程
        async getMileDetail(id, time, vehicleId) {
            const res = await this.$api.getAlarmMile({
                alarm_id: id,
                alarm_time: time,
                vehicle_id: vehicleId
            })
            if (res.status === 200) {
                this.mile = res.data.drive_mile
            }
        }
    },
    async created() {
        const res = await this.$api.getCommonListByKey({key: "config_event_deal_type"})
        if (res.status !== 200) return this.$message.error('获取报警事件处理类型失败！')
        this.dealTypeMap = res.data
        // 文本下发TTS模板
        let result = await this.$api.getTTS()
        if (!result || result.status != 200 || !result.data || !result.data.length) return
        result.data.forEach(item => {
            this.templateList.push({
                value: item.id,
                label: item.configDesc,
                template: item.configValue
            })
        });
        this.ttsmodelist = result.data
        this.ttsmodelresult = result.data[0].configValue
        this.messageModal.data.content = result.data[0].configValue
    },
}
</script>

<style scoped lang="scss">
.alarm-detail-v2 {
    .left-wrap {
        height: 100%;
        display: flex;
        flex-direction: column;
        overflow: auto;
        
        .detail-title {
            color: #2a80e0;
            font-size: 14px;
            margin-top: 5px;
        }
        
        .alarm-info-list {
            display: flex;
            flex-wrap: wrap;
            padding: 0 5px 10px 10px;
            border-bottom: solid 1px var(--background-color-lighter);
            flex-shrink: 0;
            
            .info-item {
                padding: 5px 0;
                width: 60%;
                span {
                    min-width: 60px;
                    text-align: left;

                }
                .title{
                    display: inline-block;
                    min-width: 60px;
                    text-align: right;
                    align-content: start;

                }
                &.long {
                    width: 100%;
                }
                
                &.short {
                    width: 40%
                }
                span:first-child {
                    // display: inline-block;
                    // color: var(--color-text-regular);
                    font-size: 12px;
                    min-width: 60px;
                    text-align: right;
                }
                
                span:last-child {
                    color: var(--color-text-secondary);
                    font-size: 12px;
                    margin: 0px 5px;
                }
            }
        }
        
        .alarm-card-list {
            width: 100%;
            padding: 10px;
            overflow: auto;
            flex-grow: 1;
            font-size: 12px;
            
            .info-item {
                display: inline-block;
                padding: 5px 0;
                // width: 60%;
                
                &.long {
                    width: 100%;
                }
                
                &.short {
                    width: 50%
                }
                
                span:first-child {
                    // display: inline-block;
                    font-size: 12px;
                    min-width: 60px;
                    text-align: right;
                    margin: 0px 5px;
                }
                
                span:nth-child(2) {
                    color: var(--color-text-secondary);
                    font-size: 12px;
                }
                
                span:last-child {
                    color: var(--color-text-secondary);
                    font-size: 12px;
                }
            }
            
            .card-item {
                height: 90px;
                width: 100%;
                padding: 10px 10px 0px 0px;
                cursor: pointer;
                
                &:last-child {
                    .tip-item:after {
                        display: none;
                    }
                }
                
                .tip-item {
                    height: 90px;
                    width: 18px;
                    margin: auto;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    transform: translateY(7px);
                    
                    &:before {
                        content: ' ';
                        display: block;
                        width: 8px;
                        height: 8px;
                        border: 3px solid var(--color-primary);
                        border-radius: 50%;
                    }
                    
                    &:after {
                        content: ' ';
                        display: block;
                        flex-grow: 1;
                        width: 3px;
                        background-color: var(--color-primary-o-10);
                    }
                }
                
                .card-main {
                    width: calc(100% - 65px);
                    float: right;
                    height: 75px;
                    border: solid 1px var(--background-color-lighter);
                    border-radius: 4px;
                    padding: 0px 15px;
                    margin-top: -20px;
                    
                    .top-line {
                        height: 50%;
                        width: 100%;
                        line-height: 35px;
                        display: flex;
                        
                        span {
                            color: var(--color-text-regular);
                        }
                    }
                    
                    .bottom-line {
                        height: 50%;
                        width: 100%;
                        line-height: 35px;
                        color: var(--color-text-secondary);
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                    }
                    
                    li {
                        &:first-child {
                            display: inline-block;
                            
                            i {
                                margin: 6px 0;
                                float: left;
                            }
                        }
                        
                        &:last-child {
                            float: right;
                            display: flex;
                            justify-content: center;
                            align-items: center;
                            
                            i {
                                float: right;
                                color: var(--color-text-disabled);
                                cursor: not-allowed;
                                font-size: 16px;
                                
                                &.is_video {
                                    cursor: pointer;
                                    color: var(--color-white);
                                }
                                
                                &.watched {
                                    cursor: pointer;
                                    color: var(--color-primary);
                                }
                            }
                        }
                        
                        span {
                            margin-left: 10px;
                            
                            &.alarm-name {
                                margin-left: 15px;
                            }
                            
                        }
                    }
                }
                
                .card-corner {
                    position: relative;
                    left: 55px;
                    top: 3px;
                    height: 0px;
                    width: 0px;
                    border-top: 10px solid transparent;
                    border-right: 11px solid var(--background-color-lighter);
                    border-bottom: 11px solid transparent;
                    
                    &:after {
                        content: '';
                        position: absolute;
                        top: -9px;
                        left: 1px;
                        border-top: 9px solid transparent;
                        border-right: 10px solid var(--background-color-modal);
                        border-bottom: 10px solid transparent;
                    }
                }
                
                .index-tip {
                    width: 50px;
                    height: 100%;
                    float: left;
                }
                
                &:last-child .tip-item {
                    padding-bottom: 15px;
                }
                
                &:hover {
                    .card-main {
                        background: var(--background-color-light);
                        transition: background-color .3s;
                    }
                    
                    .card-corner:after {
                        border-right-color: var(--background-color-light);
                        transition: border-right-color .3s;
                    }
                }
            }
        }
        
        .detail {
            width: 100%;
            word-break: break-word;
        }
        
        .deal-box {
            border-top: solid 1px var(--background-color-lighter);
            height: 100px;
            padding: 5px 10px;
            
            .top {
                width: 100%;
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-bottom: 5px;
                
                .dealFunction {
                    height: 100%;
                    display: flex;
                    
                    i {
                        font-size: 20px !important;
                        cursor: pointer;
                    }
                }
            }
            
            
            .select {
                float: left;
                width: calc(100% - 28px);
                flex-shrink: 0;
                height: 100%;
                display: flex;
                flex-wrap: wrap;
                align-items: center;
            }
        }
    }
    
    
    .right-wrap {
        display: flex;
        flex-direction: column;
        height: 100%;
        
        .video-wrap {
            position: relative;
            height: 230px;
            
            .video-tip {
                position: absolute;
                top: 0;
                left: 0;
                padding-top: 5px;
                padding-left: 5px;
                display: flex;
                z-index: 1;
                align-items: center;
                
                .video-tip-link {
                    line-height: 24px;
                    padding: 0 5px 0 8px;
                    border-radius: 4px;
                    font-size: 12px;
                    margin-right: 5px;
                }
            }
        }
        
        .map-wrap {
            flex-grow: 1;
            position: relative;
            
            .map {
                position: absolute;
                top: 0;
                left: 0;
                height: 100%;
                width: 100%;
                z-index: 0
            }
            
            .play-black-btn {
                position: absolute;
                top: 5px;
                right: 8px;
                width: 30px;
                height: 30px;
                z-index: 20;
                border-radius: 6px;
                background: var(--background-color-base);
                cursor: pointer;
                
                &:after {
                    content: ' ';
                    display: inline-block;
                    width: 30px;
                    height: 30px;
                    background: url("../../../../static/img/map/track_playback.png") no-repeat center;
                    cursor: pointer;
                }
            }
            
            .show-can-run {
                position: absolute;
                top: 5px;
                right: 45px;
                display: flex;
                align-items: center;
                justify-content: center;
                height: 30px;
            }
        }
    }
    
    .pdfViewBackground {
        position: relative;
        height: max-content;
        width: max-content;
        display: none;
    }
    
    .photo {
        .el-form {
            .el-form-item {
                width: 95%;
            }
            
            .el-select, .el-input-number {
                width: 100%;
            }
            
            .pony-iconv2 {
                margin-left: 5px;
            }
        }
    }
    
    .phone {
    
    }
    
    .text {
        .el-form {
            .el-form-item {
                width: 95%;
            }
            
            .el-select, .el-input-number {
                width: 100%;
            }
        }
    }
}
</style>

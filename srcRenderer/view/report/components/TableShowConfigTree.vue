<template>
		<PonyDialog width="605"
		            :loading="loading"
		            title="表格显示配置" :contentMaxHeight="510"
		            v-model="show">
				<div class="transfer" :style="formatStyle">
						<div class="transfer__box transfer--left">
								<div class="header">
										<el-checkbox
												:indeterminate="leftHalfCheck"
												v-model="leftAllCheck"
												@change="handleLeftAllCheck">
												全部
										</el-checkbox>
										<span>{{ leftChecked.length }} / {{ allUnSettingDetail.length }}</span>
								</div>
								<div class="content">
										<div class="filter">
												<el-select v-model="leftFilterValue" filterable placeholder="请选择" style="width: 100%">
														<el-option
																v-for="(item,index) in allUnSettingDetail"
																:label="item.name"
																:value="item.id"
																:key="index"
																@click.native="unSettingSearchInput(index,item.id,item)">
														</el-option>
												</el-select>
										</div>
										<div class="main">
												<el-tree
														ref="tree"
														:data="unSettingList"
														show-checkbox
														:node-key="nodeKey"
														:props="defaultProps"
														:default-expand-all="true"
														@check='getlist'>
												</el-tree>
										</div>
								</div>
						</div>
						<div class="transfer__box transfer--right">
								<div class="content">

										<div class="main">
												<draggable v-model="settingList" group="people" @start="drag=true" @end="drag=false">
														<div v-for="setting in settingList" :key="setting.key" class="sort-item">
																{{ setting.name }}
														</div>
												</draggable>
										</div>
								</div>
						</div>
				</div>
				<template slot="footer">
						<el-button size="mini" type="primary" @click="clearDeFault" v-if="disabled">恢复默认</el-button>
						<el-button size="mini" type="primary" @click="show = false">关闭</el-button>
						<el-button size="mini" type="primary" @click="changeTableSetting">确认</el-button>
				</template>
		</PonyDialog>
</template>

<script>
import draggable from 'vuedraggable'
import {getTableSettingAll, getArrayDifference,getTableSetting} from '../util/tableshowconfig'
export default {
		name: '',
		components: {
				draggable,
		},
		model: {
				prop: "defaultSetting",
				event: "change"
		},
		data() {
				return {
						show: false,
						loading: false,
						data: [],
						settingList: [],
						leftAllCheck: false, // 左侧全选
						leftHalfCheck: false, // 左侧半选
						leftChecked: [], // 左侧已经选中的数据
						leftFilterValue: '', // 下拉框搜索

						defaultProps: {
								children: 'children',
								label: 'name'
						}
				};
		},

		props: {
				// 宽度
				autoWidth: {
						type: [String, Number],
						default: '583px'
				},
				// 高度
				autoHeight: {
						type: [String, Number],
						default: '400px'
				},
				// 是否可排序
				sortable: {
						type: Boolean,
						default: true
				},
				type: {
						type: String,
				},
				/** 以下配置均按照elemen-ui transfer 标准来的 以上是新增的 */
				// tree的node-key
				nodeKey: {
						type: String,
						default: 'id'
				},
				// 传入的全部列表
				list: {
						type: Array,
						default: function () {
								return []
						},
						required: false
				},
				// 传入的已经选中的列表
				defaultSetting: {
						type: Array,
						default: function () {
								return []
						}
				},
				code:{
					type: String,
					default:'history_gps_business_table'
				},
				disabled:{
					type:Boolean,
					default:true
				}


		},
		computed: {
				formatStyle() {
						let widthStyle = typeof (this.autoWidth) == "string" ? this.autoWidth : `${this.autoWidth}px;`
						let heightStyle = typeof (this.autoHeight) == "string" ? this.autoHeight : `${this.autoHeight}px`
						return `width: ${widthStyle}; height: ${heightStyle}`
				},
				// 左侧数据
				unSettingList() {
						let data = JSON.parse(JSON.stringify(this.data))
						return data.filter((item) => {
								item.children = item.children.filter((list) => {
										if (!this.defaultSetting.includes(list.id)) {
												return list
										}
								})
								return item
						})
				},
				// 展开左侧树形数据，放在一个数组中
				allUnSettingDetail() {
						let arr = []
						this.unSettingList.map((item) => {
								item.children.map(list => arr.push(list))
						})
						return arr
				},

		},
		async mounted() {
				if (this.type) {
						let res = await this.$store.dispatch('ztreeData/getTreeData', this.type)
						if (!res) {
								this.$error('查询配置树出错!')
								this.data = []
						} else {
								this.data = res
						}
				} else if (this.list) {
						this.data = this.list
				}
				//接口获取表格配置
				let result = await this.$api.operateUserTableConfig({
						operate_type: 2,
						user_id: "-1",
						code: this.code
				})
				if (!result || result.status != 200 || !result.data.config_value) {
						return
				} else {
						this.settingList = JSON.parse(result.data.config_value).filter(item=>item)
						this.$emit('change', JSON.parse(result.data.config_value).filter(item=>item))
				}

		},
		methods: {
				//右侧数据的展示,不能根据树上选中的过滤了,因为这个排序有排好的顺序
				/**
				 *
				 * @param {*} listNew 新数组的id List
				 * @param {*} listOld 已经显示的id List
				 */
				rightDataListChange(listNew, listOld,checkList) {
						let newArr = getArrayDifference(listNew, listOld)
						newArr.length && newArr.forEach(item => {
								let index = this.settingList.findIndex(it => it.key == item)
								if (index == -1) {
										let addItem = getTableSettingAll.find(it => it.key == item)
										if(addItem&&addItem.key=='location'&&this.code!='history_gps_business_table'){
											addItem.name = '地址'
										}
										if(!addItem){
											addItem = checkList.find(it=>it.key == item)
										}
										this.settingList.push(addItem)
								} else {
										this.settingList.splice(index, 1)
								}
						})
				},
				async showModel() {
						this.leftFilterValue = ''
						this.show = true
						this.loading = true
						await this.$nextTick()
						let configTree = this.defaultSetting.map(item => item.key)
						this.$refs.tree.setCheckedKeys(configTree);
						this.leftChecked = configTree

						this.rightDataListChange(configTree, this.settingList.map(item => item.key),this.defaultSetting)
						this.loading = false

				},
				// 设置表格弹窗点击恢复按钮
			async	clearDeFault() {
					if(this.code == 'history_gps_business_table'){
						this.$refs.tree.setCheckedKeys(getTableSetting.map(item=>item.key));
						this.leftChecked = getTableSetting.map(item=>item.key)
						this.settingList = JSON.parse(JSON.stringify(getTableSetting))
					}else{
						let result = await this.$api.operateUserTableConfig({
								operate_type: 3,
								user_id: "-1",
								code: this.code,
						})
						if (!result || result.status != 200) {
								return
						}else{
						this.settingList = JSON.parse(result.data.config_value)
						this.$refs.tree.setCheckedKeys(this.settingList.map(item=>item.key));
						this.leftChecked = this.settingList.map(item=>item.key)
						}
					}
						
						this.leftHalfCheck = false
						this.leftAllCheck = false
				},
				// 设置表格弹窗点击确认按钮
				async changeTableSetting() {
						this.loading = true
						let result = await this.$api.operateUserTableConfig({
								operate_type: 1,
								user_id: "-1",
								code: this.code,
								config_value: JSON.stringify(this.settingList)
						})
						if (!result || result.status != 200) {
								this.$error(result.message || '操作失败!')
								this.loading = false
								return
						}
						this.$success('操作成功!')
						this.$emit('change', JSON.parse(JSON.stringify(this.settingList)))
						this.loading = false
						this.show = false
				},

				// 左侧下拉搜索框
				unSettingSearchInput(val, id, item) {
						let height = val * 30
						let divBox = document.querySelector(".transfer--left .main")
						divBox.scrollTop = height
						this.leftChecked.add(id)
						this.$refs.tree.setCheckedKeys(this.leftChecked);
            this.$nextTick(() => {
                this.rightDataListChange(this.leftChecked,this.settingList.map(item => item.key),this.$refs.tree.getCheckedNodes().filter(item => item.type == 1).map((item) => JSON.parse(item.business_value)))
            })
				},

				/**
				 * 左侧全选
				 * value 是否选中
				 */
				handleLeftAllCheck(val) {
						let arr = this.allUnSettingDetail.map(item => item.id)
						if (val) {
								this.$refs.tree.setCheckedKeys(arr);
								this.leftChecked = arr
								this.leftAllCheck = true
								this.leftHalfCheck = false
								this.settingList = this.allUnSettingDetail.map(item =>JSON.parse(item.business_value))

						} else {
								this.$refs.tree.setCheckedKeys([]);
								this.leftChecked = []
								this.leftAllCheck = false
								this.settingList = []
						}
				},
				/**
				 * 左侧点击选项前面的复选框
				 * @param checkedNodes
				 * @param checkedKeys
				 * @param halfCheckedNodes
				 * @param halfCheckedKeys
				 */
				getlist(checkedNodes, checkedKeys, halfCheckedNodes, halfCheckedKeys) {
						this.rightDataListChange(checkedKeys.checkedNodes.filter(item => item.type == 1).map((item) => item.id), this.settingList.map(item => item.key),checkedKeys.checkedNodes.filter(item => item.type == 1).map((item) => JSON.parse(item.business_value)))
						this.leftChecked = checkedKeys.checkedNodes.filter(item => item.type == 1).map((item) => item.id)
						this.leftAllCheck = this.leftChecked.length === this.allUnSettingDetail.length;
						this.leftHalfCheck = this.leftChecked.length > 0 && this.leftChecked.length < this.allUnSettingDetail.length;
				},

		}
}

</script>

<style lang='scss' scoped>
.transfer {
	width: 100%;
		display: flex;

		&__box {
				height: 100%;
				display: flex;
				margin-right: 10px;
				flex-direction: column;
				width: calc((100% - 10px) / 2);
				border: 1px solid var(--border-color-light);
				border-radius: 4px;

				.header {
						padding: 8px 10px;
						border-bottom: 1px solid var(--border-color-light);
						display: flex;
						justify-content: space-between;
				}

				.content {
						height: calc(100% - 35px);
						flex-grow: 1;
						padding: 8px 0px;
						display: flex;
						flex-direction: column;

						.filter {
								height: 35px;
								width: 100%;
								display: flex;
								align-items: center;
						}

						.main {
								flex-grow: 1;
								overflow: scroll;

								&::-webkit-scrollbar {
										width: 0;
								}

								.check {
										margin-right: 10px;
								}

								.operate {
										font-size: 16px;
										cursor: pointer;
								}

								.el-checkbox {
										flex-grow: 1;
								}

								ul {
										height: 100%;
								}

								.sort-item {
										padding: 0 10px;
										line-height: 30px;
										display: flex;
										align-items: center;
										font-size: 14px;
										cursor: default;
									&:nth-child(2n) {
												background-color: var(--background-color-lighter);
										}
								}
						}
				}
		}
		&--right {
			margin-right: 0;
		}


}
</style>

<template>
  <PonyDialog id="detailModal" :hasFooter="false" :loading="modal.loading" v-model="modal.show" width="950"
    :is-fullscreen="true" :content-style="{ maxHeight: '495px', overflow: 'auto' }">
    <div slot="header" class="custom-header">
      <div class="custom-title-wrap">
        <span class="title">{{ detailTitle }}</span>
        <span class="info">
          <span v-if="this.tab === 'vehicle'">
            车牌号：{{ modal.data.plateNo }}
          </span>
          <span v-else>
            企业名称：{{ modal.data.deptName }}
          </span>
          &nbsp;&nbsp;&nbsp;&nbsp;
          更新时间：{{ modal.data.redisTime }}
        </span>
      </div>
      <el-button @click="modal.show = false" circle>
        <i class="pony-iconv2 pony-guanbi" style="margin-right: 0;vertical-align: middle"></i>
      </el-button>
    </div>
    <div class="report-data-wrap">
      <div class="report-data-item">
        <div style="width: 18%;flex-shrink: 0;display: flex;justify-content: center;align-items: center"
          class="border border--r">
          <TitleRandkingSK title="安全评分" style="width: 110px;height: 110px;" :reportData="modal.data.safeScore">
          </TitleRandkingSK>
        </div>
        <div class="block-list">
          <TitleHintSK title="总里程" :num="modal.data.driveMile" unit="km" boxColor="#8476Fc" boxIcon="pony-licheng">
          </TitleHintSK>
          <TitleHintSK title="总时长" :num="modal.data.driveTime" unit="h" boxColor="#66c9ff" boxIcon="pony-shijian2">
          </TitleHintSK>
          <TitleHintSK title="总报警" :num="modal.data.eventCount" unit="个" boxColor="#f69063" boxIcon="pony-baojing">
          </TitleHintSK>
        </div>
      </div>
      <div class="detail">
        <div class="list">
          <div class="top">
            <div class="title">
              日间高速
              <span class="num">{{ modal.data.dayExScore }}</span>
            </div>
            <div class="item">
              <TitleHintSK style="width: 30%" title="里程" icon="pony-licheng" unit="km"
                :num="modal.data.highSpeedDay.driveMile" circleUnit="%" :score="modal.data.highSpeedDay.driveMile"
                :max="modal.data.driveMile" circleColor="#8476Fc" :numFontSize="20">
              </TitleHintSK>
              <TitleHintSK style="width: 30%" title="时长" icon="pony-shijian2" unit="h"
                :num="modal.data.highSpeedDay.driveTime" circleUnit="%" :score="modal.data.highSpeedDay.driveTime"
                :max="modal.data.driveTime" circleColor="#66c9ff" :numFontSize="20">
              </TitleHintSK>
              <TitleHintSK style="width: 30%" title="报警" icon="pony-baojing" unit="个"
                :num="modal.data.highSpeedDay.eventCount" circleUnit="%" :score="modal.data.highSpeedDay.eventCount"
                :max="modal.data.eventCount" circleColor="#f69063" :numFontSize="20">
              </TitleHintSK>
            </div>
            <div class="btn">
              <i class="pony-iconv2" :class="list.includes(1) ? 'pony-xia' : 'pony-zuo'" @click="change(1)"></i>
            </div>
          </div>
          <el-collapse-transition>
            <div class="bottom1" v-show="list.includes(1)">
              <div class="dimension-item" v-for="item in dimensionListSK" :key="item.key">
                <TitleAndHint style="cursor: pointer" :title="item.label" :hint="item.hint"
                  :count="modal.data.highSpeedDay[item.count]" :icon="item.unicode"
                  :max="modal.data.scoreContainerMax[item.key]"
                  :reportData="Math.round(modal.data.highSpeedDay[item.key])" @click.native="changeToDetail(0, item)">
                </TitleAndHint>
              </div>
            </div>
          </el-collapse-transition>
        </div>
        <div class="list">
          <div class="top">
            <div class="title">
              日间非高速
              <span class="num">{{ modal.data.dayNoExScore }}</span>
            </div>
            <div class="item">
              <TitleHintSK style="width: 30%" title="里程" icon="pony-licheng" unit="km"
                :num="modal.data.lowSpeedDay.driveMile" circleUnit="%" :score="modal.data.lowSpeedDay.driveMile"
                :max="modal.data.driveMile" circleColor="#8476Fc" :numFontSize="20">
              </TitleHintSK>
              <TitleHintSK style="width: 30%" title="时长" icon="pony-shijian2" unit="h"
                :num="modal.data.lowSpeedDay.driveTime" circleUnit="%" :score="modal.data.lowSpeedDay.driveTime"
                :max="modal.data.driveTime" circleColor="#66c9ff" :numFontSize="20">
              </TitleHintSK>
              <TitleHintSK style="width: 30%" title="报警" icon="pony-baojing" unit="个"
                :num="modal.data.lowSpeedDay.eventCount" circleUnit="%" :score="modal.data.lowSpeedDay.eventCount"
                :max="modal.data.eventCount" circleColor="#f69063" :numFontSize="20">
              </TitleHintSK>
            </div>
            <div class="btn">
              <i class="pony-iconv2" :class="list.includes(2) ? 'pony-xia' : 'pony-zuo'" @click="change(2)"></i>
            </div>
          </div>
          <el-collapse-transition>
            <div class="bottom1" v-show="list.includes(2)">
              <div class="dimension-item" v-for="item in dimensionListSK" :key="item.key">
                <TitleAndHint style="cursor: pointer" :title="item.label" :hint="item.hint"
                  :count="modal.data.lowSpeedDay[item.count]" :icon="item.unicode"
                  :max="modal.data.scoreContainerMax[item.key]" :reportData="Math.round(modal.data.lowSpeedDay[item.key])"
                  @click.native="changeToDetail(1, item)">
                </TitleAndHint>
              </div>
            </div>
          </el-collapse-transition>
        </div>
        <div class="list">
          <div class="top">
            <div class="title">
              夜间高速
              <span class="num">{{ modal.data.nightExScore }}</span>
            </div>
            <div class="item">
              <TitleHintSK style="width: 30%" title="里程" icon="pony-licheng" unit="km"
                :num="modal.data.highSpeedNight.driveMile" circleUnit="%" :score="modal.data.highSpeedNight.driveMile"
                :max="modal.data.driveMile" circleColor="#8476Fc" :numFontSize="20">
              </TitleHintSK>
              <TitleHintSK style="width: 30%" title="时长" icon="pony-shijian2" unit="h"
                :num="modal.data.highSpeedNight.driveTime" circleUnit="%" :score="modal.data.highSpeedNight.driveTime"
                :max="modal.data.driveTime" circleColor="#66c9ff" :numFontSize="20">
              </TitleHintSK>
              <TitleHintSK style="width: 30%" title="报警" icon="pony-baojing" unit="个"
                :num="modal.data.highSpeedNight.eventCount" circleUnit="%" :score="modal.data.highSpeedNight.eventCount"
                :max="modal.data.eventCount" circleColor="#f69063" :numFontSize="20">
              </TitleHintSK>
            </div>
            <div class="btn">
              <i class="pony-iconv2" :class="list.includes(3) ? 'pony-xia' : 'pony-zuo'" @click="change(3)"></i>
            </div>
          </div>
          <el-collapse-transition>
            <div class="bottom1" v-show="list.includes(3)">
              <div class="dimension-item" v-for="item in dimensionListSK" :key="item.key">
                <TitleAndHint style="cursor: pointer" :title="item.label" :hint="item.hint"
                  :count="modal.data.highSpeedNight[item.count]" :icon="item.unicode"
                  :max="modal.data.scoreContainerMax[item.key]"
                  :reportData="Math.round(modal.data.highSpeedNight[item.key])" @click.native="changeToDetail(2, item)">
                </TitleAndHint>
              </div>
            </div>
          </el-collapse-transition>
        </div>
        <div class="list">
          <div class="top">
            <div class="title">
              夜间非高速
              <span class="num">{{ modal.data.nightNoExScore }}</span>
            </div>
            <div class="item">
              <TitleHintSK style="width: 30%" title="里程" icon="pony-licheng" unit="km"
                :num="modal.data.lowSpeedNight.driveMile" circleUnit="%" :score="modal.data.lowSpeedNight.driveMile"
                :max="modal.data.driveMile" circleColor="#8476Fc" :numFontSize="20">
              </TitleHintSK>
              <TitleHintSK style="width: 30%" title="时长" icon="pony-shijian2" unit="h"
                :num="modal.data.lowSpeedNight.driveTime" circleUnit="%" :score="modal.data.lowSpeedNight.driveTime"
                :max="modal.data.driveTime" circleColor="#66c9ff" :numFontSize="20">
              </TitleHintSK>
              <TitleHintSK style="width: 30%" title="报警" icon="pony-baojing" unit="个"
                :num="modal.data.lowSpeedNight.eventCount" circleUnit="%" :score="modal.data.lowSpeedNight.eventCount"
                :max="modal.data.eventCount" circleColor="#f69063" :numFontSize="20">
              </TitleHintSK>
            </div>
            <div class="btn">
              <i class="pony-iconv2" :class="list.includes(4) ? 'pony-xia' : 'pony-zuo'" @click="change(4)"></i>
            </div>
          </div>
          <el-collapse-transition>
            <div class="bottom1" v-show="list.includes(4)">
              <div class="dimension-item" v-for="item in dimensionListSK" :key="item.key">
                <TitleAndHint style="cursor: pointer" :title="item.label" :hint="item.hint"
                  :count="modal.data.lowSpeedNight[item.count]" :icon="item.unicode"
                  :max="modal.data.scoreContainerMax[item.key]"
                  :reportData="Math.round(modal.data.lowSpeedNight[item.key])" @click.native="changeToDetail(3, item)">
                </TitleAndHint>
              </div>
            </div>
          </el-collapse-transition>
        </div>
      </div>
    </div>
  </PonyDialog>
</template>

<script>
import TitleAndHint from '@/view/customReport/reportComponent/circle/Title&HintSKV2'
import TitleRandkingSK from '@/view/customReport/reportComponent/circle/Title&RankingSK'
import TitleHintSK from '@/view/customReport/reportComponent/circle/Title&HintSK'
import { dimensionListSK } from './scoreDetailConfg';
import moment from "moment";

export default {
  name: "scoreDetailSK",
  components: {
    TitleAndHint,
    TitleRandkingSK,
    TitleHintSK
  },
  data() {
    return {
      modal: {
        show: false,
        loading: false,
        data: {
          plateNo: '',
          deptName: '',
          redisTime: '',
          eventCount: '',
          driveMile: '',
          driveTime: '',
          dayExScore: '',// 日间高速评分
          dayNoExScore: '',// 日间非高速评分
          nightExScore: '',// 夜间高速评分
          nightNoExScore: '',// 夜间非高速评分
          safeScore: {},
          highSpeedDay: {},
          lowSpeedDay: {},
          highSpeedNight: {},
          lowSpeedNight: {},
          scoreAndTimeList: [],
          scoreContainerMax: {}
        }
      },
      list: [],
      detailTitle: '车辆评分详情',
      tab: '',
      dimensionListSK,
      currentDayState: true,  //  false 实时的   true 查询的历史
    }
  },
  methods: {
    refreshData() {
      this.$emit('refresh');
    },
    async show(data, tab) {
      this.modal.loading = true;
      this.modal.deptId = null
      this.modal.vehicleId = null
      this.tab = tab
      if (tab === 'vehicle') {
        this.detailTitle = '车辆评分详情'
        this.modal.data.vehicleId = data.vehicleId
      } else {
        this.detailTitle = '企业评分详情'
        this.modal.data.deptId = data.deptId
      }
      this.modal.data.time = data.time
      this.modal.data.plateNo = data.plateNo
      this.modal.data.deptName = data.deptName
      this.modal.data.redisTime = data.date
      if (data.totalScore === 0) {
        this.modal.data.safeScore = {
          value: data.totalScore,
          rate: data.totalScore,

          total: 100
        }
      } else {
        this.modal.data.safeScore = {
          value: Math.round(data.totalScore),
          rate: Math.round(data.totalScore),

          total: 100,
        }
      }
      this.modal.data.eventCount = data.eventCount
      this.modal.data.driveMile = data.driveMile
      this.modal.data.driveTime = data.driveTime
      this.modal.data.dayExScore = data.dayExScore // 日间高速评分
      this.modal.data.dayNoExScore = data.dayNoExScore // 日间非高速评分
      this.modal.data.nightExScore = data.nightExScore // 夜间高速评分
      this.modal.data.nightNoExScore = data.nightNoExScore // 夜间非高速评分
      this.modal.data.highSpeedDay = data.scoreContainerList[0]
      this.modal.data.lowSpeedDay = data.scoreContainerList[1]
      this.modal.data.highSpeedNight = data.scoreContainerList[2]
      this.modal.data.lowSpeedNight = data.scoreContainerList[3]
      this.modal.data.scoreContainerMax = data.scoreContainerMax
      this.modal.show = true;
      this.modal.loading = false;
    },
    change(val) {
      if (this.list.includes(val)) {
        this.list.splice(this.list.indexOf(val), 1);
      } else {
        this.list.push(val)
      }
    },
    // 跳转赛科报警分析
    changeToDetail(timeRoad, item) {
      let data = {}
      data.timeRoad = Number(timeRoad)
      data.date = moment(this.modal.data.time[0]).format("YYYY-MM-DD") + '至' + moment(this.modal.data.time[1]).format("YYYY-MM-DD")
      data.vehicleId = Number(this.modal.data.vehicleId)
      data.deptId = this.modal.data.deptId
      let alarm = {
        'speedScore': '8',
        'crossTseScore': '9',
        'accScore': '6',
        'brakeScore': '7',
        'fcwScore': '2|3',
        'pcwScore': '1',
        'laneKeepScore': '4',
        'hmwScore': '5',
        'turnLightScore': '4|10|11',
      }
      data.alarmList = alarm[item.key]
      this.$router.push({
        path: '/home/<USER>',
        query: data
      })
    }
  }
}
</script>
<style scoped lang="scss">
#detailModal {
  /deep/ .custom-header {
    margin: 0 -10px;
    width: calc(100% + 20px);
    height: 65px;
    display: flex;
    align-items: center;
    padding: 0 15px;
    justify-content: space-between;
    background: var(--background-color-base);

    .custom-title-wrap {
      align-self: stretch;
      flex-grow: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: space-between;
      padding-left: 33px;

      .title {
        margin-top: 15px;
        font-size: 18px;
        font-weight: 500;
        color: var(--color-text-regular);
        line-height: 1;
      }

      .info {
        margin-bottom: 8px;
        color: var(--color-text-secondary);
        line-height: 1;
      }
    }
  }

  /deep/ .report-data-wrap {
    display: flex;
    flex-direction: column;
    padding-top: 15px;

    .report-data-item {
      display: flex;
      align-items: center;

      .block-list {
        display: flex;
        justify-content: space-around;
        flex-wrap: wrap;
        flex-grow: 1;
        padding: 0 0 0 10px;

        .block-item {
          display: flex;
          height: 100%;
          align-items: center;

          .fl {
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: space-around;
            border-radius: 10px;


            i {
              font-size: 30px;
              color: #fff;
            }
          }

          .fr {
            display: flex;
            align-items: flex-start;
            justify-content: center;
            flex-direction: column;
            margin-left: 10px;

            .bottom {
              .num {
                font-size: 30px;
              }

              .unit {
                font-size: 12px;
                margin-left: 5px;
              }
            }
          }
        }
      }
    }

    .detail {
      margin-top: 10px;
      background-color: var(--background-color-calendar-dayItem);
      padding: 10px 0;
      border-radius: 3px;
      border: 1px solid var(--border-color-base);

      .list {
        padding: 0 15px;
        width: 100%;
        border-bottom: 1px solid var(--border-color-lighter);

        &:last-child {
          border: 0;
        }

        .top {
          width: 100%;
          display: flex;
          align-items: center;
          padding: 5px 0;

          .title {
            width: 22%;

            .num {
              background-color: rgba(52, 184, 67, .2);
              margin-left: 5px;
              padding: 2px 7px;
              border-radius: 4px;
              color: rgb(52, 184, 67);
              font-weight: 500;
            }
          }

          .item {
            width: 73%;
            display: flex;
            justify-content: space-around;
          }

          .btn {
            width: 25px;
            height: 25px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 5px;
            cursor: pointer;
            border: 1px solid var(--border-color-base);
            background-color: var(--background-color-modal);
          }
        }

        .bottom1 {
          display: flex;
          flex-wrap: wrap;
          background-color: var(--background-color-base);
          margin-bottom: 10px;

          .dimension-item {
            width: 20%;
            height: 135px;
          }
        }

      }
    }
  }
}
</style>

<template>
		<PonyDialog :width="width"
		            :loading="loading"
		            title="表格显示配置" :contentMaxHeight="410"
		            v-model="show">
				<div class="transfer" :style="formatStyle">
						<div class="transfer__box transfer--left">
								<div class="header">
										<el-checkbox
												:indeterminate="leftHalfCheck"
												v-model="leftAllCheck"
												@change="handleLeftAllCheck"
												:disabled="disabled">
												全部
										</el-checkbox>
										<span>{{ leftChecked.length }} / {{ totalCount }}</span>
								</div>
								<div class="content">
										<div class="filter">
												<el-select v-model="leftFilterValue" filterable placeholder="请选择" style="width: 100%" :disabled="disabled">
														<template v-if="showOcr">
																<el-option-group label="基础字段">
																		<el-option
																				v-for="(item,index) in baseData"
																				:label="item.name"
																				:value="item.key"
																				:key="index"
																				@click.native="unSettingSearchInput(index,item.key,item)">
																		</el-option>
																</el-option-group>
																<el-option-group label="OCR字段">
																		<el-option
																				v-for="(item,index) in ocrData"
																				:label="item.name"
																				:value="item.key"
																				:key="index"
																				@click.native="unSettingSearchInput(index,item.key,item)">
																		</el-option>
																</el-option-group>
														</template>
														<template v-else>
																<el-option
																		v-for="(item,index) in data"
																		:label="item.name"
																		:value="item.key"
																		:key="index"
																		@click.native="unSettingSearchInput(index,item.key,item)">
																</el-option>
														</template>
												</el-select>
										</div>
										<div class="main">
												<ul>
														<template v-if="showOcr">
																<div class="group-header">
																		<div class="group-header__left">
																				<el-checkbox
																						:indeterminate="baseHalfCheck"
																						v-model="baseAllCheck"
																						@change="handleBaseAllCheck"
																						:disabled="disabled">
																					基础字段
																		</el-checkbox>
																		</div>
																		<div class="group-header__right">
																				<span>{{ baseCheckedCount }} / {{ baseData.length }}</span>
																				<i 
																						:class="['el-icon-arrow-' + (baseExpand ? 'down' : 'right'), 'expand-icon']"
																						@click="baseExpand = !baseExpand"
																				></i>
																		</div>
																</div>
																<el-collapse-transition>
																		<el-checkbox-group v-show="baseExpand" v-model="leftChecked">
																				<li v-for="(setting, index) in baseData" :key="index">
																						<el-checkbox class="check" :label="setting.key" :disabled="disabled">{{ setting.name }}</el-checkbox>
																						<slot name="prop-left"></slot>
																				</li>
																		</el-checkbox-group>
																</el-collapse-transition>
																<div class="group-header">
																		<div class="group-header__left">
																				<el-checkbox
																						:indeterminate="ocrHalfCheck"
																						v-model="ocrAllCheck"
																						@change="handleOcrAllCheck"
																						:disabled="disabled">
																						OCR字段
																		</el-checkbox>
																		</div>
																		<div class="group-header__right">
																				<span>{{ ocrCheckedCount }} / {{ ocrData.length }}</span>
																				<i 
																						:class="['el-icon-arrow-' + (ocrExpand ? 'down' : 'right'), 'expand-icon']"
																						@click="ocrExpand = !ocrExpand"
																				></i>
																		</div>
																</div>
																<el-collapse-transition>
																		<el-checkbox-group v-show="ocrExpand" v-model="leftChecked">
																				<li v-for="(setting, index) in ocrData" :key="index">
																						<el-checkbox class="check" :label="setting.key" :disabled="disabled">{{ setting.name }}</el-checkbox>
																						<slot name="prop-left"></slot>
																				</li>
																		</el-checkbox-group>
																</el-collapse-transition>
														</template>
														<template v-else>
																<el-checkbox-group v-model="leftChecked">
																		<li v-for="(setting, index) in data" :key="index">
																				<el-checkbox class="check" :label="setting.key" :disabled="disabled">{{ setting.name }}</el-checkbox>
																				<slot name="prop-left"></slot>
																		</li>
																</el-checkbox-group>
														</template>
												</ul>
										</div>
								</div>
						</div>
						<div class="transfer__box transfer--right" style="margin: 0">
								<div class="content right">
										<div class="main">
												<draggable v-model="settingList" group="people" @start="drag=true" @end="drag=false" chosenClass="active" @update="changeNum">
														<div
																v-for="(setting,index) in settingList"
																:key="setting.key"
																class="sort-item"
																:class="active == index ? 'active' : ''"
																@click="changeActive(index)"
														>
																{{ setting.name }}
														</div>
												</draggable>
										</div>
								</div>
						</div>
				</div>
				<template slot="footer">
						<el-button size="mini" type="primary" @click="clearDeFault">恢复默认</el-button>
						<el-button size="mini" type="primary" @click="show = false">关闭</el-button>
						<el-button size="mini" type="primary" @click="changeTableSetting">确认</el-button>
				</template>
		</PonyDialog>
</template>

<script>
import draggable from 'vuedraggable'
import { mapState } from 'vuex';

export default {
		name: 'TableShowConfigList',
		components: {
				draggable,
		},
		model: {
				prop: "tableSettingList",
				event: "change"
		},
		data() {
				return {
						show: false,
						loading: false,
						data: [],
						settingList: [], // 选中的数据
						leftAllCheck: false, // 左侧全选
						leftHalfCheck: false, // 左侧半选
						leftChecked: [], // 左侧已经选中的数据
						leftFilterValue: '', // 下拉框搜索
						baseAllCheck: false, // 基础字段全选
						baseHalfCheck: false, // 基础字段半选
						ocrAllCheck: false, // OCR字段全选
						ocrHalfCheck: false, // OCR字段半选
						baseExpand: true, // 基础字段展开状态
						ocrExpand: true, // OCR字段展开状态

						defaultProps: {
								children: 'children',
								label: 'name'
						},
						active: ''
				};
		},
		props: {
        width: {
            type: [String,Number],
            default: '545'
        },	
				// 宽度
				autoWidth: {
						type: [String, Number],
						default: '520px'
				},
				// 高度
				autoHeight: {
						type: [String, Number],
						default: '300px'
				},

				// 是否可排序
				sortable: {
						type: Boolean,
						default: true
				},
				type: {
						type: String,
				},
				//是否需要存入浏览器
				isSave: {
						type: Boolean,
						default: false
				},
				//存浏览器的页面名称路由)
				pageName: {
						type: String,
						default: ''
				},
				// 传入的全部列表
				list: {
						type: Array,
						default: function () {
								return []
						},
						required: false
				},
				defaultSetting: {
						type: Array
				},
				// 传入的已经选中的列表
				tableSettingList: {
						type: Array,
						default: function () {
								return []
						}
				},
				disabled:{
					type:Boolean,
					default:false
				},
				// OCR 数据列表
				ocrList: {
						type: Array,
						default: () => []
				},
				// 是否显示 OCR 分组
				showOcr: {
						type: Boolean,
						default: false
				}
		},
		computed: {
			...mapState('auth',['userInfo']),
				formatStyle() {
						let widthStyle = typeof (this.autoWidth) == "string" ? this.autoWidth : `${this.autoWidth}px;`
						let heightStyle = typeof (this.autoHeight) == "string" ? this.autoHeight : `${this.autoHeight}px`
						return `width: ${widthStyle}; height: ${heightStyle}`
				},
        // vue监听数组newValue和oldValue值一样
        checked() {
            return JSON.parse(JSON.stringify(this.leftChecked))
        },
				// 计算总数
				totalCount() {
						if (this.showOcr) {
								// 确保不重复计算相同的 key
								const allKeys = new Set([
										...this.data.map(item => item.key),
										...this.ocrList.map(item => item.key)
								])
								return allKeys.size
						}
						return this.data.length
				},
				// 基础数据
				baseData() {
						return this.data
				},
				// OCR 数据
				ocrData() {
						return this.ocrList
				},
				// 基础字段选中数量
				baseCheckedCount() {
						return this.leftChecked.filter(key => 
								this.baseData.some(item => item.key === key)
						).length
				},
				// OCR字段选中数量
				ocrCheckedCount() {
						return this.leftChecked.filter(key => 
								this.ocrData.some(item => item.key === key)
						).length
				}
		},
		watch: {
        checked: {
            handler(newVal) {
				if (this.showOcr) {
					// 基础字段全选状态
					this.baseAllCheck = this.baseCheckedCount === this.baseData.length
					this.baseHalfCheck = this.baseCheckedCount > 0 && this.baseCheckedCount < this.baseData.length
					
					// OCR字段全选状态
					this.ocrAllCheck = this.ocrCheckedCount === this.ocrData.length
					this.ocrHalfCheck = this.ocrCheckedCount > 0 && this.ocrCheckedCount < this.ocrData.length
					
					// 整体全选状态
					this.leftAllCheck = newVal.length === this.totalCount
					this.leftHalfCheck = newVal.length > 0 && newVal.length < this.totalCount
				} else {
					this.leftAllCheck = newVal.length === this.data.length
					this.leftHalfCheck = newVal.length > 0 && newVal.length < this.data.length
				}
                this.rightDataListChange(newVal)
            },
            deep: true
        }
		},
		async mounted() {
				if (this.type) {
						let res = await this.$store.dispatch('ztreeData/getTreeData', this.type)
						if (!res) {
								this.$error('查询配置树出错!')
								this.data = []
						} else {
								this.data = res
						}
				} else if (this.list) {
						this.data = this.list
				}
				let obj = localStorage.getItem(this.userInfo.id+'tableconfigNew') ? JSON.parse(localStorage.getItem(this.userInfo.id+'tableconfigNew')) : {}
				this.$emit('tableValue' ,obj[this.pageName])

		},
		methods: {
				changeNum(e) {
						this.$nextTick(() => {
								this.active = e.newIndex
						})
				},
				changeActive(value) {
						this.active = value
				},
				async showModel() {
						this.leftFilterValue = ''
						this.show = true
						this.loading = true
						await this.$nextTick()
						// 所有已经选中数据的数组
						this.leftChecked = this.tableSettingList.map(item => item.key)
						if(this.list) this.data = this.list //全部数据可能进来会切换
						this.loading = false
				},
				// 左侧下拉搜索框
				unSettingSearchInput(val, id, item) {
						let height = val * 30
						let divBox = document.querySelector(".transfer--left .main")
						divBox.scrollTop = height
						let index = this.leftChecked.findIndex(it => it === id)
						if (index === -1) {
								this.leftChecked.push(id)
						}
				},
				/**
				 * 左侧全选
				 * value 是否选中 true: 全选
				 */
				handleLeftAllCheck(val) {
						if (val) {
								if (this.showOcr) {
										// 使用 Set 去重，避免重复的 key
										this.leftChecked = [...new Set([
												...this.baseData.map(item => item.key),
												...this.ocrData.map(item => item.key)
										])]
								} else {
										this.leftChecked = this.data.map(item => item.key)
								}
								this.leftHalfCheck = false
						} else {
								this.leftChecked = []
								this.leftAllCheck = false
						}
				},
				/**
				 * 右侧数据的展示
				 * @param {*} listNew 新数组的id List
				 */
				rightDataListChange(listNew) {
						let newArr = this.getArrayDifference(listNew, this.settingList.map(item => item.key))
						newArr.length && newArr.forEach(item => {
								let index = this.settingList.findIndex((it) => it.key === item)
								if (index === -1) {
										// 在基础数据和OCR数据中查找
										let addItem = this.showOcr 
											? [...this.data, ...this.ocrList].find(it => it.key === item)
											: this.data.find(it => it.key === item)
											
										if (addItem) {
												this.settingList.push(addItem)
										}
								} else {
										this.settingList.splice(index, 1)
								}
						})
				},
				/**
				 * @description 获取两个数据的差集
				 */
				getArrayDifference(list, whole){
						return list.concat(whole).filter((v, i, arr) => {
								return arr.indexOf(v) === arr.lastIndexOf(v)
						})
				},
				// 设置表格弹窗点击恢复按钮
				clearDeFault() {
						let arr = JSON.parse(JSON.stringify(this.defaultSetting))
						this.leftChecked = arr.map(item => item.key)
						this.$nextTick(() => {
								this.settingList = arr
						})
				},
				// 设置表格弹窗点击确认按钮
				async changeTableSetting() {
						let listValue = JSON.parse(JSON.stringify(this.settingList))
						this.$emit('change',listValue)
						
						if(this.isSave){
							let obj = localStorage.getItem(this.userInfo.id+'tableconfigNew') ? JSON.parse(localStorage.getItem(this.userInfo.id+'tableconfigNew')) : {}
							obj[this.pageName] = listValue
							localStorage.setItem(this.userInfo.id+'tableconfigNew',JSON.stringify(obj))
						}
						this.show = false
				},
				// 基础字段全选
				handleBaseAllCheck(val) {
						const baseKeys = this.baseData.map(item => item.key)
						if (val) {
								// 选中所有基础字段，保持其他字段状态不变
								this.leftChecked = [...new Set([
										...this.leftChecked.filter(key => !baseKeys.includes(key)),
										...baseKeys
								])]
						} else {
								// 取消所有基础字段，保持其他字段状态不变
								this.leftChecked = this.leftChecked.filter(key => !baseKeys.includes(key))
						}
				},
				// OCR字段全选
				handleOcrAllCheck(val) {
						const ocrKeys = this.ocrData.map(item => item.key)
						if (val) {
								// 选中所有OCR字段，保持其他字段状态不变
								this.leftChecked = [...new Set([
										...this.leftChecked.filter(key => !ocrKeys.includes(key)),
										...ocrKeys
								])]
						} else {
								// 取消所有OCR字段，保持其他字段状态不变
								this.leftChecked = this.leftChecked.filter(key => !ocrKeys.includes(key))
						}
				}
		}
}

</script>

<style lang='scss' scoped>
.transfer {
		display: flex;

		&__box {
				height: 100%;
				display: flex;
				margin-right: 10px;
				flex-direction: column;
				width: calc((100% - 10px) / 2);
				border: 1px solid var(--border-color-light);
				border-radius: 4px;

				.header {
						padding: 8px 10px;
						border-bottom: 1px solid var(--border-color-light);
						display: flex;
						justify-content: space-between;
				}

				.content {
						height: calc(100% - 35px);
						flex-grow: 1;
						padding: 8px 10px;
						display: flex;
						flex-direction: column;

						.filter {
								height: 35px;
								width: 100%;
								display: flex;
								align-items: center;
						}

						.main {
								flex-grow: 1;
								overflow: scroll;

								&::-webkit-scrollbar {
										width: 0;
								}

								.check {
										margin-right: 10px;
								}

								.operate {
										font-size: 16px;
										cursor: pointer;
								}

								.el-checkbox {
										flex-grow: 1;
								}

								ul {
										height: 100%;

										li {
												line-height: 30px;
												display: flex;
												align-items: center;
												font-size: 14px;
										}
								}

								.sort-item {
										line-height: 30px;
										display: flex;
										align-items: center;
										font-size: 14px;
										cursor: default;

										&:nth-child(2n) {
												background-color: var(--background-color-lighter);
										}
								}
						}
				}
				.right {
						padding: 7px 0!important;

						.sort-item {
								padding: 0 10px;
						}

						.active {
								background-color: var(--background-color-hover)!important;
						}
				}
		}
}

.group-header {
		padding: 8px 0;
		border-bottom: 1px solid var(--border-color-light);
		display: flex;
		justify-content: space-between;
		align-items: center;
		
		&__left {
				display: flex;
				align-items: center;
				
				.el-checkbox {
						font-weight: bold;
						color: var(--color-text-secondary);
				}
		}
		
		&__right {
				display: flex;
				align-items: center;
				gap: 8px;
				
				.expand-icon {
						cursor: pointer;
						font-size: 14px;
						color: var(--color-text-secondary);
						transition: transform 0.3s;
						
						&:hover {
								color: var(--color-primary);
						}
				}
		}
}

// 添加过渡动画样式
.el-collapse-transition {
		transition: 0.3s height ease-in-out, 0.3s padding-top ease-in-out, 0.3s padding-bottom ease-in-out;
}
</style>

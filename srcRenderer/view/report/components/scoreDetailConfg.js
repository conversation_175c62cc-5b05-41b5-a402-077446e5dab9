/**
 * @Author: yezy
 * @Email: <EMAIL>
 * @Date: 2019/11/11 11:20
 * @LastEditors: yezy
 * @LastEditTime: 2019/11/11 11:20
 * @Description:
 */
export const scoreList = [
  {
    label: "里程",
    unit: "km",
    key: "driveMile",
  },
  {
    label: "时长",
    unit: "h",
    key: "driveTime",
  },
  {
    label: "最高车速",
    unit: "km/h",
    key: "maxSpeed",
  },
  {
    label: "平均车速",
    unit: "km/h",
    key: "averageSpeed",
  },
  {
    label: "高速评分",
    unit: "分",
    key: "eventExpresswayScore",
  },
  {
    label: "非高速评分",
    unit: "分",
    key: "eventUnExpresswaySafeScore",
  },
  {
    label: "总事件",
    unit: "次",
    key: "eventCount",
  },
];

export const scoreHistoryList = [
  {
    label: "里程",
    unit: "km",
    key: "driveMile",
  },
  {
    label: "时长",
    unit: "h",
    key: "driveTime",
  },
  {
    label: "最高车速",
    unit: "km/h",
    key: "maxSpeed",
  },
  {
    label: "平均车速",
    unit: "km/h",
    key: "averageSpeed",
  },
  {
    label: "高速评分",
    unit: "分",
    key: "eventExpresswayScore",
  },
  {
    label: "非高速评分",
    unit: "分",
    key: "eventUnExpresswaySafeScore",
  },
  {
    label: "总事件",
    unit: "次",
    key: "eventCount",
  },
  {
    label: "日间评分",
    unit: "分",
    key: "eventDaySafeScore",
  },
  {
    label: "夜间评分",
    unit: "分",
    key: "eventNightSafeScore",
  },
];

/** @typedef DimensionItem
 * @property {String} label 维度名称
 * @property {String} unicode 图标unicode
 * @property {String} key 数据键值
 * @property {Array<String>} hint 不同分值得提示
 * @property {Object} max 不同数据模型下的最大分值
 * */

/**
 * @type Array<DimensionItem>
 * */
export const dimensionList = [
  {
    label: "急加速",
    unicode: "\ue81b",
    key: "accScore",
  },
  {
    label: "急减速",
    unicode: "\ue817",
    key: "brakeScore",
  },
  {
    label: "追尾风险",
    unicode: "\ue7a7",
    key: "fcwScore",
  },
  {
    label: "车距控制",
    unicode: "\ue81f",
    key: "hmwScore",
  },
  {
    label: "车道保持",
    unicode: "\ue812",
    key: "laneKeepScore",
  },
  {
    label: "行人和非机动车碰撞",
    unicode: "\ue7a3",
    key: "pcwScore",
  },
  {
    label: "超速",
    unicode: "\ue819",
    key: "speedScore",
    max: { jiande: 20 },
  },
  {
    label: "转向灯使用",
    unicode: "\ue7a6",
    key: "turnLightScore",
  },
  {
    label: "急转弯",
    unicode: "\ue814",
    key: "turnScore",
  },
  {
    label: "抽烟风险",
    unicode: "\ue80c",
    key: "smokeScore",
    max: { jiande: 20 },
  },
  {
    label: "注意力不集中",
    unicode: "\ue801",
    key: "distractScore",
    max: { jiande: 20 },
  },
  {
    label: "无司机",
    unicode: "\ue961",
    key: "nodriverScore",
  },

  {
    label: "不规范驾驶",
    unicode: "\ue884",
    key: "lrreguarDriveScore",
  },
  {
    label: "速度控制",
    unicode: "\ue883",
    key: "speedNoAdasScore",
  },
  {
    label: "打电话评分",
    unicode: "\ue80b",
    key: "telScore",
    max: { jiande: 20 },
  },
  {
    label: "疲劳驾驶评分",
    unicode: "\ue81c",
    key: "tiredNoAdasScore",
  },
  {
    label: "疲劳驾驶评分",
    unicode: "\ue81c",
    key: "tiredScore",
    max: { jiande: 20 },
  },
  {
    label: "路口超速",
    unicode: "\ue7fa",
    key: "crossTseScore",
    max: { jiande: 20 },
  },
  {
    label: "路口未打灯",
    unicode: "\ue7f9",
    key: "crossNolightScore",
  },
  {
    label: "高峰期闯限行",
    unicode: "\ue7ff",
    key: "peaktermLineScore",
  },
  {
    label: "无通行证闯限行",
    unicode: "\ue7fc",
    key: "noPasscheckScore",
  },
  {
    label: "工作超时评分",
    unicode: "\ue802",
    key: "workOvertimeScore",
  },
  {
    label: "工作超时评分",
    unicode: "\ue802",
    key: "workOvertimeScore",
  },
  {
    label: " 路口左转弯超速",
    unicode: "\ue860",
    key: "crossLeftTseScore",
  },
  {
    label: " 路口右转弯超速",
    unicode: "\ue861",
    key: "crossRightTseScore",
  },
  {
    label: "路口直行超速",
    unicode: "\ue85e",
    key: "crossStrightTseScore",
  },
  {
    label: " 交警评分",
    unicode: "\ue824",
    key: "policeScore",
  },
];

export const dimensionListSK = [
  {
    label: "超速",
    unicode: "\ue819",
    key: "speedScore",
    count: "speedCount",
    hint: ["危险，注意车速", "注意车速", "继续保持", "继续保持"],
  },
  {
    label: "急转弯",
    unicode: "\ue70b",
    key: "crossTseScore",
    count: "crossTseCount",

    hint: ["危险，注意转弯", "注意转弯", "继续保持", "继续保持"],
  },
  {
    label: "急加速",
    unicode: "\ue81b",
    key: "accScore",
    count: "accCount",

    hint: ["危险，注意急加速", "注意急加速", "继续保持", "继续保持"],
  },
  {
    label: "急减速",
    unicode: "\ue817",
    key: "brakeScore",
    count: "brakeCount",

    hint: ["危险，注意急减速", "注意急减速", "继续保持", "继续保持"],
  },
  {
    label: "追尾",
    unicode: "\ue7a7",
    key: "fcwScore",
    count: "fcwCount",

    hint: ["危险，注意急减速", "注意急减速", "继续保持", "继续保持"],
  },
  {
    label: "碰撞",
    unicode: "\ue811",
    key: "pcwScore",
    count: "pcwCount",

    hint: ["危险，注意行人和非机动车", "注意行人和非机动车碰撞", "继续保持", "继续保持"],
  },
  {
    label: "车道保持",
    unicode: "\ue812",
    key: "laneKeepScore",
    count: "laneKeepCount",

    hint: ["危险，注意车道保持", "注意车道保持", "继续保持", "继续保持"],
  },
  {
    label: "车距控制",
    unicode: "\ue81f",
    key: "hmwScore",
    count: "hmwCount",

    hint: ["危险，注意车距控制", "注意车距控制", "继续保持", "继续保持"],
  },
  {
    label: "转弯",
    unicode: "\ue814",
    key: "turnLightScore",
    count: "turnLightCount",

    hint: ["危险，注意转弯", "注意转弯", "继续保持", "继续保持"],
  },
];

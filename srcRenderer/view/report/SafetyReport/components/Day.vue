<template>
  <div class="reportDay" ref="page">
    <div class="top">
      <div class="card">
        <i class="pony-iconv2 pony-baobiao1"></i>
      </div>
      <div class="company">
        <div>{{ reportData.title1 }}</div>
        <div>{{ reportData.title2 }}</div>
      </div>
      <img src="../../../../../static/img/picture/earch.png" alt="">
      <el-button type="primary" class="btn" @click="exportPdf">导出</el-button>
    </div>
    <div class="item">
      <div class="search">
        <div class="header">
          <div class="title">重点关注</div>
          <div class="config" @click="showConfigDialog"><i class="pony-iconv2 pony-shezhi"></i></div>
        </div>
        <div class="list" v-show="list.tiredDriving">
          <div class="left">疲劳驾驶</div>
          <div class="right">
            高风险频发时间段在
            <span class="red">{{ safetyData.overview_suggestion_tired }}</span>
            ,请关注调度排班的合理性
            {{ getOverviewSuggestionTiredHour(safetyData.overview_suggestion_tired_hour) }}。
          </div>
        </div>
        <div class="list" v-show="list.keyIntervention">
          <div class="left">重点干预</div>
          <div class="right">重点关注<span class="red">{{ safetyData.overview_suggestion_focus || '-' }}</span>等
            <span class="red">{{ safetyData.overview_suggestion_focus_num }}</span>辆车，风险事件多发，建议加强司机安全驾驶教育。
          </div>
        </div>
        <div class="list" v-show="list.overtimeDriving">
          <div class="left">超时驾驶</div>
          <div class="right">
            本企业有超<span class="red">{{ safetyData.overview_suggestion_overtime }}</span>的车辆日均作业时长超过<span
            class="red">{{ safetyData.overview_suggestion_overtime_hour }}</span>小时，建议合理调度运力。
          </div>
        </div>
        <div class="list" v-show="list.vehicleFault">
          <div class="left">车辆故障</div>
          <div class="right">
            本期发生<span v-if="!safetyData.overview_suggestion_exception">未发生</span>高风险故障
            <span class="red">{{ safetyData.overview_suggestion_exception }}</span>
            <span v-if="safetyData.overview_suggestion_exception">，请重点关注</span>。
          </div>
        </div>
        <div class="list" v-show="list.safetyReview">
          <div class="left">安全综述</div>
          <div class="right">
            <ul>
              <li>
                1.本期安全风险控制
                <span class="red">{{ safetyData.overview_suggestion_safety_level }}</span>
                <span v-if="safetyData.overview_suggestion_safety_level === '较好'">,继续保持</span>。
              </li>
              <li v-if="safetyData.overview_suggestion_safety_event">2.本期<span class="red">{{ safetyData.overview_suggestion_safety_event }}</span>发生较多，需重点关注。</li>
              <li v-if="safetyData.overview_suggestion_safety_hour">3.多发事件集中发生在（<span class="red">{{ safetyData.overview_suggestion_safety_hour }}</span>）。</li>
            </ul>
          </div>
        </div>
        <div class="search-item">

          <div class="right-int" :style="isExport? 'margin-top:5px':''">
            <p title="" v-if="!hasPermission('safetyReport:comments')">{{ searchValue }}</p>
            <el-input v-model="searchValue" placeholder="请输入..." v-if="hasPermission('safetyReport:comments')"></el-input>
            <el-button type="primary" class="btn" @click="copy" v-if="!hasPermission('safetyReport:comments')">复制</el-button>
            <el-button type="primary" class="btn" @click="saveRemark" v-if="hasPermission('safetyReport:comments')">保存
            </el-button>
          </div>
        </div>
      </div>
    </div>
    <div class="item">
      <div class="item__title">
        <span class="line"></span>
        <span class="title">整体数据概览</span>
        <span class="line"></span>
      </div>
      <div class="item__content" :style="{height: '120px'}">
        <DataList :list="dayList" :report-data="reportData"></DataList>
      </div>
    </div>
    <div class="item">
      <div class="card__title">车辆干预次数TOP5</div>
      <el-table
        ref="table1"
        class="el-table--radius"
        border stripe size="mini"
        :data="reportData.interferedTop5"
        :height="isExport?'':reportData.interferedTop5 && reportData.interferedTop5.length > 5 ? 208:''" style="width: 100%;">
        <el-table-column
          type="index"
          label="序号"
          width="50">
        </el-table-column>
        <el-table-column
          prop="plateNo"
          label="车牌号"
          min-width="90" show-overflow-tooltip>
        </el-table-column>
        <el-table-column
          prop="num"
          min-width="90"
          label="车辆干预次数">
        </el-table-column>
        <el-table-column label="标签" min-width="250" align="left">
          <template slot-scope="{row}">
            <span class="scope-label" v-for="(item,index) in row.labels" :key="item"
                  :style="{'color':colorList[(index+1)%4],'background-color':'rgba'+rgbList[(index+1)%4]}">{{
                eventObj[item].name
              }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="item item-attention">
      安全管理建议:安全坐席已经在安全事件发生的时候向司机发送干预信息，请安全管理者与司机再次沟通确认，提醒其规范驾驶。
    </div>
    <div class="item">
      <div class="card__title">设备异常</div>
      <el-table
        ref="table2"
        class="el-table--radius"
        border stripe size="mini"
        :data="reportData.deviceList"
        :height="isExport?'':reportData.deviceList && reportData.deviceList.length > 5 ? 208:''"
        style="width: 100%;">
        <el-table-column
          type="index"
          label="序号"
          width="50">
        </el-table-column>
        <el-table-column
          prop="plateNo"
          label="车牌号"
          min-width="90" show-overflow-tooltip>
        </el-table-column>
        <el-table-column
          prop="status"
          width="100"
          label="设备维修状态">
        </el-table-column>
        <el-table-column
          label="设备故障类型" min-width="250" align="left">
          <template slot-scope="{row}">
            <span class="scope-label" v-for="(item,index) in row.labels" :key="item"
                  :style="{'color':colorList[(index+1)%4],'background-color':'rgba'+rgbList[(index+1)%4]}">
              {{ labelsList[item - 1] }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="date" label="预计完成时间" width="135"></el-table-column>
        <el-table-column prop="desc" label="原车故障" width="277" align="left"></el-table-column>
      </el-table>
    </div>
    <PonyDialog width="400"
                title="设置" :contentMaxHeight="410"
                contentStyle="padding:0"
                :hasFooter="false"
                v-model="showDialog">
      <div class="dialog">
        <div class="tab" v-for="(item,index) in Object.keys(list) " :key=item>
          <div class="name">{{ nameList[item] }}</div>
          <el-switch
            v-model="list[item]"
            active-color="#2a80e0">
          </el-switch>
        </div>
      </div>
    </PonyDialog>
    <!-- <div class="bottom"></div> -->
  </div>
</template>

<script>
import DataList from "./DataList";
import html2canvas from "html2canvas";
import jsPDF from "jspdf";
import {mapState} from "vuex";

export default {
  name: "Day",
  components: {
    DataList
  },
  props: {
    eventObj: {
      type: Object
    }
  },
  computed: {
    ...mapState('auth', ['userInfo']),
  },
  data() {
    return {
      isExport: false,
      colorList: ['#ff6666', '#ff8866', '#2b80e0', '#7f70d1',],
      rgbList: ['(255,102,102,.1)', '(255,136,102,.1)', '(43,128,224,.1)', '(127,112,209,.1)'],
      labelsList: ['相机故障', '定位异常', '存储异常'],
      searchValue: '',
      // 时间
      date: '',
      // 所有数据
      reportData: {},
      dayList: [
        {
          title: '安全事件',
          num: 'eventNum',
          lastNum: 'eventNumLast',
          rateNum: 'eventNumRate'
        },
        {
          title: '干预次数',
          num: 'interferedNum',
          lastNum: 'interferedNumLast',
          rateNum: 'interferedNumRate'
        }
      ],
      list: {
        tiredDriving: true,
        keyIntervention: true,
        overtimeDriving: true,
        vehicleFault: true,
        safetyReview: true
      },
      nameList: {
        tiredDriving: '疲劳驾驶',
        keyIntervention: '重点干预',
        overtimeDriving: '超时驾驶',
        vehicleFault: '车辆故障',
        safetyReview: '安全综述'
      },
      showDialog: false,
      safetyData: {}
    }
  },

  methods: {
    getOverviewSuggestionTiredHour(time) {
      if (!time || !time.length) {
        return "";
      }
      for (let i = 0; i < time.length; i++) {
        if (time[i] >= 6 && time[i] <= 22) {
          return "";
        } else {
          return "，减少深夜行车";
        }
      }
    },
    async safetyDay(deptId, time) {
      const res = await this.$api.getSafetyDay({
        userId: this.userInfo.id,
        date: time,
        deptId: deptId,
        keys: ['overview_suggestion']
      })
      this.safetyData = res.data
    },
    showConfigDialog() {
      this.showDialog = true
    },
    generate(query, data) {
      this.reportData = {}
      this.date = query.start
      this.reportData = data
      this.searchValue = data.comment

      this.$nextTick(() => {
        this.$refs.table1.doLayout()
        this.$refs.table2.doLayout()
      })
    },
    copy() {
      const target = document.createElement('div');
      target.id = 'tempTarget';
      target.style.opacity = '0';
      target.innerText = this.searchValue;
      document.body.appendChild(target);

      try {
        let range = document.createRange();
        range.selectNode(target);
        window.getSelection().removeAllRanges();
        window.getSelection().addRange(range);
        document.execCommand('copy');
        window.getSelection().removeAllRanges();
        this.$info('复制成功')
      } catch (e) {
      }
      target.parentElement.removeChild(target);
    },
    saveRemark() {
      if (!this.searchValue) {
        this.$warning('请填写评语!')
        return
      }
      this.$emit('saveRemark', this.searchValue)

    },
    clearData() {
      this.reportData = {};
    },
    async exportPdf() {
      this.isExport = true
      this.$parent.loading = true
      await this.$nextTick(() => {
        this.$refs.table1.doLayout();
        this.$refs.table2.doLayout();

      })
      let titleDate = this.reportData.title2 ? this.reportData.title2.split('：') : ''
      let title = this.reportData.title1 + (titleDate ? `(${titleDate[1]})` : '');
      let targetDom = document.querySelector('.reportDay');
      window.pageYoffset = 0;
      document.documentElement.scrollTop = 0;
      document.body.scrollTop = 0;
      html2canvas(targetDom, {
        allowTaint: true,
        scale: 2, // 提升画面质量，但是会增加文件大小
        height: targetDom.scrollHeight, //canvas高
        width: targetDom.scrollWidth, //canvas宽,
        background: "#fff"
      }).then((canvas) => {
        let leftHeight = canvas.height;
        let position = 0
        let a4Width = 595.28
        let a4Height = 841.89
        let a4HeightRef = Math.floor(canvas.width / a4Width * a4Height);
        let pageData = canvas.toDataURL('image/jpeg', 1.0)
        let pdf = new jsPDF('x', 'pt', 'a4')
        let index = 0,
          canvas1 = document.createElement('canvas'),
          height;
        pdf.setDisplayMode('fullwidth', 'continuous', 'FullScreen')

        function createImpl(canvas) {
          if (leftHeight > 0) {
            index++;
            let checkCount = 0;
            if (leftHeight > a4HeightRef) {
              let i = position + a4HeightRef;
              // for (i = position + a4HeightRef; i >= position; i--) {
              //     let isWrite = true
              //     for (let j = 0; j < canvas.width; j++) {
              //         let c = canvas.getContext('2d').getImageData(j, i, 1, 1).data
              //         if (c[0] != 0xff || c[1] != 0xff || c[2] != 0xff) {
              //             isWrite = false
              //             break
              //         }
              //     }
              //     if (isWrite) {
              //         checkCount++
              //         if (checkCount >= 10) {
              //             break
              //         }
              //     } else {
              //         checkCount = 0
              //     }
              // }
              height = Math.round(i - position) || Math.min(leftHeight, a4HeightRef);
              if (height <= 0) {
                height = a4HeightRef;
              }
            } else {
              height = leftHeight;
            }
            canvas1.width = canvas.width;
            canvas1.height = height;
            let ctx = canvas1.getContext('2d');
            ctx.drawImage(canvas, 0, position, canvas.width, height, 0, 0, canvas.width, height);
            let pageHeight = Math.round(a4Width / canvas.width * height);
            if (index != 1) {
              pdf.addPage();
            }
            pdf.addImage(canvas1.toDataURL('image/jpeg', 1.0), 'JPEG', 0, 0, a4Width, (a4Width / canvas1.width * height))
            leftHeight -= height;
            position += height
            $('.pdfProgress').text(index + 1);
            $('.pdfTotal').text(index + Math.ceil(leftHeight / a4HeightRef))
            if (leftHeight > 0) {
              setTimeout(createImpl, 10, canvas);
            } else {
              pdf.save(title + '.pdf')
            }
          }
        }

        //当内容未超过pdf一页显示的范围，无需分页
        if (leftHeight < a4HeightRef) {
          pdf.addImage(pageData, 'JPEG', 0, 0, a4Width, a4Width / canvas.width * leftHeight);
          pdf.save(title + '.pdf')
        } else {
          setTimeout(createImpl, 10, canvas);
        }
        this.isExport = false
        this.$parent.loading = false
      })
    },
  }
}
</script>

<style scoped lang="scss">
.reportDay {
  padding: 20px 35px;
  width: 960px;
  background-color: var(--background-color-base);

  .top {
    display: flex;
    align-items: center;
    position: relative;
    height: 110px;
    padding: 0 30px;
    background-color: var(--color-safetyReport-top-bg);

    img {
      position: absolute;
      right: 0;
      bottom: 0;
      height: 100%;

    }

    .card {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 55px;
      height: 55px;
      border-radius: 5px;
      margin-right: 20px;
      background: linear-gradient(to top, #609ee4, #2c81e0);

      i {
        font-size: 35px;
        color: #fff;
      }
    }

    .company {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      height: 55px;

      div {
        &:nth-child(1) {
          font-weight: 800;
          font-size: 20px;
        }
      }
    }

    .btn {
      position: absolute;
      right: 13%;
      bottom: 30px;
    }
  }

  .search {
    background-color: var(--color-safetyReport-top-bg);
    border-radius: 5px;
    padding: 14px 30px;

    .header {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 10px;

      .title {
        font-weight: 600;
      }

      .config {
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 5px;
        background-color: var(--background-color-base);

        &:hover {
          cursor: pointer;
        }

        i {
          font-size: 20px;
        }
      }
    }

    .list {
      width: 100%;
      display: flex;
      align-items: center;
      background-color: var(--background-color-base);
      margin-bottom: 10px;
      padding: 10px 0;

      .left {
        width: 107px;
        border-right: 1px solid var(--border-color-base);
        color: var(--color-primary);
      }

      .right {
        flex: 1;
        text-align: justify;
        padding-left: 20px;

        li {
          margin-bottom: 4px;
        }
      }

      .red {
        color: var(--color-danger);
      }
    }

    .search-item {
      height: 48px;
      background-color: var(--background-color-base);
      display: flex;
      padding: 8px 0;

      .left-tit {
        width: 107px;
        line-height: 32px;
        border-right: 1px solid var(--border-color-base);

        i, span {
          color: var(--color-primary);
        }

        i {
          vertical-align: -4px;
          font-size: 24px;
          margin-right: 3px;
        }

        span {
          font-size: 12px;
        }
      }

      .right-int {
        width: 100%;

        /deep/ .el-input {
          width: calc(100% - 80px);

          input {
            border: none;
          }
        }

        p {
          height: 100%;
          line-height: 32px;
          text-align: left;
          padding-left: 20px;
          display: inline-block;
          vertical-align: middle;
          width: calc(100% - 80px);
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }
    }

  }

  .item {
    width: 100%;
    text-align: center;
    margin-top: 20px;
    // padding: 0 10px;

    .item__title {
      height: 50px;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 18px;
      font-weight: 800;
      margin-bottom: 5px;

      .title {
        margin: 0 3px;
        width: 120px;
      }

      .line {
        width: 80px;
        height: 1px;
        background-color: var(--border-color-base);
      }
    }

    .scope-label {
      display: inline-block;
      padding: 0 8px;
      border-radius: 5px;
      margin-right: 10px;
      margin-bottom: 3px;
      color: rgb(40, 128, 226);
      background-color: rgba(40, 128, 226, .2);
    }

    .item__content {
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
      width: 100%;
      height: 200px;
    }

    .card__title {
      text-align: left;
      margin-bottom: 5px;
    }

  }

  .item-attention {
    font-size: 14px;
    line-height: 30px;
    border-radius: 3px;
    text-align: left;
    padding: 5px 10px;
    background-color: var(--background-color-light);
  }

  .dialog {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    background-color: var(--background-color-light);
    padding: 15px;

    .tab {
      width: 46%;
      background-color: var(--background-color-base);
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 10px;
      margin-bottom: 10px;

      &:last-child {
        margin: 0;
      }
    }
  }
}

.bottom {
  height: 200px;
  background-color: var(--background-color-base);
}
</style>

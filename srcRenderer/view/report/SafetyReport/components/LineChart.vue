<template>
  <div class="chart" ref="line"></div>
</template>

<script>
import {mapState} from "vuex";

const pieColor = ["#5470c6", "#91cc75", "#fac858", '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc', '#99ff99', '#00ffff', '#5599ff', '#88aa00', '#ffb7dd', '#00aa88', '#880000', '#666666', '#ff0000', '#008080', '#dddddd', '#444444', '#ff00ff', '#800000', '#ffff77', '#e03154'];

export default {
  name: "LineChart",
  props: {
    chartData: {
      type: Object
    },
    filterValue: {
      type: Boolean,
      default: false
    },
    allValue: {
      type: Boolean,
      default: true
    }
  },
  computed: {
    ...mapState('main', ['theme']),
  },
  watch: {
    chartData: {
      deep: true,
      handler(newVal) {
        let value = this.filterValue ? newVal.data.filter(item => item.data.some(it => it) && (this.allValue ? item.name != '全部' : true)) : newVal.data
        this.option.title.text = newVal.title
        this.option.xAxis.data = newVal.xAxis
        this.option.series = value
        this.option.legend.data = value.map(item => item.name)

        if (newVal) {
          this.changeColor()
          this.chart.setOption(this.option);
        }
        this.chart.resize();

      }
    },
    'theme': {
      handler(val) {
        this.changeColor()
        this.chart.setOption(this.option, true)
      }
    }
  },
  data() {
    return {
      chart: null,
      option: {
        color: pieColor,
        title: {
          text: '',
          textStyle: {
            fontWeight: 'normal',
            fontSize: 15,
            color: '',
          },
          y: '0%'
        },
        legend: {
          data: [],
          left: '0%',
          y: '10%',
          textStyle: {
            color: ''
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: function (params) {
            var htmlStr = ''
            htmlStr += '<div>'
            params.forEach(item => {
              if (!item.value) {
                return ''
              }
              // if(params.length > 6 && item.seriesName != '全部' && !item.value)return
              htmlStr += '<div style="display: flex;align-items: center;justify-content: space-between">'
              htmlStr += '<div style="margin-right: 10px">' + item.marker + item.seriesName + '</div>'
              htmlStr += '<div>' + item.value + '</div>'
              htmlStr += '</div>'
            })
            htmlStr += '</div>';
            //没值的不显示div
            if (!params.filter(it => it.value).length) {
              return ''
            }
            return htmlStr
          }
        },
        grid: {
          left: '0',
          right: '5%',
          bottom: '3%',
          top: '25%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: [],
          axisLabel: {
            color: ''
          },
          boundaryGap: false
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            color: ''
          }
        },
        series: [
          {
            data: [],
            type: 'bar',
            showBackground: true,
            backgroundStyle: {
              color: 'rgba(180, 180, 180, 0.2)'
            }
          }
        ]
      },
      color: '#ccc'
    }
  },
  mounted() {
    this.chart = this.$echarts.init(this.$refs.line)

  },
  methods: {
    changeColor() {
      if (!this.theme) {
        this.color = localStorage.getItem('ponyTheme') === 'lightBlue' ? '#333333' : '#d3d7e0'
      } else {
        this.color = this.theme === 'lightBlue' ? '#333333' : '#d3d7e0'
      }
      this.option.title.textStyle.color = this.color
      this.option.xAxis.axisLabel.color = this.color
      this.option.yAxis.axisLabel.color = this.color
      this.option.legend.textStyle.color = this.color
    }
  }
}
</script>

<style scoped lang="scss">
.chart {
  width: 100%;
  height: 100%;

}
</style>

<template>
  <div class="chart" ref="line"></div>
</template>

<script>
import {mapState} from "vuex";

export default {
  name: "<PERSON><PERSON><PERSON>",
  props: {
    chartData: {
      type: Object
    },

  },
  computed: {
    ...mapState('main', ['theme']),
  },
  watch: {
    chartData: {
      deep: true,
      handler(newVal) {
        this.option.series[0].data = newVal.data
        if (newVal) {
          this.changeColor()
          this.chart.setOption(this.option);
        }
        this.chart.resize();

      }
    },
    'theme': {
      handler(val) {
        this.changeColor()
        this.chart.setOption(this.option, true)
      }
    }
  },
  data() {
    return {
      chart: null,
      color: '#ccc',
      option: {
        title: {
          text: '安全事件发生时段分布情况',
          textStyle: {
            fontWeight: 'normal',
            fontSize: 15,
            color: '',
          },
          y: '0%'
        },
        grid: {
          left: '0',
          right: '4%',
          bottom: '3%',
          top: '25%',
          containLabel: true
        },
        tooltip: {
          trigger: 'axis',
          // axisPointer: {
          //     type: 'shadow'
          // },
          // formatter: function (params) {
          //     var htmlStr = ''
          //     htmlStr += '<div>'
          //     params.forEach(item => {
          //         // if(params.length > 6 && item.seriesName != '全部' && !item.value)return
          //         htmlStr += '<div style="display: flex;align-items: center;justify-content: space-between">'
          //         htmlStr += '<div style="margin-right: 10px">' + item.marker + item.seriesName + '</div>'
          //         htmlStr += '<div>' + item.value + '</div>'
          //         htmlStr += '</div>'
          //     })
          //     htmlStr += '</div>';
          //     return htmlStr
          // }
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23],
          axisLabel: {
            color: ''
          }
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            color: ''
          }
        },
        series: [
          {
            data: [],
            type: 'line',
            areaStyle: {},
            smooth: true,
            itemStyle: {
              normal: {
                label: {
                  show: true,
                  // color: '#fff',
                  // position: "top",
                  // formatter: params => {
                  //     //用于渲染折点文字规律的方法
                  //     if (params.dataIndex % 2 != 0) {
                  //         return params.data;
                  //     } else {
                  //         return "";
                  //     }
                  // }

                },
                // itemStyle: {
                //     color: "rgb(250,147,101)",
                //     borderWidth: 3,

                //     // shadowColor: 'rgba(0, 0, 0, .3)',
                //     // shadowBlur: 0,
                //     // shadowOffsetY: 2,
                //     // shadowOffsetX: 2,
                // },
              }
            },
          }
        ]
      }
    }
  },
  mounted() {
    this.chart = this.$echarts.init(this.$refs.line)

  },
  methods: {
    changeColor() {
      if (!this.theme) {
        this.color = localStorage.getItem('ponyTheme') === 'lightBlue' ? '#333333' : '#d3d7e0'
      } else {
        this.color = this.theme === 'lightBlue' ? '#333333' : '#d3d7e0'
      }
      this.option.title.textStyle.color = this.color
      this.option.xAxis.axisLabel.color = this.color
      this.option.yAxis.axisLabel.color = this.color
      // this.option.legend.textStyle.color = this.color
    }
  }
}
</script>

<style scoped lang="scss">
.chart {
  width: 100%;
  height: 100%;

}
</style>

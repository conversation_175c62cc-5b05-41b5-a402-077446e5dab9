<template>
  <div class="chart" ref="pie"></div>
</template>

<script>
import {mapState} from "vuex";

const pieColor = ["#5470c6", "#91cc75", "#fac858", '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc', '#99ff99', '#00ffff', '#5599ff', '#88aa00', '#ffb7dd', '#00aa88', '#880000', '#666666', '#ff0000', '#008080', '#dddddd', '#444444', '#ff00ff', '#800000', '#ffff77', '#e03154'];

export default {
  name: "PieChart",
  props: {
    chartData: {
      type: Object
    },
    unit: {
      type: String
    }
  },
  computed: {
    ...mapState('main', ['theme']),
  },
  watch: {
    chartData: {
      deep: true,
      handler(newVal) {
        let valueData = newVal.data.filter(item => item.value && item.name != '全部')
        this.option.title.text = newVal.title
        this.option.series[0].data = valueData.length ? valueData : [{name: '无数据', value: 0}]
        if (newVal.legendType === 'right') {
          this.option.legend.orient = 'vertical'
          this.option.legend.left = '50%'
          this.option.legend.top = 'middle'
          this.option.series[0].center = ['20%', '50%']
        } else {
          this.option.legend.orient = 'horizontal'
          this.option.legend.right = '15%'
          this.option.legend.bottom = '0'
          this.option.legend.width = '255'
          this.option.series[0].center = ['50%', '40%']
        }
        let that = this
        this.option.legend.formatter = function (name) {
          let total = 0
          let tarValue
          for (let i = 0; i < valueData.length; i++) {
            total += valueData[i].value
            if (valueData[i].name === name) {
              tarValue = valueData[i].value
            }
          }
          let arr
          if (tarValue) {
            arr = [name, Math.round(((tarValue / total) * 100)).toFixed(0) + '%(' + tarValue + that.unit + ')']
          } else {
            arr = [name, '']
          }
          return arr.join(' ')
        }
        if (newVal) {
          this.changeColor()
          this.chart.setOption(this.option, true)
        }
        this.chart.resize();

      }
    },
    'theme': {
      handler(val) {
        this.changeColor()
        if (this.option.series[0].data.length) {
          this.chart.setOption(this.option, true)
        }
      }
    }
  },
  data() {
    return {
      chart: null,
      option: {
        color: pieColor,
        title: {
          text: '',
          left: 'left',
          textStyle: {
            fontWeight: 'normal',
            fontSize: 15,
            color: ''
          },
        },
        tooltip: {
          trigger: 'item',
          // formatter: "{b} : {c} ({d}%)"
        },
        legend: {
          textStyle: {
            color: ''
          },
          // formatter: function (name){
          //     console.log(name)
          //     var data = this.option.series[0].data;
          //     console.log(data)
          //     // var total = 0;
          //     // var tarValue;
          //     // for (var i = 0; i < data.length; i++){
          //     //     total += data[i].value;
          //     //     if (data[i].name == name){
          //     //         tarValue = data[i].value;
          //     //     }
          //     // }
          //     //
          //     // let arr = ['{a|'+name+'}', '{b|'+ Math.round(((tarValue / total) * 100)) +'%}']
          //
          //
          //     // return arr.join(' ')
          //     return ''
          // },
        },
        // label: {
        //     show: false
        // },
        series: [
          {
            type: 'pie',
            radius: '60%',
            data: [],
          }
        ]
      },
      color: '#ccc'
    }
  },
  mounted() {
    this.chart = this.$echarts.init(this.$refs.pie)
  },
  methods: {
    changeColor() {
      if (!this.theme) {
        this.color = localStorage.getItem('ponyTheme') === 'lightBlue' ? '#333333' : '#d3d7e0'
      } else {
        this.color = this.theme === 'lightBlue' ? '#333333' : '#d3d7e0'
      }
      this.option.title.textStyle.color = this.color
      this.option.legend.textStyle.color = this.color
    }
  }
}
</script>

<style scoped lang="scss">
.chart {
  width: 100%;
  height: 100%;
  margin: 15px 0;
}
</style>

<template>
  <div class="datalist">
    <div class="item__content__list" v-for="(item,index) in list" :key="index">
      <div class="color"></div>
      <div>{{ item.title }}</div>
      <div>{{ num(reportData[item.num]) }}</div>
      <div>上期 {{ num(reportData[item.lastNum]) }}</div>
      <div class="icon" :class="reportData[item.rateNum] > 0 ? 'up' : 'down'"
           v-if="reportData[item.rateNum]">
        <i class="pony-iconv2" :class="reportData[item.rateNum] > 0 ? 'pony-shangzhang' : 'pony-xiadie'"></i>
        {{ (reportData[item.rateNum] * 100).toFixed(0) + '%' }}
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "DataList",
  props: {
    reportData: {
      type: Object
    },
    list: {
      type: Array
    }
  },
  data() {
    return {
      num(num) {
        if (typeof num === 'number') {
          return Number(num).toFixed(0)
        }
        return num
      }
    }
  }
}
</script>

<style scoped lang="scss">
.datalist {
  display: flex;
  // justify-content: space-between;
  flex-wrap: wrap;
  width: 100%;
}

.item__content__list {
  height: 115px;
  width: calc(33.3% - 6.6px);
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  position: relative;
  letter-spacing: 2px;
  margin-bottom: 10px;
  margin-right: 10px;

  &:nth-child(n) {
    background-color: var(--color-safetyReport-content-item1);

    .color {
      background-color: var(--color-safetyReport-content-top1);
    }
  }

  &:nth-child(2n) {
    background-color: var(--color-safetyReport-content-item2);

    .color {
      background-color: var(--color-safetyReport-content-top2);
    }
  }

  &:nth-child(3n) {
    background-color: var(--color-safetyReport-content-item3);

    .color {
      background-color: var(--color-safetyReport-content-top3);
    }

    margin-right: 0px;

  }

  .color {
    width: 100%;
    height: 4px;
    position: absolute;
    top: 0;
    border-radius: 10px;
    padding: 0;
  }

  div {
    padding: 3px 20px;
    color: var(--color-safetyReport-top-color)
  }

  div:nth-child(3) {
    font-weight: 800;
    font-size: 24px;
    color: var(--color-text-primary);
  }

  &:last-child {
    margin-right: 0;
  }

  .icon {
    position: absolute;
    top: 50%;
    right: 10px;
    transform: translateY(-50%);
    letter-spacing: 0;
    font-weight: 700;
  }

  .up {
    color: rgb(230, 69, 69);
  }

  .down {
    color: rgb(55, 186, 75);
  }
}
</style>

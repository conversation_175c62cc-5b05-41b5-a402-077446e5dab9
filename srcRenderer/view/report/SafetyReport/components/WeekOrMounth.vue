<template>
  <div class="reportWeek" ref="page" v-loading="loading">
    <div class="top">
      <div class="card">
        <i class="pony-iconv2 pony-baobiao1"></i>
      </div>
      <div class="company">
        <div>{{ reportData.title1 }}</div>
        <div>{{ reportData.title2 }}</div>
      </div>
      <img src="../../../../../static/img/picture/earch.png" alt=""/>
      <el-button type="primary" class="btn" @click="exportPdf"
      >导出
      </el-button
      >
    </div>
    <div class="item">
      <div class="search">
        <div class="search-item">
          <div class="left-tit">
            <i class="pony-iconv2 pony-yishoucang"></i>
            <span>重点关注</span>
          </div>
          <div class="right-int" :style="isExport? 'margin-top:5px':''">
            <p title="" v-if="!hasPermission('safetyReport:comments')">{{ searchValue }}</p>
            <el-input v-model="searchValue" placeholder="请输入..." v-if="hasPermission('safetyReport:comments')"></el-input>
            <el-button type="primary" class="btn" @click="copy" v-if="!hasPermission('safetyReport:comments')">复制</el-button>
            <el-button type="primary" class="btn" @click="saveRemark" v-if="hasPermission('safetyReport:comments')">保存
            </el-button>
          </div>
        </div>
      </div>
    </div>
    <div class="item">
      <div class="item__title">
        <span class="line"></span>
        <span class="title">整体数据概览</span>
        <span class="line"></span>
      </div>
      <div class="item__content">
        <DataList :list="weekList" :report-data="reportData"></DataList>
      </div>
    </div>
    <div class="item item-attention">
      <p>高速/非高速：根据经纬度解析当前位置是否位于高速道路。</p>
      <p>日夜间：日间时段(06:00~22:00)，夜间时段(22:00~06:00)</p>
      <p>
        安全事件：通过合理的风险控制模型，结合车辆运行数据、驾驶员驾驶情况，识别需要及时干预的预警，分析得出安全事件。
      </p>
      <p>
        安全团队干预：安全团队结合报警视频、在线地图、实时视频等信息，对安全事件进行人工判断，及时干预。
      </p>
    </div>
    <div class="item">
      <div class="no-split">
        <div class="item__title">
          <span class="line"></span>
          <span class="title">干预数据</span>
          <span class="line"></span>
        </div>
        <el-row style="margin-top: 10px">
          <el-col :span="12" class="echarts__two">
            <div style="width: 100%;height: 100%;">
              <PieChart
                :filterValue="true"
                :chart-data="teamSafety"
                ref="teamSafetyChart"
                unit="次"
              ></PieChart>
            </div>
          </el-col>
          <el-col :span="12" class="echarts__two">
            <LineChart
              :allValue="false"
              :filterValue="true"
              :chart-data="interventionTrends"
              ref="interventionTrendsChart"
            ></LineChart>
          </el-col>
        </el-row>
      </div>
    </div>
    <div class="item item-attention">
      <p>TTS：安全团队编辑安全提示，下发至车载终端转换为语音输出。</p>
      <p>无责：安全团队通过各种辅助信息，判断该事件司机无责。</p>
      <p>分享：安全团队将该事件以链接形式分享。</p>
    </div>
    <div class="item">
      <div class="no-split">
        <div class="item__title">
          <span class="line"></span>
          <span class="title">风险数据</span>
          <span class="line"></span>
        </div>
        <el-row style="margin-bottom:25px">
          <el-col :span="longRow?24:12" :class="longRow?'echarts':'echarts__two'">
            <PieChart
              :filterValue="true"
              :chart-data="eventType"
              ref="eventTypeChart"
              unit="次"
            ></PieChart>
          </el-col>
          <el-col :span="12" class="echarts__two" v-show="!longRow">
            <LineChart
              :filterValue="true"
              :chart-data="eventTrends"
              ref="eventTrendsChart"
            ></LineChart>
          </el-col>
        </el-row>
      </div>
      <div v-show="longRow" :class="longRow?'no-split':''">
        <el-row style="margin-bottom:25px">
          <el-col :span="24" class="echarts">
            <LineChart
              :filterValue="true"
              :chart-data="eventTrends"
              ref="eventTrendsChart2"
            ></LineChart>
          </el-col>
        </el-row>
      </div>
      <div class="no-split">
        <el-row style="margin-bottom:25px">
          <el-col :span="24" class="echarts">
            <LineAreaChart
              :chart-data="eventTimes"
              ref="eventTimesChart"
            ></LineAreaChart>
          </el-col>
        </el-row>
      </div>
      <div class="no-split">
        <el-row>
          <el-col :span="24" class="echarts">
            <PieAreaChart
              :chart-data="eventDriver"
              ref="eventDriverChart"
              unit="次"
            ></PieAreaChart>
          </el-col>
        </el-row>
      </div>

    </div>
    <div class="item item-attention">
      <p>安全事件：本页面统计的安全事件为安全团队经过分析判断司机有责，并已进行相关干预的事件。</p>
      <p>安全事件时段分布：安全事件24小时趋势分布。</p>
    </div>

    <div class="item">
      <div class="item__title no-split">
        <span class="line"></span>
        <span class="title">监管合规数据</span>
        <span class="line"></span>
      </div>
      <div class="card__title">疲劳驾驶</div>
      <el-table
        ref="table1"
        class="el-table--ellipsis el-table--radius"
        border stripe highlight-current-row size="mini"
        :data="reportData.tiredList" :height="isExport?'':reportData.tiredList && reportData.tiredList.length > 5 ? 193:''"
        style="width: 100%;">
        <el-table-column
          type="index"
          label="序号"
          width="45">
        </el-table-column>
        <el-table-column
          prop="plateNo"
          label="车牌号"
          min-width="80" show-overflow-tooltip>
        </el-table-column>
        <el-table-column
          prop="num"
          label="本期疲劳次数">
        </el-table-column>
        <el-table-column
          prop="numLast"
          label="上期疲劳次数">
        </el-table-column>
      </el-table>
      <div class="card__title">超速报警</div>
      <el-table
        ref="table2"
        class="el-table--ellipsis el-table--radius"
        border stripe highlight-current-row size="mini"
        :data="reportData.speedList" :height="isExport?'':reportData.speedList && reportData.speedList.length > 5 ? 193:''"
        style="width: 100%;">
        <el-table-column
          type="index"
          label="序号"
          width="45">
        </el-table-column>
        <el-table-column
          prop="plateNo"
          label="车牌号"
          min-width="80" show-overflow-tooltip>
        </el-table-column>
        <el-table-column
          prop="num"
          label="本期超速次数">
        </el-table-column>
        <el-table-column
          prop="numLast"
          label="上期超速次数">
        </el-table-column>
      </el-table>
    </div>
    <div class="item item-attention">
      <p>疲劳驾驶：根据车辆行驶轨迹，按照连续驾驶超过4小时，连续停驶不超过20分钟的规则计算得出。</p>
      <p>超速报警：高速道路限速值 85km/h 非高速道路限速值 65km/h。</p>
    </div>
    <div class="item">
      <div class="item__title no-split">
        <span class="line"></span>
        <span class="title">设备及车辆健康度</span>
        <span class="line"></span>
      </div>
      <el-table
        ref="table3"
        class="el-table--radius"
        border
        stripe
        size="mini"
        :data="reportData.deviceList"
        :height="isExport?'':reportData.deviceList && reportData.deviceList.length > 5 ? 208:''"
        style="width: 100%;"
      >
        <el-table-column type="index" label="序号" width="45">
        </el-table-column>
        <el-table-column prop="plateNo" label="车牌号" show-overflow-tooltip width="80">
        </el-table-column>
        <el-table-column
          prop="status"
          label="设备维修状态">
        </el-table-column>
        <el-table-column
          label="设备故障类型" width="250" align="left">
          <template slot-scope="{row}">
            <span class="scope-label" v-for="(item,index) in row.labels" :key="item"
                  :style="{'color':colorList[(index+1)%4],'background-color':'rgba'+rgbList[(index+1)%4]}">{{ labelsList[item - 1] }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="date" label="预计完成时间" width="135"></el-table-column>
        <el-table-column prop="desc" label="原车故障" width="277" align="left"></el-table-column>

      </el-table>
    </div>
    <div class="item item-attention">
      <p>
        上{{ unit }}已完成修复<span>{{ (reportData.deviceLastRepaired || 0) }}</span>台 正在检修中
        <span>{{ (reportData.deviceLastRepairing || 0) }}</span>台
      </p>
    </div>
    <div class="item">
      <div class="item__title no-split">
        <span class="line"></span>
        <span class="title">车辆排名</span>
        <span class="line"></span>
      </div>
      <el-table
        ref="table4"
        class="el-table--radius"
        border
        stripe
        size="mini"
        :height="isExport?'':reportData.interferedTop5 && reportData.interferedTop5.length > 5 ? 208:''"
        :data="reportData.interferedTop5"
        style="width: 100%;"
      >
        <el-table-column type="index" label="排名" width="45"></el-table-column>
        <el-table-column prop="plateNo" label="车牌号" width="80">
        </el-table-column>
        <el-table-column prop="driver" label="驾驶员" width="80">
        </el-table-column>
        <el-table-column prop="mile" label="里程km" width="63">
        </el-table-column>
        <el-table-column prop="num" label="本期干预次数" width="58">
        </el-table-column>
        <el-table-column prop="numLast" label="上期干预次数" width="58">
        </el-table-column>
        <el-table-column
          label="标签" min-width="180" align="left">
          <template slot-scope="{row}">
            <span class="scope-label" v-for="(item,index) in row.labels" :key="item"
                  :style="{'color':colorList[(index+1)%4],'background-color':'rgba'+rgbList[(index+1)%4]}">{{ eventObj[item].name }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="item item-attention">
      <p>上述车辆为本期被干预次数较多车辆</p>
      <p>
        请根据标签内容对上述司机进行安全指导、学习，督促其改善驾驶行为。
      </p>
    </div>
    <div class="item no-split" v-if="(reportData.shareList && reportData.shareList.length)">
      <div class="item__title">
        <span class="line"></span>
        <span class="title">案例分享</span>
        <span class="line"></span>
      </div>
      <ul class="case-share" v-for="(data,ind) in shareListNum" :key="'ul'+ind">
        <li v-for="(item,index) in reportData.shareList.slice(ind*5,ind*5+5)" :key="index">
          <div class="code" :id="'code'+index">
            <img :id="'img' + index"/>
          </div>
          <p>{{ item.field || '' }}</p>
        </li>
      </ul>
    </div>
    <!-- <div class="bottom"></div> -->
  </div>
</template>

<script>
import PieChart from "./PieChart";
import PieAreaChart from "./PieAreaChart";
import ColumnChart from "./ColumnChart";
import LineChart from "./LineChart";
import LineAreaChart from "./LineAreaChart";
import DataList from "./DataList";
import html2canvas from "html2canvas";
import QRCode from "qrcode";
import jsPDF from "jspdf";

const vehicleRiskLevel = [
  "急转弯",
  "急刹车",
  "急加速",
  "空挡滑行",
  "安全带未系",
  "疲劳驾驶",
  "过长怠速",
  "超转行驶",
  "停车状态踩踏油门",
  "长时间离合",
  "粘离合",
  "带手刹行驶",
  "高档低速",
  "大油门行驶",
  "停车立即熄火",
  "冷车行驶",
  "长时间刹车",
];

const interferedTrendListLevel = [
  "全部",
  "TTS",
  "电话",
  "短信",
  "无责",
  "分享",
];
const interferedTrendListPie = ["全部", "TTS", "电话", "短信", "无责", "分享"];

const pieColor = ["#5470c6", "#91cc75", "#fac858", '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc', '#99ff99', '#00ffff', '#5599ff', '#88aa00', '#ffb7dd', '#00aa88', '#880000', '#666666', '#ff0000', '#008080', '#dddddd', '#444444', '#ff00ff', '#800000', '#ffff77', '#e03154'];

export default {
  name: "Day",
  components: {
    PieChart,
    ColumnChart,
    LineChart,
    DataList,
    LineAreaChart,
    PieAreaChart
  },
  props: {
    eventObj: {
      type: Object
    },
    unit: {
      type: String,
      default: '周'
    },
    getWidth: {
      type: Number,
      default: 0
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    shareListNum() {
      return this.reportData.shareList ? Math.ceil(this.reportData.shareList.length / 5) : 0
    }
  },
  data() {
    return {
      isExport: false,
      searchValue: "",
      longRow: false,
      // 时间
      date: "",
      // 所有数据
      reportData: {},
      colorList: ['#ff6666', '#ff8866', '#2b80e0', '#7f70d1',],
      rgbList: ['(255,102,102,.1)', '(255,136,102,.1)', '(43,128,224,.1)', '(127,112,209,.1)'],
      labelsList: ['相机故障', '定位异常', '存储异常'],
      eventType: {
        title: "事件类型占比",
        data: [],
        legendType: "right",
      },
      teamSafety: {
        title: "干预类型占比",
        data: [],
        legendType: "right",
      },

      interventionTrends: {
        title: "干预数据趋势",
        xAxis: [],
        data: [],
      },
      eventTrends: {
        title: "安全事件趋势",
        xAxis: [],
        data: [],
      },
      eventTimes: {
        title: "安全事件发生时段分布情况",
        data: [],
        legendType: "right",
      },
      eventDriver: {
        title: "原车驾驶行驶事件分布",
        legendType: "right",
        data: [],
      },
      weekList: [
        {
          title: "总车辆数/上线率",
          num: "vehicleNum",
          lastNum: "vehicleNumLast",
          rateNum: "vehicleNumRate",
        },
        {
          title: "总行驶里程(km)",
          num: "mileTotal",
          lastNum: "mileTotalLast",
          rateNum: "mileTotalRate",
        },
        {
          title: "总行驶时长(h)",
          num: "timeTotal",
          lastNum: "timeTotalLast",
          rateNum: "timeTotalRate",
        },
        {
          title: "高速里程(km)",
          num: "mileExpressway",
          lastNum: "mileExpresswayLast",
          rateNum: "mileExpresswayRate",
        },
        {
          title: "非高速里程(km)",
          num: "mileUnExpressway",
          lastNum: "mileUnExpresswayLast",
          rateNum: "mileUnExpresswayRate",
        },
        {
          title: "日间里程(km)",
          num: "mileDay",
          lastNum: "mileDayLast",
          rateNum: "mileDayRate",
        },
        {
          title: "夜间里程(km)",
          num: "mileNight",
          lastNum: "mileNightLast",
          rateNum: "mileNightRate",
        },
        {
          title: "安全事件次数",
          num: "eventNum",
          lastNum: "eventNumLast",
          rateNum: "eventNumRate",
        },
        {
          title: "安全团队干预次数",
          num: "interferedNum",
          lastNum: "interferedNumLast",
          rateNum: "interferedNumRate",
        },
      ],
    };
  },
  methods: {

    copy() {
      const target = document.createElement('div');
      target.id = 'tempTarget';
      target.style.opacity = '0';
      target.innerText = this.searchValue;
      document.body.appendChild(target);

      try {
        let range = document.createRange();
        range.selectNode(target);
        window.getSelection().removeAllRanges();
        window.getSelection().addRange(range);
        document.execCommand('copy');
        window.getSelection().removeAllRanges();
        this.$info('复制成功')
      } catch (e) {
      }
      target.parentElement.removeChild(target);
    },
    saveRemark() {
      if (!this.searchValue) {
        this.$warning('请填写评语!')
        return
      }
      this.$emit('saveRemark', this.searchValue)

    },
    generate(query, data) {
      this.clearData();
      this.date = query.start;
      this.reportData = data;
      this.searchValue = data.comment
      // // 车辆风险等级占比
      this.eventDriver.data = this.reportData.originalEventList ? this.reportData.originalEventList.map(
        (item, index) => {
          return {
            value: item,
            name: vehicleRiskLevel[index],
            // itemStyle: {
            //     color: pieColor[index],
            // },

          };
        }
      ) : [];
      // 事件类型占比
      this.eventType.data = this.reportData.eventTypeList.map(
        (item, index) => {
          return {
            value: item,
            name: index ? this.eventObj[String(index)] ? this.eventObj[String(index)].name : '' : '全部',
          };
        }
      );
      if (this.eventType.data.filter(item => item.value && item.name != '全部').length > 10) {
        this.longRow = true
      }
      // if(this.reportData.interferedTrendList.map(
      //             (item) => item.value[index]
      //         ).filter(item=>item.data.some(it=>it) && (this.allValue ? item.name != '全部':true))){

      // }
      // 安全团队干预类型占比
      this.teamSafety.data = interferedTrendListPie.map((item, index) => {
        return {
          name: item,
          value: this.reportData.interferedTrendList.reduce(
            (sum, obj) => (sum += obj.value[index]),
            0
          ),
        };
      });
      let interventionTrends = []
      interferedTrendListLevel.forEach((item, index) => {

        interventionTrends.push(this.reportData.interferedTrendList.map(
          (it) => it.value[index]
        ));
      });
      // 干预数据趋势
      interferedTrendListLevel.forEach((item, index) => {
        this.interventionTrends.data.push({
          name: item,
          type: "line",
          smooth: true,
          data: interventionTrends[index],
          itemStyle: {
            color: item == '全部' ? '#3ba272' : '',
          },
        });
      });


      this.interventionTrends.xAxis =
        this.reportData.interferedTrendList.map((item) => item.field);
      // 安全事件发生时段分布情况
      //这个单独的直接把数据给了(只给了数据)
      this.eventTimes.data = this.reportData.eventHourList;
      // 安全事件趋势
      let eventTrendList = []
      this.reportData.eventTrendList[0].value.forEach((item, index) => {

        eventTrendList.push(this.reportData.eventTrendList.map(
          (it) => it.value[index]
        ));
      });
      if (eventTrendList.filter((item, index) => item.some(it => it) && index).length > 6) {
        this.longRow = true
      }
      this.reportData.eventTrendList[0].value.forEach((item, index) => {
        this.eventTrends.data.push({
          name: index ? this.eventObj[String(index)] ? this.eventObj[String(index)].name : '' : '全部',
          type: "line",
          smooth: true,
          data: eventTrendList[index]
        });
      })

      this.eventTrends.xAxis = this.reportData.eventTrendList.map(
        (item) => item.field
      );

      this.$nextTick(() => {
        var opts = {
          errorCorrectionLevel: 'H',
          type: 'image/jpeg',
          quality: 0.3,
          margin: 1,
        }

        this.reportData.shareList && this.reportData.shareList.forEach((item, index) => {
          let dom = "img" + index
          QRCode.toDataURL(item.value, opts, function (err, url) {
            if (err) throw err
            let qrcodeDom = document.querySelector(`#${dom}`)
            qrcodeDom.src = url
          })
        })

        this.$refs.table1.doLayout();
        this.$refs.table2.doLayout();
        this.$refs.table3.doLayout();
        this.$refs.table4.doLayout();
      });
    },

    clearData() {
      this.reportData = {};
      this.eventDriver.data = [];
      this.eventTimes.data = []
      this.eventType.data = [];
      this.teamSafety.data = [];
      this.interventionTrends.data = [];
      this.interventionTrends.xAxis = [];
      this.eventTrends.data = [];
      this.eventTrends.xAxis = [];
      this.$refs.eventTypeChart.chart.clear();
      this.$refs.eventDriverChart.chart.clear();
      this.longRow = false
      this.$refs.eventTrendsChart.chart.clear();
      this.$refs.eventTrendsChart2.chart.clear();

      this.$refs.teamSafetyChart.chart.clear();
      this.$refs.interventionTrendsChart.chart.clear();
    },

    async exportPdf() {
      this.isExport = true
      this.$parent.loading = true
      await this.$nextTick(() => {
        this.$refs.table1.doLayout();
        this.$refs.table2.doLayout();
        this.$refs.table3.doLayout();
        this.$refs.table4.doLayout();
      })
      let titleDate = this.reportData.title2 ? this.reportData.title2.split('：') : ''
      let title = this.reportData.title1 + (titleDate ? `(${titleDate[1]})` : '');
      let targetDom = document.querySelector('.reportWeek');
      window.pageYoffset = 0;
      document.documentElement.scrollTop = 0;
      document.body.scrollTop = 0;
      let a4HeightRef = Math.floor(targetDom.offsetWidth / 595.28 * 841.89);
      let pages = Math.ceil(targetDom.offsetHeight / a4HeightRef)
      let widthRate = 2
      //scale是2的话,只能导6页,因为文件太大,获取不到内容了,不知道这个插件咋限制的,所以大于6页,scale写了各缩小比例
      if (pages > 6) {
        widthRate = (2 / (pages / 6)).toFixed(3)
      }
      html2canvas(targetDom, {
        allowTaint: true,
        scale: widthRate, // 提升画面质量，但是会增加文件大小
        height: targetDom.scrollHeight, //canvas高
        width: targetDom.scrollWidth, //canvas宽,
        background: "#fff",
      }).then(async (canvas) => {
        let leftHeight = canvas.height;
        let position = 0
        let a4Width = 595.28
        let a4Height = 841.89

        let a4HeightRef = Math.floor(canvas.width / a4Width * a4Height);
        let pageData = canvas.toDataURL('image/jpeg', 1.0)
        let pdf = new jsPDF('x', 'pt', 'a4')
        let index = 0,
          canvas1 = document.createElement('canvas'),
          height;
        pdf.setDisplayMode('fullwidth', 'continuous', 'FullScreen')

        function isSplit(nodes, index, pageHeight) {
          // 计算当前这块dom是否跨越了a4大小，以此分割
          // if (nodes[index].offsetTop + nodes[index].offsetHeight < pageHeight && nodes[index + 1] && nodes[index + 1].offsetTop + nodes[index + 1].offsetHeight > pageHeight) {
          //     return true;
          // // }
          // console.log(nodes[index],'nodes[index]',index);
          // console.log(pageHeight,'pageHeight');
          // console.log(a4HeightRef,'a4HeightRef');

          if (nodes[index].offsetTop * widthRate + nodes[index].offsetHeight * widthRate > pageHeight && nodes[index].offsetTop * widthRate < pageHeight) {
            // console.log(true);
            return true;
          }
          // console.log(false);
          return false;
        }

        function createImpl(canvas) {
          if (leftHeight > 0) {
            index++;
            let checkCount = 0;

            if (leftHeight > a4HeightRef) {
              let i = position + a4HeightRef;
              let eChartsList = document.getElementsByClassName('no-split')
              // console.log(eChartsList,'eChartsList');
              for (let j = 0; j < eChartsList.length; j++) {
                let multiple = Math.ceil((eChartsList[j].offsetTop + eChartsList[j].offsetHeight) * widthRate / a4HeightRef);
                // console.log(eChartsList[j],'eChartsList[j]',j);
                // console.log(eChartsList[j].offsetTop,'eChartsList[j].offsetTop');
                // console.log(position,'position');
                // console.log(i,'i');

                if (position <= eChartsList[j].offsetTop * widthRate && i > eChartsList[j].offsetTop * widthRate && isSplit(eChartsList, j, i)) {
                  if (index >= multiple - 1) {
                    for (i = (eChartsList[j].offsetTop + eChartsList[j].offsetHeight) * widthRate; i >= eChartsList[j].offsetTop * widthRate; i--) {
                      let isWrite = true
                      for (let j = 0; j < canvas.width; j++) {
                        let c = canvas.getContext('2d').getImageData(j, i, 1, 1).data
                        if (c[0] != 0xff || c[1] != 0xff || c[2] != 0xff) {
                          isWrite = false
                          break
                        }
                      }
                      if (isWrite) {
                        checkCount++
                        if (checkCount >= 10) {
                          break
                        }
                      } else {
                        checkCount = 0
                      }
                    }
                  }
                }


              }
              height = Math.round(i - position) || Math.min(leftHeight, a4HeightRef);
              if (height <= 0) {
                height = a4HeightRef;
              }
            } else {
              height = leftHeight;
            }
            canvas1.width = canvas.width;
            canvas1.height = height;
            let ctx = canvas1.getContext('2d');
            ctx.drawImage(canvas, 0, position, canvas.width, height, 0, 0, canvas.width, height);
            let pageHeight = Math.round(a4Width / canvas.width * height);
            if (index != 1) {
              pdf.addPage();
            }
            pdf.addImage(canvas1.toDataURL('image/jpeg', 1.0), 'JPEG', 0, 0, a4Width, a4Width / canvas1.width * height)
            leftHeight -= height;
            position += height
            $('.pdfProgress').text(index + 1);
            $('.pdfTotal').text(index + Math.ceil(leftHeight / a4HeightRef))
            if (leftHeight > 0) {
              createImpl(canvas)
              // setTimeout(createImpl, 10, canvas);
            } else {
              pdf.save(title + '.pdf')
            }
          }
        }

        //当内容未超过pdf一页显示的范围，无需分页
        if (leftHeight < a4HeightRef) {
          pdf.addImage(pageData, 'JPEG', 0, 0, a4Width, a4Width / canvas.width * leftHeight);
          pdf.save(title + '.pdf')
        } else {
          createImpl(canvas)
          // await setTimeout(createImpl, 10, canvas);
        }
      })
      setTimeout(() => {
        this.isExport = false
        this.$parent.loading = false
      }, 2000)
      // this.isExport = false
      // this.$parent.loading = false
    },
  },
};
</script>

<style scoped lang="scss">
.reportWeek {
  padding: 20px 35px;
  width: 960px;
  background-color: var(--background-color-base);

  .top {
    display: flex;
    align-items: center;
    position: relative;
    height: 110px;
    padding: 0 30px;
    background-color: var(--color-safetyReport-top-bg);

    img {
      position: absolute;
      right: 0;
      bottom: 0;
      height: 100%;
    }

    .card {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 55px;
      height: 55px;
      border-radius: 5px;
      margin-right: 20px;
      background: linear-gradient(to top, #609ee4, #2c81e0);

      i {
        font-size: 35px;
        color: #fff;
      }
    }

    .company {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      height: 55px;

      div {
        &:nth-child(1) {
          font-weight: 800;
          font-size: 20px;
        }
      }
    }

    .btn {
      position: absolute;
      right: 13%;
      bottom: 30px;
    }
  }

  .search {
    background-color: var(--color-safetyReport-top-bg);
    height: 75px;
    border-radius: 5px;
    padding: 14px 30px;

    .search-item {
      height: 48px;
      background-color: var(--background-color-base);
      display: flex;
      padding: 8px 0;

      .left-tit {
        width: 107px;
        line-height: 32px;
        border-right: 1px solid var(--border-color-base);

        i,
        span {
          color: var(--color-primary);
        }

        i {
          vertical-align: -4px;
          font-size: 24px;
          margin-right: 3px;
        }

        span {
          font-size: 12px;
        }
      }

      .right-int {
        flex: 1;

        /deep/ .el-input {
          width: calc(100% - 80px);

          input {
            border: none;
          }
        }

        p {
          height: 100%;
          display: inline-block;
          vertical-align: middle;
          width: calc(100% - 80px);
          line-height: 32px;
          text-align: left;
          padding-left: 20px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }
    }
  }

  .no-split {
    background-color: var(--bacground-chart-color);
  }

  .item {
    width: 100%;
    text-align: center;
    margin-top: 20px;

    .item__title {
      height: 50px;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 18px;
      font-weight: 800;
      margin-bottom: 5px;

      .title {
        margin: 0 20px;
      }

      .line {
        width: 80px;
        height: 1px;
        background-color: var(--border-color-base);
      }
    }

    .scope-label {
      display: inline-block;
      padding: 0px 8px;
      border-radius: 5px;
      margin-right: 10px;
      margin-bottom: 3px;
      color: rgb(40, 128, 226);
      background-color: rgba(40, 128, 226, .2);
    }

    .item__content {
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
      width: 100%;
      // height: 200px;
    }

    .echarts {
      height: 300px;
    }

    .echarts__two {
      height: 300px;
      display: flex;
    }

    .card__title {
      text-align: left;
      margin: 5px 0;
    }

    .card__content {
      width: 100%;
      height: 100%;
      display: flex;
      flex-wrap: wrap;
      text-align: left;

      .card {
        width: 33.3%;
        display: flex;
        align-items: center;

        .left {
          width: 55px;
          height: 55px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 10px;
          margin-right: 20px;

          i {
            font-size: 30px;
          }
        }

        .right {
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          height: 55px;

          span {
            &:nth-child(2) {
              font-weight: 800;
              font-size: 29px;
            }
          }
        }
      }
    }

    .red {
      background-color: var(--color-safetyReport-red);

      i {
        color: var(--color-safetyReport-red-icon);
      }
    }

    .yellow {
      background-color: var(--color-safetyReport-yellow);

      i {
        color: var(--color-safetyReport-yellow-icon);
      }
    }

    .green {
      background-color: var(--color-safetyReport-green);

      i {
        color: var(--color-safetyReport-green-icon);
      }
    }
  }

  .item-attention {
    font-size: 14px;
    line-height: 35px;
    border-radius: 3px;
    text-align: left;
    padding: 8px 20px;
    background-color: var(--background-color-light);

    p {
      line-height: 26px;

      span {
        color: var(--color-text-regular);
        font-size: 16px;
        font-weight: 600;
        padding: 0 5px;
      }
    }
  }

  .case-share {
    width: 100%;
    display: grid;
    grid-template-columns: 165px 165px 165px 165px 165px; //后面跟几个数据就是一行排几个
    justify-content: space-between;
    // flex-wrap: wrap;    //父元素加一个换行
    li {
      // float: left;
      width: 165px;
      // margin-right: 30px;
      .code {
        width: 100%;
        height: 165px;

        img {
          width: 100%;
          height: 100%;
        }
      }

      p {
        text-align: center;
        margin: 10px 0;
      }

      &:nth-child(5n) {
        margin-right: 0px;
      }
    }

  }
}

.rankingCard {
  padding: 0 10px;
  display: block;
  margin: 0 auto;
  width: 80px;
  border-radius: 5px;
  font-size: 12px;
  color: #fff;
}

.high-level {
  background-color: var(--color-danger);
}

.middle-level {
  background-color: var(--color-warning);
}

.low-level {
  background-color: var(--color-primary);
}

.bottom {
  height: 80px;
  background-color: var(--background-color-base);
}
</style>

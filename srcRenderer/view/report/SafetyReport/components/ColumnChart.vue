<template>
  <div class="chart" ref="line"></div>
</template>

<script>
import {mapState} from "vuex";

export default {
  name: "Column<PERSON><PERSON>",
  props: {
    chartData: {
      type: Object
    }
  },
  computed: {
    ...mapState('main', ['theme']),
  },
  watch: {
    chartData: {
      deep: true,
      handler(newVal) {
        this.option.title.text = newVal.title
        let valueData = []
        newVal.data.forEach((item, index) => {
          if (item) {
            valueData.push({
              type: index,
              value: item
            })
          }
        })
        this.option.series[0].data = valueData.map(item => item.value)
        this.option.xAxis.data = valueData.map(item => newVal.xAxis[item.type])

        if (newVal) {
          this.changeColor()
          this.chart.setOption(this.option)
        }
      }
    },
    'theme': {
      handler(val) {
        this.changeColor()
        this.chart.setOption(this.option, true)
      }
    }
  },
  data() {
    return {
      chart: null,
      option: {
        title: {
          text: '',
          textStyle: {
            fontWeight: 'normal',
            fontSize: 15,
            color: ''
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: function (params) {
            var htmlStr = ''
            htmlStr += '<div>'
            params.forEach(item => {
              htmlStr += '<div style="display: flex;align-items: center;justify-content: space-between">'
              htmlStr += '<div style="margin-right: 10px">' + item.marker + item.axisValueLabel + '</div>'
              htmlStr += '<div>' + item.value + '</div>'
              htmlStr += '</div>'
            })
            htmlStr += '</div>';
            return htmlStr
          }
        },
        grid: {
          left: '0',
          right: '4%',
          bottom: '3%',
          top: '13%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: [],
          axisLabel: {
            interval: 0,
            rotate: 40,
            color: ''
          }
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            color: ''
          }
        },
        series: [
          {
            data: [],
            type: 'bar',
            showBackground: true,
            backgroundStyle: {
              color: 'rgba(180, 180, 180, 0.2)'
            },
            itemStyle: {
              normal: {
                color: '#2a80e0'
              }
            },
          }
        ]
      },
      color: '#ccc'
    }
  },
  mounted() {
    this.chart = this.$echarts.init(this.$refs.line)
  },
  methods: {
    changeColor() {
      if (!this.theme) {
        this.color = localStorage.getItem('ponyTheme') === 'lightBlue' ? '#333333' : '#d3d7e0'
      } else {
        this.color = this.theme === 'lightBlue' ? '#333333' : '#d3d7e0'
      }
      this.option.title.textStyle.color = this.color
      this.option.xAxis.axisLabel.color = this.color
      this.option.yAxis.axisLabel.color = this.color
    }
  }
}
</script>

<style scoped lang="scss">
.chart {
  width: 100%;
  height: 100%;
}
</style>

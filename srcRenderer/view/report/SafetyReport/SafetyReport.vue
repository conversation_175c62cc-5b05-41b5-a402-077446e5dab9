<template>
  <Layout tag="div" :asideWidth="290" :contentLoading="loading" class="safetyReport">
    <template slot="aside">
      <div class="query-top bg bg--light">
        <ElementTree type="department" ref="tree" @node-click="nodeClick"></ElementTree>
      </div>

      <div class="query-bottom bg bg--light">
        <div class="query-item">
          <el-select v-model="type" placeholder="请选择">
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </div>
        <div class="query-item">
          <el-date-picker
            v-show="type === 0"
            v-model="day"
            :picker-options="startDatePickerOptions"
            type="date" value-format="yyyy-MM-dd"
            :clearable="false">
          </el-date-picker>
          <el-date-picker
            v-show="type === 1"
            v-model="week"
            type="week"
            :picker-options="startweekPickerOptions"
            format="yyyy 第 WW 周"
            placeholder="选择周">
          </el-date-picker>
          <el-date-picker
            v-show="type === 2"
            v-model="month"
            type="month"
            :picker-options="startDatePickerOptions"
            placeholder="选择月">
          </el-date-picker>
        </div>
        <div class="query-item">
          <el-button size="mini" type="primary" style="width: 100%;" @click="search">
            查询
          </el-button>
        </div>
      </div>
    </template>
    <template slot="content">
      <div class="content-warp">
        <Day ref="day" v-show="!type" @saveRemark="saveRemark" :eventObj="eventObj"></Day>
        <WeekOrMounth v-show="type" ref="weekOrMounth" :type="type" @saveRemark="saveRemark" :eventObj="eventObj"
                      :unit="type == 1 ? '周' : '月' " :loading=loading></WeekOrMounth>
      </div>
    </template>
  </Layout>
</template>

<script>
import moment from "moment";
import Day from './components/Day'
import WeekOrMounth from './components/WeekOrMounth'


export default {
  name: "safetyReport",
  components: {
    Day,
    WeekOrMounth
  },
  data() {
    return {
      loading: false,
      eventObj: {},
      type: 0,
      deptId: '',
      week: moment().format('YYYY-MM-DD'),
      day: moment().subtract(1, 'days').format('YYYY-MM-DD'),
      month: moment().format('YYYY-MM-DD'),
      options: [{
        value: 0,
        label: '日报'
      }, {
        value: 1,
        label: '周报'
      }, {
        value: 2,
        label: '月报'
      }],
      //记录滚动条位置
      scrollRecordObj: {
        scrollTop: 0,
        scrollLeft: 0,
      }
    }
  },
  watch: {
    'type': function (val) {
      if (val) {
        this.$refs.weekOrMounth.clearData()
      } else {
        this.$refs.day.clearData()
      }
    }
  },
  computed: {
    // 开始时间限制，禁用今天之后的选项
    startDatePickerOptions: function () {
      return {
        disabledDate: (date) => {
          return (moment().endOf('day').toDate() - date) < 0
        },
        firstDayOfWeek: 1
      }
    },
    startweekPickerOptions: function () {
      return {
        disabledDate: (date) => {
          return (moment().endOf('week').toDate() - date) < 0
        },
        firstDayOfWeek: 1
      }
    },
  },
  methods: {
    async getAlarmName() {
      let result = await this.$api.getSysDictByCode({code: 'danger_event_type'})
      result.forEach(item => {
        this.eventObj[Number(item.value)] = item
      })
    },
    // 单选树节点选择
    nodeClick(data, node) {
      if (data.type !== 3 && data.type !== 2) {
        return this.$warning('请选择车队或企业！')
      }
      this.deptId = data.id
    },
    async saveRemark(value) {
      if (!this.deptId) {
        return this.$warning('请先选择车队或企业！')
      }
      let data = {
        comment: value,
        deptId: this.deptId,
      }
      if (this.type === 0) {
        data.start = this.day
        data.end = this.day
      } else if (this.type === 1) {
        data.start = moment(this.week).weekday(0).format("YYYY-MM-DD")
        data.end = moment(this.week).weekday(6).format("YYYY-MM-DD")
      } else if (this.type === 2) {
        data.start = moment(this.month).startOf('month').format("YYYY-MM-DD")
        data.end = moment(this.month).endOf('month').format("YYYY-MM-DD")
      }
      let result = await this.$api.vcareComment(data)
      if (!result || result.status != 200) {
        this.$warning(result.message || '保存失败!')
        return
      }
      this.$success('保存成功!')

    },
    async search() {
      if (!this.deptId) {
        return this.$warning('请先选择车队或企业！')
      }
      this.loading = true
      let data = {
        version: 2,
        deptId: this.deptId,
      }
      if (this.type === 0) {
        data.start = this.day
        data.end = this.day
        this.searchSafety()
      } else if (this.type === 1) {
        data.start = moment(this.week).weekday(0).format("YYYY-MM-DD")
        data.end = moment(this.week).weekday(6).format("YYYY-MM-DD")
      } else if (this.type === 2) {
        data.start = moment(this.month).startOf('month').format("YYYY-MM-DD")
        data.end = moment(this.month).endOf('month').format("YYYY-MM-DD")
      }
      const res = await this.$api.safetyReport(data)
      this.loading = false
      if (res.status !== 200) {
        return this.$error('查询错误！')
      }
      if (this.type) {
        this.$refs.weekOrMounth.generate(data, res.data)
      } else {
        this.$refs.day.generate(data, res.data)
      }
    },
    searchSafety() {
      this.$refs.day.safetyDay(this.deptId,this.day)
    }
  },
  mounted() {
    this.getAlarmName()
    this.$refs.tableWrapLaylot.addEventListener('scroll', (e) => {
      this.scrollRecordObj.scrollTop = e.target.scrollTop
      this.scrollRecordObj.scrollLeft = e.target.scrollLeft
    })
  },
  activated(){
      this.$refs.tableWrapLaylot.scrollTop = this.scrollRecordObj.scrollTop
      this.$refs.tableWrapLaylot.scrollLeft = this.scrollRecordObj.scrollLeft
  },
}
</script>

<style scoped lang="scss">
.safetyReport {
  .query-top {
    height: calc(100% - 145px);
  }

  .query-bottom {
    margin-top: 5px;
    padding: 10px;

    .query-item {
      display: flex;
      height: 40px;
      line-height: 40px;
      justify-content: space-between;
      align-items: center;

      > div {
        flex-grow: 1;
      }

      > span {
        font-size: 12px;
        white-space: nowrap;
        margin-right: 5px;
      }
    }
  }

  .content-warp {
    // padding-top: 20px;
    width: 960px;
    margin: auto;
  }
}
</style>

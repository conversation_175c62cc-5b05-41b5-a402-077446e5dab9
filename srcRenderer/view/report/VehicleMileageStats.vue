<template>
    <Layout class="vehicle-mileage-stats" v-loading="loading">
        <template slot="query">
            <SelectTreeInput v-model="query.vehicle" :condition="condition"
                             type="vehicle" style="width:120px"
                             placeholder="请选择车辆" title="请选择车辆"
            ></SelectTreeInput>
            <StartEndTime itemHeight="35" style="margin-left: 10px"
                          v-model="selectStartEndTime" valueFormat="timestamp" timeType="date" :isLimit="true" showType="heng">
            </StartEndTime>
            <el-button type="primary" style="margin-left: 5px" @click="getData" :loading="loading">查询</el-button>
        </template>
        <template slot="content">
            <div style="height: 100%;display: flex;flex-direction: column" v-loading="loading">
                <div class="flex" style="flex-shrink: 0;">
                    <el-card class="custom-card" shadow="hover" v-for="item in trendList" :key="item.key">
                        <LabelValueUnit :label="item.label" unit="km" :showTrend="true"
                                        :reportData="item.value"
                        ></LabelValueUnit>
                    </el-card>
                </div>
                <el-tabs v-model="activeTab" type="border-card" @tab-click="tabChange"
                         style="height: 300px;margin: 10px 0;flex-shrink: 0; ">
                    <el-tab-pane name="trend" label="里程趋势">
                        <!--                        <div class="mile-trend">-->
                        <!--                            <div class="trend-box">-->
                        <!--                                <TitleAndTrend style="width: 120px;height: 120px;"></TitleAndTrend>-->
                        <!--                            </div>-->
                        <!--                            <div class="table-box">-->
                        <MultipleLine v-bind="chart.trend" ref="trendChart"></MultipleLine>
                        <!--                            </div>-->
                        <!--                        </div>-->
                    </el-tab-pane>
                    <el-tab-pane name="area" label="区域里程" lazy>
                        <MultipleLine v-bind="chart.area" ref="areaChart"></MultipleLine>
                    </el-tab-pane>
                    <el-tab-pane name="time" label="时段里程" lazy>
                        <MultipleLine v-bind="chart.time" ref="timeChart"></MultipleLine>
                    </el-tab-pane>
                </el-tabs>
                <!-- 这层不能少 在旧版chrome内核版本下 flex-grow：1会影响子元素height：100%(flex-direction:column情况下)-->

                <div class="flex-grow flex" style="position: relative">
                    <div class="flex-grow" style="position: absolute; width: 100%; height: 100%">
                        <!-- <div class="flex-grow flex">
                            <div class="flex-grow"> -->
                        <el-table
                            id="driverTable"
                            :data="table.data"
                            height="100%"
                            style="width: 100%"
                            border
                            highlight-current-row
                            :default-sort="{prop: 'mile', order: 'descending'}"
                        >
                            <el-table-column type="index" width="80" label="排名"/>
                            <el-table-column prop="dept" label="部门"/>
                            <el-table-column prop="name" label="驾驶员"/>
                            <el-table-column prop="mile" sortable label="总里程">
                                <template slot-scope="scope">
                                    <span>{{ parseFloat(scope.row.mile) }}</span>
                                    <i
                                        class="ml-arrow"
                                        :class="{'ml-icon-arrow-up':parseFloat(scope.row.mile) > parseFloat(scope.row.l_mile),
                          'ml-icon-arrow-down':parseFloat(scope.row.mile) < parseFloat(scope.row.l_mile),
                          'ml-icon-minus':parseFloat(scope.row.mile) == parseFloat(scope.row.l_mile)}"
                                    ></i>
                                </template>
                            </el-table-column>
                            <el-table-column prop="motorway_mile" label="高速里程"/>
                            <el-table-column prop="city_mile" label="非高速里程"/>
                            <el-table-column prop="day_mile" label="日间里程"/>
                            <el-table-column prop="night_mile" label="夜间里程"/>
                            <el-table-column prop="sli_mile" label="超速里程"/>
                        </el-table>
                    </div>
                </div>
            </div>
        </template>
    </Layout>
</template>

<script>
/**
 * @Author: yezy
 * @Email: <EMAIL>
 * @Date: 2019/12/27 9:59
 * @LastEditors: yezy
 * @LastEditTime: 2019/12/27 9:59
 * @Description:
 */
import SelectTreeInput from '@/components/common/SelectTreeInput'
import LabelValueUnit from '@/view/customReport/reportComponent/text/LabelValueUnit'
// import TitleAndTrend from '@/view/customReport/reportComponent/circle/Title&Trend'
import MultipleLine from '@/view/customReport/reportComponent/line/MultipleLine'
import moment from "moment";
import StartEndTime from "@/components/common/StartEedTime";

export default {
    name: "vehicleMileageStats",
    components: {
        SelectTreeInput,
        LabelValueUnit,
        // TitleAndTrend,
        MultipleLine,
        StartEndTime
    },
    data() {
        return {
            chart: {
                trend: {
                    title: '里程趋势',
                    valueUnit: 'km',
                    defaultValue: {
                        label: ['默认'],
                        data: [
                            {name: '默认', data: [1]}
                        ]
                    },
                    reportData: null,
                },
                area: {
                    title: '区域里程',
                    valueUnit: 'km',
                    defaultValue: {
                        label: ['默认'],
                        data: [
                            {name: '默认', data: [1]}
                        ]
                    },
                    reportData: null,
                },
                time: {
                    title: '时段里程',
                    valueUnit: 'km',
                    defaultValue: {
                        label: ['默认'],
                        data: [
                            {name: '默认', data: [1]}
                        ]
                    },
                    reportData: null,
                }
            },
            query: {
                vehicle: null,
                timeRange: [
                    moment().subtract(8, 'days').startOf('day').toDate(),
                    moment().subtract(1, 'days').startOf('day').toDate()
                ]
            },
            loading: false,
            trendList: [
                {key: 'total', label: '总里程', value: {value: 0, rate: 0}},
                {key: 'motorway', label: '高速里程', value: {value: 0, rate: 0}},
                {key: 'city', label: '非高速里程', value: {value: 0, rate: 0}},
                {key: 'day', label: '日间里程', value: {value: 0, rate: 0}},
                {key: 'night', label: '夜间里程', value: {value: 0, rate: 0}},
                {key: 'sli', label: '超速里程', value: {value: 0, rate: 0}},
            ],
            activeTab: 'trend',
            table: {
                data: []
            },
            selectStartEndTime: [
                moment().startOf("day").valueOf(),
                moment().endOf("day").valueOf(),
            ],
        }
    },
    // watch: {
    //     'query': {
    //         handler: 'getData',
    //         deep: true,
    //     }
    // },
    watch: {
        'selectStartEndTime': function (newVal, oldVal) {
            this.query.timeRange[0] = newVal[0]
            this.query.timeRange[1] = newVal[1]
        }
    },
    activated() {
        this.handleJumpLink();
    },
    methods: {
        handleJumpLink(){
            let parmas = this.$route.query;
            if (!parmas || !parmas.vehicleId) return;
            this.query.vehicle =  {label: parmas.label, value: parmas.vehicleId}
            this.query.timeRange = [
                parmas.start,
                parmas.end,
            ]
            this.getData()
            // this.$nextTick(async () => {
            //     await this.searchVehicleRun();
            //     await this.$router.push("/home/<USER>");
            // });
        },
        tabChange(tab) {
            this.$nextTick(() => {
                this.$refs[tab.name + 'Chart'].resize();
            })
        },
        condition(node) {
            return node.type === 4
        },
        async getData() {
            if (!this.query.vehicle) return this.$warning('请选择车辆！')
            this.loading = true;
            try {
                let res = await this.$api.getMileVehicleReport({
                    vehicle_id: this.query.vehicle.value,
                    type: 7,
                    s_date: moment(this.query.timeRange[0]).format('YYYYMMDD'),
                    e_date: moment(this.query.timeRange[1]).format('YYYYMMDD'),
                })
                if (res.rs === 1) {
                    this.table.data = res.vehicle_mile_detail;
                    this.trendList.forEach(item => {
                        Object.assign(item.value, {
                            value: parseFloat(res.mile_total[`${item.key}_mile`]),
                            rate: parseInt(res.mile_total[`${item.key}_trend`]) *
                                parseFloat(res.mile_total[`${item.key}_mom`]),
                        })
                    });
                    const chartData = res.mile_days;
                    const chartLabel = chartData.map(item => item.date);
                    this.chart.trend.reportData = {
                        label: chartLabel,
                        data: [{name: '车辆里程', data: chartData.map(item => parseFloat(item.mile))}]
                    }
                    this.chart.area.reportData = {
                        label: chartLabel,
                        data: [
                            {name: '高速', data: chartData.map(item => parseFloat(item.motorway_mile))},
                            {name: '非高速', data: chartData.map(item => parseFloat(item.city_mile))}
                        ]
                    }
                    this.chart.time.reportData = {
                        label: chartLabel,
                        data: [
                            {name: '日间', data: chartData.map(item => parseFloat(item.day_mile))},
                            {name: '夜间', data: chartData.map(item => parseFloat(item.night_mile))}
                        ]
                    }
                } else {
                    throw new Error('查询出错，请重试')
                }
            } catch (e) {
                this.$error(e)
            } finally {
                this.loading = false
            }
        }
    },
}
</script>

<style scoped lang="scss">
.vehicle-mileage-stats {
    .custom-card {
        height: 120px;
        width: 16.6666%;
        background: var(--background-color-light);

        & + .custom-card {
            margin-left: 10px;
        }
    }

    .mile-trend {
        height: 100%;

        > .trend-box {
            height: calc(100% - 40px);
            display: flex;
            justify-content: center;
            align-items: center;
            float: left;
            padding: 0 50px;
            margin: 20px 0;
            border-right: 1px solid var(--border-color-base);
        }

        > .table-box {
            height: 100%;
            overflow: hidden;
        }
    }
}
</style>

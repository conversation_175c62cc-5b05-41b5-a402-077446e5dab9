<template>
  <Layout :content-loading="loading" class="car" :has-color="true">
    <template slot="aside" class="score-aside">
      <div class="query-top">
        <ElementTree
          ref="companyTree"
          type="department"
          :checkMode="true"
          @check="onCheckCompany"
        ></ElementTree>
      </div>
      <div class="query-bottom bg bg--lighter">
        <div class="query-item">
          <span>开始日期</span>
          <el-date-picker
            v-model="tableHistory.query.dateRange[0]"
            type="date"
            :picker-options="pickerOptions"
            @change="dateStartChange"
          >
          </el-date-picker>
        </div>
        <div class="query-item">
          <span>结束日期</span>
          <el-date-picker
            v-model="tableHistory.query.dateRange[1]"
            type="date"
            :picker-options="endDatePickerOptions"
          >
          </el-date-picker>
        </div>
        <div class="query-item">
          <span>按日显示</span>
          <el-switch
            style="margin-left: 3px"
            active-color="#13ce66"
            inactive-color="#1d233b"
            v-model="tableHistory.query.hasDay"
          >
          </el-switch>
        </div>
        <div class="query-item">
          <span>企业/车队</span>
          <el-select
            style="width: 100%"
            v-model="query.historydept"
            placeholder="请选择"
          >
            <el-option :value="2" label="企业"></el-option>
            <el-option :value="3" label="车队"></el-option>
          </el-select>
        </div>
        <div class="query-item">
          <el-button
            size="mini"
            type="primary"
            style="width: 100%;"
            @click="getTableHistoryData"
            >{{ $t("common.query") }}</el-button
          >
        </div>
      </div>
    </template>

    <template slot="content">
      <Layout
        class="layout--no-padding-horizontal"
        :content-loading="tableHistory.loading"
      >
        <template slot="query">
          <el-button type="primary" @click="exportTableHistoryData">{{
            $ct("export")
          }}</el-button>
          <el-popover
            popper-class="my-el-popover"
            placement="bottom-start"
            width="300"
            v-model="visible"
          >
            <div class="top">
              <div class="sort-direction">
                <span
                  class="up"
                  :class="isReverse === false ? 'true' : ''"
                  @click="sortDirection"
                  ><i class="pony-iconv2 pony-shengxu"></i
                ></span>
                <span
                  class="down"
                  :class="isReverse === true ? 'true' : ''"
                  @click="sortDirection"
                  ><i class="pony-iconv2 pony-jiangxu"></i
                ></span>
              </div>
              <div class="recover">
                <span @click="recover"
                  ><i class="el-icon-refresh-left"></i>恢复</span
                >
              </div>
            </div>
            <div class="content">
              <ul>
                <li
                  class="item"
                  v-for="(item, index) in sortList"
                  :key="item.prop"
                  @click="changSort(item)"
                >
                  <span :class="item.isChecked === true ? 'select' : ''" style="cursor:pointer">{{
                    item.sortIndex
                  }}</span
                  >{{ $ct("label." + item.prop) }}
                </li>
              </ul>
            </div>
            <div class="bottom">
              <el-button @click="sortListBtn">确定</el-button>
            </div>
            <div slot="reference" style="display: flex; align-items: center">
              <el-button
                type="primary"
                :icon="
                  isReverse === true
                    ? 'pony-iconv2 pony-jiangxu'
                    : 'pony-iconv2 pony-shengxu'
                "
                style="height: 28px;width: 20px;display: flex;align-items: center;justify-content: center;margin-left: 5px"
              ></el-button>
            </div>
          </el-popover>
          <div class="break-item"></div>
          <el-pagination
            small
            background
            :total="tableHistory.total"
            :page-size="30"
            :current-page.sync="tableHistory.query.page"
            layout="prev, pager, next, total"
          >
          </el-pagination>
        </template>
        <template slot="content">
          <el-table
            ref="historyTable"
            :data="filterTableHistoryData"
            border
            stripe
            highlight-current-row
            height="100%"
          >
            <el-table-column
              type="index"
              align="center"
              :label="$ct('label.index')"
              :index="
                (index) =>
                  (tableHistory.query.page - 1) * tableHistory.query.size +
                  index +
                  1
              "
            ></el-table-column>
            <el-table-column
              min-width="150"
              prop="deptName"
              :label="$ct('label.deptName')"
              show-overflow-tooltip
            ></el-table-column>
            <el-table-column
              prop="date"
              min-width="180"
              :label="$ct('label.date')"
            ></el-table-column>
            <el-table-column prop="totalScore" :label="$ct('label.totalScore')">
              <template slot-scope="scope">
                 <div
                      v-if="scope.row.totalScore < 0"
                    >
                      {{ formatCeil(null, null, scope.row.totalScore) }}
                    </div>
                    <div
                      class="score-card"
                      :style="getTotalScoreStyle(scope.row.totalScore)"
                      v-else
                    >
                      {{ formatCeil(null, null, scope.row.totalScore) }}
                    </div>
              </template>
            </el-table-column>
            <el-table-column :label="$ct('label.detail')">
              <template slot-scope="scope">
                <div>
                  <el-button
                    type="text"
                    icon="pony-iconv2 pony-xiangqing"
                    size="mini"
                    @click="showDetail(scope.row)"
                  ></el-button>
                  <el-button
                    type="text"
                    icon="pony-iconv2 pony-cheliangpingfen"
                    size="mini"
                    @click="jump(scope.row)"
                  ></el-button>
                </div>
              </template>
            </el-table-column>
            <el-table-column
              min-width="100"
              prop="driveTime"
              :label="$ct('label.driveTime')"
            ></el-table-column>
            <el-table-column
              min-width="100"
              prop="driveMile"
              :label="$ct('label.driveMile')"
            ></el-table-column>
            <el-table-column
              min-width="130"
              prop="eventCount"
              :label="$ct('label.eventCount')"
            ></el-table-column>
            <!-- <el-table-column
              v-for="item in currentHeaderList"
              :key="item.prop"
              :prop="item.prop"
              :formatter="formatCeil"
              :min-width="item.minWidth"
              :label="$ct('label.' + item.prop)"
            >
            </el-table-column> -->
              <el-table-column
                  min-width="100"
                  prop="dayExScore"
                  :label="$ct('label.dayExScore')"
                ></el-table-column>
                <el-table-column
                  min-width="110"
                  prop="dayNoExScore"
                  :label="$ct('label.dayNoExScore')"
                ></el-table-column>
                <el-table-column
                  min-width="100"
                  prop="nightExScore"
                  :label="$ct('label.nightExScore')"
                ></el-table-column>
                <el-table-column
                  min-width="110"
                  prop="nightNoExScore"
                  :label="$ct('label.nightNoExScore')"
                ></el-table-column>
          </el-table>
        </template>
      </Layout>
      <ScoreDetail ref="scoreDetail"></ScoreDetail>
      <ScoreDetailSK ref="scoreDetailSK"></ScoreDetailSK>
    </template>
  </Layout>
</template>

<script>
Array.prototype.orderBy = Array.prototype.orderBy = function(
  orderArray,
  isReverse
) {
  if (typeof orderArray === "boolean" || typeof orderArray === "undefined") {
    isReverse = orderArray;
    orderArray = "";
  }
  if (typeof orderArray === "string") {
    let str = orderArray;
    orderArray = [];
    orderArray.push(str);
  }
  return this.sort((a, b) => {
    return compare(a, b, orderArray, isReverse);
  });
};

// 排序确认
function compare(a, b, orderArray, isReverse) {
  let c = orderArray[0];
  if (orderArray.length > 1 && a[c] === b[c]) {
    return compare(a, b, orderArray.slice(1), isReverse);
  } else {
    return  a[c] ==b[c]?0:(isReverse? a[c] > b[c]: a[c]  < b[c] )? -1: 1;
  }
}
import ExportJsonExcel from "js-export-excel";
import ScoreDetailSK from "../../components/ScoreDetailSK";

import ScoreDetail from "../../components/ScoreDetail";
const TABLE_HEADER = [
    { minWidth: 120, prop: "dayExScore" },
  { minWidth: 120, prop: "dayNoExScore" },
  { minWidth: 120, prop: "nightExScore" },
  { minWidth: 120, prop: "nightNoExScore" },
];
export default {
  name: "enterprise",
  components: {
    ScoreDetail,
    ScoreDetailSK,
  },
  data() {
    return {
      visible: false, // 弹窗显示与隐藏
      loading: false,
      sortList: [], // 排序弹窗里面的列表
      sort: [], // 已选中的的排序条件
      recoverList: [], // 存储排序弹窗恢复的数据
      isReverse: true, // 排序弹窗升序和倒序
      tableHeader: {
        vehicle: { monitor: [], history: [] },
      },
      checkedNodesCompany: [],
      query: {
        vehicleIds: [],
        monitordept: 2,
        historydept: 2,
      },
      tableHistory: {
        query: {
          page: 1,
          size: 30,
          hasDay: false,
          dateRange: [
            moment()
              .startOf("day")
              .subtract(1, "days")
              .toDate(),
            moment()
              .endOf("day")
              .subtract(1, "days")
              .toDate(),
          ],
        },
        data: [],
        sort: "default",
        sortKey: null,
        total: 0,
        loading: false,
      },
      pickerOptions: {
        disabledDate: function(date) {
          return (
            date -
              moment()
                .endOf("day")
                .subtract(1, "days")
                .toDate() >
            0
          );
        },
      },
    };
  },
  computed: {
    // 历史评分分页
    filterTableHistoryData: function() {
      let arr = this.tableHistory.data;
      let start =
        (this.tableHistory.query.page - 1) * this.tableHistory.query.size;
      let end = this.tableHistory.query.page * this.tableHistory.query.size;
      return arr.slice(start, end);
    },
    endDatePickerOptions: function() {
      return {
        disabledDate: (date) => {
        return (
          moment(date).endOf("day") - this.tableHistory.query.dateRange[0] < 0
        );
        },
      };
      },
  },
  async created() {
    try {
      this.pageLoading = true;
     this.sortList.push({
        prop: "totalScore",
        sortIndex: "1",
        isChecked: true,
      },{
        prop:"dayExScore",
        isChecked: false,
      },{
         prop:"dayNoExScore",
        isChecked: false,
      }
      ,{
         prop:"nightExScore",
        isChecked: false,
      }
      ,{
         prop:"nightNoExScore",
        isChecked: false,
      }
      );
      this.sort.push("totalScore");
    } catch (e) {
    } finally {
      this.pageLoading = false;
    }
  },
  methods: {
    // 跳转到车辆评分传的参数
    jump(row) {
      let arr = {
        deptId: row.deptId,
        dayStart: this.tableHistory.query.dateRange[0],
        dayEnd: this.tableHistory.query.dateRange[1],
        hasDay: this.tableHistory.query.hasDay ? 1 : 0,
        sort: this.sort,
        sortList: this.sortList,
        isReverse: this.isReverse,
      };
      this.$emit("jumpCar", arr);
    },
    // 排序确认按钮
    sortListBtn() {
      // 采用深拷贝
      let list = JSON.parse(JSON.stringify(this.recoverList));
      this.tableHistory.data = list.orderBy(this.sort, this.isReverse);
      this.visible = false;
    },
    // 排序方向
    sortDirection() {
      this.isReverse = !this.isReverse;
    },
    // 排序
    changSort(data) {
      // 如果点击的排序选项已经选中，则取消选中
      if (data.isChecked === true) {
        // 获取当前序号
        let sortNum = data.sortIndex;

        // 在已选中的排序列表中删除
        let indexOf = this.sort.indexOf(data.prop);
        this.sort.splice(indexOf, 1);

        // 点击选项取消选择并且大于当前序号的排序选项序号-1
        this.sortList.forEach((item, index) => {
          if (item.prop === data.prop) {
            this.$set(
              this.sortList[index],
              "isChecked",
              !this.sortList[index].isChecked
            );
            this.$set(this.sortList[index], "sortIndex", "");
          }

          if (item.sortIndex > sortNum) {
            this.$set(
              this.sortList[index],
              "sortIndex",
              this.sortList[index].sortIndex - 1
            );
          }
        });
      } else {
        // 如果点击的排序选项没有选中
        this.sortList.forEach((item, index) => {
          if (item.prop === data.prop) {
            this.$set(
              this.sortList[index],
              "isChecked",
              !this.sortList[index].isChecked
            );
            this.$set(this.sortList[index], "sortIndex", this.sort.length + 1);
            this.sort.push(this.sortList[index].prop);
          }
        });
      }
    },
    // 排序弹窗恢复
    recover() {
      this.sort = [];
      // 排序列表全部取消选中
      this.sortList.forEach((item, index) => {
        this.$set(this.sortList[index], "isChecked", false);
        this.$set(this.sortList[index], "sortIndex", "");
      });
      this.tableHistory.data = this.recoverList;
    },
    // 树点击事件
    onCheckCompany(data, { checkedNodes }) {
      let userType = [2, 3];
      this.checkedNodesCompany = checkedNodes.filter((item) =>
        userType.includes(item.type)
      );
    },
    // 查询历史评分
    async getTableHistoryData() {
      let res;
      let params = {
        dayStart: this.DateFormat(this.tableHistory.query.dateRange[0]),
        dayEnd: this.DateFormat(this.tableHistory.query.dateRange[1]),
        hasDay: this.tableHistory.query.hasDay ? 1 : 0,
        deptType: this.query.historydept,
      };

      let deptIds = this.checkedNodesCompany
        .filter((item) => item.type == this.query.historydept)
        .map((item) => item.id);

      if (deptIds.length === 0) {
        this.$warning(this.$ct("messageInfo.1"));
        return;
      }

      try {
        this.tableHistory.loading = true;
        res = await this.$api.seccoDimensionScore({
          ...params,
          deptIds: deptIds,
        });

        if (res.status === 200) {
          if(res.data.length > 0) {
          res.data.forEach((item, index) => {
            item._originIndex = index;
          });
          this.tableHistory.data = res.data;
          this.tableHistory.total = res.data.length;
          this.recoverList = res.data;
          // 默认按照总分排序
          this.sortListBtn();
          this.$nextTick(() => {
            this.$refs.historyTable.doLayout();
          });
          }else{
            this.$warning("未查询到数据")
          this.tableHistory.loading = false;
          }

        } else {
          this.$error(res.data);
        }
      } catch (e) {
        this.$error(this.$ct("messageInfo.3"));
      } finally {
        this.tableHistory.loading = false;
      }
    },
    // 列表总分的宽度
    formatCeil(row, column, cellValue, index) {
      if (Number(cellValue) < 0) {
        return "-";
      } else if (typeof cellValue == "number") {
        return Math.round(Number(cellValue) * 100) / 100;
      } else {
        return cellValue;
      }
    },
    // 列表总分的样式
    getTotalScoreStyle(score) {
      let color;
      switch (true) {
        case score >= 0 && score < 30:
          color = "rgb(255, 87, 87)";
          break;
        case score >= 30 && score < 60:
          color = "rgb(252, 196, 25)";
          break;
        case score >= 60:
          color = "rgb(52, 184, 67)";
          break;
      }
      return `background-color:${color}`;
    },
    dateStartChange(date) {
      if (this.tableHistory.query.dateRange[1] - date < 0) {
        this.$set(this.tableHistory.query.dateRange, 1, date);
      }
    },
    // 显示详情弹窗
    showDetail(row) {
        row.time = this.tableHistory.query.dateRange
      this.$refs.scoreDetailSK.show(row, "enterprise");
    },
    // 导出历史评分excel
    exportTableHistoryData() {
      let currentList = this.tableHistory.data;
      if (!currentList.length) {
        this.$warning(this.$ct("messageInfo.4"));
        return;
      }
      let excelBody = [];
      let f = (v) => {
        return this.formatCeil(null, null, v);
      };
      currentList.forEach((item, index) => {
        let array = [
          index + 1,
          item.plateNo,
          item.deptName,
          item.date,
          f(item.totalScore),
          item.driveTime,
          item.driveMile,
          item.eventCount,
        ];
        TABLE_HEADER.forEach((header) => {
          array.push(f(item[header.prop]));
        });
        excelBody.push(array);
      });
      const headers = [
        "index",
        "plateNo",
        "deptName",
        "date",
        "totalScore",
        "driveTime",
        "driveMile",
        "eventCount",
      ];
     TABLE_HEADER.forEach((item) => {
        headers.push(item.prop);
      });
      let options = {
        fileName: "企业/车队评分报表",
        datas: [
          {
            sheetData: excelBody,
            sheetHeader: headers.map((item) => this.$ct(`label.${item}`)),
            columnWidths: [
              "3",
              "10",
              "10",
              "10",
              "7",
              "7",
              "7",
              "7",
              "7",
              "7",
              "7",
              "7",
              "7",
              "7",
              "7",
              "7",
              "7",
              "7",
              "7",
              "7",
              "7",
              "7",
              "7",
            ],
          },
        ],
      };
      ExportJsonExcel(options).saveExcel();
    },
  },
};
</script>

<style scoped lang="scss">
.car {
  .query-top {
    height: 100%;
    overflow: auto;
  }

  .query-bottom {
    margin-top: 5px;
    padding: 10px;

    .query-item {
      display: flex;
      height: 40px;
      line-height: 40px;
      justify-content: space-between;
      align-items: center;

      > span {
        font-size: 12px;
        white-space: nowrap;
        margin-right: 5px;
      }
    }
  }

  .sort-btn {
    margin-left: 5px;

    .pony-iconv2 {
      font-size: 12px;
    }
  }

  .score-card {
    color: #ffffff;
    border-radius: 4px;
    text-align: center;
  }

  .score-aside {
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
  }
}
</style>
<style lang="scss">
.my-el-popover {
  padding: 0;

  .top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 12px 0 12px;

    .sort-direction {
      display: flex;
      text-align: center;

      .up {
        width: 25px;
        border: 1px solid var(--border-color-base);
        border-radius: 4px 0 0 4px;
      }

      .down {
        width: 25px;
        border: 1px solid var(--border-color-base);
        margin-left: -1px;
        border-radius: 0 4px 4px 0;
      }

      .true {
        color: var(--color-primary);
        border: 1px solid var(--color-primary);
        z-index: 9999;
      }
    }

    .recover {
      color: var(--color-primary);
    }
  }

  .content {
    border-bottom: 1px solid var(--border-color-base);
    padding: 0 12px 12px 12px;

    ul {
      display: flex;
      flex-wrap: wrap;

      li {
        display: flex;
        align-items: center;
        width: 137px;
        height: 20px;
        font-size: 14px;
        margin-top: 10px;

        span {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 20px;
          height: 20px;
          border: 1px solid var(--border-color-base);
          margin-right: 5px;
          border-radius: 3px;
        }

        .select {
          background-color: var(--color-primary);
        }
      }
    }
  }

  .bottom {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding: 4px;
  }
}
</style>

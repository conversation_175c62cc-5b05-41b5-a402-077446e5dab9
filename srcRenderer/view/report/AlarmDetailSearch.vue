<template>
  <Layout class="alarm-detail-search" tag="div" :contentLoading="table.loading" :asideWidth="320">
    <template slot="aside">
      <div class="query-top ">
        <el-tabs stretch type="border-card" v-model="vehicleOrAlarmTabs">
          <!-- 车辆选择面板 -->
          <el-tab-pane :label="$ct('label.tabVehicle')" name="vehicle">
            <el-tabs v-model="activeTab" stretch class="no-padding" style="padding: 5px 10px 0;">
              <!-- 车辆列表面板 -->
              <el-tab-pane :label="$ct('label.vehicleList')" name="vehicle">
                <ElementTree ref="vehicleTree" :checkMode="true" type="vehicle" @check="selectNodes" state onlineCountTip>
                </ElementTree>
              </el-tab-pane>
              <!-- 驾驶员列表面板 -->
              <el-tab-pane :label="$ct('label.driverList')" name="driver" lazy>
                <ElementTree ref="driverTree" :checkMode="true" type="driverTree" @check="selectNodes1"></ElementTree>
              </el-tab-pane>
            </el-tabs>
          </el-tab-pane>
          <!-- 报警选择面板 -->
          <el-tab-pane :label="$ct('label.tabAlarm')" style="overflow-x: hidden;overflow-y: auto" name="alarm">
            <!--                        <AsideAlarm :filter="['business']" :count="alarmCount" @change="handleAlarmChange" />-->
            <!-- 报警明细的报警选择 -->
            <AsideAlarm :filter="[]" :count="alarmCount" @change="handleAlarmChange"
              v-show="this.activeTab2 == 'alarmDetails'" ref="asideAlarm" :show-alarm-count="asideAlarmShowCount" />
            <!-- 报警统计的报警选择 -->
            <AsideAlarm1 :filter="[]" :count="alarmSummaryCount" @change="handleAlarmChange2" :checkedAll="false"
              v-show="this.activeTab2 == 'alarmSummary'" ref="asideAlarm1" :show-alarm-count="asideAlarm1ShowCount" />
          </el-tab-pane>
        </el-tabs>
      </div>
      <div class="query-bottom bg bg--light box-shadow">
        <StartEndTime itemHeight="35" :timeTitle="['开始时间', '结束时间']"
          titleStyle="font-size:12px;margin-left:5px;margin-right:5px" v-model="selectStartEndTime"
          valueFormat="timestamp" timeType="datetime" :isLimit="true">
        </StartEndTime>
        <!-- <div class="query-item">
            <span>{{ $ct("startTime") }}</span>
            <el-date-picker type="datetime" :picker-options="pickerOptions" @change="dateStartChange" v-model="query.startTime">
            </el-date-picker>
        </div>
        <div class="query-item">
            <span>{{ $ct("endTime") }}</span>
            <el-date-picker type="datetime" :picker-options="endDatePickerOptions" v-model="query.endTime" @change="dateEndChange">
            </el-date-picker>
        </div> -->
        <div class="query-item">
          <span>{{ $ct("minSpeed") }}</span>
          <el-input-number v-model="query.minSpeed" :min="0" :max="120" :step="5" placeholder="（km/h)"></el-input-number>
        </div>
        <div class="query-item">
          <span>{{ $ct("maxSpeed") }}</span>
          <el-input-number v-model="query.maxSpeed" :min="query.minSpeed" :max="150" :step="5"
            placeholder="（km/h)"></el-input-number>
        </div>
        <div class="query-item">
          <span>处理类型</span>
          <el-cascader v-model="query.dealTypeNew" :options="eventListOptions" :show-all-levels="false"></el-cascader>
        </div>
        <div class="query-item">
          <el-button size="mini" type="primary" :loading="table.loading" style="width: 100%;" @click="getTableData(1)"
            v-if="this.activeTab2 == 'alarmDetails'">
            {{ $ct("query") }}
          </el-button>

          <el-button size="mini" type="primary" :loading="summaryTable.loading" style="width: 100%;"
            @click="getSummaryTableData(1)" v-else>
            {{ $ct("query") }}
          </el-button>
        </div>
      </div>
    </template>
    <template slot="query"></template>
    <template slot="content">
      <el-tabs v-model="activeTab2" type="border-card">
        <el-tab-pane name="alarmDetails" label="报警明细">
          <layout>
            <template slot="query">
              <el-button type="primary" @click="exportDetailsExcel" :loading="reportLoading">
                {{ $ct("export") }}
              </el-button>
              <el-button type="primary" @click="batchProcessing">批量处理</el-button>
              <el-button type="primary" @click="uplodeAlarm" v-if="hasPermission('AlarmDetails:overload')">添加客车超员报警
              </el-button>
              <el-button size="mini" type="primary" @click="operateTableSetting">表格显示设置</el-button>

              <!-- v-if="hasPermission('AlarmDetails:overload')" -->
              <span class="warning-hint text text--danger">{{ $ct("hint") }}</span>
              <div class="break-item"></div>
              <el-pagination small background layout="prev, pager, next, total,sizes" :pager-count="5"
                :current-page="query.page" :page-sizes="[10, 20, 30, 40, 50, 100, 1000]" :total="query.total"
                :page-size="query.size" @current-change="getTableData" @size-change="handleSizeChange">
              </el-pagination>
            </template>
            <template slot="content">
              <el-table class="el-table--radius box-shadow" ref="adasAlarmTable" :data="table.data" border stripe
                highlight-current-row height="100%" style="width: 100%" @selection-change="handleSelectionChange">
                <el-table-column type="selection" :selectable="selected" width="55">
                </el-table-column>
                <el-table-column :label="$ct('label.index')" type="index" width="50">
                  <template slot-scope="scope">
                    <span>{{ (query.page - 1) * query.size + 1 + scope.$index }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="80">
                  <template slot-scope="scope">
                    <el-button type="text" title="详情" @click="getDetail(scope.row)">
                      <i class="pony-iconv2 pony-xiangqing"></i>
                    </el-button>
                    <!-- // is_video是否有附件标识(0:没有, 1:有)
                    // is_play是否已下载标识(0:没下载, 1:已下载) -->
                    <!-- text--primary:白色   text--brand:蓝色-->
                    <i :title="$ct('label.videoHint')" class="pony-iconv2 pony-bofangquan text text--disabled"
                      v-if="scope.row.is_video == -1 && scope.row.is_play !== 1"></i>
                    <i v-else
                      :class="['pony-iconv2 pony-bofangquan text', { 'text--primary': scope.row.is_video == 1 && scope.row.is_play == 0, disabled: scope.row.is_video == 0, 'text--brand': scope.row.is_play == 1, },]"
                      @click="videoPlay(scope.row)"></i>
                  </template>
                </el-table-column>
                <el-table-column v-for="(item, index) in tableSettingList" :key="index" :min-width="item.size + 'px'"
                  header-align="center" :align="item.align" :prop="item.key" :label="item.name" show-overflow-tooltip>
                  <template slot-scope="{row}">
                    <span v-if="item.key == 'alarm_type' && !row.level">{{ row.alarm_type }}</span>
                    <el-tag v-if="item.key == 'alarm_type' && row.level"
                      :type="row.level == 1 ? 'default' : row.level == 2 ? 'warning' : 'danger'">
                      {{ row.alarm_type }}
                    </el-tag>
                    <span v-if="item.key == 'company_name'">{{ row.company_name }} >>
                      {{ row.fleet_name }}</span>
                    <span v-if="item.key == 'duration'">{{ MillisecondFormat(row.duration * 1000) }}</span>
                    <span v-if="!item.type">{{ row[item.key] }}</span>
                  </template>
                </el-table-column>
                <!-- <el-table-column prop="agent_name" label="区域" min-width="150" show-overflow-tooltip v-if="hasPermission('lockMap:detail')" />

                <el-table-column label="单位" min-width="150" align="left" header-align="center" show-overflow-tooltip>
                    <template slot-scope="scope">
                        <span>{{ scope.row.company_name }} >>
                            {{ scope.row.fleet_name }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="plate_no" :label="$ct('label.plateNo')" min-width="91" show-overflow-tooltip />
                <el-table-column prop="driver_name" :label="$ct('label.driver')" min-width="90" show-overflow-tooltip />
                <el-table-column prop="alarm_type" min-width="180" :label="$ct('label.alarmType')" show-overflow-tooltip>
                    <template slot-scope="scope">
                        <span v-if="!scope.row.level">{{ scope.row.alarm_type }}</span>
                        <el-tag v-else :type="scope.row.level == 1 ? 'default' : scope.row.level == 2 ? 'warning' : 'danger'">
                            {{ scope.row.alarm_type }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="terminal_driver_name" min-width="110" label="驾驶员(登签)" show-overflow-tooltip />

                <el-table-column prop="alarm_time" min-width="140" label="开始时间" />
                <el-table-column prop="alarm_time_end" min-width="140" label="结束时间" />
                <el-table-column prop="duration" min-width="80" label="持续时间" show-overflow-tooltip>
                    <template slot-scope="scope">
                        <span>{{
                                MillisecondFormat(scope.row.duration * 1000)
                            }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="alarm_level" min-width="70" :label="$ct('label.alarmLevel')" show-overflow-tooltip />
                <el-table-column prop="gps_speed" :label="$ct('label.speed') + '(km/h)'" min-width="90" />
                <el-table-column prop="jurisdiction_name" label="辖区" min-width="100" show-overflow-tooltip v-if="hasPermission('lockMap:detail')" />

                <el-table-column label="处理方式" min-width="100" show-overflow-tooltip>
                    <template slot-scope="scope">
                        <span>{{ scope.row.deal_type_name }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="处理人" min-width="90" show-overflow-tooltip>
                    <template slot-scope="scope">
                        <span>{{ scope.row.deal_user_name }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="处理时间" min-width="140" show-overflow-tooltip prop="deal_time"></el-table-column>
                <el-table-column label="处理备注" min-width="150" show-overflow-tooltip>
                    <template slot-scope="scope">
                        <span>{{ scope.row.deal_desc }}</span>
                    </template>
                </el-table-column>
                <el-table-column align="center" label="开始位置" min-width="300" show-overflow-tooltip>
                    <template slot-scope="scope">
                        <span>{{ scope.row.location }}</span>
                    </template>
                </el-table-column>
                <el-table-column align="center" label="结束位置" min-width="300" show-overflow-tooltip>
                    <template slot-scope="scope">
                        <span>{{ scope.row.location_end }}</span>
                    </template>
                </el-table-column> -->
              </el-table>
            </template>
          </layout>
        </el-tab-pane>
        <el-tab-pane name="alarmSummary" label="报警统计">
          <layout :contentLoading="summaryTable.loading">
            <template slot="query">
              <el-button type="primary" @click="exportSummaryExcel" :loading="reportLoading">
                {{ $ct("export") }}
              </el-button>
              <span class="warning-hint text text--danger">提示:驾驶员在查询时间段内产生报警时，车辆列显示绑定车牌号，否则为空</span>
              <div class="break-item"></div>
              <el-pagination background small :pager-count="5" :current-page.sync="pager.current"
                layout="prev, pager, next, total" :page-size="pager.size" :total="pager.total">
              </el-pagination>
            </template>
            <template slot="content">
              <el-table class="el-table--radius el-table--ellipsis box-shadow" ref="adasAlarmSummaryTable"
                :data="formatList" border stripe highlight-current-row height="100%" size="mini" style="width: 100%">
                <el-table-column type="index" :index="(index) => index + 1 + pageStart" label="序号" min-width="50" fixed>
                </el-table-column>
                <el-table-column label="单位" width="220" fixed align="left" header-align="center" show-overflow-tooltip>
                  <template slot-scope="scope">
                    <span>{{ scope.row.company_name }} >>
                      {{ scope.row.fleet_name }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="plate_no" label="车牌号" width="100" fixed show-overflow-tooltip />
                <el-table-column prop="driver_name" label="驾驶员" width="130" fixed show-overflow-tooltip />
                <el-table-column prop="alarms" :label="formatList.length
                  ? formatList[0].alarms[index].field
                  : '报警类型'
                  " width="220" v-for="(item, index) in columnNum" :key="item" show-overflow-tooltip>
                  <template slot-scope="{ row }">
                    <span title="报警数/处理数" style="cursor:pointer ">
                      {{ row.alarms[index].value }}
                    </span>
                  </template>
                </el-table-column>
                <el-table-column label="" />
              </el-table>
            </template>
          </layout>
        </el-tab-pane>
      </el-tabs>
    </template>
    <Player ref="player" @played="playedAction"></Player>
    <AlarmDetailV2 ref="alarmDetail" @change="getTableData(query.page)"></AlarmDetailV2>
    <PassengerOverloadAlarm ref="PassengerOverloadAlarm"></PassengerOverloadAlarm>
    <BatchProcessingDialog ref="batchProcessingDialog" @change="getTableData(query.page)"></BatchProcessingDialog>
    <!-- 表格显示配置弹窗 -->
    <TableShowConfigList ref="tableShowConfigList" v-model="tableSettingList" :list="allSettingListFin"
      @tableValue="getSetValue" pageName="alarmDetailsSearch" :isSave="true" :defaultSetting="defaultSettingList"
      @change="settable">
    </TableShowConfigList>
  </Layout>
</template>

<script>
/**
 * @Author: yezy
 * @Email: <EMAIL>
 * @Date: 2019/11/6 15:27
 * @LastEditors: yezy
 * @LastEditTime: 2019/11/6 15:27
 * @Description:
 */
import AsideAlarm from "@/components/common/AsideAlarm";
import AlarmDetailV2 from "./components/AlarmDetailV2";
import PassengerOverloadAlarm from "./components/PassengerOverloadAlarm";
import BatchProcessingDialog from "./components/BatchProcessingDialog";
import moment from "moment";
import TableShowConfigList from "@/view/report/components/TableShowConfigList";
import { allSettingList } from "./tablejs/alarmDetailSearch.js";

const ExportJsonExcel = require("js-export-excel");
// 解决同一个页面多次使用同一组件数据互相干扰问题：
// 解决方案为 注册成两个实例分别调用组件
const AsideAlarm1 = () => import("@/components/common/AsideAlarm");
import StartEndTime from "@/components/common/StartEedTime";
import CompareScrollVue from './compare/components/CompareScroll.vue';


export default {
  name: "alarmDetailSearch",
  components: {
    AsideAlarm,
    AsideAlarm1,
    AlarmDetailV2,
    PassengerOverloadAlarm,
    BatchProcessingDialog,
    StartEndTime,
    TableShowConfigList
  },
  data() {
    return {
      vehicleOrAlarmTabs: 'vehicle',
      dealTypeList: [],
      activeTab: "vehicle",
      // 报警明细上一次查询所记录的相关的值
      alarmDetailLastTimeQueryData: {
        queriedTabName: "vehicle",
        // 记录查询时所勾选的报警类型
        checkedAlarmNodes: [],
        // 分类还是全部
        alarmType: "",
      },
      // 报警统计上一次查询所记录的相关的值
      alarmSummaryLastTimeQueryData: {
        queriedTabName: "vehicle",
        // 报警统计查询后，记录下来本次查询所勾选的报警类型
        checkedAlarmNodes: [],
        // 分类还是全部
        alarmType: "",
      },
      activeTab2: "alarmDetails",
      query: {
        startTime: moment().startOf("days"),
        endTime: moment().endOf("days"),
        page: 1,
        size: 30,
        total: 0,
        minSpeed: 0,
        maxSpeed: 150,
        dealTypeNew: [null],
      },
      rowData: null,
      // 报警明细数据
      table: {
        data: [],
        loading: false,
      },
      //  报警汇总数据
      summaryTable: {
        data: [],
        loading: false,
      },
      pager: {
        current: 1,
        size: 30,
        total: 0,
      },
      pickerOptions: {
        disabledDate: function (date) {
          return (
            date -
            moment()
              .endOf("day")
              .toDate() >
            0
          );
        },
      },
      alarmCount: {},
      alarmList: [],
      // 报警统计的报警节点选择
      alarmSummaryList: [],
      alarmSummaryCount: {},
      columnNum: 1,
      reportLoading: false,
      currentNodes: [],
      currentNodes1: [],
      // 表格批量选中列表
      selectList: [],
      selectStartEndTime: [
        moment().startOf("day").valueOf(),
        moment().endOf("day").valueOf(),
      ],
      exportData: {
        start: null,
        end: null
      },
      tableSettingList: [],
    };
  },
  computed: {
    // 报警明细 的 报警选择是否显示数量
    asideAlarmShowCount() {
      return this.activeTab === this.alarmDetailLastTimeQueryData.queriedTabName;
    },
    // 报警统计 的 报警选择是否显示数量
    asideAlarm1ShowCount() {
      return this.activeTab === this.alarmSummaryLastTimeQueryData.queriedTabName;
    },
    pageStart() {
      return (this.pager.current - 1) * this.pager.size;
    },
    formatList() {
      return this.summaryTable.data.slice(
        (this.pager.current - 1) * this.pager.size,
        this.pager.current * this.pager.size
      );
    },
    endDatePickerOptions: function () {
      return {
        disabledDate: (date) => {
          return (
            date -
            moment()
              .endOf("day")
              .toDate() >
            0 || moment(date).endOf("day") - this.query.startTime < 0
          );
        },
      };
    },
    eventListOptions: function () {
      return [
        {
          value: null,
          label: '全部'
        },
        {
          value: 1,
          label: '处理类型',
          children: this.dealTypeList.slice(1),
        },
        {
          value: 0,
          label: '未处理',
        },
      ]
    },
    allSettingListFin() {
      return allSettingList.filter(item => !item.promise || (item.promise && this.hasPermission(item.promise)))
    },
    defaultSettingList() {
      return allSettingList.filter(item => !item.promise || (item.promise && this.hasPermission(item.promise)))
    }
  },
  watch: {
    "$route.query": async function (val) {
      await this.$nextTick();
      !this._inactive && this.checkQuery();
    },
    selectStartEndTime: function (newVal, oldVal) {
      this.query.startTime = moment(newVal[0]).format('YYYY-MM-DD HH:mm:ss')
      this.query.endTime = moment(newVal[1]).format('YYYY-MM-DD HH:mm:ss')
    },
    // 监听 车辆列表/驾驶员列表 的切换
    activeTab(newVal, oldVal) {
      // 根据 报警明细/报警统计，获取对应 报警选择tree 的 ref
      const asideAlarmRef = this.$refs[this.activeTab2 === "alarmDetails" ? "asideAlarm" : "asideAlarm1"];
      // 如果当前是 报警明细
      if (this.activeTab2 === "alarmDetails") {
        // 如果查询过
        if (this.alarmDetailLastTimeQueryData.alarmType) {
          // 如果切换到的是 上次查询时使用的 车辆/驾驶员 页签
          if (newVal === this.alarmDetailLastTimeQueryData.queriedTabName) {
            // 回显 报警选择的勾选
            asideAlarmRef.setCheckedKeys(this.alarmDetailLastTimeQueryData);
          } else {
            // 否则清空勾选
            asideAlarmRef.clearChecked();
          }
        }
      } else {
        // 如果查询过
        if (this.alarmSummaryLastTimeQueryData.alarmType) {
          // 否则如果是 报警统计
          // 如果切换到的是 上次查询时使用的 车辆/驾驶员 页签
          if (newVal === this.alarmSummaryLastTimeQueryData.queriedTabName) {
            // 回显 报警选择的勾选
            asideAlarmRef.setCheckedKeys(this.alarmSummaryLastTimeQueryData);
          } else {
            // 否则清空勾选
            asideAlarmRef.clearChecked();
          }
        }
      }
    },
  },
  methods: {
    handleSizeChange(val) {
      this.query.size = val
      this.getTableData(this.query.page)
    },
    settable() {
      this.$nextTick(() => {
        this.$refs.adasAlarmTable.doLayout()
      })
    },
    // 点击设置表格按钮
    operateTableSetting() {
      this.$refs.tableShowConfigList.showModel()
    },
    getSetValue(val) {
      if (!val) {
        this.tableSettingList = this.defaultSettingList
      } else {
        this.tableSettingList = val
      }
    },
    selected(row, index) {
      return row.deal_type == 0
    },
    // 表格批量选中
    handleSelectionChange(val) {
      this.selectList = val.map(item => {
        return {
          alarm_id: item.alarm_id,
          vehicle_id: item.vehicle_id,
          alarm_type_code: item.alarm_type_code,
          alarm_type_class: item.alarm_type_class,
          alarm_date: item.alarm_date,
          terminal_no: item.terminal_no
        }
      })
    },
    // 批量处理
    batchProcessing() {
      if (this.selectList.length === 0) {
        return this.$warning('请选择报警！')
      }
      this.$refs.batchProcessingDialog.showDialog(this.selectList)
    },
    // 添加乘客超员报警
    uplodeAlarm() {
      this.$refs["PassengerOverloadAlarm"].showModal()
    },
    selectNodes(data, { checkedNodes }) {
      this.currentNodes = checkedNodes;
    },
    selectNodes1(data, { checkedNodes }) {
      this.currentNodes1 = checkedNodes;
    },
    dateStartChange(date) {
      if (this.query.endTime - date < 0) {
        this.query.endTime = moment(date)
          .add(1, "days")
          .subtract(1, "seconds")
          .toDate();
      }
      if (this.query.endTime - date >= 31 * 86400 * 1000) {
        this.query.endTime = new Date(+date + 31 * 86400 * 1000);
      }
    },
    dateEndChange(date) {
      if (date - this.query.startTime > 31 * 86400 * 1000) {
        this.query.startTime = new Date(date - 31 * 86400 * 1000);
      }
    },
    // 导出报警明细(后端导出)
    async exportDetailsExcel() {
      let params = {
        start_time: moment(this.query.startTime).format("YYYY-MM-DD HH:mm:ss"),
        end_time: moment(this.query.endTime).format("YYYY-MM-DD HH:mm:ss"),
        max_speed: this.query.maxSpeed,
        min_speed: this.query.minSpeed,
        count: this.query.size,
        page: this.query.page,
        type: this.activeTab === "vehicle" ? 4 : 3,
        alarmtype_list: this.alarmList,
        deal_type: this.query.dealTypeNew[0] == 1 ? this.query.dealTypeNew[1] : this.query.dealTypeNew[0],
        headers: this.tableSettingList.map(item => item.key)

      };
      const idList = this.activeTab === "vehicle"
          ? this.currentNodes
            .filter((item) => item.type === 4)
            .map((item) => item.id)
          : this.currentNodes1
            .filter((item) => item.type === 4)
            .map((item) => item.id);
      if (!idList.length) {
        this.$warning(this.$ct("messageInfo.0"));
        return;
      }
      if (this.activeTab === "vehicle") {
        params.vehicle_id = idList;
      } else {
        params.driver_id = idList;
      }
      // if (this.query.total >= 10000) {
      //     this.$info(this.$ct("messageInfo.1"));
      //     return;
      // }
      if(this.table.data.length === 0) return this.$warning("没有数据可以导出")
      let title = `${this.exportData.start}~${this.exportData.end}--${this.hasPermission('vehiclealarm:SGS') ? '报警数据' : '报警报表明细'}`
      this.reportLoading = true;
      await this.$utils.excelExport(
        "/ponysafety2/a/report/exportadasalarm",
        JSON.stringify(params),
        title + ".xlsx",
        true,
      );
      this.reportLoading = false;
    },
    // 导出报警统计(前端导出)
    async exportSummaryExcel() {
      if (!this.summaryTable.data.length) {
        this.$warning("没有数据可以导出");
        return;
      }
      let excelBody = [];
      let color = "@000000"
      let sheetName = ["单位@dept@8000@000000", "车牌号@plate_no@5000@000000"];
      this.summaryTable.data[0].alarms.forEach((item) => {
        let value = `${item.field}@${item.id}@8000${color}`;
        sheetName.push(value);
      });
      let resData = this.summaryTable.data
      resData.forEach((item, index) => {
        let array = [];
        item.dept = `${item.company_name} >> ${item.fleet_name}`
        item.alarms.forEach((count) => {
          item[count.id] = count.value
        });
        excelBody.push(item);
      });
      let fileName = `报警报表统计(${this.exportData.start} ~ ${this.exportData.end} )`
      let params = {
        sheetName: "报警统计报表",
        title: fileName,
        headers: sheetName,
        dataList: excelBody,
      }
      let paramsList = []
      paramsList.push(params)
      this.reportLoading = true;

      await this.$utils.jsExcelExport(JSON.stringify(paramsList), fileName + '.xlsx')
      this.reportLoading = false;
    },
    // 报警明细数据查询
    async getTableData(page = 1) {
      this.exportData.start = moment(this.query.startTime).format("YYYY-MM-DD HH:mm:ss")
      this.exportData.end = moment(this.query.endTime).format("YYYY-MM-DD HH:mm:ss")
      this.selectList = []
      let params = {
        start_time: moment(this.query.startTime).format("YYYY-MM-DD HH:mm:ss"),
        end_time: moment(this.query.endTime).format("YYYY-MM-DD HH:mm:ss"),
        max_speed: this.query.maxSpeed,
        min_speed: this.query.minSpeed,
        count: this.query.size,
        page: page,
        type: this.activeTab === "vehicle" ? 4 : 3,
        alarmtype_list: this.alarmList,
        deal_type: this.query.dealTypeNew[0] == 1 ? this.query.dealTypeNew[1] : this.query.dealTypeNew[0]
      };
      this.query.page = page;
      const idList =
        this.activeTab === "vehicle"
          ? this.currentNodes
            .filter((item) => item.type === 4)
            .map((item) => item.id)
          : this.currentNodes1
            .filter((item) => item.type === 4)
            .map((item) => item.id);
      if (!idList.length) {
        this.$warning(this.$ct("messageInfo.0"));
        return;
      }
      if (!this.alarmList.length)
        return this.$warning("请选择报警类型！");
      if (this.activeTab === "vehicle") {
        params.vehicle_id = idList;
      } else {
        params.driver_id = idList;
      }
      this.table.loading = true;
      this.table.data = [];
      this.alarmCount = {};
      try {
        this.$api.getAlarmCount(params).then((countRes) => {
          if (countRes.rs === 1) {
            let temp = {};
            countRes.alarmcount_list.forEach((item) => {
              temp[+item.name] = item.count;
            });
            this.alarmCount = temp;
          } else {
            this.alarmCount = {};
          }
        });
        let infoRes = await this.$api.getAlarmInfoPage(params);
        if (infoRes.rs === 1 && infoRes.alarmInfoList.length) {
          this.query.total = infoRes.count;
          this.table.data = infoRes.alarmInfoList;
        } else {
          this.query.total = 0;
          this.$message.warning("未查询到数据！");
        }
        this.$nextTick(() => {
          this.$refs["adasAlarmTable"].doLayout();
        });
        // 记录本次查询成功时，所切换到的是 车辆列表还是驾驶员列表
        this.alarmDetailLastTimeQueryData.queriedTabName = this.activeTab;
        // 记录选择的节点
        this.alarmDetailLastTimeQueryData.checkedAlarmNodes = Object.assign([], this.alarmList);
        // 记录报警选择使用的是 全部 还是 分组
        const asideAlarmRef = this.$refs[this.activeTab2 === "alarmDetails" ? "asideAlarm" : "asideAlarm1"];
        this.alarmDetailLastTimeQueryData.alarmType = asideAlarmRef.alarmType;
      } catch (e) {
        this.$error(this.$ct("messageInfo.2"));
      } finally {
        this.table.loading = false;
      }
    },
    // 报警统计查询
    async getSummaryTableData() {
      this.exportData.start = moment(this.query.startTime).format("YYYY-MM-DD HH:mm:ss")
      this.exportData.end = moment(this.query.endTime).format("YYYY-MM-DD HH:mm:ss")
      let params = {
        start_time: moment(this.query.startTime).format(
          "YYYY-MM-DD HH:mm:ss"
        ),
        end_time: moment(this.query.endTime).format("YYYY-MM-DD HH:mm:ss"),
        max_speed: this.query.maxSpeed,
        min_speed: this.query.minSpeed,
        alarmtype_list: this.alarmSummaryList,
        deal_type: this.query.dealTypeNew[0] == 1 ? this.query.dealTypeNew[1] : this.query.dealTypeNew[0]
        //   vehicle_id: [191],
        //   alarmtype_list: [110, 5, 57, 58, 301, 302, 306, 307, 10],
      };
      const idList =
        this.activeTab === "vehicle"
          ? this.currentNodes
            .filter((item) => item.type === 4)
            .map((item) => item.id)
          : this.currentNodes1
            .filter((item) => item.type === 4)
            .map((item) => item.id);
      if (this.activeTab === "vehicle") {
        params.vehicle_id = idList;
        params.type = 4;
      } else {
        params.driver_id = idList;
        params.type = 3;
      }
      if (!idList.length) return this.$warning("请选择车辆！");
      if (!this.alarmSummaryList.length)
        return this.$warning("请选择报警类型！");
      this.summaryTable.loading = true;
      this.columnNum = 1;
      this.summaryTable.data = [];
      this.pager.total = 0;
      this.alarmSummaryCount = {};
      this.$api.getAlarmCount(params).then((countRes) => {
        if (countRes.rs === 1) {
          let temp = {};
          countRes.alarmcount_list.forEach((item) => {
            temp[+item.name] = item.count;
          });
          this.alarmSummaryCount = temp;
        } else {
          this.alarmSummaryCount = {};
        }
      });
      let result = await this.$api.getAlarmSummaryInfoPage(params);
      if (!result || result.status !== 200) {
        this.$error(result.message);
        this.summaryTable.loading = false;
        return;
      }
      if (!result.data || !result.data.length) {
        this.$warning("未查询到数据");
        this.summaryTable.loading = false;
        return;
      }
      this.summaryTable.data = result.data;
      this.pager.total = result.data.length;
      this.columnNum = this.summaryTable.data[0].alarms.length;
      this.summaryTable.loading = false;
      this.$nextTick(() => {
        this.$refs["adasAlarmSummaryTable"].doLayout();
      });
      // 记录下当前查询成功时，所切换到的是 车辆列表还是驾驶员列表
      this.alarmSummaryLastTimeQueryData.queriedTabName = this.activeTab;
      this.alarmSummaryLastTimeQueryData.checkedAlarmNodes = Object.assign([], this.alarmSummaryList);
      // 记录报警选择使用的是 全部 还是 分组
      const asideAlarmRef = this.$refs[this.activeTab2 === "alarmDetails" ? "asideAlarm" : "asideAlarm1"];
      this.alarmSummaryLastTimeQueryData.alarmType = asideAlarmRef.alarmType;
    },
    // 报警选择节点获取
    handleAlarmChange(alarmList) {
      this.alarmList = alarmList;
    },
    // 报警统计节点获取
    handleAlarmChange2(alarmSummaryList) {
      console.log('触发', alarmSummaryList);
      this.alarmSummaryList = alarmSummaryList;
    },
    videoPlay(row) {
      if (row.is_video !== 0) {
        let date = moment(row.alarm_time, "YYYY-MM-DD HH:mm:ss").valueOf();
        this.$refs.player.play({
          alarmId: row.alarm_id,
          timestamp: date,
          label: row.plate_no,
          vehicleId: row.vehicle_id,
          terminalNo: row.terminal_no,
          alarmSerial: row.alarm_serial
        }, row.media_list);
      }
    },
    playedAction(alarmId) {
      let row = this.table.data.find((item) => item.alarm_id === alarmId);
      if (row) row.is_play = 1;
    },
    async checkQuery() {
      return;
    },
    //展开多选树节点
    checkCurrentVehicle(Id) {
      this.$refs["vehicleTree"].$refs.tree.setCurrentKey(Id);
      this.$refs["vehicleTree"].$refs.tree.setChecked(Id, true);
      this.$refs["vehicleTree"].$refs.tree.getNode(Id).expand(null, true);
    },
    getDetail(row) {
      this.$refs["alarmDetail"].showModal(row);
    },
    async handleJumpLink() {
      let parmas = this.$route.query
      if (!parmas || !parmas.vehicle_id) return;
      let vehicleId = JSON.parse(parmas.vehicle_id)
      await this.$refs["vehicleTree"].waitForInit;
      const $tree = this.$refs["vehicleTree"].$refs["tree"];
      this.$refs["vehicleTree"].$refs.tree.setCheckedKeys(vehicleId);
      if (vehicleId.length) {
        vehicleId.forEach((item) => {
          this.checkCurrentVehicle(item);
        });
      }
      if (parmas.start_time || parmas.end_time) {
        this.selectStartEndTime = [moment(parmas.start_time).valueOf(), moment(parmas.end_time).valueOf()];
        this.query.startTime = parmas.start_time
        this.query.endTime = parmas.end_time
      }

      this.currentNodes = $tree.getCheckedNodes();
      this.alarmList = JSON.parse(parmas.alarmtype_list)
      this.$refs.asideAlarm.$refs.allTree.setCheckedKeys(this.alarmList)
      // this.changeToDetail(parmas)
      this.$nextTick(async () => {
        if (this.currentNodes.length) {
          this.getTableData()
        }
        await this.$router.push('/home/<USER>')
      })
    }
  },
  async mounted() {
    this.checkQuery();
    this.handleJumpLink()
    this.dealTypeList = await this.$store.dispatch('dictionary/getFormatListByCode', 'event_deal_type');
  },
  activated() {
    this.handleJumpLink()
  }
};
</script>

<style scoped lang="scss">
.tip-text {
  margin-left: 10px;
  color: #7e8da5;
}

.alarm-detail-search {
  .query-top {
    height: calc(100% - 270px);
  }

  .query-bottom {
    margin-top: 5px;
    height: 260px;
    padding: 10px;

    .query-item {
      display: flex;
      height: 40px;
      line-height: 40px;
      justify-content: space-between;
      align-items: center;

      /deep/ .el-date-editor.el-input,
      .el-date-editor.el-input__inner {
        width: 100% !important;
      }

      >div {
        flex-grow: 1;
      }

      >span {
        font-size: 12px;
        white-space: nowrap;
        margin-right: 5px;
        margin-left: 5px;
      }
    }
  }

  .warning-hint {
    font-size: 12px;
    padding-left: 10px;
  }

  .disabled {
    color: darkgray;
    cursor: not-allowed;
  }
}
</style>

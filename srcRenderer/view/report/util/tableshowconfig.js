
//表格默认必须存在字段
const getTableSetting = [
    { name: '车牌号', key: 'plate_no', size: '100', align:"center", type: 0 },
    { name: '定位时间', key: 'gps_time', size: '150', align:"center", type: 0 },
    { name: '经度', key: 'lng', size: '80', align:"center", type: 0 },
    { name: '纬度', key: 'lat', size: '80', align:"center", type: 0 },
    { name: '油耗', key: 'oil_consum', size: '80', align:"center", type: 0 },
    { name: '转速', key: 'rota_speed', size: '80', align:"center", type: 0 },
    { name: 'VSS速度', key: 'pulse_speed', size: '80', align:"center", type: 0 },
    { name: '方向', key: 'dire', size: '80', align:"center", type: 0 },
    { name: 'ACC', key: 'acc', size: '80', align:"center", type: 0 },
    { name: '里程', key: 'mile', size: '100', align:"center", type: 0 },
    { name: '位置', key: 'location', size: '300', align:"left", type: 0 },
]
//可配置字段
const getTableSettingAll = [
    { name: '车牌号', key: 'plate_no', size: '100', align:"center", type: 0 },
    { name: '定位时间', key: 'gps_time', size: '150', align:"center", type: 0 },
    { name: '经度', key: 'lng', size: '80', align:"center", type: 0 },
    { name: '纬度', key: 'lat', size: '80', align:"center", type: 0 },
    { name: '速度', key: 'gps_speed', size: '80', align:"center", type: 0 },
    { name: 'VSS速度', key: 'pulse_speed', size: '80', align:"center", type: 0 },
    { name: '方向', key: 'dire', size: '80', align:"center", type: 0 },
    { name: 'ACC', key: 'acc', size: '80', align:"center", type: 0 },
    { name: '里程', key: 'mile', size: '100', align:"center", type: 0 },
    { name: '位置', key: 'location', size: '300', align:"left", type: 0 },
    { name: '原始里程', key: 'ori_mile', size: '80', align:"center", type: 0 },

    { name: '右转向灯信号', key: 'signal_right_light', size: '100', align:"center", type: 0 },
    { name: '左转向灯信号', key: 'signal_left_light', size: '100', align:"center", type: 0 },
    { name: '制动信号', key: 'signal_break', size: '100', align:"center", type: 0 },
    { name: '倒挡信号', key: 'signal_reverse', size: '100', align:"center", type: 0 },
    { name: '空挡信号', key: 'signal_neutral', size: '100', align:"center", type: 0 },
    { name: '离合器状态', key: 'signal_clutch', size: '100', align:"center", type: 0 },
    { name: '近光灯信号', key: 'signal_low_light', size: '100', align:"center", type: 0 },
    { name: '远光灯信号', key: 'signal_high_light', size: '100', align:"center", type: 0 },
    { name: '雾灯信号', key: 'signal_fog_light', size: '100', align:"center", type: 0 },
    { name: '示廓灯', key: 'signal_outline_light', size: '100', align:"center", type: 0 },
    { name: '喇叭信号', key: 'signal_horn', size: '100', align:"center", type: 0 },
    { name: '空调状态', key: 'signal_air', size: '100', align:"center", type: 0 },
    { name: '缓速器工作', key: 'signal_speed_less', size: '100', align:"center", type: 0 },
    { name: 'ABS工作', key: 'signal_abs', size: '100', align:"center", type: 0 },
    { name: '加热器工作', key: 'signal_heater', size: '100', align:"center", type: 0 },
    { name: 'IO状态', key: 'signal_io_status', size: '100', align:"center", type: 0 },

    { name: '油耗', key: 'oil_consum', size: '80', align:"center", type: 0 },
    { name: '转速', key: 'rota_speed', size: '80', align:"center", type: 0 },
    { name: '平均燃油经济性', key: 'fuelEconomy', size: '120', align:"center", type: 0 },
    { name: '车辆运输状态', key: 'table_trans_state', size: '110', align:"center", type: 0 },
    
    { name: '门状态', key: 'cold_door', size: '80', align:"left", type: 0 },
    { name: '压缩机状态', key: 'cold_compress', size: '100', align:"center", type: 0 },

    { name: '正反转状态', key: 'erp_rotate_state', size: '100', align:"center", type: 0 },
    { name: '开关状态', key: 'erp_rotate_on', size: '80', align:"center", type: 0 },
    { name: '反转圈数', key: 'erp_rotate_num', size: '80', align:"center", type: 0 },
    // {name: '日里程', key: 'dayMile', size: '120', align: "center", type: 0},

    { name: '篷布状态', key: 'muck_pb', size: '100', align:"center", type: 0 },
    { name: '举升状态', key: 'muck_lift', size: '100', align:"center", type: 0 },
    { name: '空重载状态', key: 'muck_load', size: '100', align:"center", type: 0 },
    { name: '锁车', key: 'muck_lock_status', size: '100', align:"center", type: 0 },
    { name: '限速', key: 'muck_speed_limit', size: '100', align:"center", type: 0 },
    { name: '限举', key: 'muck_lift_limit', size: '100', align:"center", type: 0 },
    { name: '锁车原因', key: 'muck_lift_limit', size: '100', align:"center", type: 0 },
    { name: '限速原因', key: 'muck_speed_reason', size: '100', align:"center", type: 0 },
    { name: '限举原因', key: 'muck_lift_reason', size: '100', align:"center", type: 0 },

    { name: '闪灯状态', key: 'lamp_status', size: '100', align:"center", type: 0 },
    { name: '打卡状态', key: 'lamp_pass', size: '100', align:"center", type: 0 },
    { name: '紧急状态', key: 'lamp_emergency', size: '100', align:"center", type: 0 },
    { name: '巡逻状态', key: 'lamp_patrol', size: '100', align:"center", type: 0 },
    { name: '肩灯状态', key: 'lamp_power', size: '100', align:"center", type: 0 },
    { name: '曲率半径', key: 'curvature', size: '100', align:"center", type: 0 },

    { name: 'CANBOX通讯状态', key: 'canbox_message_status', size: '140', align:"center", type: 0 },
    { name: '发动机ECU通讯状态', key: 'engine_ecu_status', size: '140', align:"center", type: 0 },
    { name: '发动机工作状态', key: 'engine_work_status', size: '140', align:"center", type: 0 },
    { name: 'AEB主机状态', key: 'aeb_host_status', size: '140', align:"center", type: 0 },
    




]
const JHtable = JSON.parse("[{\"name\":\"运输单号\",\"key\":\"transSerial\",\"size\":\"100\",\"align\":\"center\",\"type\":0},{\"name\":\"运单状态\",\"key\":\"transStatus\",\"size\":\"100\",\"align\":\"center\",\"type\":0},{\"name\":\"车辆信息\",\"key\":\"plateNo\",\"size\":\"100\",\"align\":\"center\",\"type\":0},{\"name\":\"司机姓名\",\"key\":\"driverName\",\"size\":\"100\",\"align\":\"center\",\"type\":0},{\"name\":\"司机手机号\",\"key\":\"driverPhone\",\"size\":\"150\",\"align\":\"center\",\"type\":0},{\"name\":\"出发站点\",\"key\":\"startPoint\",\"size\":\"150\",\"align\":\"center\",\"type\":0},{\"name\":\"到达站点\",\"key\":\"endPoint\",\"size\":\"150\",\"align\":\"center\",\"type\":0},{\"name\":\"运营公司\",\"key\":\"companyName\",\"size\":\"150\",\"align\":\"center\",\"type\":0},{\"name\":\"去程线路\",\"key\":\"goLine\",\"size\":\"150\",\"align\":\"center\",\"type\":0},{\"name\":\"返程线路\",\"key\":\"returnLine\",\"size\":\"150\",\"align\":\"center\",\"type\":0},{\"name\":\"线路总里程(km)\",\"key\":\"lineMileView\",\"size\":\"150\",\"align\":\"center\",\"type\":0},{\"name\":\"要求到达时间\",\"key\":\"planArriveTime\",\"size\":\"150\",\"align\":\"center\",\"type\":0},{\"name\":\"实际出发时间\",\"key\":\"goDepartureTime\",\"size\":\"150\",\"align\":\"center\",\"type\":0},{\"name\":\"实际到达时间\",\"key\":\"goArriveTime\",\"size\":\"150\",\"align\":\"center\",\"type\":0},{\"name\":\"实际返程时间\",\"key\":\"returnDepartureTime\",\"size\":\"150\",\"align\":\"center\",\"type\":0},{\"name\":\"实际回厂时间\",\"key\":\"returnArriveTime\",\"size\":\"150\",\"align\":\"center\",\"type\":0},{\"name\":\"实际总里程(km)\",\"key\":\"driveMileView\",\"size\":\"150\",\"align\":\"center\",\"type\":0},{\"name\":\"总重合率\",\"key\":\"lineRate\",\"size\":\"80\",\"align\":\"center\",\"type\":0},{\"name\":\"装车时间\",\"key\":\"loadTime\",\"size\":\"150\",\"align\":\"center\",\"type\":0},{\"name\":\"司机确认出发时间\",\"key\":\"confirmDepartureTime\",\"size\":\"150\",\"align\":\"center\",\"type\":0},{\"name\":\"实际送达时间\",\"key\":\"goArriveTime\",\"size\":\"150\",\"align\":\"center\",\"type\":0},{\"name\":\"总时长\",\"key\":\"durationView\",\"size\":\"150\",\"align\":\"center\",\"type\":0},{\"name\":\"是否偏移\",\"key\":\"offsetView\",\"size\":\"150\",\"align\":\"center\",\"type\":0},{\"name\":\"偏移占比\",\"key\":\"offsetRate\",\"size\":\"150\",\"align\":\"center\",\"type\":0},{\"name\":\"偏移里程(km)\",\"key\":\"offsetMileView\",\"size\":\"150\",\"align\":\"center\",\"type\":0},{\"name\":\"偏移时长\",\"key\":\"offsetDurationView\",\"size\":\"150\",\"align\":\"center\",\"type\":0},{\"name\":\"偏移次数\",\"key\":\"offsetTimes\",\"size\":\"150\",\"align\":\"center\",\"type\":0}]")
/**
 * @description 获取两个数据的差集
 */
 const getArrayDifference = (list, whole) => {
    return list.concat(whole).filter((v, i, arr) => {
        return arr.indexOf(v) === arr.lastIndexOf(v)
    })
}
export {
    getTableSetting,
    getTableSettingAll,
    getArrayDifference,
    JHtable
}

<template>
  <Layout class="carFuelAnalysis" :contentLoading="table.loading" :has-color="true">
    <template slot="aside">
      <div class="query-top">
        <ElementTree ref="vehicleTree" :checkMode="true" type="vehicle" @check="onCheckVehicle"></ElementTree>
      </div>
      <div class="query-bottom bg bg--lighter">
        <StartEndTime itemHeight="35" v-model="selectStartEndTime" valueFormat="timestamp" timeType="date" :isLimit="true"
                      :timeLimitForCur="1">
        </StartEndTime>
        <div class="query-item">
          <span>查询类型</span>
          <el-select v-model="queryList.dimension" placeholder="选择查询类型" @change="clear()">
            <el-option v-for="item in queryTypeList" :key="item.value" :label="item.name" :value="item.value"></el-option>
          </el-select>
        </div>
        <div class="query-item brand">
          <span>品牌</span>
          <!--					<el-select-->
          <!--							v-model="queryList.brand"-->
          <!--							placeholder="选择品牌">-->
          <!--						<el-option v-for="item in dictionary.vehicleBrand" :key="item.id" :label="item.name"-->
          <!--						           :value="item.name"></el-option>-->
          <!--					</el-select>-->
          <DictionarySelect v-model="queryList.brand" code="vehicle_brand" placeholder="请选择品牌" @change="modelChange">
          </DictionarySelect>
        </div>
        <div class="query-item brand">
          <span>车型</span>
          <el-select v-model="queryList.model" placeholder="请选择车型" clearable>
            <el-option v-for="item in dictionary.vehicleModel" :key="item.label" :label="item.label"
                       :value="item.value"></el-option>
          </el-select>
        </div>


        <div class="query-item">
          <el-button size="mini" type="primary" style="width: 100%;" @click="search"
                     :loading="table.loading">查询
          </el-button>
        </div>
      </div>
    </template>

    <template slot="query">
      <div class="query-item">
        <el-button type="primary" @click="exportExcel" :loading="exportLoading">导出</el-button>
        <el-popover placement="bottom" width="250" trigger="hover">
          <p>本页面里程根据配置统计发动机里程/时长或GPS里程/时长，可能与其他页面里程/时长不一致</p>
          <i :class="['pony-iconv2 pony-bangzhu']" style="margin-left:10px; cursor: pointer;" slot="reference"></i>
        </el-popover>
        <span style="font-size:12px;margin-left:15px;color:#ff5359"
              v-if="currentQuery.currentColumnList != 'IDLE'">此页面油耗均为行驶油耗</span>
      </div>
      <div class="break-item"></div>
      <div class="query-item">
        <el-pagination small background :current-page.sync="table.page" :page-size="table.size"
                       layout="prev, pager, next, total" :total="table.list.length">
        </el-pagination>
      </div>
    </template>

    <el-table ref="table" class="el-table--ellipsis el-table--radius" slot="content" border stripe highlight-current-row
              size="mini" :data="formatVehicleList" @sort-change="sortChange" height="100%" style="width: 100%">
      <el-table-column type="index" :index="(index) => index + 1 + pageStart" label="序号" width="50"
                       fixed></el-table-column>
      <el-table-column label="操作" min-width="70px">
        <template slot-scope="{row}">
          <span class="pony-iconv2 pony-xiangqing primary" @click="detailChart(row)" title="详情"></span>
          <span class="pony-iconv2 pony-rili primary" @click="detailCalendar(row)" title="油耗日历"></span>
        </template>
      </el-table-column>
      <el-table-column label="单位" min-width="120px" show-overflow-tooltip>
        <template slot-scope="{row}">
          <span style="display: flex; align-items: flex-start">{{
              row.companyName + '>>' + row.deptName
            }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="plateNo" label="车牌号" show-overflow-tooltip align="center" width="100"></el-table-column>
      <el-table-column prop="brand" label="品牌" show-overflow-tooltip width="100"></el-table-column>
      <!-- sortable="custom" -->
      <el-table-column prop="mile" :label="'总里程 |（km）'" :render-header="renderheader" sortable="custom"
                       min-width="100px"></el-table-column>
      <el-table-column prop="time" :label="'总时长 |（h）'" :render-header="renderheader" sortable="custom"></el-table-column>
      <el-table-column prop="fuel" :label="'总油耗 |（L）'" :render-header="renderheader" sortable="custom"></el-table-column>
      <el-table-column prop="fuelHkm" :label="'百公里总油耗 |（L/100km）'" :render-header="renderheader" min-width="120px"
                       sortable="custom">
        <template slot-scope="{row}">
          <div class="column">
            <span>{{ row.fuelHkm === '-' ? '0' : row.fuelHkm }}</span>
            <el-popover placement="bottom" width="270" trigger="hover" v-if="row.fuelHkm >= 50  && row.mile <= 50">
              <p>当里程值过小时、油耗计算会存在误差</p>
              <i :class="['pony-iconv2 pony-bangzhu']" style="margin-left:10px; cursor: pointer;" slot="reference"></i>
            </el-popover>
          </div>
        </template>
      </el-table-column>

      <el-table-column :label="searchType + item" v-for="(item, index) in columnList[currentQuery.currentColumnList]"
                       :key="index">
        <!-- 循环套循环竟然会跟不上，还不知道为啥，初步猜测可能是因为浏览器辨识不到它（数组）的变化，认为没变，所以页面没更新，加了一个强制更新-->
        <span v-if="queryList.dimension !== 'IDLE'">
          <el-table-column :label="it.name" v-for="it in ziduanList" :key="it.key" :render-header="renderheader"
                           :min-width="it.size ? it.size : ''" show-overflow-tooltip>
            <template slot-scope="{row}">
              <span>{{
                  !row.dimension[item] ? '' : row.dimension[item][it.key] === '-' ? '0' : row.dimension[item][it.key]
                }}</span>
            </template>
          </el-table-column>
        </span>
        <span v-else>
          <el-table-column :label="it.name" v-for="it in IDLEList" :key="it.key" :render-header="renderheader"
                           :min-width="it.size ? it.size : ''" show-overflow-tooltip>
            <template slot-scope="{row}">
              <span>{{
                  !row.dimension[item] ? '' : row.dimension[item][it.key] === '-' ? '0' : row.dimension[item][it.key]
                }}</span>
            </template>
          </el-table-column>
        </span>
      </el-table-column>
    </el-table>
    <DetailCharts ref="detailCharts" :currentColumnList="currentQuery.currentColumnList"></DetailCharts>
  </Layout>
</template>

<script>

import moment from "moment";
import DetailCharts from './components/DetailChart'
import DictionarySelect from "../../../components/common/DictionarySelect";
import StartEndTime from "@/components/common/StartEedTime";

const ExportJsonExcel = require('js-export-excel')
import {export_json_to_excel} from './util/Export2Excel'

export default {
  name: "carFuelAnalysis",
  components: {DetailCharts, DictionarySelect, StartEndTime},
  data() {
    return {
      queryList: {
        start: moment().subtract(1, 'days').format("YYYY-MM-DD"),
        end: moment().subtract(1, 'days').format("YYYY-MM-DD"),
        brand: '',
        model: '',
        dimension: 'IDLE'
      },
      currentQuery: {
        currentColumnList: 'IDLE',
        start: '',
        end: ''
      },
      columnList: {
        HEIGHT: ['0~200米', '200~500米', '500~2000米', '>2000米'],
        SPEED: ['0~20km/h', '20~40km/h', '40~60km/h', '60~80km/h', '80~100km/h', '>100km/h'],
        LOAD: ['重载', '轻载', '预冷'],
        IDLE: ['行驶', '怠速']
      },
      ziduanList: [
        {
          name: '里程 |（km）',
          key: 'mile'
        },
        {
          name: '时长 |（h）',
          key: 'time'
        },
        {
          name: '油耗 |（L）',
          key: 'fuel'
        },
        {
          name: '百公里油耗 |（L/100km）',
          key: 'fuelHkm',
          size: '100px'
        }
      ],
      IDLEList: [
        {
          name: '时长 |（h）',
          key: 'time'
        },
        {
          name: '油耗 |（L）',
          key: 'fuel'
        },
        {
          name: '百公里油耗 |（L/100km）',
          key: 'fuelHkm',
          size: '100px'
        }
      ],
      queryTypeList: [
        {
          name: '海拔',
          value: 'HEIGHT'
        },
        {
          name: '速度',
          value: 'SPEED'
        },
        {
          name: '载重',
          value: 'LOAD'
        },
        {
          name: '怠速',
          value: 'IDLE'
        },
      ],
      //excel那个列的顺序，合并单元格时要用,从H后面开始，前面的是表头固定的
      excelList: ['I1:L1', 'M1:P1', 'Q1:T1', 'U1:X1', 'Y1:AB1', 'AC1:AF1'],
      dictionary: {
        vehicleBrand: [],
        vehicleModel: [],
      },
      vehicleIdList: [],
      table: {
        loading: false,
        list: [],
        page: 1,
        size: 30,
        sort: '',
        dimensionList: []
      },
      searchType: '',
      selectStartEndTime: [
        moment().subtract(1, 'days').startOf("day").valueOf(),
        moment().subtract(1, 'days').endOf("day").valueOf(),
      ],
      exportLoading: false
    }
  },
  computed: {
    pageStart() {
      return (this.table.page - 1) * this.table.size
    },
    formatVehicleList() {
      return this.table.list.slice(this.pageStart, this.pageStart + this.table.size)
    }
  },
  watch: {
    'selectStartEndTime': function (newVal, oldVal) {
      this.queryList.start = moment(newVal[0]).format("YYYY-MM-DD")
      this.queryList.end = moment(newVal[1]).format("YYYY-MM-DD")
    }
  },
  methods: {
    sortCurrentProp({column, prop, order}) {
      // 字符串排序
      this.table.list.sort((a, b) => {
        switch (order || "default") {
          case "default":
            return;
          case "ascending":
            if (a[prop] > b[prop]) return 1;
            if (a[prop] == b[prop]) return 0;
            if (a[prop] < b[prop]) return -1;
          case "descending":
            if (a[prop] > b[prop]) return -1;
            if (a[prop] == b[prop]) return 0;
            if (a[prop] < b[prop]) return 1;
        }
      });
    },
    clear() {
      this.table.list = []
    },
    async modelChange() {
      let result = await this.$api.getCodeTypeList({
        code: 'vehicle_model',
        description: this.queryList.brand,
      })
      this.queryList.model = ''
      this.dictionary.vehicleModel = result.map(item => {
        const value = parseInt(item.value);
        return {
          label: item.name,
          value: isNaN(value) ? item.value : value,
        }
      })
    },
    // 导出
    exportExcel() {
      let multiHeader = [
        ['序号', '单位', '车牌号', '品牌', '总里程(km)', '总时长(h)', '总油耗(L)', '总百公里油耗(L/km)'],
      ];
      let tHeader = ['', '', '', '', '', '', '', ''];
      // 进行所有表头的单元格合并
      let merges = [
        'A1:A2', 'B1:B2', 'C1:C2', 'D1:D2', 'E1:E2', 'F1:F2', 'G1:G2', 'H1:H2'
      ];
      this.columnList[this.currentQuery.currentColumnList].forEach((item, index) => {
        multiHeader[0].push(this.searchType + item, '', '', '')
        tHeader.push('里程(km)', '时长(h)', '油耗(L)', '百公里油耗(L/100km)')
        merges.push(this.excelList[index])
      })
      this.exportLoading = true
      let excelBody = []
      this.table.list.forEach((item, index) => {
        let array = []
        array.push(
          index + 1,
          item.companyName + '>>' + item.deptName,
          item.plateNo,
          item.brand,
          item.mile,
          item.time,
          item.fuel,
          item.fuelHkm,
        )
        this.columnList[this.currentQuery.currentColumnList].forEach(current => {
          this.ziduanList.forEach(it => {
            array.push(item.dimension[current][it.key])
          })
        })
        excelBody.push(array)
      })
      export_json_to_excel({
        multiHeader, // 这里是第一行的表头
        // multiHeader2, // 这里是第二行的表头
        header: tHeader, // 这里是第三行的表头
        data: excelBody,
        filename: '油耗分析',
        merges,
      });
      this.exportLoading = false
    },
    // 表头换行
    renderheader(h, {column, $index}) {
      return h('span', {}, [
        h('span', {}, column.label.split('|')[0]),
        h('br'),
        h('span', {}, column.label.split('|')[1])
      ]);

    },
    // async readyDictionary() {
    // 	let apiList = [
    // 		this.$api.getSysDictByCode({code: 'vehicle_brand'}),
    // 		this.$api.getSysDictByCode({code: 'vehicle_model'})
    // 	]
    // 	let result = await Promise.all(apiList)
    // 	if (!result || !result.length) {
    // 		this.$warning('读取必要字典出错！请刷新重试')
    // 		return
    // 	}
    // 	this.dictionary.vehicleBrand = result[0] || []
    // 	this.dictionary.vehicleModel = result[1] || []
    // },
    sortChange({column, prop, order}) {
      // 字符排序
      // this.table.list.sort((a, b) => {
      //     switch (order || 'default') {
      //         case 'default':
      //             return
      //         case 'ascending':
      //             if(a[prop] > (b[prop])) return 1
      //             if(a[prop] == (b[prop])) return 0
      //             if(a[prop] < (b[prop])) return -1
      //         case 'descending':
      //             if(a[prop] > (b[prop])) return -1
      //             if(a[prop] == (b[prop])) return 0
      //             if(a[prop] < (b[prop])) return 1
      //     }
      // })
      // 纯数字排序
      this.table.list.sort((a, b) => {
        if (prop == 'fuelHkm') {
          if (a[prop] == '-') {
            a[prop] = 0
          }
          if (b[prop] == '-') {
            b[prop] = 0
          }
        }
        switch (order || 'default') {
          case 'default':
            return
          case 'ascending':
            return a[prop] - b[prop];
          case 'descending':
            return b[prop] - a[prop];
        }
      })
    },
    //图表详情
    detailChart(row) {
      this.$refs.detailCharts.showModel(row)
    },

    // 日历详情
    detailCalendar(row) {
      let {vehicleId, start, end} = row
      let needList = {
        vehicleId,
        start,
        end
      }
      this.$router.push({
        path: '/home/<USER>',
        query: needList
      })
    },
    // 树点击事件
    onCheckVehicle(data, {checkedNodes}) {
      let nodes = checkedNodes.filter(item => item.type == 4);
      this.vehicleIdList = nodes.map(item => Number(item.id));
    },
    // 搜索数据
    async search() {
      if (this.vehicleIdList.length === 0) {
        return this.$message.warning('请选择车辆！')
      }
      if (!this.queryList.dimension) {
        return this.$message.warning('请选择查询类型！')
      }
      this.table.loading = true
      const res = await this.$api.fuelconsumptionNew({
        ...this.queryList,
        vehicleIdList: this.vehicleIdList,
      })
      if (res.status !== 200) {
        this.table.loading = false
        return this.$message.warning('查询出错!')
      }
      if (!res.data.length) {
        this.table.list = []
        this.table.loading = false
        return this.$message.warning('未查询到相关数据!')
      }
      this.table.list = res.data
      //给当前查询数据类型赋值
      this.currentQuery.currentColumnList = this.queryList.dimension
      this.currentQuery.start = this.queryList.start
      this.currentQuery.end = this.queryList.end

      this.$nextTick(() => {
        const res = this.queryTypeList.filter(item => item.value === this.queryList.dimension)
        this.searchType =res[0].value!='IDLE' ?res[0].name + '  ' + '·' + '  ':""
        this.$refs.table.doLayout()
        this.$forceUpdate()
      })
      this.table.loading = false
    }
  }
}
</script>

<style lang='scss' scoped>
.carFuelAnalysis {

  .query-top {
    height: calc(100% - 265px);
  }

  .query-bottom {
    margin-top: 5px;
    padding: 10px;

    .query-item {
      display: flex;
      height: 40px;
      //line-height: 40px;
      justify-content: space-between;
      align-items: center;


      > span {
        display: inline-block;
        width: 46px;
        font-size: 12px;
        white-space: nowrap;
        margin-right: 10px;
      }

      > div {
        flex: 1;
      }

      .table-list {
        width: 100%;
        height: 28px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        box-sizing: border-box;
        background-color: var(--background-color-base);
        border-radius: 4px;
        border: 1px solid var(--border-color-base);
        color: (--border-color-lighter);
        padding: 0 10px;
        line-height: 28px;
        font-size: 12px;

        .table-num {
          width: 75px;
          height: 20px;
          background-color: var(--border-color-light);
          border: 1px solid var(--border-color-extra-light);
          border-radius: 5px;
          line-height: 20px;
          padding: 0 5px;
        }
      }

    }

    .brand {
      span {
        text-align-last: justify;
        text-align: justify;
      }
    }
  }

  .el-table {
    .primary {
      color: var(--color-primary);
    }
  }

  .column {
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
</style>

<template>
    <Layout :has-color="true" 
        :contentLoading="contentLoading"
        class="refidreport">

        <template slot="aside">
            <div class="query-top">
                <!-- <ZtreeMatics style="padding: 10px;" treeType="vehicle" ref="vehicleZtree"></ZtreeMatics> -->
                <ElementTree type="vehicle" ref="vehicleTree" :checkMode="true" @check="selectNodes" node-key="id">
                </ElementTree>
            </div>
            <div class="query-bottom bg bg--lighter">
                <StartEndTime 
                    :title="['开始时间', '结束时间']"
                    v-model="timeSelect"
                    valueFormat="timestamp">
                </StartEndTime>
                <div class="query-item">
                    <el-button size="mini" type="primary" style="width: 100%; margin-top: 10px"
                        @click="searchVechicleTrack">开始查询
                    </el-button>
                </div>
            </div>
        </template>

        <template slot="query">
            <div class="query-item">
                <el-button type="primary" @click="exportTerminalExcle" :loading="reportLoading">导出</el-button>
            </div>
            <div class="break-item"></div>
            <div class="query-item">
                <el-pagination small background
                    :current-page.sync="table.page"
                    :page-size="table.size"
                    :total="table.list.length"
                    layout="prev, pager, next, total">
                </el-pagination>
            </div>
        </template>

        <template slot="content">
            <el-table
                ref="table"
                class="el-table--ellipsis el-table--radius"
                border stripe highlight-current-row
                :data="formatList"
                height="100%" style="width: 100%;">
                <el-table-column width="60" type="index" align="center" :index="(index) => index + 1 + pageStart" label="序号"></el-table-column>
                <el-table-column prop="plateNo" label="车牌号" min-width="100" show-overflow-tooltip></el-table-column>
                <el-table-column prop="grossWeight" label="毛重" min-width="90" show-overflow-tooltip>
                    <template slot-scope="scope" >
                        <span v-if="scope.row.grossWeight != null">{{scope.row.grossWeight}}kg</span>
                        <span v-else>-</span>
                    </template>
                </el-table-column>
                <el-table-column prop="tareWeight" label="皮重" min-width="90" show-overflow-tooltip>
                    <template slot-scope="scope" >
                        <span v-if="scope.row.tareWeight != null">{{scope.row.tareWeight}}kg</span>
                        <span v-else>-</span>
                    </template>
                </el-table-column>
                <el-table-column prop="weight" label="净重" min-width="90" show-overflow-tooltip>
                    <template slot-scope="scope" >
                        <span v-if="scope.row.weight != null">{{scope.row.weight}}kg</span>
                        <span v-else>-</span>
                    </template>
                </el-table-column>
                <el-table-column prop="startTime" label="开始时间" min-width="130"></el-table-column>
                <el-table-column prop="endTime" label="结束时间" min-width="130"></el-table-column>
                <el-table-column label="单位" prop="company" show-overflow-tooltip header-align="center" align="left" min-width="250"></el-table-column>
                <el-table-column prop="rfid" label="标签ID号" min-width="180" show-overflow-tooltip></el-table-column>
                <el-table-column prop="terminalNo" label="设备号" min-width="100" show-overflow-tooltip></el-table-column>
                <el-table-column prop="rfidPoint" label="收集点" min-width="150" show-overflow-tooltip></el-table-column>

                <el-table-column prop="location" label="地址" show-overflow-tooltip header-align="center" align="left" min-width="300"></el-table-column>
            </el-table>
        </template>

    </Layout>
</template>

<script>
import StartEndTime from '@/components/common/StartEedTime'
const ExportJsonExcel = require('js-export-excel')
export default {
    name: 'RefidReport',
    components: { StartEndTime },
    data () {
        return {
            contentLoading: false,

            table: {
                page: 1,
                size: 30,
                list: []
            },

            queryList: {
                startTime: '',
                endTime: '',
                // vehicleIds: [3873],
                vehicleIds: []
            },

            timeSelect: [moment().subtract(1, 'day').startOf('day').valueOf(), moment().endOf('day').valueOf()],
            reportLoading:false
        };
    },

    computed: {
        pageStart() {
            return (this.table.page - 1) * this.table.size
        },
        formatList() {
            return this.table.list.slice(this.pageStart, this.pageStart + this.table.size)
        },
    },

    mounted() {

    },
    activated(){
        this.handleJumpLink()
    },
    methods: {
        async handleJumpLink() {
            let parmas = this.$route.query;
            if (!parmas || !parmas.vehicleId) return;
            if (parmas.startTime) {
                if (isFinite(parmas.startTime)) {
                    this.timeSelect = [
                        moment(+parmas.startTime),
                        moment(+parmas.endTime),
                    ];
                } else {
                    this.timeSelect = [
                        moment(parmas.startTime),
                        moment(parmas.endTime),
                    ];
                }
            }
            this.queryList.vehicleIds = [parmas.vehicleId]
            await this.$refs["vehicleTree"].waitForInit;
            this.$refs["vehicleTree"].$refs.tree.setCheckedKeys([parmas.vehicleId]);
            this.$refs["vehicleTree"].$refs.tree.getNode(parmas.vehicleId).expand(null, true);
            this.$nextTick(async () => {
                await this.searchVechicleTrack(true);
                await this.$router.push("/home/<USER>");
            });
            },
        selectNodes(current, { checkedNodes }) {
        this.queryList.vehicleIds = checkedNodes
        .filter((item) => item.type == 4)
        .map((item) => item.id);
        },
        async searchVechicleTrack() {
            
            if(!this.queryList.vehicleIds.length) {
                this.$warning('请选择查询的车辆')
                return
            }
            Object.assign(this.queryList, {
                startTime: this.timeSelect[0],
                endTime: this.timeSelect[1]
            })

            this.contentLoading = true
            this.table.page = 1
            this.table.list = []
            let result = await this.$api.queryRfid(this.queryList)

            if(!result || result.status != 200) {
                this.contentLoading = false
                this.$error(result.message ||  '查询失败')
                return
            }
            if(!result.data.length) {
                this.contentLoading = false
                this.$warning('未查询到数据')
                return
            }
            this.table.list = result.data.sort((a,b)=>b.startTime - a.startTime)
            this.table.list.forEach(item => {
                item.startTime = this.TimeFormat(item.startTime)
                item.endTime = this.TimeFormat(item.endTime)
            })
            this.$nextTick(() => {
                this.$refs['table'].doLayout()
            })
            this.contentLoading = false
        },

       async exportTerminalExcle() {
            if (this.table.list.length == 0) {
                this.$warning("没有数据可以导出");
                return;
            }
             let vehicle = ""
            if(this.queryList.vehicleIds.length==1){
                  vehicle =   this.queryList.vehicleIds[0]
            }
            let sheetName =  [
                            "单位@company@10000@000000",
                            "车牌号@plateNo@8000@000000",
                            "设备号@terminalNo@12000@000000",
                            "开始时间@startTime@10000@000000",
                            "结束时间@endTime@10000@000000",
                            "标签ID号@rfid@10000@000000",
                            "称重(kg)@formatweight@8000@000000",
                            "毛重(kg)@formatgrossWeight@8000@000000",

                            "皮重(kg)@formattareWeight@8000@000000",

                            "地址@location@14000@000000",

                        ]
						let params={}
						let paramsList = [];
            let resData = this.table.list.map(item=>{
                    item.formatweight = item.weight != null ? item.weight  +'kg' : '-'
                    item.formatgrossWeight = item.grossWeight != null ? item.grossWeight  +'kg' : '-'
                    item.formattareWeight = item.tareWeight != null ? item.tareWeight  +'kg' : '-'
                    return item
            });
            let fileName=`${moment(this.queryList.startTime).format(
                    "YYYY-MM-DD HH:mm:ss"
                )} ~ ${moment(this.queryList.endTime).format(
                    "YYYY-MM-DD HH:mm:ss"
                )}--${vehicle}RFID记录`
            let title = `${vehicle}RFID记录(${moment(this.queryList.startTime).format(
                    "YYYY-MM-DD HH:mm:ss"
                )} ~ ${moment(this.queryList.endTime).format(
                    "YYYY-MM-DD HH:mm:ss"
                )} )`
								params = {
									sheetName: "RFID记录",
									title: title,
									headers:sheetName,
									dataList: resData,
								}
								paramsList.push(params)
                    this.reportLoading = true
						 await this.$utils.jsExcelExport(JSON.stringify(paramsList),fileName+'.xlsx')
                         this.reportLoading = false
            
            // if(!this.table.list.length) {
            //     this.$warning('没有数据可以导出')
            //     return
            // }
            // let keyList = ['company', 'plateNo', 'terminalNo', 'startTime', 'endTime', 'rfid', 'weight','location']
            // let excelBody = []
            // this.table.list.forEach((item, index) => {
            //     let array = [index + 1].concat(keyList.map(prop => {
            //         if(prop=='weight'){
            //             item[prop] = item[prop] != null ? item[prop] +'kg' : '-'
            //         }
            //        return  item[prop]
                    
            //         }))
                
            //     excelBody.push(array)
            // })
            // let options = {
            //     fileName: `${ this.TimeFormat(this.queryList.startTime)} ~ ${ this.TimeFormat(this.queryList.endTime) } RFID记录`,
            //     datas: [
            //         {
            //             sheetData: excelBody,
            //             sheetHeader: ['序号', '单位', '车牌号', '设备号', '开始时间', '结束时间', '标签ID号','称重', '地址'],
            //             columnWidths: ['3'].concat(keyList.map(item => '8'))
            //         }
            //     ]
            // }
            // ExportJsonExcel(options).saveExcel();
        }
    }
}

</script>

<style lang='scss' scoped>
.refidreport {
    &__detail {
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 50px;
        padding: 10px;
        background-color: var(--border-color-lighter);

        span {
            color: var(--color-text-placeholder)
        }
    }

    .query-top {
        height: calc(100% - 145px);
    }
    .query-bottom {
        margin-top: 5px;
        padding: 10px;

        .query-item {
            display: flex;
            height: 40px;
            line-height: 40px;
            justify-content: space-between;
            align-items: center;

            > span {
                font-size: 12px;
                white-space: nowrap;
                margin-right: 5px;
            }
        }
    }
}
</style>
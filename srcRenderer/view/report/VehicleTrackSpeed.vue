<template>
    <Layout :has-color="true" :contentLoading="table.loading">

        <template slot="aside">
            <!-- <ZtreeMatics style="padding: 10px;" treeType="vehicle" ref="vehicleZtree"></ZtreeMatics> -->
             <ElementTree type="vehicle" ref="vehicleTree" :checkMode="true" @check="selectNodes" node-key="id">
            </ElementTree>
        </template>

        <template slot="query">
            <StartEndTime
          itemHeight="35"
          v-model="selectStartEndTime"
          valueFormat="timestamp"
          timeType="datetime"
          :isLimit="true"
           showType="heng"
        >
        </StartEndTime>
            <!-- <div class="query-item">
                <span>{{$ct('timeFrame')}}</span>
                <el-date-picker
                    @change="dateStartChange"
                    v-model="queryList.begin_time"
                    type="datetime" value-format="yyyy-MM-dd HH:mm:ss"
                    :clearable="false"
                    :picker-options="pickerOptions">
                </el-date-picker>
                <el-date-picker
                    @change="endDateStartChange"
                    style="margin-left: 10px"
                    v-model="queryList.end_time"
                    type="datetime" value-format="yyyy-MM-dd HH:mm:ss"
                    :clearable="false"
                    :picker-options="endDatePickerOptions">
                </el-date-picker>
            </div> -->
            <div class="query-item">
                <span>{{$ct('overspeedDuration')}}</span>
                <el-select v-model="queryList.sli_continue" >
                    <el-option v-for="(item, index) in selectDesc.time" :key="index"
                        :value="item.value" :label="item.name">
                    </el-option>
                </el-select>
            </div>
            <div class="query-item">
                <span>{{$ct('speedLimit')}}</span>
                <el-select v-model="queryList.sli_rule_values" multiple collapse-tags>
                    <el-option v-for="(item, index) in selectDesc.limit" :key="index"
                        :value="item.value" :label="item.name">
                    </el-option>
                </el-select>
            </div>
            <div class="query-item">
                <span>{{$ct('overspeedRate')}}</span>
                <el-select v-model="queryList.sli_ratio" >
                    <el-option v-for="(item, index) in selectDesc.rule" :key="index"
                        :value="item.value" :label="item.name">
                    </el-option>
                </el-select>
            </div>
            <div class="query-item">
                <el-button type="primary" @click="searchVechicleTrack" :loading="table.loading">{{$ct('query')}}</el-button>
            </div>
            <div class="break-item"></div>
            <el-button type="primary" @click="exportTerminalExcle" :loading="table.loading">{{$ct('export')}}</el-button>
        </template>

        <template slot="footer">
            <el-pagination small background
                v-show="table.activeQuery == 'whole'"
                :current-page.sync="table.page"
                :page-size="table.size"
                layout="prev, pager, next, total"
                :total="table.list.length">
            </el-pagination>

            <el-pagination small background
                v-show="table.activeQuery == 'detail'"
                :current-page.sync="detail.page"
                :page-size="detail.size"
                layout="prev, pager, next, total"
                :total="detail.list.length">
            </el-pagination>
        </template>

        <template slot="content">
            <el-tabs v-model="table.activeQuery" type="border-card" style="border-radius: 4px;">
                <el-tab-pane :label="$ct('collect')" name="whole" style="padding-top: 5px">
                    <el-table
                        class="el-table--ellipsis el-table--radius"
                        border stripe highlight-current-row
                        :data="formatVehicleList"
                        @sort-change="sortCurrentProp"
                        height="100%" style="width: 100%;"
                        ref="totalTable">
                        <el-table-column width="60" type="index" align="center"
                            :index="(index) => index + 1 + pageStart" :label="$ct('index')">
                        </el-table-column>
                        <el-table-column :label="$ct('unit')" show-overflow-tooltip min-width="300" align="left" header-align="center">
                            <template slot-scope="scope">
                                <span>{{ scope.row.company_name }} >> {{ scope.row.fleet_name }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column prop="plate_no" :label="$ct('plateNo')" min-width="150"></el-table-column>
                        <el-table-column prop="begin_time" :label="$ct('beginTime')" min-width="150"></el-table-column>
                        <el-table-column prop="end_time" :label="$ct('endTime')" min-width="150"></el-table-column>
                        <el-table-column prop="sli_count" :label="$ct('count')" min-width="150" sortable></el-table-column>
                        <el-table-column label="详情" min-width="50">
                            <template slot-scope="{ row }">
                                <el-button type="text" title="详情" size="mini" @click="showDetail(row)">
                                     <i class="pony-iconv2 pony-xiangqing"></i>
                            </el-button>
                        </template>
                         </el-table-column>
                    </el-table>
                </el-tab-pane>
                <el-tab-pane :label="$ct('detail')" name="detail"  style="padding-top: 5px">
                    <el-table
                        class="el-table--ellipsis el-table--radius"
                        border stripe highlight-current-row
                        :data="formatDetailList"
                        @sort-change="sortDetailProp"
                        height="100%" style="width: 100%;"
                        ref="detailTable">
                        <el-table-column width="60" type="index" align="center"
                            :index="(index) => index + 1 + detailStart" :label="$ct('index')">
                        </el-table-column>
                        <el-table-column :label="$ct('unit')" show-overflow-tooltip min-width="300" align="left" header-align="center">
                            <template slot-scope="scope">
                                <span>{{ scope.row.company_name }} >> {{ scope.row.fleet_name }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column prop="plate_no" :label="$ct('plateNo')" min-width="150"></el-table-column>
                        <el-table-column prop="max_speed" :label="$ct('maxSpeed')" min-width="120" sortable></el-table-column>
                        <el-table-column prop="min_speed" :label="$ct('minSpeed')" min-width="120" sortable></el-table-column>
                        <el-table-column prop="avg_speed" :label="$ct('avgSpeed')" min-width="120" sortable></el-table-column>
                        <el-table-column prop="sli_ratio" :label="$ct('sliRatio')" min-width="140" sortable></el-table-column>
                        <el-table-column prop="sli_rule_value" :label="$ct('sli')" min-width="150" sortable></el-table-column>
                        <el-table-column prop="begin_time" :label="$ct('beginTime')" min-width="150" sortable></el-table-column>
                        <el-table-column prop="end_time" :label="$ct('endTime')" min-width="150" sortable></el-table-column>
                        <el-table-column prop="drive_time" :label="$ct('driveTime')" min-width="150" sortable></el-table-column>
                        <el-table-column prop="drive_mile" :label="$ct('driveMile')" min-width="150" sortable></el-table-column>
                        <el-table-column prop="begin_location" :label="$ct('beginLocation')" min-width="450" align="left" header-align="center"></el-table-column>
                        <el-table-column prop="end_location" :label="$ct('endLocation')" min-width="450" align="left" header-align="center"></el-table-column>
                    </el-table>
                </el-tab-pane>
            </el-tabs>
        </template>

    </Layout>
</template>

<script>
const ExportJsonExcel = require('js-export-excel')
import StartEndTime from "@/components/common/StartEedTime";

export default {
    name: 'vehicleTrackSpeed',
    components: { StartEndTime },
    data () {
        return {
            selectDesc: {
                time: [
                    { value: 0, name: '0s' },
                    { value: 10, name: '10s' },
                    { value: 30, name: '30s' },
                    { value: 60, name: '60s' },
                    { value: 120, name: '120s' },
                ],
                limit: [
                    { value: 40, name: '40 km/h' },
                    { value: 50, name: '50 km/h' },
                    { value: 60, name: '60 km/h' },
                    { value: 70, name: '70 km/h' },
                    { value: 80, name: '80 km/h' },
                    { value: 90, name: '90 km/h' },
                    { value: 100, name: '100 km/h' },
                    { value: 110, name: '110 km/h' },
                    { value: 120, name: '120 km/h' },
                ],
                rule: [
                    { value: 0, name: '0%' },
                    { value: 20, name: '20%' },
                    { value: 50, name: '50%' },
                ]
            },

            queryList: {
                begin_time: moment().subtract(1, 'days').startOf('day').format('YYYY-MM-DD HH:mm:ss'),
                end_time:  moment().subtract(1, 'days').endOf('day').format('YYYY-MM-DD HH:mm:ss'),
                sli_continue: 0,
                sli_ratio: 0,
                sli_rule_values: [60],
                vehicle_ids: []
            },

            pickerOptions: {
                disabledDate: function (date) {
                    return (date - moment().subtract(1, 'days').toDate()) > 0
                }
            },

            table: {
                activeQuery: 'whole',
                loading: false,
                list: [],
                page: 1,
                size: 30,
            },

            detail: {
                list: [],
                page: 1,
                size: 30,
            },
            detailRow:[],
            selectStartEndTime: [
        moment().startOf("day").subtract(1, 'days').valueOf(),
        moment().endOf("day").subtract(1, 'days').valueOf(),
      ],

        };
    },

    computed: {
        endDatePickerOptions: function () {
            return {
                disabledDate: (date) => {
                    return (date - moment().subtract(1, 'days').endOf('day').toDate()) > 0 ||
                        date - this.queryList.begin_time < 0;
                }
            }
        },

        pageStart() {
            return (this.table.page - 1) * this.table.size
        },
        formatVehicleList() {
            return this.table.list.slice(this.pageStart, this.pageStart + this.table.size)
        },

        detailStart() {
            return (this.detail.page - 1) * this.detail.size
        },
        formatDetailList() {
            return this.detail.list.slice(this.detailStart, this.detailStart + this.detail.size)
        },
    },
    watch: {
        selectStartEndTime: function (newVal, oldVal) {
      this.queryList.begin_time = moment(newVal[0]).format('YYYY-MM-DD HH:mm:ss')
      this.queryList.end_time = moment(newVal[1]).format('YYYY-MM-DD HH:mm:ss')
    }
    },
    mounted() {

    },

    methods: {
        selectNodes(current, { checkedNodes }) {
        this.queryList.vehicle_ids = checkedNodes
        .filter((item) => item.type == 4)
        .map((item) => item.id);
        },
        cleanUp() {
            this.table.list = []
            this.table.page = 1
            this.detail.list = []
            this.detail.page = 1
            this.table.loading = false
        },

        sortCurrentProp(column) {
            if(!column.order || !column.prop) return
            if(column.order == 'ascending') {
                this.table.list.sort((a, b) => +(a[column.prop]) - +(b[column.prop]))
            } else {
                this.table.list.sort((a, b) => +(b[column.prop]) - +(a[column.prop]))
            }
        },

        sortDetailProp(column) {
            if(!column.order || !column.prop) return
            if(column.order == 'ascending') {
                this.detail.list.sort((a, b) => +(a[column.prop]) - +(b[column.prop]))
            } else {
                this.detail.list.sort((a, b) => +(b[column.prop]) - +(a[column.prop]))
            }
        },

        async searchVechicleTrack() {
            if(!this.queryList.vehicle_ids || !this.queryList.vehicle_ids.length) {
                this.$warning(this.$ct('chooseCar'))
                return
            }

            this.cleanUp()
            this.table.loading = true
            let result = await this.$api.getRecordsli(this.queryList)
            if(!result || result.status != 200) {
                this.$error(result.message || this.$ct('queryError'))
                this.table.loading = false
                return
            }
            this.table.list = result.data.sli_data_list_pre || []
            // this.detail.list = result.data.sli_data_list || []
            this.detailRow = result.data.sli_data_list || []
            this.$nextTick(()=>{
                this.$refs["totalTable"].doLayout()
                this.$refs["detailTable"].doLayout()
                this.table.loading = false
            })
        },

        exportTerminalExcle() {
            let excelBody = []
            let excelHeader = []
            let columnWidths = []
            let excelName = ''

            if(this.table.activeQuery == 'whole') {
                if(!this.table.list.length) {
                    this.$warning(this.$ct('noData'))
                    return
                }
                excelName = this.$ct('overspeedlist')
                excelHeader = [this.$ct('index'),this.$ct('unit'),this.$ct('plateNo'),this.$ct('beginTime'),this.$ct('endTime'),this.$ct('count')]
                this.table.list.forEach((item, index) => {
                    let array = []
                    array.push(
                        index + 1, item.company_name + '>>' + item.fleet_name,
                        item.plate_no, item.begin_time, item.end_time, item.sli_count
                    )
                    excelBody.push(array)
                })
                columnWidths = ['3', '12', '8', '10', '10', '6']
            } else {
                if(!this.detail.list.length) {
                    this.$warning(this.$ct('noData'))
                    return
                }
                excelName = this.$ct('overspeeddetail')
                excelHeader = [
                    this.$ct('index'),this.$ct('unit'),this.$ct('plateNo'),this.$ct('maxSpeed'),this.$ct('minSpeed'),this.$ct('avgSpeed'), this.$ct('sliRatio'), this.$ct('sli'),
                    this.$ct('beginTime'), this.$ct('endTime'), this.$ct('driveTime'), this.$ct('driveMile'), this.$ct('beginLocation'), this.$ct('endLocation')
                ]
                this.detail.list.forEach((item, index) => {
                    let array = []
                    array.push(
                        index + 1, item.company_name + '>>' + item.fleet_name,
                        item.plate_no, item.max_speed, item.min_speed, item.avg_speed,
                        item.sli_ratio, item.sli_rule_value, item.begin_time, item.end_time,
                        item.drive_time, item.drive_mile, item.begin_location, item.end_location
                    )
                    excelBody.push(array)
                })
                columnWidths = ['3', '12', '8', '6', '6', '6', '6', '6', '10', '10', '9', '9', '18', '18']
            }
            this.table.loading = true
            let options = {
                fileName: `${this.queryList.begin_time} ~ ${this.queryList.end_time} ${ excelName }`,
                datas: [
                    {
                        sheetData: excelBody,
                        sheetHeader: excelHeader,
                        columnWidths: columnWidths
                    }
                ]
            }
            ExportJsonExcel(options).saveExcel();
            this.table.loading = false

        },

        dateStartChange(date) {
            if (moment(this.queryList.end_time).valueOf() - moment(date).valueOf() < 0) {
                this.queryList.end_time = moment(date).endOf('day').format('YYYY-MM-DD HH:mm:ss');
            }
        },
        endDateStartChange(date) {
            console.log(date);
            if (moment(this.queryList.begin_time).valueOf() - moment(date).valueOf() > 0) {
                this.queryList.begin_time = moment(date).startOf('day').format('YYYY-MM-DD HH:mm:ss');
            }
        },
        // 页面跳转
       async showDetail(row){
            this.table.activeQuery='detail',
            this.detail.list = this.detailRow.length ? this.detailRow.filter(it=>it.vehicle_id == row.vehicle_id) :  []
        },

    }
}

</script>

<style lang='scss' scoped>

</style>

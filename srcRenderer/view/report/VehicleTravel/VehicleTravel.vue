<template>
     <Layout tag="div" class="vehicle-travel" :contentLoading="loading">
        <template slot="query">
            <div class="query-item">
                <SelectTreeInput v-model="vehicleId" type="vehicle"
                                 placeholder="请选择车辆"
                                 title="请选择车辆" style="width: 130px;"
                                 :condition="inputCondition">
                </SelectTreeInput>
            </div>
            <div class="query-item">
                <span>时间：</span>
                <el-date-picker style="margin-left: 10px"
                            v-model="query.timeRange"
                            type="datetimerange"
                            :default-time="['00:00:00','23:59:59']"
                            align="right"
                            @change="dataTimeChange"
                            unlink-panels
                            range-separator="至"
                            start-placeholder="开始时间"
                            end-placeholder="结束时间"
                            :picker-options="pickerOptions">
                </el-date-picker>
            </div>
            <div class="query-item">
                <el-button size="mini" type="primary" @click="getReportData()" :loading="loading">查询</el-button>
            </div>
            <div class="break-item"></div>

            <div class="query-item ident">
                <p v-if="resultData.plateNo">
                    <span class="icon">
                        <i class="pony-iconv2 pony-chepai"></i>
                    </span>
                    <span>{{resultData.plateNo}}</span>
                </p>
                <p v-if="resultData.dept">
                    <span class="icon">
                        <i class="pony-iconv2 pony-qiye"></i>
                    </span>
                    <span>{{resultData.dept}}</span>
                </p>
            </div>
        </template>
        
        <template slot="content">
            <div class="content">
                <div class="chart-list">
                    <el-card shadow="never">
                        <div class="title"><i style="background-color:#5B9AF6"></i><span>行驶里程</span></div>
                        <div class="chart-list-card">
                            <MileCard :itemNum="0" :totalNum="resultData.mileTotal"></MileCard>
                        </div>
                        <div class="percent">
                            <div class="percent-item">
                                <div class="percent-title">
                                    <div class="percent-icon"><i class="pony-iconv2 pony-gaosu"></i></div>
                                    <span>高速占比</span>
                                </div>
                                <div class="percent-list-warp">
                                     <div class="percent-list">
                                        <span class="name">高速里程</span>
                                        <el-progress class="speed" :text-inside="true" :stroke-width="20" :percentage="resultData.mileRatioExpressway" color="#5B9AF6"></el-progress>
                                        <span class="num"><b :title="resultData.mileExpressway">{{resultData.mileExpressway}}</b> km</span>
                                    </div>
                                    <div class="percent-list">
                                        <span class="name">非高速里程</span>
                                        <el-progress class="speed" :text-inside="true" :stroke-width="20" :percentage="resultData.mileRatioUnExpressway" color="#5B9AF6"></el-progress>
                                        <span class="num"><b :title="resultData.mileUnExpressway">{{resultData.mileUnExpressway}}</b> km</span>
                                    </div>
                                </div>
                            </div>
                            <div class="percent-item">
                                <div class="percent-title">
                                    <div class="percent-icon"><i class="pony-iconv2 pony-rijian"></i></div>
                                    <span>日夜间占比</span>
                                </div>
                                <div class="percent-list-warp">
                                     <div class="percent-list">
                                        <span class="name">日间里程</span>
                                        <el-progress class="speed" :text-inside="true" :stroke-width="20" :percentage="resultData.mileRatioDay" color="#5B9AF6"></el-progress>
                                        <span class="num"><b :title="resultData.mileDay">{{resultData.mileDay}}</b> km</span>
                                    </div>
                                    <div class="percent-list">
                                        <span class="name">夜间里程</span>
                                        <el-progress class="speed" :text-inside="true" :stroke-width="20" :percentage="resultData.mileRatioNight" color="#5B9AF6"></el-progress>
                                        <span class="num"><b :title="resultData.mileNight">{{resultData.mileNight}}</b> km</span>
                                    </div>
                                </div>
                            </div>

                        </div>
                    </el-card>
                    <el-card shadow="never">
                        <div class="title"><i style="background-color:#7A86FF"></i><span>油耗</span></div>
                        <div class="chart-list-card">
                            <MileCard :itemNum="1" :totalNum="resultData.fuelTotal"></MileCard>
                        </div>
                        <div class="percent">
                            <div class="top-part">
                                <div class="big-total">
                                    <div class="icon" >
                                        <div class="position-icon">
                                            <div class="speed-icon" style="background-color:rgba(122, 134, 255,.3)">
                                                <i class="pony-iconv2 pony-youhao11" style="color:#7A86FF"></i>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="speed-num">
                                        <div class="speed-num-warp">
                                            <p><span>{{resultData.fuelHkm}}</span><i>L/100km</i></p>
                                            <p>综合百公里油耗</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="percent-item">
                                <div class="percent-title">
                                    <div class="percent-icon" style="background-color:rgba(122, 134, 255,.3)"><i class="pony-iconv2 pony-youhao1" style="color:#7A86FF"></i></div>
                                    <span>油耗占比</span>
                                </div>
                                <div class="percent-list-warp">
                                    <div class="percent-list">
                                        <span class="name">行驶油耗</span>
                                        <el-progress class="oli" :text-inside="true" :stroke-width="20" :percentage="resultData.fuelRatioRun" color="#7A86FF"></el-progress>
                                        <span class="num"><b :title="resultData.fuelRun">{{resultData.fuelRun}}</b> L</span>
                                    </div>
                                    <div class="percent-list">
                                        <span class="name">怠速油耗</span>
                                        <el-progress class="oli" :text-inside="true" :stroke-width="20" :percentage="resultData.fuelRatioIdle" color="#7A86FF"></el-progress>
                                        <span class="num"><b :title="resultData.fuelIdle">{{resultData.fuelIdle}}</b> L</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </el-card>
                    <el-card shadow="never">
                        <div class="title"><i style="background-color:#8073E5"></i><span>行驶时间</span></div>
                        <div class="chart-list-card">
                            <MileCard :itemNum="2" :totalNum="resultData.timeOnline"></MileCard>
                        </div>
                        <div class="percent">
                            <div class="top-part">
                                <div class="echartsPie" ref="echartsPie" style="height:100%"></div>
                                <!-- <el-progress type="circle" :percentage="resultData.timeRatioOnline" color="#8073E5" :stroke-width="8" :width="150" :format="formatText"></el-progress> -->
                            </div>
                            <div class="percent-item">
                                <div class="percent-title">
                                    <div class="percent-icon" style="background-color:rgba(128, 115, 229,.3)"><i class="pony-iconv2 pony-shichangzhanbi" style="color:#8073E5"></i></div>
                                    <span>行驶时间占比</span>
                                </div>
                                <div class="percent-list-warp">
                                    <div class="percent-list">
                                        <span class="name">驾驶时长</span>
                                        <el-progress class="time" :text-inside="true" :stroke-width="20" :percentage="resultData.timeRatioRun" color="#8073E5"></el-progress>
                                        <span class="num"><b :title="resultData.timeRun">{{resultData.timeRun}}</b> h</span>
                                    </div>
                                    <div class="percent-list">
                                        <span class="name">怠速时长</span>
                                        <el-progress class="time" :text-inside="true" :stroke-width="20" :percentage="resultData.timeRatioIdle" color="#8073E5"></el-progress>
                                        <span class="num"><b :title="resultData.timeIdle">{{resultData.timeIdle}}</b> h</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </el-card>
                    <div class="total">
                        <div class="total-chart">
                            <el-card shadow="never">
                                <div class="total-violations">
                                    <div class="icon"><i class="pony-iconv2 pony-feiyong"></i></div>
                                    <div class="total-num">
                                        <p class="tital-name">总计费用</p>
                                        <p class="num"><span :title="resultData.feeTotal
                                        
                                        
                                        ">{{resultData.feeTotal}}</span>元</p>
                                    </div>
                                </div>
                                <div class="echarts-warp">
                                    <div class="show-echarts" ref="echarts">

                                    </div>
                                </div>
                            </el-card>
                        </div> 
                        <div class="speed-list">
                            <el-card shadow="never">
                                <div class="big-total">
                                    <div class="icon">
                                        <div class="position-icon">
                                            <div class="speed-icon">
                                                <i class="pony-iconv2 pony-chesu1"></i>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="speed-num">
                                        <div class="speed-num-warp">
                                            <p><span>{{resultData.speedAvg}}</span><i>km/h</i></p>
                                            <p>平均速度</p>
                                        </div>
                                    </div>
                                </div>
                            </el-card>
                            <el-card shadow="never">
                                <div class="big-total">
                                    <div class="icon">
                                        <div class="position-icon">
                                            <div class="speed-icon">
                                                <i class="pony-iconv2 pony-zuigaochesu"></i>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="speed-num">
                                        <div class="speed-num-warp">
                                            <p><span>{{resultData.speedMax}}</span><i>km/h</i></p>
                                            <p>最高车速</p>
                                        </div>
                                    </div>
                                </div>
                            </el-card>
                        </div>
                    </div>
                </div>
                <div class="bad-driving">
                    <el-card shadow="never" style="height:100%">
                        <div class="title"><i style="background-color:#FF7A7A"></i><span>不良驾驶行为</span></div>
                        <div class="total-violations">
                            <div class="icon"><i class="pony-iconv2 pony-baojing"></i></div>
                            <div class="total-num">
                                <p class="tital-name">总计违规</p>
                                <p class="num"><span>{{resultData.alarmTotal}}</span>次</p>
                            </div>
                        </div>
                        <div class="shadow-one"></div>
                        <div class="shadow-two"></div>

                        <div class="ranking-list">
                            <el-table
                                ref="table"
                                :cell-style="cellStyle"
                                :data="resultData.alarmList"
                                highlight-current-row
                                style="width: 100%">
                                <el-table-column align="center" type="index" label="排名" min-width="50">
                                    <template slot-scope="scope">
                                        <span v-if="scope.$index >= 3">
                                            {{scope.$index + 1}}
                                        </span>
                                        <span v-else class="ranking-img">
                                            <img :src="'./static/imgNewVersion/ranking/ranking'+(scope.$index + 1)+'.png'" alt="">
                                        </span>
                                    </template>
                                </el-table-column>
                                <el-table-column align="center" label="违规行为" min-width="120" prop="field" show-overflow-tooltip></el-table-column>
                                <el-table-column label="违规次数" align="center" min-width="50" prop="value"></el-table-column>
                            </el-table>
                        </div>
                    </el-card>
                </div>
            </div>
        </template>
    </Layout>
</template>

<script>
import MileCard from './components/MileCard'
import SelectTreeInput from "@/components/common/SelectTreeInput";
export default {
    name: 'vehicleTravel',
    components: { SelectTreeInput,MileCard },
    data () {
        return {
            loading:false,
            vehicleId:null,
            query:{
                vehicleId:null,
                timeRange:[
                        moment().subtract(1, 'days').startOf('day'),
                        moment().endOf('day')
                    ]
            },
            pickerOptions: {
                    disabledDate(time) {
                        return time.getTime() > moment().endOf('day').valueOf() || time.getTime() < moment().subtract(30,'days').endOf('day').valueOf();
                    }
                },
            modal: {
                chartInstance: null,
                echartsPie:null,
                echartsPieOPtions:{

                },
                options: {
                },
            },
            feelListName:["油费","过路费","维修费"],
            resultData:{
                mileTotal:0,
                mileRatioExpressway:0,
                mileExpressway:0,
                mileRatioUnExpressway:0,
                mileUnExpressway:0,
                mileRatioDay:0,
                mileDay:0,
                mileRatioNight:0,
                mileNight:0,
                timeRatioOnline:0,
                timeOnline:0,
                timeRatioRun:0,
                timeRun:0,
                timeRatioIdle:0,
                timeIdle:0,
                fuelTotal:0,
                fuelHkm:0,
                fuelRatioRun:0,
                fuelRun:0,
                fuelRatioIdle:0,
                fuelIdle:0,
                feeTotal:0,
                feeList:[],
                speedAvg:0,
                speedMax:0,
                alarmTotal:0,
                alarmList:[],
            },
        };
    },

    computed: {

    },
    watch:{
        'vehicleId':function(val){
            this.query.vehicleId = val.value
        },
        
    },
    mounted() {
        this.initChart()
    },

    methods: {
        dataTimeChange(val){
            if(val[0].getTime() == val[1].getTime()){
                this.$warning('开始时间不能和结束时间一致！')
                this.query.timeRange[1] = moment(val[1]).endOf('day')
            }

        },
        initChart(){
            this.initDefaultChart()
            this.initDefaultChartPie()

            if(this.modal.chartInstance) {
                this.modal.chartInstance.resize()
            }
            if(this.modal.echartsPie) {
                this.modal.echartsPie.resize()
            }
        },
        
        async initDefaultChartPie(data = []) {
                this.modal.echartsPie = this.$echarts.init(this.$refs.echartsPie,'light')
                await this.$nextTick()
                let options = {
                     
                    color:['#8073E5','rgba(128, 115, 229,.3)'],
                    series: [
                        {
                            type: 'pie',
                            radius: ['98%', '85%'],
                            avoidLabelOverlap: false,
                            selectedOffset:0,
                            hoverAnimation: false,
                            itemStyle: {
                                borderRadius: 3,
                            },
                            label: {
                                show: false,
                                position: 'center',
                                formatter: function(data){
                                        return `{d|${data.percent}%}\n在线率`
                                    },
                                rich: {
                                    d: {
                                        color:'#8073E5',
                                        fontSize:20,
                                        height:30
                                    }
                                }

                            },
                           
                            labelLine: {
                                show: false
                            },
                            data: [
                                {    
                                    value: this.resultData.timeRatioOnline,
                                    name: '',
                                    selected:true,     //默认选中第一块
                                    label:{
                                        show:true,     //默认显示第一块
                                        fontSize: '14',
                                    }
                                },
                                {value: 100 - this.resultData.timeRatioOnline, name: ''},
                            ]
                            }
                        ]
                    
                     
                }
               
                this.modal.echartsPie.setOption(options);
                window.addEventListener("resize", () => {
                    this.modal.echartsPie.resize();
                  });
            },
        async initDefaultChart(data = []) {
                this.modal.chartInstance = this.$echarts.init(this.$refs.echarts,'light')
                await this.$nextTick()
                this.modal.options = {
                      legend: {
                        y:'center',
                        right:0,
                        icon:'circle',
                        itemGap:24,
                        itemWidth: 15,
                        itemHeight: 15,
                        orient: 'vertical',
                        borderColor: '#fff',
                    },
                   
                    color:['#5470C6','#4EC2A9','#FAC858'],
                    series: 
                        {
                            type: 'pie',
                            radius: ['40%', '75%'],
                            avoidLabelOverlap: false,
                            itemStyle: {
                                borderRadius: 8,
                                borderColor: '#fff',
                                borderWidth: 2,
                                
                            },
                            label: {
                                show: false,
                                position: 'center'
                            },
                            emphasis: {
                                label: {
                                    show: true,
                                    fontSize: '14',
                                    // formatter: '{c}\n{b}(元)',
                                    formatter: function(data){
                                        return `{c|${data.data.value}}\n{b|${data.data.name}(元)}`
                                    },

                                },
                                
                            },
                            data,
                        }
                    
                     
                }
                // 悬浮在饼图上，中间的字体和对应的色块保持一致
                this.modal.chartInstance.on("mouseover",params=>{
                    this.modal.chartInstance.setOption({
                        series:{
                            label:{
                                emphasis:{
                                    rich:{
                                        c:{
                                            fontSize: 35,
                                            color:params.color
                                        }
                                    }
                                }
                            }
                        }
                    })
                })
                this.modal.chartInstance.setOption(this.modal.options);
                window.addEventListener("resize", () => {
                    this.modal.chartInstance.resize();
                  });
            },
        // formatText(percent){
        //     // return `\<p\>${percent}%\<\/p\>\n在线率`
        //     return percent+'%\n在线率'
        // },
        inputCondition(treeNode) {
            return treeNode.type === 4
        },
        async getReportData(){
            if(!this.query.vehicleId){
                this.$warning('请选择车辆！')
                return 
            }
            this.loading = true
            let params = {
                vehicleId:this.query.vehicleId,
                start:moment(this.query.timeRange[0]).format('YYYY-MM-DD HH:mm:ss'),
                end:moment(this.query.timeRange[1]).format('YYYY-MM-DD HH:mm:ss')
            }
            try{
                let result = await this.$api.vehicleTripReport(params)
                if(!result || result.status != 200){
                    this.$error(result.message || '查询出错')
                    return 
                }
                this.resultData = result.data
                //这几个是>5个字符的，不保留小数
                let numInt = [
                    'mileExpressway',
                    'mileUnExpressway',
                    'mileDay',
                    'mileNight',
                    'timeRun',
                    'timeIdle',
                    'fuelRun',
                    'fuelIdle',
                ]
                numInt.forEach(item=>{
                    if(this.resultData[item].toString().length >= 5){
                        this.resultData[item] = Number(this.resultData[item]).toFixed(0)
                    }
                })
                let totalInt = [
                    'mileTotal',
                    'timeOnline',
                    'fuelTotal',
                ]
                totalInt.forEach(item=>{
                    if(this.resultData[item].toString().length >= 7){
                        this.resultData[item] = Number(this.resultData[item]).toFixed(0)
                    }
                })
                let data = result.data.feeList.map((item,index)=>{
                    return {
                        value:item,
                        name:this.feelListName[index]
                    }
                })
                let dataPie = [
                    {    
                    value: this.resultData.timeRatioOnline,
                    name: '',
                    selected:true,     //默认选中第一块
                    label:{
                        show:true,     //默认显示第一块
                        fontSize: '14',
                    }
                    },
                    {value: 100 - this.resultData.timeRatioOnline, name: ''},
                ]
                this.initDefaultChart(data)
                this.initDefaultChartPie(dataPie)

            }catch(e){
                this.$error(e)
            }finally{
                this.loading = false
            }
            
        },
        exportReportData(){

        },
        cellStyle(data) {
        // 表格的第三列加粗
            if (data.columnIndex == 2) {
                return 'font-weight:bold;font-size:16px'
            } else {
                return 'font-size:14px'
            }
        }

    }
}

</script>

<style lang='scss' scoped>
$mile-color:#5B9AF6;
$behavior-color:#FF7A7A;
$total-color:#4EC2A9;
$font-size:var(--color-text-regular);
$oli-color:#7A86FF;
$size-big:48px;
.vehicle-travel {
    .ident {
        p {
            margin-left: 20px;
            .icon {
                display: inline-block;
                width: 25px;
                height: 25px;
                border-radius: 3px;
                text-align: center;
                vertical-align: -2px;
                background-color: var(--border-color-extra-light);
                i {
                    font-size: 24px;
                    color:var(--color-text-secondary)
                }
            }
            span:nth-of-type(2){
                color: $font-size;
                font-weight: bold;
                font-size: 16px;
                margin-left: 10px;
            }
        }
    }
    .content {
        width: 100%;
        height: 100%;
        .el-card {
            .big-total {
                width: 100%;
                height: 100%;
                .icon {
                    float: left;
                    width: 50%;
                    height: 100%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    .position-icon {
                        width: 55%;
                        height: 0;
                        padding: 55% 0 0 0;
                        position: relative;
                        .speed-icon {
                            position: absolute;
                            top: 0;
                            // margin-top: -25%;
                            width: 100%;
                            height: 100%;
                            display: flex;
                            justify-content: center;
                            align-items: center;
                            border-radius: 50%;
                            background-color: rgba(45, 179, 236,.3);
                            i {
                                font-size: 75px;
                                color: rgb(45, 179, 236);
                            }
                        }
                    }
                    
                }
                .speed-num{
                    width: 50%;
                    height: 100%;
                    float: left;
                    display: flex;
                    align-items: center;
                    .speed-num-warp {
                        width: 100%;
                        p {
                        color: $font-size;
                        span {
                            font-size: $size-big;
                            font-weight: bold;
                            color: $font-size;
                            line-height: 1;
                        }
                        i {
                            font-style: normal;
                            // margin-left: 5px;
                            font-size: 18px;
                        }
                    }
                    }
                    
                }                   

            }
        }
        /deep/.el-card__body {
            height: 100%;
        }

        //不良驾驶行为的总计违规以及总计费用
        .total-violations {
                margin-top: 15px;
                width: 100%;
                height: 135px;
                border-radius: 5px;
                background-color: $behavior-color;
                padding: 25px;
                .icon {
                    float: left;
                    width: 85px;
                    height: 85px;
                    line-height: 85px;
                    border-radius: 3px;
                    text-align: center;
                    background-color: rgba(255,255,255,.1);
                    i {
                        font-size: 75px;
                        color: #fff;
                    }
                }
                .total-num {
                    width: calc(100% - 85px);
                    float: left;
                    padding-left: 10%;
                    // text-align: center;
                    .tital-name {
                        line-height: 30px;
                        font-weight: bold;
                    }
                    p {
                        color: rgba(255,255,255,.8);
                        font-size: 16px;
                    }
                    .num {
                        line-height: 1;
                        span {
                            color: #fff;
                            font-size: $size-big;
                            margin-right: 5px;
                            display: inline-block;
                            white-space: nowrap;
                            overflow: hidden;
                            max-width: calc(100% - 21px);
                            /* line-height: 1; */
                            vertical-align: -10px;
                            text-overflow: ellipsis;
                        }
                    }
                }
            }
        .title {
            i {
                display: inline-block;
                width: 3px;
                height: 20px;
                margin-right: 8px;
                vertical-align: -5px;
                border-radius: 3px;
            }
        }
        .chart-list {
            float: left;
            width: 80%;
            height: 100%;
            overflow: hidden;
            >.el-card {
                float: left;
                margin: 10px 10px 0px 0;
                height: calc(50% - 10px);
                width: calc(50% - 10px);
                .chart-list-card {
                    float: left;
                    width: 35%;
                    margin-top: 10px;
                    height: calc(100% - 30px);
                }

                // 进度条item
                .percent {
                    width: 65%;
                    height: 100%;
                    float: left;
                    padding: 10px 0px 10px 25px;
                    .top-part {
                        height: calc(50% - 18px);
                        text-align: center;
                        // /deep/.el-progress {
                        //     width: 100%;
                        //     height: 100%;
                        //     .el-progress-circle {
                        //         width: 100%;
                        //         height: 100%;
                        //     }
                        // // }
                        //     /deep/.el-progress__text {
                        //         white-space: pre-wrap;
                        //         color: #8073E5;
                        //         line-height: 25px;
                        //     }
                    }
                    .percent-item {
                        height: calc(50% - 18px);
                        .percent-title {
                            .percent-icon {
                                display: inline-block;
                                width: 30px;
                                height: 30px;
                                border-radius: 5px;
                                background-color: rgba(91, 154, 246,.3);
                                i {
                                    color: $mile-color;
                                    font-size: 30px;
                                    vertical-align: middle;
                                }
                            }
                            span {
                                color: $font-size;
                                font-weight: bold;
                                margin-left: 10px;
                            }
                        }
                        .percent-list-warp {
                            height: calc(100% - 30px);
                            display: flex;
                            flex-direction: column;
                            justify-content: space-around;
                            .percent-list {
                                width: 100%;
                                color: $font-size;
                                margin-top: 10px;
                                display: flex;
                                .name {
                                    display: inline-block;
                                    width: 70px;
                                    text-align-last:justify;
                                    text-align:justify;
                                }
                                .num {
                                    width: 80px;
                                    margin-top: -6px;
                                    b {
                                        overflow: hidden;
                                        white-space: nowrap;
                                        text-overflow: ellipsis;
                                        font-size: 22px;
                                        color: $font-size;
                                        display: inline-block;
                                        max-width: calc(100% - 24px);
                                        vertical-align: -6px;
                                    }
                                }
                                .el-progress {
                                    width: 58%;
                                    flex: 1;
                                    display: inline-block;
                                    margin: 0 15px;
                                    vertical-align: 2px;
                                    
                                    .el-progress-bar__inner {
                                        text-align: left;
                                    }
                                }
                                .speed {
                                    /deep/.el-progress-bar__outer {
                                        background-color: rgba(91, 154, 246,.3);
                                    }
                                }
                                .oli {
                                    /deep/.el-progress-bar__outer {
                                        background-color: rgba(122, 134, 255,.3);
                                    }
                                }
                                .time {
                                    /deep/.el-progress-bar__outer {
                                        background-color: rgba(128, 115, 229,.3);
                                    }
                                }
                            }
                        }
                        
                    
                    }
                    .percent-item:nth-of-type(2) {
                        margin-top: 10px;
                    }

                }
                
            }

            // 总计费用
            .total {
                float: right;
                width: calc(50% - 10px);
                height: 50%;
                margin-right: 10px;
                overflow: hidden;
                .total-chart {
                    width: calc(50% - 5px);
                    height: calc(100% - 10px);
                    float: left;
                    margin-top: 10px;
                    .el-card {
                        width: 100%;
                        height: 100%;
                        /deep/.el-card__body {
                            padding: 0;
                        }
                        .total-violations {
                            background-color: $total-color;
                            margin-top: 0;
                            border-radius: 3px;
                            .total-num {
                                min-width: 100px;
                                float: left;
                                // text-align: center;
                                padding-left: 10%;
                                
                            }

                        }
                        .echarts-warp {
                            width: 100%;
                            height: calc(100% - 135px);
                            overflow: hidden;
                            .show-echarts {
                                width: 100%;
                                height: 100%;
                                margin-left: -10%;
                            }
                        }
                        
                    }

                }
                .speed-list {
                    float: right;
                    width: calc(50% - 5px);
                    height: 100%;
                    margin-left: 5px;
                    .el-card {
                        width: 100%;
                        height: calc(50% - 10px);
                        margin-top: 10px;
                        /deep/.el-card__body {
                            padding: 0;
                        }
                    }
                }

                }
            }

        //不良驾驶行为
        .bad-driving {
            float: right;
            height: calc(100% - 10px);
            width: 20%;
            margin-top: 10px;
            /deep/.el-card__body {
                height: 100%;
            }
            
            .shadow-one {
                width: 85%;
                margin: auto;
                height: 16px;
                border-radius: 0 0 4px 4px;
                background-color: rgba(255, 122, 122,.3);
            }
            .shadow-two {
                width: 65%;
                margin: auto;

                height: 12px;
                border-radius: 0 0 3px 3px;
                background-color: rgba(255, 122, 122,.1);
            }
            .ranking-list{
                margin-top: 20px;
                height: calc(100% - 218px);
                /deep/.el-table::before {
                        background-color: transparent;
                }
                /deep/.el-table {
                    height: 100%;
                    .ranking-img {
                        width: 30px;
                        height: 30px;
                        img {
                            width: 100%;
                            height: 100%;
                        }
                    }
                    th {
                        background-color: transparent;
                        border-bottom: none;
                        .cell {
                            font-weight: normal;
                            font-size: 14px;
                            color: var(--color-text-primary)
                        }
                    }
                    td {
                        border-bottom: none;
                        height: 40px;
                        
                    }
                    
                }
            }
        }
        .el-card {
            background-color: var(--background-color-light);
        }
    }
}

</style>

<template>
    <Layout :has-color="true" 
        :contentLoading="table.loading"
        class="workingTableList">

        <template slot="aside">
            <div class="query-top">
                <!-- <ZtreeMatics treeType="vehicle" ref="vehicleTree" 
                    :checkMode="true" style="padding: 10px;">
                </ZtreeMatics> -->
                <ElementTree type="vehicle" ref="vehicleTree" :checkMode="true" @check="selectNodes" node-key="id">
                        </ElementTree>
            </div>
            <div class="query-bottom bg bg--lighter">
                <div class="query-item">
                    <span>开始时间:</span>
                    <el-date-picker 
                        @change="dateStartChange"
                        :clearable="false"
                        :picker-options="startDatePickerOptions"
                        v-model="queryList.start_date" type="date"  
                        value-format="yyyy-MM-dd HH:mm:ss"
                        placeholder="选择日期">
                    </el-date-picker>
                </div>
                <div class="query-item">
                    <span>结束时间:</span>
                    <el-date-picker 
                        :clearable="false"
                        :picker-options="endDatePickerOptions"
                        v-model="queryList.end_date" type="date"  
                        value-format="yyyy-MM-dd HH:mm:ss"
                        placeholder="选择日期">
                    </el-date-picker>
                </div>
                <div class="query-item">
                    <el-button size="mini" type="primary" style="width: 100%;"
                        @click="searchCurrentDetail" :loading="table.loading">查询
                    </el-button>
                </div>
            </div>
        </template>

        <template slot="query">
            <div class="query-item">
                <el-button size="mini" type="primary" @click="exportDataInfo">导出</el-button>
            </div>
            <div class="break-item"></div>
            <div class="query-item">
                <el-pagination background small
                    :current-page.sync="table.page"
                    :page-size="table.size"
                    layout="prev, pager, next, total"
                    :total="table.data.length">
                </el-pagination>
            </div>
        </template>

        <template slot="content">
            <el-table
                class="el-table--ellipsis el-table--radius"
                border stripe highlight-current-row size="mini" 
                :data="formatList" 
                @sort-change="sortCurrentProp"
                height="100%" style="width: 100%"
                ref="table">
                <el-table-column type="index" :index="(index) => index + 1 + pageStart" label="序号" width="50"></el-table-column>
                <el-table-column prop="company_name" label="单位" min-width="300" show-overflow-tooltip header-align="center" align="left">
                    <template slot-scope="{ row }">
                        {{ row.company_name }} >> {{ row.dept_name }}
                    </template>
                </el-table-column>
                <el-table-column prop="plate_no" label="车牌号" min-width="150"></el-table-column>
                <el-table-column prop="plate_no" label="日期" min-width="150">
                    <template slot-scope="{ row }">
                        {{ DateFormat(row.stats_date) }}
                    </template>
                </el-table-column>
                <el-table-column prop="start_time" label="开始时间" min-width="150"></el-table-column>
                <el-table-column prop="end_time" label="结束时间" min-width="150"></el-table-column>
                <el-table-column prop="run_time" label="在途时长" min-width="130" sortable>
                    <template slot-scope="{ row }">
                        {{ row.run_time_view }}
                    </template>
                </el-table-column>
                <el-table-column prop="today_run_time" label="在途时长(实时监控)" min-width="180" sortable>
                    <template slot-scope="{ row }">
                        {{ row.today_run_time_view }}
                    </template>
                </el-table-column>
                <el-table-column prop="acc_time" label="等待时长" min-width="130" >
                    <template slot="header">
                        <div class="tableHeader">
                            <p>等待时长</p>
                <p>
                  <el-popover placement="top" width="240" trigger="hover">
                    <p>
                        等待时长 = 工作时长-在途时长
                    </p>
                    <i class="pony-iconv2 pony-bangzhu bangzhu" slot="reference" style="font-size:18px;vertical-align: -1px;"></i>
                </el-popover>
                </p>
                        </div>
              </template>
                    <template slot-scope="{ row }">
                        {{ row.wait_time_view }}
                    </template>
                </el-table-column>
                <el-table-column prop="acc_time" label="工作时长" min-width="130" >
                    <template slot="header">
                        <div class="tableHeader">

                <p>工作时长</p>
                <p>
                  <el-popover placement="top" width="240" trigger="hover">
                    <p>
                        工作时长 = 结束时间-开始时间
                    </p>
                    <i class="pony-iconv2 pony-bangzhu bangzhu" slot="reference" style="font-size:18px;vertical-align: -1px;"></i>
                </el-popover>
                </p>
            </div>
                </template>
                    <template slot-scope="{ row }">
                        {{ row.work_time_view }}
                    </template>
                </el-table-column>
                <el-table-column prop="acc_time" label="行驶时长" min-width="130" sortable>
                    <template slot-scope="{ row }">
                        {{ row.acc_time_view }}
                    </template>
                </el-table-column>
                
                <el-table-column prop="online_time" label="在线时长" min-width="130" sortable>
                    <template slot-scope="{ row }">
                        {{ row.online_time_view }}
                    </template>
                </el-table-column>
                <el-table-column prop="plate_no" label="操作" min-width="50">
                    <template slot-scope="{ row }">
                        <el-button type="text" title="轨迹回放" 
                            @click="jumpOnPlayBack(row)">
                            <i class="pony-iconv2 pony-guijihuifang"></i>
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
        </template>

    </Layout>
</template>

<script>
const ExportJsonExcel = require('js-export-excel')
import { transSecondToHMSCN } from '../../monitor/util/monitorUtil'
export default {
    name: 'workingTableList',
    components: {  },
    data () {
        return {
            startDatePickerOptions: {
                disabledDate: function (date) {
                    return (moment().subtract(1, 'day').endOf('day').toDate() - date ) < 0
                }
            },

            queryList: {
                start_date: moment().subtract(1, 'day').startOf('day').format('YYYY-MM-DD HH:mm:ss'),
                end_date: moment().subtract(1, 'day').endOf('day').format('YYYY-MM-DD HH:mm:ss'),
                vehicle_ids: [], 
            },

            table: {
                loading: false,
                page: 1,
                size: 30,
                data: [],
            }

        };
    },

    filters: {

    },

    computed: {
        endDatePickerOptions: function () {
            return {
                disabledDate: (date) => {
                    return (date - moment().subtract(1, 'day').endOf('day').toDate()) > 0 ||
                        date - moment(this.queryList.start_date).valueOf() < 0;
                }
            }
        },
        formatTitle() {
            return moment(this.queryList.start_date).valueOf() == moment(this.queryList.end_date).valueOf()?
                `${moment(this.queryList.start_date).format('YYYY年MM月DD日')} 车辆工作时长报表`:
                `${moment(this.queryList.start_date).format('YYYY年MM月DD日')} ~ ${ moment(this.queryList.end_date).format('YYYY年MM月DD日') } 车辆工作时长报表`
        },
        pageStart() {
            return (this.table.page - 1) * this.table.size
        },
        formatList() {
            return this.table.data.slice(this.pageStart, this.pageStart + this.table.size)
        }
    },

    mounted() {

    },

    methods: {
        jumpOnPlayBack(row) {
            if(!row.vehicle_id || !row.start_time || row.start_time == '-') {
                this.$warning('该车辆未运行')
                return
            }
            let obj = {
                vehicleId: row.vehicle_id,
                startTime: row.start_time,
                endTime: row.end_time
            }
            this.$router.push({
                path:'/home/<USER>',
                query: obj
            })
        },
        selectNodes(current, { checkedNodes }) {
         this.queryList.vehicle_ids = checkedNodes
        .filter((item) => item.type == 4)
        .map((item) => item.id);
        },
        async searchCurrentDetail() {
            if(!this.queryList.vehicle_ids.length) {
                this.$warning('请至少选择一辆车');
                return
            }

            this.table.loading = true
            this.table.data = []
            this.table.page = 1

            let result = await this.$api.statisticsOfOperationDuration(JSON.parse(JSON.stringify(this.queryList)))
            if(!result || result.status != 200) {
                this.table.loading = false
                this.$error(result.message || '查询出错')
                return
            }
            if(!result.data.length) {
                this.table.loading = false
                this.$warning(result.message || '未查询到数据')
                return
            }

            this.table.data = result.data
            this.$nextTick(() => {
                this.$refs["table"].doLayout();
                this.table.loading = false
            })
        },

        exportDataInfo() {
            if(!this.table.data.length) {
                this.$warning('没有数据可以导出')
                return
            }
            let exportProp = ['plate_no','stats_date', 'start_time', 'end_time', 'run_time_view', 'today_run_time_view','wait_time_view','work_time_view','acc_time_view','online_time_view']
            let excelBody = []
            this.table.data.forEach((item, index) => {
                let result = [index + 1].concat(`${item.company_name} >> ${item.dept_name}`) .concat(exportProp.map(prpo => item[prpo]))
                excelBody.push(result)
            })
            let options = {
                fileName: this.formatTitle,
                datas: [
                    {
                        sheetData: excelBody,
                        sheetHeader:['序号', '单位', '车牌号','日期', '开始工作时间', '结束工作时间', '在途时长','在途时长(实时监控)','等待时长','工作时长','行驶时长', '在线时长'],
                        columnWidths: ['3','18','8','10','10','10', '8', '8', '8', '8','8','8']
                    }
                ]
            }
            ExportJsonExcel(options).saveExcel();
        },

        sortCurrentProp(column) {
            if(!column.order || !column.prop) return
            if(column.order == 'ascending') {
                this.table.data.sort((a, b) => (a[column.prop] - b[column.prop]))
            } else {
                this.table.data.sort((a, b) => (b[column.prop] - a[column.prop]))
            }
        },

        dateStartChange(date) {
            if (moment(this.queryList.end_date).valueOf() - moment(date).valueOf() < 0) {
                this.queryList.end_date = date;
            }
        },
    }
}

</script>

<style lang='scss' scoped>
.workingTableList {
    .query-top {
        height: calc(100% - 145px);
    }   
    .query-bottom {
        margin-top: 5px;
        padding: 10px;
        .query-item {
            display: flex;
            height: 40px;
            line-height: 40px;
            justify-content: space-between;
            align-items: center;
            > span {
                font-size: 12px;
                white-space: nowrap;
                margin-right: 5px;
            }
        }
    }
    .tableHeader{
        display: flex;
        justify-content: center;
    align-items: flex-start;
    line-height: 23px;
    }
}
</style>

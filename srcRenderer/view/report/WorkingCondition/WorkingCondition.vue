<template>
    <Layout tag="div"
        :has-color="true"
        :asideWidth="290"
        :contentLoading="table.loading"
        class="workingCondition">

        <template slot="aside">
            <div class="query-top">
                <el-tabs v-model="table.activeTab" stretch type="border-card">
                    <el-tab-pane label="车辆" name="vehicleTree">
                        <!-- <ZtreeMatics treeType="vehicle" ref="vehicleTree" :checkMode="true" style="padding: 10px;"></ZtreeMatics> -->
                        <ElementTree type="vehicle" ref="vehicleTree" :checkMode="true" @check="selectNodes" node-key="id">
                        </ElementTree>
                    </el-tab-pane>
                    <el-tab-pane label="驾驶员" name="driverTree" disabled>
                        <!-- <ZtreeMatics treeType="driverTree" ref="driverTree" :checkMode="true" style="padding: 10px;"></ZtreeMatics> -->
                        <ElementTree type="driverTree" ref="driverTree" :checkMode="true" @check="selectNodes1" node-key="id">
                        </ElementTree>
                    </el-tab-pane>
                </el-tabs>
            </div>

            <div class="query-bottom bg bg--lighter">
                <div class="query-item">
                    <span>开始时间:</span>
                    <el-date-picker 
                        :clearable="false"
                        @change="dateStartChange"
                        :picker-options="startDatePickerOptions"
                        v-model="queryList.start_time" type="date"  
                        value-format="yyyy-MM-dd" placeholder="选择日期">
                    </el-date-picker>
                </div>
                <div class="query-item">
                    <span>结束时间:</span>
                    <el-date-picker 
                        :clearable="false"
                        :picker-options="endDatePickerOptions"
                        v-model="queryList.end_time" type="date"  
                        value-format="yyyy-MM-dd" placeholder="选择日期">
                    </el-date-picker>
                </div>
                <div class="query-item">
                    <el-button size="mini" type="primary" style="width: 100%;" :loading="table.loading"
                        @click="searchCurrentDetail">查询
                    </el-button>
                </div>
            </div>
        </template>

        <template slot="query">
            <div class="query-item" style="font-size: 20px; ">
                <el-button size="mini" type="primary" @click="exportDataInfo">导出</el-button>
            </div>
            <div class="break-item"></div>
            <div class="query-item" style="font-size: 20px; ">
                {{ formatTitle }}
            </div>
            <div class="break-item"></div>
        </template>

        <template slot="footer">
            <el-pagination background small
                :current-page.sync="table.page"
                :page-size="table.size"
                layout="prev, pager, next, total"
                :total="table.data.length">
            </el-pagination>
        </template>
        
        <template slot="content">
            <div class="condition condition--bg">
                <div class="name-part"></div>
                <ul class="chart-part"><li v-for="item in (25)" :key="item"></li></ul>
                <div class="matics-part"></div>
            </div>
            <div class="condition condition--title">
                <div class="name-part"></div>
                <div class="chart-part">
                    <span :class="{ 'night-part': index >= queryList.night || index <= queryList.day }" 
                        v-for="(item, index) in (25)" :key="item">{{ index }}
                    </span>
                </div>
                <div class="matics-part dfc">工作时长日夜分布</div>
            </div>
            <div class="condition--content">
                <div class="condition condition--detail" v-for="(item, index) in formatList" :key="index">
                    <div class="name-part dfaw">
                        <span>{{ item.plate_no }}</span>
                        <span>{{ item.date }}</span>
                        <span>{{ item.workTime }}</span>
                    </div>
                    <div class="chart-part dfc">
                        <div class="night night--start" :style="startNight"></div>
                        <AreaChart class="lineChart" :chartResult="item.accLine"></AreaChart>
                        <div class="night night--end" :style="endNight"></div>
                    </div>
                    <div class="matics-part dfc">
                        <PieChart :chartData="item.chartObj"></PieChart>
                    </div>
                </div>
            </div>

        </template>

    </Layout>
</template>

<script>
import AreaChart from './AreaChart'
import PieChart from './PieChart'
const ExportJsonExcel = require('js-export-excel')
export default {
    name: 'workingCondition',
    components: { AreaChart, PieChart },
    data () {
        return {
            startDatePickerOptions: {
                disabledDate: function (date) {
                    return (moment().subtract(1, 'day').endOf('day').toDate() - date ) < 0
                }
            },

            queryList: {
                start_time: moment().subtract(1, 'day').format('YYYY-MM-DD'),
                end_time: moment().subtract(1, 'day').format('YYYY-MM-DD'),
                target_id_list: [], 
                type: 0,    // 1 车 2 司机
                day: 6,
                night: 22
            },

            table: {
                activeTab: 'vehicleTree',
                loading: false,
                page: 1,
                size: 10,
                data: [],
            },
            vehicleIdList:[],
            driverIdList:[],
        };
    },

    computed: {
        endDatePickerOptions: function () {
            return {
                disabledDate: (date) => {
                    return (date - moment().subtract(1, 'day').endOf('day').toDate()) > 0 ||
                        date - moment(this.queryList.start_time).valueOf() < 0;
                }
            }
        },
        formatTitle() {
            return moment(this.queryList.start_time).valueOf() == moment(this.queryList.end_time).valueOf()?
                `${moment(this.queryList.start_time).format('YYYY年MM月DD日')} 车辆工作状态报表`:
                `${moment(this.queryList.start_time).format('YYYY年MM月DD日')} —— 
                    ${ moment(this.queryList.end_time).format('YYYY年MM月DD日') } 车辆工作状态报表`
        },
        startNight() {
            return `width: ${(this.queryList.day / 24) * 100}%`
        },
        endNight() {
            return `width: ${((24 - this.queryList.night) / 24) * 100}%`
        },

        pageStart() {
            return (this.table.page - 1) * this.table.size
        },
        formatList() {
            return this.table.data.slice(this.pageStart, this.pageStart + this.table.size)
        }
    },

    mounted() {

    },

    methods: {
        selectNodes(current, { checkedNodes }) {
        this.vehicleIdList = checkedNodes
        .filter((item) => item.type == 4)
        .map((item) => item.id);
        },
        selectNodes1(current, { checkedNodes }) {
        this.driverIdList = checkedNodes
        .filter((item) => item.type == 4)
        .map((item) => item.id);
        },
        exportDataInfo() {
            if(!this.table.data.length) {
                this.$warning('没有数据可以导出')
                return
            }

            let exportProp = ['companyDept', 'plate_no', 'date', 'workStart', 'workEnd', 'workTime']

            let excelBody = []
            this.table.data.forEach((item, index) => {
                let result = [index + 1].concat(exportProp.map(prpo => item[prpo])) 
                excelBody.push(result)
            })

            let options = {
                fileName: this.formatTitle,
                datas: [
                    {
                        sheetData: excelBody,
                        sheetHeader:['序号', '单位', '车牌号', '日期', '开始工作时间', '结束工作时间', '工作时长'],
                        columnWidths: ['3','18','8','10','10', '10', '10', '8']
                    }
                ]
            }

            ExportJsonExcel(options).saveExcel();
        },

        dateStartChange(date) {
            if (moment(this.queryList.end_time).valueOf() - moment(date).valueOf() < 0) {
                this.queryList.end_time = date;
            }
        },

        async searchCurrentDetail() {
            this.queryList.target_id_list = this.table.activeTab == 'vehicleTree'?this.vehicleIdList:this.driverIdList;
            if(!this.queryList.target_id_list.length) {
                this.$warning(this.table.activeTab == 'vehicleTree'?'请选择车辆':'请选择司机');
                return
            }
            this.table.loading = true
            this.queryList.type = this.table.activeTab == 'vehicleTree'?1:2
            let result = await this.$api.getVehicleWorkTimeV2(this.queryList)
            if(!result || result.status != 200) {
                this.$warning(result.message);
                this.table.data = []
                this.table.page = 1
                this.table.loading = false
                return
            }
            let handleList = []
            result.data.forEach(item => {
                let result = this.handleDayNight(item.acc_list)

                handleList.push({
                    id: item.vehicle_id || item.driver_id,
                    plate_no: item.plate_no,
                    date: item.date,
                    // 2020 08-05 10:19 项老板加的
                    companyDept: `${ item.export_company_name } ~ ${ item.export_dept_name }`,
                    workTime: item.export_drive_time,
                    workStart: item.export_start_time,
                    workEnd: item.export_end_time,

                    chartObj: [
                        { value: item.time_stats.drive_time_day, name:'日间行驶' },
                        { value: item.time_stats.drive_time_night, name:'夜间行驶' },
                        { value: item.time_stats.stop_time_day, name:'日间停车' },
                        { value: item.time_stats.stop_time_night, name:'夜间停车' },
                    ],
                    accLine: JSON.stringify({
                        date: item.date,
                        value: result
                    })
                })
            })
            this.table.data = handleList
            this.table.loading = false
        },

        handleDayNight(list) {
            let result = []

            if(list.length) {
                //  当天那一天 以及当天黑白分割线的毫秒值
                let date = this.DateFormat(list[0].start_time)
                let dateDay = moment(`${ date } 06:00:00`).valueOf()
                let dateNight = moment(`${ date } 20:00:00`).valueOf()

                list.forEach(item => {
                    let data = []
                    if(item.start_time < dateDay) {
                        if(item.end_time < dateDay) {
                            data =[{
                                value: [0, item.start_time, item.end_time, item.end_time - item.start_time]
                            }]
                        }
                        if(item.end_time > dateDay) {
                            data = [
                                { value: [0, item.start_time, dateDay, dateDay - item.start_time] },
                                { value: [1, dateDay, item.end_time, item.end_time - dateDay] },
                            ]
                        }
                        if(item.end_time > dateNight) {
                            data = [
                                { value: [0, item.start_time, dateDay, dateDay - item.start_time] },
                                { value: [1, dateDay, dateNight, dateNight - dateDay] },
                                { value: [0, dateNight, item.end_time, item.end_time - dateNight] },
                            ]
                        }
                    } else {
                        if(item.start_time < dateNight) {
                            if(item.end_time < dateNight) {
                                data =[{
                                    value: [1, item.start_time, item.end_time, item.end_time - item.start_time]
                                }]
                            }
                            if(item.end_time > dateNight) {
                                data = [
                                    { value: [1, item.start_time, dateNight, dateNight - item.start_time] },
                                    { value: [0, dateNight, item.end_time, item.end_time - dateNight] },
                                ]
                            }
                        } else {
                            data =[{
                                value: [0, item.start_time, item.end_time, item.end_time - item.start_time]
                            }]
                        }
                    }
                    data.forEach(data => {
                        data.itemStyle = {
                            normal: {
                                color: data.value[0]?'#55CD52':'#429EFD'
                            }
                        }
                    })
                    result = result.concat(data)
                })
            }

            return result
        },


        exportReportTable() {

        }
    }
}

</script>

<style lang='scss' scoped>
.workingCondition {
    .query-top {
        height: calc(100% - 145px);
    }   
    .query-bottom {
        margin-top: 5px;
        padding: 10px;
        .query-item {
            display: flex;
            height: 40px;
            line-height: 40px;
            justify-content: space-between;
            align-items: center;
            > span {
                font-size: 12px;
                white-space: nowrap;
                margin-right: 5px;
            }
        }
    }
}
.condition {
    display: flex;
    justify-content: space-between;
    flex-direction: row;
    .name-part {
        width: 115px;
        height: 100%;
        padding: 15px 0;
        font-size: 12px;
        span {
            width: 100%;
            text-align: center;
        }
    }
    .chart-part {
        height: 100%;
        width: calc(100% - 350px);
        flex-grow: 1;
        position: relative;

        .lineChart {
            z-index: 2;
        }
    }
    .matics-part {
        height: 100%;
        width: 200px;
    }
    &--content {
        height: calc(100% - 40px);
        overflow-y: auto;
        overflow-x: hidden;
        &::-webkit-scrollbar {
            width: 0;
        }
    }
    &--bg {
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        left: 0;
        padding-top: 40px;
        .chart-part {
            display: flex;
            justify-content: space-between;
            li {
                position: relative;
                width: 0;
                display: flex;
                justify-content: center;
                border-right: dashed 1px var(--border-color-lighter);
            }
        }
    }
    &--title {
        height: 40px;
        width: 100%;
        .chart-part {
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid var(--color-text-placeholder);
            span {
                width: 0;
                position: relative;
                display: flex;
                justify-content: center;
                &::before {
                    content: ' ';
                    position: absolute;
                    bottom: -10px;
                    left: 50%;
                    width: 1px;
                    height: 4px;
                    background: var(--color-text-placeholder);
                }
                &.night-part {
                    color: var(--color-text-placeholder);
                }
            }
        }
    }
    &--detail {
        height: 100px;
        width: 100%;
        position: relative;
        margin-top: 10px;
        border: 1px solid var(--border-color-base);
        .chart-part {
            .night {
                background-color: var(--background-color-lighter);
                height: 100%;
                position: absolute;
                top: 0;
                &--start {
                    left: 0;
                }
                &--end {
                    right: 0;
                }
            }
        }
    }
}
</style>

<template>
  <Layout class="vehicle-mileage" :has-color="true" :content-loading="table.loading">
    <template slot="aside">
      <ElementTree type="vehicle" ref="tree" :checkMode="true" @check="selectNodes"></ElementTree>
    </template>
    <template slot="query">
      <div class="query-item">
        <span>开始时间：</span>
        <el-date-picker v-model="query.dateRange[0]" style="width: 150px;" type="date" :picker-options="pickerOptions"
          @change="dateStartChange">
        </el-date-picker>
      </div>
      <div class="query-item">
        <span>结束时间：</span>
        <el-date-picker v-model="query.dateRange[1]" style="width: 150px" type="date"
          :picker-options="endDatePickerOptions">
        </el-date-picker>
      </div>
      <div class="break-item">
        <el-button type="primary" @click="getTableList(1)" :loading="table.loading">查询</el-button>
        <el-button type="primary" @click="exportTable" :loading="exportLoading">导出</el-button>
      </div>
      <div class="query-item">
        <!--                <el-button type="primary" >-->
        <!--                    <i class="pony-iconv2 pony-shezhi"></i>-->
        <!--                    规则设置-->
        <!--                </el-button>-->
        <el-pagination background small layout="prev, pager, next, total" :pager-count="5"
          :current-page.sync="pager.current" @current-change="getTableList" :page-size="pager.size" :total="pager.total">
        </el-pagination>
      </div>
    </template>
    <template slot="content">
      <el-table class="box-shadow el-table--radius" ref="table" :data="table.data" border stripe highlight-current-row
        size="mini" height="100%" style="width: 100%" @row-click="toggleRowExpansion" @expand-change="onRowExpand">
        <el-table-column type="expand">
          <div slot-scope="{row}" style="margin:-10px -40px;" v-loading="row.runStopTable.loading">
            <div class="query-wrap">
              <div class="query-item">
                <span style="margin-right: 10px">显示：</span>
                <el-select v-model="row.runStopTable.query.type">
                  <el-option :value="-1" label="全部"></el-option>
                  <el-option :value="1" label="仅显示行车"></el-option>
                  <el-option :value="0" label="仅显示停车"></el-option>
                </el-select>
              </div>
              <div class="break-item">
                <el-button type="primary" @click="getTableDetail(row)">查询</el-button>
                <el-button type="primary" @click="exportTableDetail(row)"
                  :loading="row.runStopTable.exportLoading">导出</el-button>
              </div>
              <div class="query-item">
                <el-pagination background small layout="prev, pager, next, total" :pager-count="5"
                  :current-page.sync="row.runStopTable.pager.current"
                  @current-change="(index) => getTableDetail(row, index)" :page-size="row.runStopTable.pager.size"
                  :total="row.runStopTable.pager.total">
                </el-pagination>
              </div>
            </div>
            <!-- :data="getCurrentPageData(row.runStopTable)" -->
            <el-table class="box-shadow el-table--radius" ref="detail" @wheel.stop border stripe highlight-current-row
              size="mini" :data="row.runStopTable.data" height="200px" style="width: 100%">
              <el-table-column type="index" align="center" width="80" :label="$t('common.index')"
                :index="(index) => index + 1 + ((row.runStopTable.pager.current - 1) * row.runStopTable.pager.size)"></el-table-column>
              <el-table-column label="行车/停车">
                <template slot-scope="scope">
                  <span title="行车" class="pony-iconv2 pony-hangshi" style="color:#4BD625"
                    v-show="scope.row.runstop_type == 1"></span>
                  <span title="停车" class="pony-iconv2 pony-tingche" style="color:#318DD1"
                    v-show="scope.row.runstop_type == 0"></span>
                </template>
              </el-table-column>
              <el-table-column prop="begin_time" width="170" label="开始时间"></el-table-column>
              <el-table-column prop="end_time" width="170" label="结束时间"></el-table-column>
              <el-table-column prop="mile" label="里程(km)"></el-table-column>
              <el-table-column prop="timeE" label="时长">
              </el-table-column>
              <el-table-column prop="begin_location" align="left" label="开始位置" show-overflow-tooltip>
              </el-table-column>
              <el-table-column prop="end_location" align="left" label="结束位置" show-overflow-tooltip>
              </el-table-column>
              <el-table-column label="操作" width="60">
                <template slot-scope="{row}">
                  <i class="pony-iconv2 pony-guijihuifang" title="地图" @click="jumpToPlayBack(row)"></i>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-table-column>
        <el-table-column :index="(index) => index + 1 + pageStart" type="index" width="50"
          :label="$t('common.index')"></el-table-column>
        <el-table-column prop="department" label="所属" show-overflow-tooltip></el-table-column>
        <el-table-column prop="plate_no" label="车牌号"></el-table-column>
        <el-table-column prop="mile" label="累计行驶里程(km)"></el-table-column>
        <el-table-column prop="run_time" label="累计行驶时长(h)"></el-table-column>
        <el-table-column prop="stop_time" label="累计停驶时长(h)"></el-table-column>
        <el-table-column prop="stop_count" label="累计停车次数"></el-table-column>
        <el-table-column label="操作" width="100">
          <template slot-scope="{row}">
            <el-button type="text" title="轨迹回放" @click.prevent="jumpToPlayBack(row)">
              <i class="pony-iconv2 pony-guijihuifang"></i>
            </el-button>
            <!-- <i class="pony-iconv2 pony-guijihuifang" title="地图" @click.native.prevent="jumpToPlayBack(row)"></i> -->
          </template>
        </el-table-column>
      </el-table>
    </template>
  </Layout>
</template>

<script>
/**
 * @Author: yezy
 * @Email: <EMAIL>
 * @Date: 2019/12/23 15:41
 * @LastEditors: yezy
 * @LastEditTime: 2019/12/23 15:41
 * @Description:
 */

export default {
  name: "vehicleRunStop",
  data() {
    return {
      query: {
        dateRange: [
          moment().subtract(1, 'days').startOf('day').toDate(),
          moment().subtract(1, 'days').endOf('day').toDate(),
        ],
      },
      pager: {
        current: 1,
        size: 30,
        total: 0,
      },
      pickerOptions: {
        disabledDate: function (date) {
          return (date - moment().subtract(1, 'days').endOf('day').toDate()) > 0
        }
      },
      table: {
        loading: false,
        data: []
      },
      vehicleIds: [],
      exportLoading: false,
    }
  },
  computed: {
    endDatePickerOptions: function () {
      return {
        disabledDate: (date) => {
          return (date - moment().subtract(1, 'days').endOf('day').toDate()) > 0 ||
            date - this.query.dateRange[0] < 0;
        }
      }
    },
    pageStart() {
      return (this.pager.current - 1) * this.pager.size
    },
  },
  methods: {
    getCurrentPageData(runStopTable) {
      console.log('runStopTable', runStopTable)
      const start = (runStopTable.pager.current - 1) * runStopTable.pager.size;
      return runStopTable.data.slice(
        start,
        start + runStopTable.pager.size,
      )
    },
    dateStartChange(date) {
      if (this.query.dateRange[1] - date < 0) {
        this.$set(this.query.dateRange, 1, date)
      }
    },
    async getTableList(pageIndex = 1) {
      this.table.loading = true;
      this.pager.current = pageIndex;
      try {
        const params = {
          start_time: this.DateFormat(this.query.dateRange[0]),
          end_time: this.DateFormat(this.query.dateRange[1]),
          vehicle_ids: this.vehicleIds,
          page: pageIndex,
          count: this.pager.size,
          type: -1,
        }
        let res = await this.$api.getVehicleRunStopInfo(params);
        if (res.rs === 1) {
          this.table.data = res['vehicle_run_stops_all'].map(item => {
            return {
              ...item,
              department: `${item.company_name} >> ${item.dept_name}`,
              loading: false,
              runStopTable: {
                data: [],
                loading: false,
                exportLoading: false,
                query: {
                  type: -1,
                },
                pager: {
                  current: 1,
                  size: 30,
                  total: 0,
                }
              },
            }
          })
          this.pager.total = res.count;
        } else {
          throw new Error('查询出错')
        }
      } catch (e) {
        this.$error(e)
      } finally {
        this.table.loading = false
      }

    },
    async getTableDetail(row, pageIndex = 1) {
      const table = row.runStopTable;
      table.loading = true;
      table.pager.current = pageIndex;
      try {
        const params = {
          start_time: this.DateFormat(this.query.dateRange[0]),
          end_time: this.DateFormat(this.query.dateRange[1]),
          type: table.query.type,
          vehicle_id: row.vehicle_id,
          page: pageIndex,
          count: table.pager.size,
        }
        let res = await this.$api.getVehicleRunStopInfoD(params);
        table.data = res.vehicle_run_stops.map(item => ({
          ...item,
          vehicle_id: res.vehicle_id,
        })) || [];
        table.pager.total = res.count || 0
      } catch (e) {
        this.$error(e)
      } finally {
        table.loading = false;
      }
    },

    selectNodes(data, { checkedNodes }) {
      this.vehicleIds = checkedNodes.filter(item => item.type === 4)
        .map(item => item.id)
    },
    toggleRowExpansion(row) {
      this.$refs['table'].toggleRowExpansion(row);
    },
    onRowExpand(row, expandRows) {
      if (expandRows.length && expandRows.includes(row)) {
        this.getTableDetail(row);
        //限制同时只能展开一行
        expandRows.forEach(item => {
          if (item === row) return;
          this.$refs['table'].toggleRowExpansion(item, false);
        })
      }
    },
    async exportTable() {
      const params = {
        start_time: this.DateFormat(this.query.dateRange[0]),
        end_time: this.DateFormat(this.query.dateRange[1]),
        vehicle_ids: this.vehicleIds,
        page: 1,
        count: this.pager.size,
        type: -1,
      }
      this.exportLoading = true;
      await this.$utils.excelExport(
        "/ponysafety2/a/report/exportgetvehiclerunstopinfo",
        JSON.stringify(params),
        "行车停车统计报表" + ".xls"
      );
      this.exportLoading = false;
    },
    async exportTableDetail(row) {
      const params = {
        start_time: this.DateFormat(this.query.dateRange[0]),
        end_time: this.DateFormat(this.query.dateRange[1]),
        type: row.runStopTable.query.type,
        vehicle_id: row.vehicle_id,
        page: 1,
        count: this.pager.size,
      }
      row.runStopTable.exportLoading = true;
      await this.$utils.excelExport(
        "/ponysafety2/a/report/exportgetvehiclerunstopinfoD",
        JSON.stringify(params),
        "行车停车明细报表" + ".xls"
      );
      row.runStopTable.exportLoading = false;
    },
    jumpToPlayBack(row) {
      let startTime = moment(this.query.dateRange[0]).startOf('day')
      let endTime = moment(this.query.dateRange[1]).endOf('day')
      row = {
        startTime: row.beginTimeE || startTime,
        endTime: row.endTimeE || endTime,
        vehicleId: row.vehicle_id,
      }
      this.$router.push({
        path: '/home/<USER>',
        query: row
      })
    }

  }
}
</script>

<style scoped lang="scss"></style>

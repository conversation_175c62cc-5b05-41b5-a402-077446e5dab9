<template>
  <Layout :has-color="true" :contentLoading="table.loading" class="erpList">

    <template slot="query">
      <div class="query-item">
        <div class="title">生产时间：</div>
        <el-date-picker type="datetime" placeholder="选择日期" v-model="queryList.productDateBegin">
        </el-date-picker>
        <span class="line">—</span>
        <el-date-picker type="datetime" placeholder="选择日期" v-model="queryList.productDateEnd">
        </el-date-picker>
      </div>
      <div class="query-item">
        <div class="title">搅拌站：</div>
        <div class="searchDiv" @click="selectStation(2)">已选{{ queryList.mixingStationIds.length }}个</div>
      </div>
      <div class="query-item">
        <div class="title">车牌号：</div>
        <el-autocomplete v-model="vehicleId" :fetch-suggestions="querySearch" placeholder="请输入车牌"
          :trigger-on-focus="false" @select="handleSelect" style="width: 170px;" clearable
          @clear='clearVehicleId'></el-autocomplete>
      </div>

      <div class="query-item" v-show="showConfig">
        <span class="title">待卸时间：</span>
        <el-input v-model="queryList.unloadingTimeBegin" style="width: 70px;"></el-input>
        <span class="line">—</span>
        <el-input v-model="queryList.unloadingTimeEnd" style="width: 70px;"></el-input>
      </div>
      <div class="query-item" v-show="showConfig">
        <span class="title">卸料时间：</span>
        <el-input v-model="queryList.dischargingTimeBegin" style="width: 70px;"></el-input>
        <span class="line">—</span>
        <el-input v-model="queryList.dischargingTimeEnd" style="width: 70px;"></el-input>
      </div>
      <div class="query-item" v-show="showConfig">
        <div class="title">总运时：</div>
        <el-input v-model="queryList.projectDurationBegin" style="width: 100px;"></el-input>
        <span class="line">—</span>
        <el-input v-model="queryList.projectDurationEnd" style="width: 100px;"></el-input>
      </div>
      <div class="query-item" v-show="showConfig">
        <div class="title">总运距：</div>
        <el-input v-model="queryList.projectDistanceBegin" style="width: 100px;"></el-input>
        <span class="line">—</span>
        <el-input v-model="queryList.projectDistanceEnd" style="width: 100px;"></el-input>
      </div>


      <!--            <div class="search-item">-->
      <!--                <div class="title">驾驶员：</div>-->
      <!--                <div class="searchDiv" @click="selectStation(1)">已选{{queryList.driverName.length}}个</div>-->
      <!--            </div>-->
      <div class="query-item" v-show="showConfig">
        <div class="title">驾驶员：</div>
        <el-input v-model="queryList.driverName" style="width: 150px;"></el-input>
      </div>
      <div class="query-item" v-show="showConfig">
        <div class="title">工程名称：</div>
        <el-input v-model="queryList.projectName" style="width: 150px;"></el-input>
      </div>
      <div class="query-item" v-show="showConfig">
        <div class="title">砼浇筑部位：</div>
        <el-input v-model="queryList.constructionPlace" style="width: 150px;"></el-input>
      </div>
      <div class="query-item" v-show="showConfig">
        <div class="title">任务单号：</div>
        <el-input v-model="queryList.taskNo" style="width: 150px;"></el-input>
      </div>
      <div class="query-item" v-show="showConfig">
        <div class="title">强度等级：</div>
        <el-input v-model="queryList.printConcrete" style="width: 150px;"></el-input>
      </div>
      <div class="query-item" v-show="showConfig">
        <div class="title">楼号：</div>
        <el-input v-model="queryList.ipcName" style="width: 150px;"></el-input>
      </div>
      <div class="query-item" style="padding: 0">
        <p class="query-more" @click="showConfig = !showConfig">
          更多<i :class="['pony-iconv2', showConfig ? 'pony-shang' : 'pony-xia']"></i>
        </p>
      </div>

      <div style="display: flex; flex-direction: row;width: 100%;margin-bottom: 4px">
        <div class="query-item">
          <div class="break-item">
            <el-button size="mini" type="primary" @click="search">查询</el-button>
          </div>
          <div class="break-item">
            <el-button size="mini" type="primary" @click="exportExcel">导出</el-button>
          </div>
          <div class="break-item">
            <el-button size="mini" type="primary" @click="operateTableSetting">设置表格</el-button>
          </div>
          <div class="break-item">
            <el-button size="mini" type="primary" @click="add">手动派单</el-button>
          </div>
        </div>
        <div class="break-item"></div>

        <div class="query-item">
          <el-pagination background small :current-page.sync="table.page" :page-size="table.size"
            layout="prev, pager, next, total" :total="table.list.length">
          </el-pagination>
        </div>
      </div>
    </template>

    <template slot="content">
      <el-table class="el-table--ellipsis" highlight-current-row stripe border size="mini" v-loading="table.loading"
        ref="table" :data="formList" style="width: 100%" @sort-change="sortCurrentProp" height="100%">
        <el-table-column fixed type="index" label="序号" width="50">
          <template slot-scope="scope">
            {{ scope.$index + 1 + pageStart }}
          </template>
        </el-table-column>
        <el-table-column fixed sortable="custom" prop="carNo" label="车号" width="150"></el-table-column>
        <el-table-column label="轨迹回放" min-width="100">
          <template slot-scope="{row}">
            <el-button type="text" size="mini" @click.native.stop="jumpOnPlayBack(row)" :disabled="row.vehicleId === '0'">
              <i class="pony-iconv2 pony-guijihuifang"></i>
            </el-button>
          </template>
        </el-table-column>

        <el-table-column v-for="(item, index) in tableSettingList" :key="index" :width="item.size" header-align="center"
          :align="item.align" :prop="item.key" :label="item.name" :sortable="item.sort" show-overflow-tooltip>
          <template slot-scope="scope">
            <span v-if="!item.type">{{ scope.row[item.key] }}</span>
          </template>
        </el-table-column>
      </el-table>
    </template>

    <TableShowConfigList ref="tableShowConfigList" v-model="tableSettingList" :filterableSearch=true
      :list="allSettingList" :defaultSetting="defaultSettingList" @change="settable"></TableShowConfigList>

    <AddDialog ref="addDialog"></AddDialog>

    <PonyDialog v-model="isShowDialog" :title="ponyDialogTitle" ref="pony" :width="400"
      content-style="height:550px; width: 100%; padding:0">
      <ElementTree :type="ponyDialogType" ref="department" :checkMode="checkMode" style="padding: 10px;"
        :extendSetting="{ view: { showIcon: false } }" node-key="id" @check="selectNodes"
        v-if="ponyDialogType !== 'vehicle'">
      </ElementTree>

      <template slot="footer">
        <el-button size="mini" type="primary" @click="isShowDialog = false">关闭</el-button>
        <el-button size="mini" type="primary" @click="changeStation">确认</el-button>
      </template>
    </PonyDialog>
  </Layout>
</template>

<script>
const ExportJsonExcel = require('js-export-excel')
import moment from "moment";
import TableShowConfigList from "../components/TableShowConfigList";
import AddDialog from "./AddDialog";
import { allSettingList, defaultSettingList } from './ErpTableList';
import pinyinMatch from "pinyin-match";

export default {
  name: "erpList",
  components: {
    TableShowConfigList,
    AddDialog
  },
  data() {
    return {
      tableSettingList: [],
      _vehicleInfoData: {},
      allSettingList,
      defaultSettingList,
      table: {
        setting: [],
        list: [],
        page: 1,
        size: 30,
        total: 0,
        loading: false
      },
      show: false, // 表格显示配置弹窗
      isShowDialog: false, // 树形弹窗
      ponyDialogTitle: '', // 树形弹窗标题
      ponyDialogType: '', // 树形弹窗类型
      ponyDialogFlag: '', // 树形弹窗标识符
      queryList: {
        productDateBegin: moment().subtract(0, 'days').startOf('day').toDate(),// 生产时间开始时间戳 必须
        productDateEnd: moment().endOf('day').toDate(), // 生产时间结束时间戳 必须
        vehicleIds: [], // 车辆id列表
        projectName: '', // 工程名称
        constructionPlace: '', // 砼浇筑部位
        taskNo: '', // 任务单号
        projectDurationBegin: '', // 总运时左
        projectDurationEnd: '', // 总运时右
        projectDistanceBegin: '', // 总运距左
        projectDistanceEnd: '', // 总运距右
        printConcrete: '', // 砼强度等级
        unloadingTimeBegin: null, // 待卸时间左
        unloadingTimeEnd: null, // 待卸时间右
        driverName: '', // 驾驶员
        dischargingTimeBegin: null, // 卸料时间左
        dischargingTimeEnd: null, // 卸料时间右
        ipcName: '', // 拌楼号
        mixingStationIds: [], // 搅拌站id
      },
      value: '', // 树形弹窗标识符 0 车号 1 驾驶员 2 搅拌站
      showConfig: false, //搜索条件字段的显示或者隐藏
      checkMode: true, //树是否多选
      vehicleId: '',
      vehicleNameList: [], //
    }
  },
  computed: {
    pageStart() {
      return (this.table.page - 1) * this.table.size
    },
    formList() {
      return this.table.list.slice(this.pageStart, this.pageStart + this.table.size)
    }
  },
  async mounted() {
    if (sessionStorage.getItem('erpTableSetting')) {
      this.tableSettingList = JSON.parse(sessionStorage.getItem('erpTableSetting'))
    } else {
      this.tableSettingList = JSON.parse(JSON.stringify(this.defaultSettingList))
    }
    await this.getPageSize()
    let result = await this.$api.getCarInfoAndGpsAlarm()
    this.vehicleNameList = result.data.lastGps.map(item => {
      return {
        value: item.industry_type === 8 ? item.alias + '-' + item.plate : item.plate,
        id: item.id
      }
    })
    this.handleJumpData()
  },
  methods: {
    clearVehicleId() {
      this.queryList.vehicleIds = []
    },
    querySearch(value, cb) {
      let results = []
      results = this.vehicleNameList.filter(data => {
        return pinyinMatch.match(data.value, value)
      }
      );
      // 调用 callback 返回建议列表的数据
      cb(results)
    },
    handleSelect(item) {
      this.queryList.vehicleIds = []
      this.queryList.vehicleIds[0] = item.id
    },
    //多选树
    selectNodes(current, { checkedNodes }) {
      if (this.ponyDialogType == 'driverTree') {
        this.queryList.driverName = checkedNodes
          .filter((item) => item.type == 4)
          .map((item) => item.id);
      } else {
        this.queryList.mixingStationIds = checkedNodes
          .filter((item) => item.type == 3)
          .map((item) => item.id);
      }
    },
    //单选
    selectNode(data, node) {
      if (data.type != 4) {
        this.$message({
          type: "warning",
          message: "请选择车辆！",
          showClose: true,
        });
        return;
      }
      this.queryList.vehicleIds[0] = data.id;
    },
    //混合排序,数字,字母,汉字排序
    sortCurrentProp(column) {
      if (!column.order || !column.prop) return
      let chineseChars = [], chars = [], codeChars = [], nullList = []
      let prop = column.prop
      this.table.list.forEach(item => {
        let reg = new RegExp("[\\u4E00-\\u9FFF]+", "g");
        let reg1 = /^[\d]+$/
        if (!item[prop]) {
          nullList.push(item)
        } else if (reg.test(item[prop])) {
          chineseChars.push(item);   // 姓名首字符为中文的
        } else if (reg1.test(item[prop])) {
          chars.push(item);   // 姓名首字符非中文的（字母，数字）
        } else {
          codeChars.push(item)
        }
      })
      if (column.order === 'ascending') {
        chars.sort((a, b) => a[prop] - b[prop]);
        codeChars.sort((a, b) => a[prop].charCodeAt(0) - b[prop].charCodeAt(0));
        chineseChars.sort((a, b) => a[prop].localeCompare(b[prop]));
      } else {
        chars.sort((a, b) => b[prop] - a[prop]);
        codeChars.sort((a, b) => b[prop].charCodeAt(0) - a[prop].charCodeAt(0));
        chineseChars.sort((a, b) => b[prop].localeCompare(a[prop]));
      }
      this.table.list = chars.concat(codeChars).concat(chineseChars).concat(nullList)
    },
    async getPageSize() {
      const res = await this.$api.getCommonListByKey({
        key: "user_page_size"
      })
      if (res.status !== 200) {
        return this.$error('查询用户配置错误！')
      } else {
        if (!res.data.id || res.data !== '' || res.data !== undefined) {
          this.table.size = res.data.config_value
        }
      }
    },
    // 跳转轨迹回放
    jumpOnPlayBack(row) {
      let startTime = moment(row.productDate)
      let endTime
      if (row.backTime) {
        endTime = moment(row.backTime)
      } else {
        endTime = moment(row.productDate).endOf('d')
      }
      let parmas = {
        vehicleId: row.vehicleId,
        startTime: startTime,
        endTime: endTime,
        mixingStationId: row.mixingStationId,
        workFenseId: row.workFenseId
      }
      this.$router.push({
        path: '/home/<USER>',
        query: parmas
      })
    },
    add() {
      this.$refs.addDialog.showModel()
    },
    settable(value) {
      sessionStorage.setItem('erpTableSetting', JSON.stringify(value))
      this.$nextTick(() => {
        this.$refs.table.doLayout()
      })
    },
    async search() {
      this.table.loading = true
      this._vehicleInfoData = {}

      if (this.queryList.productDateBegin === null && this.queryList.productDateBegin === '') {
        this.$message({ type: 'warning', showClose: true, message: '请选择生产时间' });
      } else {
        this.queryList.productDateBegin = moment(this.queryList.productDateBegin).format("YYYY-MM-DD HH:mm:ss")
      }
      if (this.queryList.productDateEnd === null && this.queryList.productDateEnd === '') {
        this.$message({ type: 'warning', showClose: true, message: '请选择生产时间' });
      } else {
        this.queryList.productDateEnd = moment(this.queryList.productDateEnd).format("YYYY-MM-DD HH:mm:ss")
      }

      let res = await this.$api.erpOrderList(this.queryList)
      if (res.data.length === 0) {
        this.table.loading = false
        this.$message({ type: 'warning', showClose: true, message: '暂无数据' });
        this.table.list = []
        return
      }

      let arr = res.data
      arr.forEach((item) => {
        if (item.leaveTime && item.arriveTime) {
          item.hoursOnSite = this.transSecondToHMSCN((item.leaveTime - item.arriveTime) / 1000)
        } else {
          item.hoursOnSite = '-'
        }
        item.productDate = this.formatDate(item.productDate)
        item.deliveryTime = this.formatDate(item.deliveryTime)
        item.arriveTime = this.formatDate(item.arriveTime)
        item.startupTime = this.formatDate(item.startupTime)
        item.completedTime = this.formatDate(item.completedTime)
        item.leaveTime = this.formatDate(item.leaveTime)
        item.backTime = this.formatDate(item.backTime)
        item.takeTime = this.formatDate(item.takeTime)
        item.supplyTimes = Number(item.supplyTimes)
        if (item.pon === 0) {
          item.pon = '不需要'
        } else {
          item.pon = '需要'
        }
        if (item.taskStatus === "1") {
          item.taskStatus = '正供'
        } else if (item.taskStatus === "2") {
          item.taskStatus = '出厂'
        } else if (item.taskStatus === "3") {
          item.taskStatus = '在工地'
        } else if (item.taskStatus === "4") {
          item.taskStatus = '回厂'
        } else if (item.taskStatus === "5") {
          item.taskStatus = '供毕'
        }
      })

      this.table.list = arr
      this.table.list.forEach((item) => {
        this._vehicleInfoData[item.vehicleId] = item
      })
      this.table.loading = false
      this.$nextTick(() => {
        this.$refs['table'].doLayout()
      })
    },
    formatDate(date) {
      if (date == null || date == '') {
        return ''
      } else {
        var now = new Date(date);
        var year = now.getFullYear();  //取得4位数的年份
        var month = now.getMonth() + 1;  //取得日期中的月份，其中0表示1月，11表示12月
        var date = now.getDate() < 10 ? '0' + now.getDate() : now.getDate();      //返回日期月份中的天数（1到31）
        var hour = now.getHours() < 10 ? '0' + now.getHours() : now.getHours();     //返回日期中的小时数（0到23）
        var minute = now.getMinutes() < 10 ? '0' + now.getMinutes() : now.getMinutes(); //返回日期中的分钟数（0到59）
        var second = now.getSeconds() < 10 ? '0' + now.getSeconds() : now.getSeconds(); //返回日期中的秒数（0到59）
        return year + "-" + month + "-" + date + " " + hour + ":" + minute + ":" + second;
      }
    },
    transSecondToHMSCN(value) {
      if (value === '-') {
        return ''
      }
      var secondTime = parseInt(value)
      var minuteTime = 0
      var hourTime = 0
      var dayTime = 0
      if (secondTime >= 60) {
        minuteTime = parseInt(secondTime / 60);
        secondTime = parseInt(secondTime % 60);
        if (minuteTime >= 60) {
          hourTime = parseInt(minuteTime / 60);
          minuteTime = parseInt(minuteTime % 60);
          if (hourTime >= 24) {
            dayTime = parseInt(hourTime / 24)
            hourTime = parseInt(hourTime % 24)
          }
        }
      }
      var result = ''
      // if (secondTime > 0) {
      //     result = "" + parseInt(secondTime) + " 秒";
      // }
      if (minuteTime > 0) {
        result = "" + parseInt(minuteTime) + " 分 " + result;
      }
      if (hourTime > 0) {
        result = "" + parseInt(hourTime) + " 时 " + result;
      }
      if (dayTime > 0) {
        result = "" + parseInt(dayTime) + " 天 " + result;
      }
      return result;
    },
    // 打开树形弹窗
    async selectStation(value) {
      this.ponyDialogFlag = value
      if (value === 0) {
        this.ponyDialogTitle = '车号'
        this.ponyDialogType = 'vehicle'
        this.checkMode = false
      } else if (value === 1) {
        this.ponyDialogTitle = '驾驶员'
        this.ponyDialogType = 'driverTree'
        this.checkMode = true
      } else if (value === 2) {
        this.ponyDialogTitle = '搅拌站'
        this.ponyDialogType = 'mixingStationTree'
        this.checkMode = true
      }
      this.isShowDialog = true
      await this.$nextTick()

      await this.$refs['department'].waitForInit
      if (value === 0) {
        if (this.queryList.vehicleIds.length) {
          this.$refs['department'].$refs['tree'].setCurrentKey(this.queryList.vehicleIds[0])
          this.$refs["department"].$refs.tree.getNode(this.queryList.vehicleIds[0]).expand(null, true);
        }
      } else if (value === 1) {
        if (this.queryList.driverName) {
          this.$refs['department'].$refs['tree'].setCheckedKeys(this.queryList.driverName)
        }
      } else if (value === 2) {
        if (this.queryList.mixingStationIds) {
          this.$refs['department'].$refs['tree'].setCheckedKeys(this.queryList.mixingStationIds)
        }
      }
    },
    // 树形弹窗点击确定
    changeStation() {

      this.isShowDialog = false
    },
    // 点击设置表格按钮
    operateTableSetting() {
      this.$refs.tableShowConfigList.showModel()
    },
    // 导出excel
    exportExcel() {
      if (!this.table.list.length) {
        this.$message({ showClose: true, message: "暂无数据", type: 'warning' })
        return
      }
      let excelBody = []
      this.table.list.forEach((item, index) => {
        let array = [index + 1]
        array.push(item['carNo'])
        this.tableSettingList.forEach(rule => {
          array.push(item[rule.key])
        })
        excelBody.push(array)
      })

      let options = {
        fileName: "ERP订单",
        datas: [
          {
            sheetData: excelBody,
            sheetHeader: ['序号'].concat(['车号']).concat(this.tableSettingList.map(item => item.name)),
            columnWidths: this.tableSettingList.map(item => '10')
          }
        ]
      }
      ExportJsonExcel(options).saveExcel();
    },
    async handleJumpData() {
      let parmas = this.$route.query
      if (!parmas || !parmas.vehicleId) return
      if (parmas.start) {
        this.queryList.productDateBegin = parmas.start
        this.queryList.productDateEnd = parmas.end
        this.queryList.vehicleIds = [parmas.vehicleId]
        this.vehicleId = parmas.plateNo
      }
      this.$nextTick(async () => {
        this.search()
        await this.$router.push('/home/<USER>')
      })
    }
  },
  activated() {
    this.handleJumpData()
  }
}
</script>

<style lang='scss' scoped>
.erpList {
  .query-item {
    .line {
      margin: 0 10px;
      color: var(--border-color-light);

    }

    .query-more {
      width: 100%;
      text-align: center;
      font-size: 12px;
      line-height: 1;
      color: var(--color-primary);
      cursor: pointer;
    }

    .searchDiv {
      height: 28px;
      width: 150px;
      line-height: 27px;
      border-radius: 4px;
      border: 1px solid var(--border-color-light);
      color: var(--color-text-regular);
      padding: 0 15px;
      font-size: 12px;
    }
  }

}
</style>

<template>
  <Layout tag="div" class="VehicleRunTrack"
          :contentLoading="table.loading"
          :has-color="true">

    <template slot="aside">
      <div class="query-top">
        <ElementTree ref="vehicleTree" :checkMode="false" type="vehicle"></ElementTree>
      </div>
      <div class="query-bottom bg bg--lighter">
        <div class="query-item" style="height:70px;margin-bottom:10px">
          <StartEndTime itemHeight="35" style="width:100%"
                        titleStyle="font-size:12px;margin-right:3px"
                        :timeTitle="['开始时间:','结束时间:']"
                        v-model="selectStartEndTime" timeType="datetime" :isLimit="true">
          </StartEndTime>
          <!-- <span>开始时间:</span>
          <el-date-picker
              @change="dateStartChange"
              :clearable="false"
              :picker-options="startDatePickerOptions"
              v-model="queryList.start" type="datetime"
              value-format="yyyy-MM-dd HH:mm:ss"
              placeholder="选择日期">
          </el-date-picker> -->
        </div>
        <!-- <div class="query-item">
            <span>结束时间:</span>
            <el-date-picker
                :clearable="false"
                :picker-options="endDatePickerOptions"
                v-model="queryList.end" type="datetime"
                value-format="yyyy-MM-dd HH:mm:ss"
                placeholder="选择日期">
            </el-date-picker>
        </div> -->
        <div class="query-item" @click="operateTableSetting">
          <span> 数 据 项 :</span>
          <div class="table-list" style="width: 79%">
            <div class="table-num">已选中{{ table.setting.length }}个</div>
            <i class="el-icon-arrow-down"></i>
          </div>
        </div>
        <div class="query-item">
          <span> 融 合 度 :</span>
          <div style="width: 79%" class="table-list">
            <el-input-number v-model="timeUnit" :min="0.5" :max="30" label="描述文字"
                             :step="0.5"></el-input-number>
            <span style="margin-right: 8px;">分钟</span>
            <!-- <span>原始数据</span>
             <el-checkbox v-model="checked" ></el-checkbox>
              -->
          </div>
          <!-- <div style="width: 79%" class="table-list">
             <el-input-number v-model="timeUnit" :min="0.5" :max="30" label="描述文字" :step="0.5" ></el-input-number>
             <span>分</span>
         </div> -->
        </div>
        <div class="qury-item">
          <el-checkbox v-model="checked">原始数据</el-checkbox>
        </div>

        <div class="query-item">
          <el-button size="mini" type="primary" style="width: 100%;" @click="search" :loading="table.loading">查询</el-button>
        </div>
      </div>
    </template>

    <template slot="query">
      <div class="query-item">
        <el-button type="primary" @click="exportExcel">导出</el-button>
      </div>
      <div class="break-item"></div>
      <div class="query-item">
        <el-pagination small background
                       :current-page.sync="table.page"
                       :page-size="table.size"
                       layout="prev, pager, next, total"
                       :total="formatterTotal">
        </el-pagination>
      </div>
    </template>
    <el-table
      ref="table"
      class="el-table--ellipsis el-table--radius" slot="content"
      border stripe highlight-current-row size="mini"
      :data="formatVehicleList"
      height="100%" style="width: 100%">
      <el-table-column type="index" :index="(index) => index + 1 + pageStart" label="序号" width="50"
                       fixed></el-table-column>
      <el-table-column prop="plateNo" label="车牌号" width="150" fixed></el-table-column>
      <el-table-column prop="time" label="时间" width="150" fixed></el-table-column>

      <el-table-column v-for="(item, index) in table.setting" :key="index"
                       :width="item.business_value" header-align="center" :align="item.align" :prop="item.id"
                       :label="tipObj[item.id] ? item.name +'('+tipObj[item.id] +')':item.name" :sortable="item.sort"
                       show-overflow-tooltip>
        <template slot="header" slot-scope="{column,$index}">
          <span class="tableHeader">{{column.label}}&#x200E;</span>
        </template>
        <template slot-scope="scope">
          <span>{{ scope.row[item.id] }}</span>
        </template>
      </el-table-column>

    </el-table>

    <PonyDialog v-model="show" title="表格显示配置"
                :width="670" content-style="height:600px; overflow:auto">
      <TransferPlusTree
        ref="transferPlusTree"
        v-model="defaultSettingList"
        :filterableSearch=true
        :node-key="'id'"
        auto-width="500"
        autoHeight="100%"
        :defaultSetting="getTableSetting"
        :type="['getDataItemTreeSuperFleet:0','getDataItemTreeSuperFleet:1']">
      </TransferPlusTree>
      <template slot="footer">
        <el-button size="mini" type="primary" @click="clearDeFault">恢复默认</el-button>
        <el-button size="mini" type="primary" @click="show = false">关闭</el-button>
        <el-button size="mini" type="primary" @click="changeTableSetting">确认</el-button>
      </template>
    </PonyDialog>

  </Layout>
</template>

<script>
import TransferPlusTree from '../../components/common/TransferDraggleTreeTab'
import StartEndTime from "@/components/common/StartEedTime";

const ExportJsonExcel = require('js-export-excel')

export default {
  name: "carDataSearch",
  components: {
    TransferPlusTree,
    StartEndTime
  },
  data() {
    return {
      // 是否勾选融合度
      checked: true,
      timeUnit: 0,
      queryList: {
        start: moment().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
        end: moment().endOf('day').format('YYYY-MM-DD HH:mm:ss'),
        columnList: [],
        vehicleIdList: [],
      },
      selectStartEndTime: [
        moment().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
        moment().endOf('day').format('YYYY-MM-DD HH:mm:ss'),
      ],
      table: {
        show: false,
        loading: false,
        list: [],
        page: 1,
        size: 30,
        setting: [],
      },
      idCurrentNodes: '',
      defaultSettingList: [], // 弹窗中已选中的表头
      // 默认表头
      getTableSetting: [
        {name: 'ACC状态', id: 'acc', size: '200', align: "center"},
        {name: '发动机转速', id: 'engineSpeed', size: '200', align: "center"},
        {name: '剩余油量百分比', id: 'oilRate', size: '200', align: "center"},
        {name: '蓄电池电压', id: 'batteryVoltage', size: '200', align: "center"},
        {name: '发动机冷却水温度', id: 'waterTemp', size: '200', align: "center"},
        {name: '冷却剂液位', id: 'coolLevel', size: '200', align: "center"},
        {name: '机油温度', id: 'oilTemp', size: '200', align: "center"},
        {name: '载重', id: 'weight', size: '200', align: "center"},
        {name: '取力器PTO状态', id: 'PTO', size: '200', align: "center"},
        {name: '油门开度', id: 'throttleOpen', size: '200', align: "center"},
      ],
      tipObj: {
        ev_speed: 'km/h',
        ev_mile: 'km',
        ev_voltage: 'V',
        ev_current: 'A',
        ev_accelerator: '%',
        ev_rpm: 'r/min',
        ev_fuelEconomy: 'L/100km',
        ev_soe: 'kw.H',
        ev_batteryCharged: 'kw.H',
        ev_batteryDischarged: 'kw.H',
        engineSpeed: 'rpm',
        oilRate: '%',
        batteryVoltage: 'V',
        waterTemp: '℃',
        coolLevel: '%',
        oilTemp: '℃',
        weight: 'kg',
        throttleOpen: '%',
        engineIdleOilConsume: 'L',
        engineIdleTime: 'h',
        engineRunTime: 'h',
        engineOilConsume: 'L',
        mile: 'km',
        oilPressure: 'kpa',
        engineRotate: 'r',
        airTemp: '℃',
      },
      show: false,
      formatterTotal: 0,
      exportList: ''
    }
  },
  computed: {
    pageStart() {
      return (this.table.page - 1) * this.table.size
    },
    formatVehicleList() {
      let tableList = []
      let tables = this.table.setting.map((item) => item.id)

      this.table.list.filter((item) => {
        for (let i = 0; i < tables.length; i++) {
          let tableItem = tables[i]
          if (item[tableItem] && item[tableItem] !== '' && item[tableItem] !== undefined) {
            tableList.push(item)
            return
          }
        }
      })
      // 要导出的数据
      this.exportList = tableList
      // 总数
      this.formatterTotal = tableList.length
      // 分页
      tableList = tableList.slice(this.pageStart, this.pageStart + this.table.size)
      this.$nextTick(() => {
        this.$refs['table'].doLayout()
      })
      return tableList
    },
    // 开始时间限制，禁用今天之后的选项
    startDatePickerOptions: function () {
      return {
        disabledDate: (date) => {
          return (moment().endOf('day').toDate() - date) < 0
        }
      }
    },
    // 结束时间限制，禁用今天之后的选项、开始时间之前的选项、开始时间后2天选项
    endDatePickerOptions: function () {
      return {
        disabledDate: (date) => {
          return (date - moment().endOf('day').toDate()) > 0 ||
            date - moment(this.queryList.start).startOf('day').valueOf() < 0 ||
            date - moment(this.queryList.start).startOf('day').valueOf() > 2 * 24 * 60 * 60 * 1000
        }
      }
    },
  },
  watch: {
    'selectStartEndTime': function (newVal, oldVal) {
      this.queryList.start = newVal[0]
      this.queryList.end = newVal[1]
    }
  },
  activated() {
    this.handleJumpLink()
  },
  mounted() {
    if (localStorage.getItem('carDataTableSetting')) {
      let localSetting = localStorage.getItem('carDataTableSetting')
      this.table.setting = JSON.parse(localSetting)
    } else {
      this.table.setting = this.getTableSetting
      this.defaultSettingList = this.getTableSetting.map(item => item.id)
    }
  },
  methods: {
    async handleJumpLink() {
      let parmas = this.$route.query;
      if (!parmas || !parmas.vehicleId) return;
      this.selectStartEndTime = [
        parmas.startTime,
        parmas.endTime,
      ]
      this.queryList.start = parmas.startTime
      this.queryList.end = parmas.endTime
      this.queryList.vehicleIdList = [parmas.vehicleId]
      await this.waitForInit;
      let obj = this.table.setting.find(item => item.id == 'oilRate')
      if (!obj) {
        this.table.setting.push({name: '剩余油量百分比', id: 'oilRate', size: '200', align: "center", business_value: '180'})
      }
      this.$nextTick(async () => {
        this.$refs["vehicleTree"].$refs.tree.setCurrentKey(parmas.vehicleId);
        let par = this.$refs["vehicleTree"].$refs.tree.getNode(parmas.vehicleId)
        par?.expand(null, true);
        await this.search();
        this.$router.replace({
          ...this.$route,
          query: {},
        })
      });
    },
    // 点击设置表格按钮
    operateTableSetting() {
      this.defaultSettingList = this.table.setting.map(item => item.id)
      this.show = true
    },
    // 设置表格弹窗点击恢复按钮
    clearDeFault() {
      this.$refs.transferPlusTree.clearDeFault()
    },
    // 设置表格弹窗点击确认按钮
    changeTableSetting() {
      this.table.setting = this.$refs.transferPlusTree.getTableList()
      localStorage.setItem('carDataTableSetting', JSON.stringify(this.table.setting))
      this.show = false
    },
    // 点击开始时间，默认将结束时间设置为2天后
    dateStartChange(date) {
      let newDate = moment(date).endOf('day').toDate()
      this.queryList.end = moment(newDate).add(2, 'days').format('YYYY-MM-DD HH:mm:ss')
    },
    // 搜索数据
    async search() {
      this.table.page = 1
      let vehicle = this.$refs.vehicleTree.$refs.tree.getCurrentNode()
      if (!vehicle || vehicle.type !== 4) {
        this.$message.warning('请选择车辆！')
        return
      } else {
        this.queryList.vehicleIdList = [vehicle.id]
      }
      // this.queryList.columnList = this.table.setting.map(item => item.id)
      this.queryList.analysis = true
      if (this.queryList.vehicleIdList.length === 0) {
        return this.$message.warning('请选择车辆！')
      }
      let queryTimeUnit
      if (this.checked) {
        this.timeUnit = 0
        queryTimeUnit = 0
      } else {
        queryTimeUnit = this.timeUnit * 60000
      }
      this.table.loading = true
      let res = await this.$api.getCarDataSearch(Object.assign(this.queryList, {
        timeUnit: queryTimeUnit
      }))
      if (res.status === 200) {
        if (res.data.page.length === 0) {
          this.table.loading = false
          this.table.list = []
          return this.$message.warning('暂无数据！')
        }
        res.data.page.forEach((item) => {
          // if (item['engineSpeed'] !== '' && item['engineSpeed'] !== undefined) item['engineSpeed'] = item['engineSpeed'] + 'rpm'
          // if (item['oilRate'] !== '' && item['oilRate'] !== undefined) item['oilRate'] = item['oilRate'] + '%'
          // if (item['batteryVoltage'] !== '' && item['batteryVoltage'] !== undefined) item['batteryVoltage'] = item['batteryVoltage'] + 'V'
          // if (item['waterTemp'] !== '' && item['waterTemp'] !== undefined) item['waterTemp'] = item['waterTemp'] + '℃'
          // if (item['coolLevel'] !== '' && item['coolLevel'] !== undefined) item['coolLevel'] = item['coolLevel'] + '%'
          // if (item['oilTemp'] !== '' && item['oilTemp'] !== undefined) item['oilTemp'] = item['oilTemp'] + '℃'
          // if (item['weight'] !== '' && item['weight'] !== undefined) item['weight'] = item['weight'] + 'kg'
          // if (item['throttleOpen'] !== '' && item['throttleOpen'] !== undefined) item['throttleOpen'] = item['throttleOpen'] + '%'
          // if (item['engineIdleOilConsume'] !== '' && item['engineIdleOilConsume'] !== undefined) item['engineIdleOilConsume'] = item['engineIdleOilConsume'] + 'L'
          // if (item['engineIdleTime'] !== '' && item['engineIdleTime'] !== undefined) item['engineIdleTime'] = item['engineIdleTime'] + 'h'
          // if (item['engineRunTime'] !== '' && item['engineRunTime'] !== undefined) item['engineRunTime'] = item['engineRunTime'] + 'h'
          // if (item['engineOilConsume'] !== '' && item['engineOilConsume'] !== undefined) item['engineOilConsume'] = item['engineOilConsume'] + 'L'
          // if (item['mile'] !== '' && item['mile'] !== undefined) item['mile'] = item['mile'] + 'km'
          // if (item['oilPressure'] !== '' && item['oilPressure'] !== undefined) item['oilPressure'] = item['oilPressure'] + 'kpa'
          // if (item['engineRotate'] !== '' && item['engineRotate'] !== undefined) item['engineRotate'] = item['engineRotate'] + 'r'
          // if (item['airTemp'] !== '' && item['airTemp'] !== undefined) item['airTemp'] = item['airTemp'] + '℃'
          if (item['gearboxPos'] !== '' && item['gearboxPos'] !== undefined) item['gearboxPos'] = this.changeBoxPos(item['gearboxPos'])
        })
        this.table.list = res.data.page
        this.table.loading = false
        this.$nextTick(() => {
          this.$refs['table'].doLayout()
        })
      } else {
        this.$message.error('查询出错！')
        this.table.loading = false
      }
    },
    changeBoxPos(val) {
      if (Number(val) === 0) {
        return '空挡'
      } else {
        return val + '档'
      }
    },
    // 导出excel
    exportExcel() {
      if (!this.table.list.length) {
        this.$message({showClose: true, message: "暂无数据", type: 'warning'})
        return
      }
      let keyList = ['plateNo'].concat(['time']).concat(this.table.setting.map(item => item.id))

      let excelBody = []
      this.exportList.forEach((item, index) => {
        let array = [index + 1].concat(keyList.map(prop => item[prop]))
        excelBody.push(array)
      })
      let options = {
        fileName: "CAN历史数据明细",
        datas: [
          {
            sheetData: excelBody,
            sheetHeader: ['序号'].concat(['车牌号']).concat(['时间']).concat(this.table.setting.map(item => this.tipObj[item.id] ? item.name + '(' + this.tipObj[item.id] + ')' : item.name)),
            columnWidths: this.table.setting.map(item => '10')
          }
        ]
      }
      ExportJsonExcel(options).saveExcel();
    }
  }
}
</script>

<style lang='scss' scoped>
.VehicleRunTrack {

  .query-top {
    height: calc(100% - 245px);
  }

  .query-bottom {
    margin-top: 5px;
    padding: 10px;

    .query-item {
      display: flex;
      height: 40px;
      line-height: 40px;
      justify-content: space-between;
      align-items: center;
      font-size: 12px;

      > span {
        font-size: 12px;
        white-space: nowrap;
        margin-right: 5px;
      }

      .table-list {
        width: 100%;
        height: 28px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        box-sizing: border-box;
        background-color: var(--background-color-base);
        border-radius: 4px;
        border: 1px solid var(--border-color-base);
        padding: 0 10px;
        line-height: 28px;
        font-size: 12px;
        flex-wrap: wrap;

        > span {
          font-size: 12px;
          white-space: nowrap;
          margin-left: 5px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsiss;
        }

        /deep/ .el-checkbox {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsiss;
        }

        .table-num {
          width: 75px;
          height: 20px;
          background-color: var(--border-color-light);
          border: 1px solid var(--border-color-extra-light);
          border-radius: 5px;
          line-height: 20px;
          padding: 0 5px;
        }
      }
    }


  }

  .el-checkbox {
    margin-left: 173px !important;
  }

  /deep/ .el-checkbox__label {
    font-size: 12px;
    padding: 4px;
  }
}

.tableHeader {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  direction: rtl;
  width: 100%;
  display: inline-block;
  height: 15px;
  line-height: 20px;
}
</style>

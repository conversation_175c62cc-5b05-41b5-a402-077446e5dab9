<template>
  <Layout class="ztc-stats" :content-loading="loading">
    <template slot="aside">
      <ElementTree type="department" class="query-top bg bg--light" @node-click="nodeClick"></ElementTree>
      <div class="query-bottom bg bg--light">
        <div class="query-item">
          <el-select v-model="query.type">
            <el-option label="按日选择" :value="0"></el-option>
            <el-option label="按周选择" :value="1"></el-option>
            <el-option label="按月选择" :value="2"></el-option>
          </el-select>
        </div>
        <div class="query-item">
          <el-date-picker v-if="query.type === 0" v-model="query.date" :clearable="false" type="date"></el-date-picker>
          <el-date-picker v-if="query.type === 1" v-model="query.date" :clearable="false" type="week"
            format="yyyy 第 WW 周"></el-date-picker>
          <el-date-picker v-if="query.type === 2" v-model="query.date" :clearable="false" type="month"></el-date-picker>
        </div>
        <div class="query-item">
          <el-button type="primary" @click="getData" style="width: 100%;" :loading="loading">查询</el-button>
        </div>
      </div>
    </template>
    <template slot="query">
      <div class="break-item" style="text-align: center;position: relative;">
        <h2>{{ reportTitle }}</h2>
        <el-button style="position: absolute;right: 0;top:0" type="primary" @click="exportExcel"
          :loading="exportLoading">导出</el-button>
      </div>
    </template>
    <template slot="content">
      <section class="label-box-container">
        <div v-for="(item, index) in labelList" :key="index" :class="['label-box', { multiple: item instanceof Array }]">
          <template v-if="item instanceof Array">
            <div class="label">
              <span v-for="tar in item" :key="tar.label">{{ tar.label }}</span>
            </div>
            <div class="value">
              <span v-for="tar in item" :key="tar.label">{{ labelData[tar.key] }}</span>
            </div>
          </template>
          <template v-else>
            <div class="label">
              {{ item.label }}
            </div>
            <div class="value">
              {{ labelData[item.key] }}
            </div>
          </template>
        </div>
      </section>
      <section>
        <div class="flex" style="line-height: 28px;">
          <h3 class="flex-grow">报警趋势情况分析</h3>
          <el-button type="text" @click="chartMode = !chartMode">
            <i class="pony-iconv2 pony-qiehuan"></i>
            切换
          </el-button>
        </div>
        <div class="bg bg--light border border--radius" style="height: 256px;">
          <NormalLine title="总报警次数" valueUnit='次' :defaultValue="chartDefault" :reportData="chartData" v-if="chartMode">
          </NormalLine>
          <el-table class="el-table--radius" v-else :data="tableData" stripe border highlight-current-row height="256">
            <el-table-column v-for="item in tableColumn" :label="item.label" :prop="item.key" :key="item.key"
              :sortable="item.sortable"></el-table-column>
          </el-table>
        </div>
      </section>
      <section style="margin-top: 10px">
        <div class="flex" style="line-height: 28px;">
          <h3 class="flex-grow">下属{{ subordinate }}运行情况</h3>
        </div>
        <el-table class="el-table--radius" :data="tableData2" stripe border highlight-current-row height="320">
          <el-table-column v-for="item in tableColumn2" :key="item.label" :label="item.label" :prop="item.key[0]"
            :show-overflow-tooltip="item.showOverflowTooltip" :min-width="item.width" :sortable="item.sortable">
            <template slot-scope="{row}">
              {{ item.key.map(item => row[item]).join(' / ') }}
            </template>
          </el-table-column>

        </el-table>
      </section>
    </template>
  </Layout>
</template>

<script>
/**
 * @Author: yezy
 * @Email: <EMAIL>
 * @Date: 2020/2/3 11:30
 * @LastEditors: yezy
 * @LastEditTime: 2020/2/3 11:30
 * @Description:
 */
import NormalLine from '@/view/customReport/reportComponent/line/NormalLine'
import ExportJsonExcel from 'js-export-excel';

const labelList = [
  [{ label: '运营车辆数', key: 'onlineCount' }, { label: '总车辆数', key: 'allCount' }],
  [{ label: '总行驶里程(km)', key: 'allDriveMile' }, { label: '时长(h)', key: 'allDriveTime' }],
  [{ label: '平均日里程(km)', key: 'avgDriveMile' }, { label: '时长(h)', key: 'avgDriveTime' }],
  [{ label: '日间行驶里程(km)', key: 'dayDriveMile' }, { label: '时长(h)', key: 'dayDriveTime' }],
  [{ label: '夜间行驶里程(km)', key: 'nightDriveMile' }, { label: '时长(h)', key: 'nightDriveTime' }],
  { label: '疲劳驾驶次数', key: 'tiredDriving', width: 120, sortable: true },
  { label: '路口超速次数', key: 'speedCross', width: 120, sortable: true },
  { label: '打电话次数', key: 'phone', width: 120, sortable: true },
  { label: '驾驶员异常次数', key: 'driver', width: 150, sortable: true },
  { label: '闯限行次数', key: 'breakThroughLine', width: 120, sortable: true },
]
const tableColumn = [
  { label: '日期', key: 'day' },
  { label: '总报警次数', key: 'allCount', sortable: true },
  { label: '疲劳驾驶次数', key: 'tiredDriving', sortable: true },
  { label: '路口超速次数', key: 'speedCross', sortable: true },
  { label: '打电话次数', key: 'phone', sortable: true },
  { label: '驾驶异常次数', key: 'driver', sortable: true },
  { label: '闯限行次数', key: 'breakThroughLine', sortable: true },
]
export default {
  name: "ZTCStats",
  components: {
    NormalLine,
  },
  data() {
    return {
      labelList,
      tableColumn,
      query: {
        type: 0,
        date: moment().startOf('day').subtract(1, 'days').toDate()
      },
      target: null,
      exportLoading: false,
      labelData: {
        allCount: 0,
        onlineCount: 0,
        allDriveMile: 0,
        allDriveTime: 0,
        avgDriveMile: 0,
        avgDriveTime: 0,
        dayDriveMile: 0,
        dayDriveTime: 0,
        nightDriveMile: 0,
        nightDriveTime: 0,
        tiredDriving: 0,
        speedCross: 0,
        phone: 0,
        driver: 0,
        breakThroughLine: 0
      },
      chartMode: false,
      chartDefault: [],
      chartData: null,
      tableData: [],
      tableData2: [],
      loading: false,
    }
  },
  computed: {
    reportTitle: function () {
      if (!this.target) return '请选择一个组织部门'
      let dateStr;
      switch (this.query.type) {
        case 0:
          dateStr = moment(this.query.date).format('YYYY年MM月DD日')
          break;
        case 1:
          dateStr = moment(this.query.date).format('YYYY年WW周')
          break;
        case 2:
          dateStr = moment(this.query.date).format('YYYY年MM月')
          break;
      }
      return `${this.target.name}${dateStr}综合报表`
    },
    subordinate: function () {
      switch (this.target && this.target.type) {
        case 2:
          return '部门'
        case 1:
          return '企业'
        default:
          return '车辆'
      }
    },
    tableColumn2: function () {
      const temp = labelList.concat([]);
      temp.shift();
      temp.unshift({
        label: this.subordinate,
        key: 'name',
        showOverflowTooltip: true,
        width: 150
      }, {
        label: (this.target && this.target.type === 3) ? '运营天数' : '运营车辆数',
        key: 'onlineDayOrOnlineVehicle',
        width: 140,
        sortable: true
      })
      return temp.map(item => {
        if (item instanceof Array) {
          return {
            label: item.map(item => item.label).join(' / '),
            width: 180,
            key: item.map(item => item.key),
          }
        }
        return {
          ...item,
          label: item.label,
          key: [item.key],
        };
      })
    },
  },
  methods: {
    isArray(obj) {
      return obj instanceof Array
    },
    nodeClick(data, node, $node) {
      this.target = data;
      this.getData();
    },
    async getData() {
      if (!this.target) {
        this.$warning('请选择查询对象');
        return
      }
      try {
        this.loading = true;
        let params = {};
        switch (this.query.type) {
          case 0:
            params = {
              startDay: this.DateFormat(this.query.date),
              endDay: this.DateFormat(this.query.date),
            }
            break;
          case 1:
            params = {
              startDay: moment(this.query.date).startOf('week').format('YYYY-MM-DD'),
              endDay: moment(this.query.date).endOf('week').format('YYYY-MM-DD'),
            }
            break;
          case 2:
            params = {
              startDay: moment(this.query.date).startOf('month').format('YYYY-MM-DD'),
              endDay: moment(this.query.date).endOf('month').format('YYYY-MM-DD'),
            }
            break;
        }
        let res = await this.$api.queryConsolidatedStatement({
          objId: this.target.id,
          ...params
        })
        if (res.status === 200) {
          this.labelData = res.data[1];
          this.tableData = res.data[2];
          this.chartData = this.tableData.map(item => ({
            label: item.day,
            value: item.allCount,
          }))
          this.tableData2 = res.data[3];
        }
      } catch (e) {
        this.$error(e)
      } finally {
        this.loading = false;
      }
    },
    exportExcel() {
      if (!this.target) {
        this.$warning('请选择查询对象');
        return
      }
      this.exportLoading = true
      let option = {};
      option.fileName = this.reportTitle;
      option.datas = [
        {
          sheetName: '总览',
          sheetHeader: ['维度', '数值'],
          sheetFilter: ['label', 'value'],
          columnWidths: [30, 30],
          sheetData: this.labelList.map(item => {
            if (item instanceof Array) {
              return {
                label: item.map(item => item.label).join(' / '),
                value: item.map(item => this.labelData[item.key]).join(' / ')
              }
            } else {
              return {
                label: item.label,
                value: this.labelData[item.key],
              }
            }
          }),
        },
        {
          sheetName: '报警趋势情况分析',
          sheetHeader: tableColumn.map(item => item.label),
          sheetFilter: tableColumn.map(item => item.key),
          columnWidths: new Array(this.tableColumn.length).fill(15),
          sheetData: this.tableData,
        },
        {
          sheetName: `下属${this.subordinate}运行情况`,
          sheetHeader: this.tableColumn2.map(item => item.label),
          columnWidths: new Array(this.tableColumn2.length).fill(15),
          sheetData: this.tableData2.map(rowData => {
            return this.tableColumn2.map(item => {
              return item.key.map(item => rowData[item]).join(' / ')
            })
          }),
        },
      ];
      new ExportJsonExcel(option).saveExcel(); //保存
      this.exportLoading = false

    }
  }
}
</script>

<style scoped lang="scss">
.ztc-stats {
  .query-top {
    height: calc(100% - 145px);
  }

  .query-bottom {
    margin-top: 5px;
    height: 140px;
    padding: 10px;

    .query-item {
      display: flex;
      height: 40px;
      line-height: 40px;
      justify-content: space-between;
      align-items: center;

      >div {
        flex-grow: 1;
      }

      >span {
        font-size: 12px;
        white-space: nowrap;
        margin-right: 5px;
      }
    }
  }

  .label-box-container {
    display: flex;
    flex-wrap: wrap;
    margin-right: -12px;
    padding: 10px 0 0;

    .label-box {
      width: calc(20% - 12px);
      height: 80px;
      margin: 0 12px 12px 0;
      border-radius: 4px;
      padding: 10px 15px;
      line-height: 30px;
      background: var(--background-color-lighter);
      color: var(--color-text-secondary);

      &.multiple {
        >div {
          display: flex;
          justify-content: space-between;
        }

        >.value {
          >span:first-child {
            color: var(--color-primary);
          }

          >span:last-child {
            color: var(--color-text-primary);
          }
        }
      }

      >.label {
        display: flex;
        justify-content: flex-start;
      }

      >.value {
        font-size: 20px;
        display: flex;
        justify-content: center;
        color: var(--color-primary);
        font-weight: bold;
      }
    }
  }

}
</style>

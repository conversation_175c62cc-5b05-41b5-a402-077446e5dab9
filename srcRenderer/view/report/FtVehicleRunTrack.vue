<template>
  <Layout tag="div" class="VehicleRunTrack" :contentLoading="table.loading" :has-color="true">
    <template slot="aside">
      <div class="query-top">
        <!-- <ZtreeMatics style="padding: 10px;" treeType="vehicle" ref="vehicleZtree" :checkMode="false">
								</ZtreeMatics> -->
        <ElementTree type="vehicle" ref="vehicleZtree" :checkMode="false" @node-click="selectNodes"> </ElementTree>
      </div>
      <div class="query-bottom bg bg--lighter">
        <StartEndTime
          itemHeight="35"
          v-model="selectStartEndTime"
          valueFormat="timestamp"
          timeType="datetime"
          :isLimit="true"
          :timeLimit="1"
        >
        </StartEndTime>
        <div class="query-item">
          <el-button size="mini" type="primary" style="width: 100%" @click="searchVechicleTrack" :loading="table.loading">
            {{ $ct("search") }}
          </el-button>
        </div>
      </div>
    </template>

    <template slot="query">
      <div class="query-item">
        <el-button type="primary" @click="showVehicleTrackLine">{{ $ct("modal.name") }}</el-button>
      </div>
      <div class="query-item">
        <el-button type="primary" @click="tableShowConfig">{{ $ct("modal.tableTitle") }}</el-button>
      </div>
      <div class="query-item">
        <span>最小速度：</span>
        <el-input-number v-model="numMin" @change="handleChange"></el-input-number>
      </div>
      <div class="query-item">
        <span>最大速度：</span>
        <el-input-number v-model="numMax" @change="handleChange"></el-input-number>
      </div>
      <div class="break-item"></div>
      <el-button type="primary" @click="exportTerminalExcle" :loading="reportLoading">{{ $ct("export") }} </el-button>
    </template>

    <template slot="footer">
      <el-pagination
        small
        background
        :current-page.sync="table.page"
        :page-size="table.size"
        layout="prev, pager, next, total"
        :total="table.list.length"
      >
      </el-pagination>
    </template>

    <el-table
      ref="table"
      class="el-table--ellipsis el-table--radius"
      slot="content"
      border
      stripe
      highlight-current-row
      size="mini"
      :data="formatVehicleList"
      height="100%"
    >
      <el-table-column
        type="index"
        :index="(index) => index + 1 + pageStart"
        :label="$ct('table.index')"
        min-width="50"
      ></el-table-column>
      <!-- <el-table-column prop="plate_no" label="车牌号" min-width="100"></el-table-column>
            <el-table-column prop="gps_time" :label="$ct('table.gpsTime')" min-width="150"></el-table-column>
            <el-table-column prop="lng" :label="$ct('table.lng')" min-width="100"></el-table-column>
            <el-table-column prop="lat" :label="$ct('table.lat')" min-width="100"></el-table-column>
            <el-table-column prop="oil_consum" :label="$ct('table.oil_consum')" min-width="80"></el-table-column>
            <el-table-column prop="rota_speed" :label="$ct('table.rota_speed')" min-width="80"></el-table-column>
            <el-table-column prop="gps_speed" :label="$ct('table.speed')" min-width="80"></el-table-column>
            <el-table-column prop="pulse_speed" :label="$ct('table.vsspeed')" min-width="80"></el-table-column>
            <el-table-column prop="dire" :label="$ct('table.dire')" min-width="80"></el-table-column>
            <el-table-column prop="acc" :label="$ct('table.acc')" min-width="80"></el-table-column>
            <el-table-column prop="mile" :label="$ct('table.mile')" min-width="100"></el-table-column> -->
      <el-table-column
        :prop="item.key"
        :label="item.name"
        :min-width="item.size"
        v-for="(item, index) in defaultSettingList"
        :key="index"
        show-overflow-tooltip
      ></el-table-column>

      <!-- <el-table-column header-align="center" align="left" prop="location" :label="$ct('table.location')"
                             min-width="300" show-overflow-tooltip></el-table-column> -->
    </el-table>
    <PonyDialog width="950" :title="$ct('modal.title')" :contentMaxHeight="410" v-model="table.show">
      <ul class="track__detail dfc">
        <li>{{ $ct("modal.avgSpeed") }}: {{ modal.source.avg_speed }} km/h</li>
        <li>{{ $ct("modal.mile") }}: {{ modal.source.mile }} km</li>
        <li>{{ $ct("modal.time") }}: {{ modal.source.time }} h</li>
      </ul>

      <div class="track__chart">
        <div class="chart" ref="chart"></div>
      </div>
    </PonyDialog>

    <TableShowConfig
      ref="tableShowConfig"
      v-model="defaultSettingList"
      :filterableSearch="true"
      :node-key="'id'"
      type="getDataItemTreeTableConfig"
    ></TableShowConfig>
  </Layout>
</template>

<script>
import TableShowConfig from "./components/TableShowConfigTree";
import moment from "moment";
import StartEndTime from "@/components/common/StartEedTime";

const ExportJsonExcel = require("js-export-excel");
export default {
  name: "ftVehicleRunTrack",
  components: {
    TableShowConfig,
    StartEndTime,
  },
  data() {
    return {
      table_list: [],
      numMax: 150,
      numMin: 0,
      queryList: {
        qureygpstime: 0,
        gps_time_begin: moment().startOf("day").format("YYYY-MM-DD HH:mm:ss"),
        gps_time_end: moment().endOf("day").format("YYYY-MM-DD HH:mm:ss"),
        vehicle_id: "",
      },
      table: {
        show: false,
        loading: false,
        list: [],
        page: 1,
        size: 30,
      },
      modal: {
        chartInstance: null,
        options: {},
        source: {
          avg_speed: 0,
          mile: 0,
          time: 0,
        },
      },
      defaultSettingList: [],
      currentNode: null,
      reportLoading: false,
      vehicleName: "",
      selectStartEndTime: [moment().startOf("day").valueOf(), moment().endOf("day").valueOf()],
      config: {},
      configDes:{
        '1':'水位',
        '2':'剩余油量',
        '3':'装运体积'
      }
    };
  },

  computed: {
    // //表头最终的配置
    // finallTableConfig(){
    //     //getTableSetting是默认配置defaultSettingList是配置的配置
    //     return getTableSetting
    // },
    pageStart() {
      return (this.table.page - 1) * this.table.size;
    },
    formatVehicleList() {
      return this.table.list.slice(this.pageStart, this.pageStart + this.table.size);
    },
  },
  watch: {
    selectStartEndTime: function (newVal, oldVal) {
      this.queryList.gps_time_begin = moment(newVal[0]).format("YYYY-MM-DD HH:mm:ss");
      this.queryList.gps_time_end = moment(newVal[1]).format("YYYY-MM-DD HH:mm:ss");
    },
  },
  mounted() {
    // this.defaultSettingList = getTableSetting
  },
  methods: {
    selectNodes(current) {
      if (current.type == 4) {
        this.currentNode = current.id;
        this.vehicleName = current.name;
      }
    },
    //表格显示配置弹框
    tableShowConfig() {
      this.$refs.tableShowConfig.showModel();
    },
    handleChange() {
      this.table.list = this.table_list.filter((item) => this.numMin <= item.gps_speed && item.gps_speed <= this.numMax);
    },
    clearUp() {
      this.table.list = [];
      this.table.page = 1;
      this.table.loading = false;

      // 只有初始化过才需要清空
      if (this.modal.options.xAxis) {
        this.modal.options.xAxis.data = [];
        this.modal.options.series[0].data = [];
        this.modal.chartInstance.setOption(this.modal.options);
      }
    },
    async searchVechicleTrack() {
      this.table.page = 1;
      let VehicleIdList = this.currentNode;
      if (!VehicleIdList) {
        this.$message({
          showClose: true,
          message: this.$ct("message.noVehice"),
          type: "warning",
        });
        return;
      }
      this.queryList.vehicle_id = VehicleIdList;
      this.table.loading = true;
      let configRes = await this.$api.getWaterLevelPercent({
        vehicle_id: this.queryList.vehicle_id,
      });
      if(configRes.status==200){
        let index = this.defaultSettingList.findIndex(it=>it.key=='wash_water_level')
        if(index!=-1){
            this.defaultSettingList[index].name = configRes.data[123].type?this.configDes[configRes.data[123].type]:this.defaultSettingList[index].name
        }
      }
      let result = await this.$api.postGpsRecordInfo(JSON.parse(JSON.stringify(this.queryList)));
      if (!result || result.RS != 1) {
        this.clearUp();
        this.$message({
          showClose: true,
          message: result.Reason || this.$ct("message.queryError"),
          type: "warning",
        });
        return;
      }
      if (!result.GpsRecordLists.length) {
        this.clearUp();
        this.$message({
          showClose: true,
          message: this.$ct("message.noData"),
          type: "warning",
        });
        return;
      }
      this.modal.source = {
        avg_speed: result.avg_speed,
        mile: result.mile,
        time: result.time,
      };
      this.table_list = result.GpsRecordLists; //全部数据存储，便于筛选全部数据
      this.table.list = result.GpsRecordLists;
      this.table.list = this.table_list.filter((item) => this.numMin <= item.gps_speed && item.gps_speed <= this.numMax);

      this.$nextTick(() => {
        this.table.loading = false;
        this.$refs["table"].doLayout();
      });
    },
    showVehicleTrackLine() {
      this.table.show = true;
      this.$nextTick(() => {
        this.initDefaultChart();
        if (this.modal.chartInstance) {
          this.modal.chartInstance.resize();
        }
      });
    },
    initDefaultChart() {
      let xAxisList = [];
      let seriesList = [];
      if (this.table.list.length) {
        this.table.list.forEach((item) => {
          xAxisList.push(item.gps_time);
          seriesList.push(item.gps_speed);
        });
      }
      this.modal.chartInstance = this.$echarts.init(this.$refs.chart);
      this.modal.options = {
        xAxis: {
          axisLine: {
            onZero: false,
            lineStyle: {
              color: "#666666",
            },
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            show: true,
          },
          splitLine: {
            show: false,
          },
          type: "category",
          data: xAxisList,
        },
        yAxis: {
          name: `${this.$ct("yAxisName")}(km/h)`,
          axisLine: {
            onZero: false,
            lineStyle: {
              color: "#666666",
            },
          },
          axisPointer: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          splitLine: {
            show: false,
          },
          axisLabel: {
            show: true,
          },
          type: "value",
        },
        series: [
          {
            data: seriesList,
            type: "line",
            smooth: true,
            itemStyle: {
              normal: {
                color: "#ee7738",
              },
            },
          },
        ],
      };

      this.modal.chartInstance.setOption(this.modal.options);
    },
    async exportTerminalExcle() {
      if (!this.table.list.length) {
        this.$message({
          showClose: true,
          message: this.$ct("message.noDataExport"),
          type: "warning",
        });
        return;
      }
      let sheetHeader = [
        // "车牌号@plate_no@8000@000000",
        // "定位时间@gps_time@8000@000000",
        // "经度@lng@8000@000000",
        // "纬度@lat@8000@000000",
        // "油耗@oil_consum@8000@000000",
        // "转速@rota_speed@8000@000000",
        // "速度@gps_speed@8000@000000",
        // "VSS速度@pulse_speed@8000@000000",
        // "方向@dire@8000@000000",
        // "ACC@acc@8000@000000",
        // "里程@mile@8000@000000",
        // "位置@location@12000@000000",
      ];

      let header;
      if (this.defaultSettingList.length) {
        header = this.defaultSettingList.map((item) => {
          return `${item.name}@${item.key}@5000@000000`;
        });
        // sheetHeader.splice(11, 0, ...header)
        sheetHeader = header;
      }
      let excelBody = [];
      this.table.list.forEach((item, index) => {
        excelBody.push(item);
      });
      let params = {};
      let paramsList = [];
      let fileName = `${moment(this.queryList.gps_time_begin).format("YYYY-MM-DD HH:mm:ss")} ~ ${moment(
        this.queryList.gps_time_end
      ).format("YYYY-MM-DD HH:mm:ss")}--${this.vehicleName}车辆轨迹`;
      let title = `${this.vehicleName}车辆轨迹(${moment(this.queryList.gps_time_begin).format("YYYY-MM-DD HH:mm:ss")} ~ ${moment(
        this.queryList.gps_time_end
      ).format("YYYY-MM-DD HH:mm:ss")} )`;
      params = {
        sheetName: "车辆轨迹",
        title: title,
        headers: sheetHeader,
        dataList: excelBody,
      };
      paramsList.push(params);
      this.reportLoading = true;

      await this.$utils.jsExcelExport(JSON.stringify(paramsList), fileName + ".xlsx");
      this.reportLoading = false;
    },

    dateStartChange(date) {
      let newDate = moment(date).endOf("day").toDate();
      this.queryList.gps_time_end = moment(newDate).add(0, "days").format("YYYY-MM-DD HH:mm:ss");
    },
  },

  activated() {
    if (this.modal.chartInstance) {
      this.modal.chartInstance.resize();
    }
  },
};
</script>
<style lang="scss" scoped>
.speedSearch {
  margin-left: 20px;
}

.VehicleRunTrack {
  .query-top {
    height: calc(100% - 150px);
  }

  .query-bottom {
    margin-top: 5px;
    padding: 10px;

    .query-item {
      display: flex;
      height: 40px;
      line-height: 40px;
      justify-content: space-between;
      align-items: center;

      > span {
        font-size: 12px;
        white-space: nowrap;
        margin-right: 5px;
      }
    }
  }

  .track__detail {
    height: 50px;
    width: 100%;

    li {
      margin: 0 10px;
      font-size: 14px;
    }
  }

  .track__chart {
    height: 250px;
    width: 100%;

    .chart {
      width: 928px;
      height: 100%;
    }
  }
}
</style>

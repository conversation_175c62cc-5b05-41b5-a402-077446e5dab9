<template>
  <Layout class="AlarmDetailSearchSK" :contentLoading="table.loading" :aside-width="320">
    <template slot="aside">
      <div class="query-top">
        <el-tabs stretch type="border-card">
          <el-tab-pane label="车辆选择">
            <el-tabs v-model="activeTab" stretch class="no-padding" style="padding: 5px 10px 0;">
              <el-tab-pane label="车辆列表" name="vehicle">
                <ElementTree ref="vehicleTree" :checkMode="true" type="vehicle" @check="selectNodes" state onlineCountTip>
                </ElementTree>
              </el-tab-pane>
              <el-tab-pane label="驾驶员列表" name="driver" lazy>
                <ElementTree ref="driverTree" :checkMode="true" type="driverTree" @check="selectNodesDriver">
                </ElementTree>
              </el-tab-pane>
              <el-tab-pane label="企业列表" name="company">
                <ElementTree ref="companyTree" :checkMode="true" type="company" @check="selectNodesCompany"></ElementTree>
              </el-tab-pane>
            </el-tabs>
          </el-tab-pane>
          <el-tab-pane label="报警选择" style="overflow-x: hidden;overflow-y: auto; margin-top: 10px">
            <el-tree ref="alarmTree" :data="alarmTreeList" node-key="value" :props="defaultProps" show-checkbox
              default-expand-all @check='getList'>
            </el-tree>
          </el-tab-pane>
        </el-tabs>
      </div>
      <div class="query-bottom bg bg--lighter">
        <div class="query-item">
          <span>开始时间:</span>
          <el-date-picker style="width: 100%" type="date" :picker-options="pickerOptions" @change="dateStartChange"
            v-model="query.start">
          </el-date-picker>
        </div>
        <div class="query-item">
          <span>结束时间:</span>
          <el-date-picker style="width: 100%" type="date" :picker-options="endDatePickerOptions" v-model="query.end"
            @change="dateEndChange">
          </el-date-picker>
        </div>
        <div class="query-item">
          <span>评分区间:</span>
          <el-select v-model="query.timeRoadList" multiple collapse-tags style="width: 100%" placeholder="请选择">
            <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </div>
        <div class="query-item">
          <el-button size="mini" type="primary" :loading="table.loading" style="width: 100%;" @click="search">
            查询
          </el-button>
        </div>
      </div>
    </template>
    <template slot="content">
      <el-tabs v-model="activeTab2" type="border-card">
        <el-tab-pane name="alarmDetails" label="报警明细">
          <layout>
            <template slot="query">
              <el-button type="primary" @click="exportExcel">导出</el-button>
              <div class="break-item"></div>
              <el-pagination small background layout="prev, pager, next, total" :current-page.sync="table.page"
                :page-size="table.size" :total="table.total">
              </el-pagination>
            </template>
            <template slot="content">
              <el-table class="el-table--radius box-shadow" ref="adasAlarmTable" :data="detailFormatList" border stripe
                highlight-current-row height="100%" style="width: 100%">
                <el-table-column type="index" :index="(index) => index + 1 + detailPageStart" label="序号"
                  fixed></el-table-column>
                <el-table-column label="操作" width="80">
                  <template slot-scope="scope">
                    <el-button type="text" title="详情" @click="getDetail(scope.row)">
                      <i class="pony-iconv2 pony-xiangqing"></i>
                    </el-button>

                    <!-- // is_video是否有附件标识(0:没有, 1:有)
																				// is_play是否已下载标识(0:没下载, 1:已下载) -->
                    <!-- text--primary:白色   text--brand:蓝色-->
                    <i :title="$ct('label.videoHint')" class="pony-iconv2 pony-bofangquan text text--disabled"
                      v-if="scope.row.is_video == -1 && scope.row.is_play !== 1"></i>

                    <i v-else
                      :class="['pony-iconv2 pony-bofangquan text', { 'text--primary': scope.row.is_video == 1 && scope.row.is_play == 0, disabled: scope.row.is_video == 0, 'text--brand': scope.row.is_play == 1, },]"
                      @click="videoPlay(scope.row)"></i>
                  </template>
                </el-table-column>
                <el-table-column label="单位" min-width="120" align="left" header-align="center" show-overflow-tooltip>
                  <template slot-scope="scope">
                    <span>{{ scope.row.company_name }} >>
                      {{ scope.row.fleet_name }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="plate_no" label="车牌号" min-width="91" show-overflow-tooltip />
                <el-table-column prop="driver_name" label="驾驶员" min-width="60" show-overflow-tooltip />
                <el-table-column prop="alarm_type" min-width="122" label="报警类型" show-overflow-tooltip />
                <el-table-column prop="alarm_time" min-width="135" label="开始时间" />
                <el-table-column prop="alarm_time_end" min-width="135" label="结束时间" />
                <el-table-column prop="duration" min-width="80" label="持续时间" show-overflow-tooltip>
                  <template slot-scope="scope">
                    <span>{{ MillisecondFormat(scope.row.duration * 1000) }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="drive_mile" min-width="80" label="持续里程" />
                <el-table-column prop="alarm_level" min-width="70" label="报警级别" show-overflow-tooltip />
                <el-table-column prop="gps_speed" label="车速(km/h)" min-width="90" />
                <el-table-column prop="deal_type_name" label="处理方式" min-width="100"
                  show-overflow-tooltip></el-table-column>
                <el-table-column prop="deal_user_name" label="处理人" min-width="90" show-overflow-tooltip></el-table-column>
                <el-table-column prop="deal_time" label="处理时间" min-width="120" show-overflow-tooltip></el-table-column>
                <el-table-column prop="deal_desc" label="处理备注" min-width="120" show-overflow-tooltip></el-table-column>
                <el-table-column prop="location" align="left" label="开始位置" min-width="140"
                  show-overflow-tooltip></el-table-column>
                <el-table-column prop="location_end" align="left" label="结束位置" min-width="140"
                  show-overflow-tooltip></el-table-column>
              </el-table>
            </template>
          </layout>
        </el-tab-pane>
        <el-tab-pane name="alarmSummary" label="报警统计">
          <layout :contentLoading="summaryTable.loading">
            <template slot="query">
              <el-button type="primary" @click="exportSummaryExcel">导出</el-button>
              <div class="break-item"></div>
              <el-pagination background small :pager-count="5" :current-page.sync="summaryTable.page"
                layout="prev, pager, next, total" :page-size="summaryTable.size" :total="summaryTable.total">
              </el-pagination>
            </template>
            <template slot="content">
              <el-table class="el-table--radius el-table--ellipsis box-shadow" ref="adasAlarmSummaryTable"
                :data="formatList" border stripe highlight-current-row height="100%" size="mini" style="width: 100%">
                <el-table-column type="index" :index="(index) => index + 1 + pageStart" label="序号"
                  fixed></el-table-column>
                <el-table-column label="操作">
                  <template slot-scope="scope">
                    <el-button type="text" title="详情" @click="changeToDetail(scope.row)">
                      <i class="pony-iconv2 pony-xiangqing"></i>
                    </el-button>
                  </template>
                </el-table-column>
                <el-table-column prop="companyName" label="单位" show-overflow-tooltip></el-table-column>
                <el-table-column prop="plateNo" label="车牌号"></el-table-column>
                <el-table-column prop="driverName" label="司机名称"></el-table-column>
                <el-table-column prop="date" label="日期" width="200"></el-table-column>
                <el-table-column prop="timeRoadView" label="评分区间"></el-table-column>
                <el-table-column prop="driveTimeView" label="行驶时长"></el-table-column>
                <el-table-column prop="driveMileView" label="行驶里程"></el-table-column>
                <el-table-column prop="alarms" :label="formatList.length ? formatList[0].alarmList[index].field : '报警类型'"
                  min-width="140" v-for="(item, index) in columnNum" :key="item" show-overflow-tooltip>
                  <template slot-scope="{ row }">
                    <span title="处理数/报警数" style="cursor:pointer ">
                      {{ row.alarmList[index].value }}
                    </span>
                  </template>
                </el-table-column>
              </el-table>
            </template>
          </layout>
        </el-tab-pane>
      </el-tabs>
    </template>
    <AlarmDetailV2 ref="alarmDetail" @change="searchDetail()"></AlarmDetailV2>
    <Player ref="player" @played="playedAction"></Player>
  </Layout>
</template>

<script>
import moment from "moment";
import ExportJsonExcel from "js-export-excel";
import AlarmDetailV2 from "./components/AlarmDetailV2";

export default {
  name: "alarmDetailSearchSK",
  components: {
    AlarmDetailV2
  },
  data() {
    return {
      activeTab: "vehicle",
      activeTab2: "alarmDetails",
      query: {
        start: moment().startOf("days"),
        end: moment().endOf("days"),
        scoreTypeList: [], // 评分类型列表
        idList: [], // 对应查询类型对象的id列表
        idType: '',  // 查询类型(1:车辆, 2:驾驶员, 3:企业)
        timeRoadList: [] // 日夜间条件列表
      },
      carList: [], // 选中车辆列表
      driverList: [], // 选中驾驶员列表
      companyList: [], // 选中企业列表
      alarmList: [], // 选中报警列表
      // 报警明细数据
      table: {
        page: 1,
        size: 30,
        total: 0,
        data: [],
        loading: false,
      },
      // 报警统计数据
      summaryTable: {
        page: 1,
        size: 30,
        total: 0,
        data: [],
        loading: false,
      },
      pickerOptions: {
        disabledDate: function (date) {
          return (date - moment().endOf("day").toDate() > 0);
        },
      },
      options: [
        {
          value: '0',
          label: '日间高速'
        },
        {
          value: '1',
          label: '日间非高速'
        },
        {
          value: '2',
          label: '夜间高速'
        },
        {
          value: '3',
          label: '夜间非高速'
        }
      ],
      // 所有报警列表
      alarmTreeList: [
        {
          name: '全选',
          children: []
        }
      ],
      // 报警统计页面表头显示的报警列表
      alarmTableList: [],
      columnNum: 1,
      defaultProps: {
        children: 'children',
        label: 'name'
      },
    }
  },
  computed: {
    detailPageStart() {
      return (this.table.page - 1) * this.table.size
    },
    detailFormatList() {
      return this.table.data.slice((this.table.page - 1) * this.table.size, this.table.page * this.table.size)
    },
    pageStart() {
      return (this.summaryTable.page - 1) * this.summaryTable.size
    },
    formatList() {
      return this.summaryTable.data.slice((this.summaryTable.page - 1) * this.summaryTable.size, this.summaryTable.page * this.summaryTable.size)
    },
    endDatePickerOptions: function () {
      return {
        disabledDate: (date) => {
          return (date - moment().endOf("day").toDate() > 0 || moment(date).endOf("day") - this.query.start < 0);
        },
      };
    },
  },
  async mounted() {
    this.alarmTreeList[0].children = await this.$api.getSysDictByCode({ code: 'secco_score_type' })
  },
  activated() {
    let parmas = this.$route.query
    if (!parmas || Object.keys(parmas).length === 0) return

    let alarmList = []
    if (parmas.alarmList.indexOf('|') !== -1) {
      parmas.alarmList.split('|').forEach(item => {
        alarmList.push({ key: item })
      })
    } else {
      alarmList.push({ key: parmas.alarmList })
    }
    parmas.alarmList = alarmList

    this.changeToDetail(parmas)
    this.$nextTick(async () => {
      await this.$router.push('/home/<USER>')
    })
  },
  methods: {
    dateStartChange(date) {
      if (this.query.end - date < 0) {
        this.query.end = moment(date).add(1, "days").subtract(1, "seconds").toDate();
      }
      if (this.query.end - date >= 31 * 86400 * 1000) {
        this.query.end = new Date(+date + 31 * 86400 * 1000);
      }
    },
    dateEndChange(date) {
      if (date - this.query.start > 31 * 86400 * 1000) {
        this.query.start = new Date(date - 31 * 86400 * 1000);
      }
    },
    // 选择车辆
    selectNodes(data, { checkedNodes }) {
      this.carList = checkedNodes.filter((item) => item.type === 4).map((item) => item.id)
    },
    // 选择驾驶员
    selectNodesDriver(data, { checkedNodes }) {
      this.driverList = checkedNodes.filter((item) => item.type === 4).map((item) => item.id)
    },
    // 选择企业
    selectNodesCompany(data, { checkedNodes }) {
      this.companyList = checkedNodes.filter((item) => item.type === 2).map((item) => item.id)
    },
    // 选择报警
    getList(checkedNodes, checkedKeys, halfCheckedNodes, halfCheckedKeys) {
      this.query.scoreTypeList = checkedKeys.checkedNodes.map(item => item.value)
    },
    // 点击查询按钮
    search() {
      if (this.activeTab === 'vehicle') {
        if (this.carList.length !== 0) {
          this.query.idList = this.carList
        } else {
          return this.$warning('请选择车辆！')
        }
        this.query.idType = 1
      } else if (this.activeTab === 'driver') {
        if (this.driverList.length !== 0) {
          this.query.idList = this.driverList
        } else {
          return this.$warning('请选择驾驶员！')
        }
        this.query.idType = 2
      } else {
        if (this.companyList.length !== 0) {
          this.query.idList = this.companyList
        } else {
          return this.$warning('请选择企业！')
        }
        this.query.idType = 3
      }
      if (this.query.scoreTypeList.length === 0) {
        return this.$warning('请选择报警！')
      }
      if (this.query.timeRoadList.length === 0) {
        return this.$warning('请选择评分区间！')
      }

      if (this.activeTab2 === 'alarmDetails') {
        this.searchDetail()
      } else {
        this.searchSummary()
      }
    },
    // 查询明细
    async searchDetail() {
      this.table.loading = true
      this.table.data = []
      this.table.total = 0
      this.columnNum = 1

      const res = await this.$api.seccoAlarmDetail({
        idType: this.query.idType,
        idList: this.query.idList,
        start: moment(this.query.start).format("YYYY-MM-DD"),
        end: moment(this.query.end).format("YYYY-MM-DD"),
        scoreTypeList: this.query.scoreTypeList,
        timeRoadList: this.query.timeRoadList
      })
      this.table.loading = false
      if (res.rs !== 1) {
        return this.$warning(res.reason || '查询错误！')
      } else if (res.alarmInfoList.length === 0) {
        return this.$warning('暂无数据！')
      }
      this.table.data = res.alarmInfoList
      this.table.total = res.count
    },
    // 查询统计
    async searchSummary() {
      this.summaryTable.loading = true
      this.summaryTable.data = []
      this.summaryTable.total = []
      const res = await this.$api.seccoAlarmStats({
        idType: this.query.idType,
        idList: this.query.idList,
        start: moment(this.query.start).format("YYYY-MM-DD"),
        end: moment(this.query.end).format("YYYY-MM-DD"),
        scoreTypeList: this.query.scoreTypeList,
        timeRoadList: this.query.timeRoadList
      })
      this.summaryTable.loading = false
      if (res.status !== 200) {
        return this.$error('查询错误！')
      } else if (res.data.length === 0) {
        return this.$warning('暂无数据！')
      }
      this.summaryTable.data = res.data
      this.summaryTable.total = res.data.length
      this.columnNum = this.summaryTable.data[0].alarmList.length
    },
    // 报警统计跳转报警明细
    changeToDetail(item) {
      this.query.idList = []
      if (!item.deptId) {
        this.activeTab = 'vehicle'
        this.carList = []
        this.carList.push(item.vehicleId.toString())
        this.query.idList.push(item.vehicleId.toString())
        this.$refs.vehicleTree.$refs.tree.setCheckedKeys(this.carList)
      } else {
        this.activeTab = 'company'
        this.companyList = []
        this.companyList.push(item.deptId)
        this.query.idList.push(item.deptId)
        this.$refs.companyTree.$refs.tree.setCheckedKeys(this.companyList)
      }
      this.query.timeRoadList = []
      this.query.timeRoadList.push(item.timeRoad.toString())
      this.query.start = moment(item.date.split('至')[0], "YYYY-MM-DD")
      this.query.end = moment(item.date.split('至')[1], "YYYY-MM-DD")

      //报警
      this.query.scoreTypeList = []
      this.query.scoreTypeList = item.alarmList.map(item => item.key.toString())
      setTimeout(() => {
        this.$refs.alarmTree.setCheckedKeys(this.query.scoreTypeList)
      }, 1000)
      this.activeTab2 = 'alarmDetails'
      this.search()
    },
    // 打开报名明细详情弹窗
    getDetail(row) {
      this.$refs["alarmDetail"].showModal(row);
    },
    // 播放视频
    videoPlay(row) {
      if (row.is_video !== 0) {
        let date = moment(row.alarm_time, "YYYY-MM-DD HH:mm:ss").valueOf();
        this.$refs.player.play({
          alarmId: row.alarm_id,
          timestamp: date,
          label: row.plate_no,
          vehicleId: row.vehicle_id,
          terminalNo: row.terminal_no,
          alarmSerial: row.alarm_serial
        }, row.media_list);
      }
    },
    playedAction(alarmId) {
      let row = this.table.data.find((item) => item.alarm_id === alarmId);
      if (row) row.is_play = 1;
    },
    // 导出报警详情
    exportExcel() {
      if (this.table.data.length === 0) {
        return this.$message.warning('暂无数据！')
      }
      let excelBody = []
      this.table.data.forEach((item, index) => {
        let array = []
        array.push(
          index + 1,
          `${item.company_name} >> ${item.fleet_name}`,
          item.plate_no,
          item.driver_name,
          item.alarm_type,
          item.alarm_time,
          item.alarm_time_end,
          this.MillisecondFormat(item.duration),
          item.drive_mile,
          item.alarm_level,
          item.gps_speed,
          item.deal_type_name,
          item.deal_user_name,
          item.deal_time,
          item.deal_desc,
          item.location,
          item.location_end,
        )
        excelBody.push(array)
      })
      let options = {
        fileName: '报警明细',
        datas: [
          {
            sheetName: "报警明细查询",
            sheetData: excelBody,
            sheetHeader: ['序号', '单位', '车牌号', '驾驶员', '报警类型', '开始时间', '结束时间', '持续时间', '持续里程', '报警级别', '车速(km/h)', '处理方式', '处理人', '处理时间', '处理备注', '开始位置', '结束位置'],
            columnWidths: ['3', '8', '8', '8', '8', '8', '8', '8', '8', '8', '8', '8', '8', '8', '8', '8', '8'],
          }
        ]
      }
      ExportJsonExcel(options).saveExcel();
    },
    // 导出报警统计
    exportSummaryExcel() {
      if (!this.summaryTable.data.length) {
        this.$warning("没有数据可以导出");
        return;
      }
      let excelBody = [];
      let sheetName = ["单位", "车牌号", "司机名称", "日期", "评分区间", "行驶时长", "行驶里程"];
      let columns = ["12", "8", "8", "8", "8", "8", "8"];
      this.summaryTable.data.forEach((item, index) => {
        let array = [];
        array.push(item.companyName, item.plateNo, item.driverName, item.date, item.timeRoadView, item.driveTimeView, item.driveMileView);
        item.alarmList.forEach((count) => {
          array.push(count.value);
        });
        excelBody.push(array);
      });
      this.summaryTable.data[0].alarmList.forEach((item) => {
        sheetName.push(item.field);
        columns.push("11");
      });
      let options = {
        fileName: `${moment(this.query.start).format(
          "YYYY-MM-DD HH:mm:ss"
        )} ~ ${moment(this.query.end).format(
          "YYYY-MM-DD HH:mm:ss"
        )} 报警统计`,
        datas: [
          {
            sheetData: excelBody,
            sheetHeader: sheetName,
            columnWidths: columns,
          },
        ],
      };
      ExportJsonExcel(options).saveExcel();
    },
  }
}
</script>

<style lang="scss" scoped>
.AlarmDetailSearchSK {

  .query-top {
    height: calc(100% - 180px);
  }

  .query-bottom {
    padding: 10px;

    .query-item {
      display: flex;
      height: 40px;
      line-height: 40px;
      justify-content: space-between;
      align-items: center;

      >span {
        font-size: 12px;
        white-space: nowrap;
        margin-right: 5px;
      }

      .table-list {
        width: 100%;
        height: 28px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        box-sizing: border-box;
        background-color: var(--background-color-base);
        border-radius: 4px;
        border: 1px solid var(--border-color-base);
        color: (--border-color-lighter);
        padding: 0 10px;
        line-height: 28px;
        font-size: 12px;

        .table-num {
          width: 75px;
          height: 20px;
          background-color: var(--border-color-light);
          border: 1px solid var(--border-color-extra-light);
          border-radius: 5px;
          line-height: 20px;
          padding: 0 5px;
        }
      }

    }
  }
}
</style>

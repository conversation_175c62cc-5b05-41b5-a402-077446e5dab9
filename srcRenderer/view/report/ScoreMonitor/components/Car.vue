<template>
  <Layout :content-loading="loading" class="car" :has-color="true">
    <template slot="aside" class="score-aside">
      <div class="query-top">
        <ElementTree ref="vehicleTree" type="vehicle" :checkMode="true" @check="onCheckVehicle"></ElementTree>
      </div>
      <div class="query-bottom bg bg--lighter" v-if="activeTab2 == 'monitor'">
        <div class="query-item">
          <el-button size="mini" type="primary" style="width: 100%;" @click="getTableMonitorData" :loading="loading">
            {{ $t("common.query") }}
          </el-button>
        </div>
      </div>
      <div class="query-bottom bg bg--lighter" v-else>
        <StartEndTime itemHeight="35" v-model="tableHistory.query.dateRange" valueFormat="timestamp" timeType="date"
          :isLimit="true" :timeLimitForCur="1">
        </StartEndTime>
        <!-- <div class="query-item">
                  <span>开始日期</span>
                  <el-date-picker
                    v-model="tableHistory.query.dateRange[0]"
                    type="date"
                    :picker-options="pickerOptions"
                    @change="dateStartChange"
                  >
                  </el-date-picker>
                </div>
                <div class="query-item">
                  <span>结束日期</span>
                  <el-date-picker
                    v-model="tableHistory.query.dateRange[1]"
                    type="date"
                    :picker-options="endDatePickerOptions"
                  >
                  </el-date-picker>
                </div> -->
        <div class="query-item">
          <span>按日显示</span>
          <el-switch style="margin-left: 3px" active-color="#13ce66" inactive-color="var(--background-color-check-tab)"
            v-model="tableHistory.query.hasDay">
          </el-switch>
        </div>
        <div class="query-item">
          <el-button size="mini" type="primary" style="width: 100%;" @click="getTableHistoryData">
            {{ $t("common.query") }}
          </el-button>
        </div>
      </div>
    </template>

    <template slot="content">
      <el-tabs v-model="activeTab2" type="border-card">
        <el-tab-pane :label="$ct('monitor')" name="monitor">
          <Layout class="layout--no-padding-horizontal" :content-loading="tableMonitor.loading">
            <template slot="query">
              <el-button size="mini" type="primary" style="margin-left:10px;width:92px" @click="showDealModal"
                v-if="hasPermission('scoreMonitor:DIYscore')">自定义评分
              </el-button>
              <el-button class="exportExcel" type="primary" style="margin-left:10px" @click="exportTableMonitorData"
                :loading="exportLoading">
                {{ $ct("export") }}
              </el-button>
              <span class="text text--danger" style="padding-left: 8px">{{
                $ct("queryTip")
              }}</span>
              <div class="break-item"></div>
              <el-pagination small background :total="tableMonitor.total" :page-size="30"
                :current-page.sync="tableMonitor.query.page" layout="prev, pager, next, total">
              </el-pagination>
            </template>
            <template slot="content">
              <el-table ref="table" :data="filterTableMonitorData" border stripe highlight-current-row height="100%">
                <el-table-column type="index" align="center" :label="$ct('label.index')" :index="(index) =>
                  (tableMonitor.query.page - 1) * tableMonitor.query.size +
                  index +
                  1
                  "></el-table-column>
                <el-table-column min-width="120" prop="plateNo" :label="$ct('label.plateNo')"
                  show-overflow-tooltip></el-table-column>
                <el-table-column min-width="150" prop="deptName" :label="$ct('label.deptName')"
                  show-overflow-tooltip></el-table-column>
                <el-table-column prop="totalScore" :label="$ct('label.totalScore')">
                  <template slot-scope="scope">
                    <div :style="getTotalScoreStyle(scope.row.totalScore)" class="score-card">
                      {{ formatCeil(null, null, scope.row.totalScore) }}
                    </div>
                  </template>
                </el-table-column>
                <el-table-column :label="$ct('label.detail')">
                  <template slot-scope="scope">
                    <el-button type="text" icon="pony-iconv2 pony-xiangqing" size="mini"
                      @click="showDetail(scope.row)"></el-button>
                  </template>
                </el-table-column>
                <!-- <el-table-column
                                  min-width="100"
                                  prop="driveTime"
                                  :label="$ct('label.driveTime')"
                                ></el-table-column>
                                <el-table-column
                                  min-width="100"
                                  prop="driveMile"
                                  :label="$ct('label.driveMile')"
                                ></el-table-column>
                                <el-table-column
                                  min-width="130"
                                  prop="eventCount"
                                  :label="$ct('label.eventCount')"
                                ></el-table-column> -->
                <el-table-column v-for="item in currentHeaderList" :key="item.value" :prop="item.value"
                  :formatter="formatCeil" :min-width="130" :label="item.name">
                </el-table-column>
              </el-table>
            </template>
          </Layout>
        </el-tab-pane>
        <el-tab-pane label="历史评分" name="history" lazy>
          <Layout class="layout--no-padding-horizontal" :content-loading="tableHistory.loading">
            <template slot="query">
              <el-button class="exportExcel" type="primary" style="margin-left:10px" @click="exportTableHistoryData"
                :loading="exportHistoryLoading">
                {{ $ct("export") }}
              </el-button>
              <el-popover popper-class="my-el-popover" placement="bottom-start" width="350" v-model="visible">
                <div class="top">
                  <div class="sort-direction">
                    <span class="up" :class="isReverse === false ? 'true' : ''" @click="sortDirection">
                      <i class="pony-iconv2 pony-shengxu"></i>
                    </span>
                    <span class="down" :class="isReverse === true ? 'true' : ''" @click="sortDirection"><i
                        class="pony-iconv2 pony-jiangxu"></i></span>
                  </div>
                  <div class="recover">
                    <span @click="recover"><i class="el-icon-refresh-left"></i>恢复</span>
                  </div>
                </div>
                <div class="content">
                  <ul>
                    <li class="item" v-for="(item, index) in sortList" :key="item.name" @click="changSort(item)">
                      <span :class="item.isChecked === true ? 'select' : ''">{{ item.sortIndex }}</span>{{ item.name }}
                    </li>
                  </ul>
                </div>
                <div class="bottom">
                  <el-button @click="sortListBtn">确定</el-button>
                </div>
                <div slot="reference" style="display: flex; align-items: center">
                  <el-popover placement="bottom" width="80" trigger="hover" content="列表自定义排序"
                    style="margin: 0!important;">
                    <el-button slot="reference" type="primary"
                      :icon="isReverse === true ? 'pony-iconv2 pony-jiangxu' : 'pony-iconv2 pony-shengxu'"
                      style="height: 28px;width: 20px;display: flex;align-items: center;justify-content: center;margin-left: 5px">
                    </el-button>
                  </el-popover>
                </div>
              </el-popover>
              <span class="text text--danger" style="padding-left: 8px">{{
                $ct("queryTip")
              }}</span>
              <div class="break-item"></div>
              <el-pagination small background :total="tableHistory.total" :page-size="30"
                :current-page.sync="tableHistory.query.page" layout="prev, pager, next, total">
              </el-pagination>
            </template>
            <template slot="content">
              <el-table ref="historyTable" :data="filterTableHistoryData" border stripe highlight-current-row
                height="100%">
                <el-table-column type="index" align="center" :label="$ct('label.index')"
                  :index="(index) => (tableMonitor.query.page - 1) * tableMonitor.query.size + index + 1"></el-table-column>
                <el-table-column min-width="120" prop="plateNo" :label="$ct('label.plateNo')"
                  show-overflow-tooltip></el-table-column>
                <el-table-column min-width="150" prop="deptName" :label="$ct('label.deptName')"
                  show-overflow-tooltip></el-table-column>
                <el-table-column prop="date" min-width="180" :label="$ct('label.date')"></el-table-column>
                <el-table-column prop="totalScore" :label="$ct('label.totalScore')">
                  <template slot-scope="scope">
                    <div class="score-card" :style="getTotalScoreStyle(scope.row.totalScore)">
                      {{ formatCeil(null, null, scope.row.totalScore) }}
                    </div>
                  </template>
                </el-table-column>
                <el-table-column :label="$ct('label.detail')">
                  <template slot-scope="scope">
                    <el-button type="text" icon="pony-iconv2 pony-xiangqing" size="mini"
                      @click="showDetail(scope.row)"></el-button>
                  </template>
                </el-table-column>
                <!-- <el-table-column
                                  min-width="100"
                                  prop="driveTime"
                                  :label="$ct('label.driveTime')"
                                ></el-table-column>
                                <el-table-column
                                  min-width="100"
                                  prop="driveMile"
                                  :label="$ct('label.driveMile')"
                                ></el-table-column>
                                <el-table-column
                                  min-width="130"
                                  prop="eventCount"
                                  :label="$ct('label.eventCount')"
                                ></el-table-column> -->
                <el-table-column v-for="item in currentHeaderList" :key="item.value" :prop="item.value"
                  :formatter="formatCeil" :min-width="130" :label="item.name">
                </el-table-column>
              </el-table>
            </template>
          </Layout>
        </el-tab-pane>
      </el-tabs>
      <scoreDetail ref="scoreDetail"></scoreDetail>
    </template>

    <PonyDialog v-model="dealModal.show" width="700px" @confirm="batchAll" :loading="dealModal.loading"
      style="left:calc(50% - 670px)">
      <span slot="title">自定义评分</span>
      <div class="Dialogconcent">
        <div class="Dialogtree">
          <ElementTree ref="vehicleTree" type="vehicle" :checkMode="true" @check="DialogonCheckVehicle"></ElementTree>
        </div>
        <div class="Dialogform">
          <el-form ref="form" :rules="rules" :model="dealModal.form" :label-position="labelPosition" label-width="100px"
            size="mini">
            <el-form-item label="已 选 择  : " prop="vehicleIdList">
              <span style="margin-right:190px">{{ dealModal.form.vehicleIdList.length }} 辆</span>
            </el-form-item>
            <el-form-item label="扣 分 项 : " prop="name">
              <el-input v-model="dealModal.form.name" placeholder="请输入扣分项名称
            " style="width:280px" clearable></el-input>
            </el-form-item>
            <el-form-item label="扣 分 值 : " prop="score">
              <el-input v-model="dealModal.form.score" placeholder="请输入扣分值" style="width:280px" clearable>
              </el-input>
            </el-form-item>
          </el-form>
          <span class="text text--danger" style="float:left;margin-right:10px;
                    position:absolute;bottom:10px;
                    right:0;text-align:right">
            每车当日仅保存最新扣分操作
          </span>
        </div>
      </div>
    </PonyDialog>
  </Layout>
</template>

<script>
import ExportJsonExcel from "js-export-excel";
import ScoreDetail from "../../components/ScoreDetail";
import { mapState } from "vuex";
import StartEndTime from "@/components/common/StartEedTime";


const TABLE_HEADER = [
  { minWidth: 120, prop: "accScore" },
  { minWidth: 120, prop: "brakeScore" },
  { minWidth: 120, prop: "fcwScore" },
  { minWidth: 120, prop: "hmwScore" },
  { minWidth: 120, prop: "laneKeepScore" },
  { minWidth: 170, prop: "pcwScore" },
  { minWidth: 100, prop: "speedScore" },
  { minWidth: 120, prop: "crossTseScore" },
  { minWidth: 140, prop: "turnLightScore" },
  { minWidth: 100, prop: "turnScore" },
  { minWidth: 120, prop: "smokeScore" },
  { minWidth: 140, prop: "distractScore" },
  { minWidth: 140, prop: "lrreguarDriveScore" },
  { minWidth: 120, prop: "speedNoAdasScore" },
  { minWidth: 140, prop: "telScore" },
  { minWidth: 140, prop: "tiredNoAdasScore" },
  { minWidth: 140, prop: "tiredScore" },
  { minWidth: 140, prop: "crossLeftTseScore" },
  { minWidth: 140, prop: "crossRightTseScore" },
  { minWidth: 140, prop: "crossStrightTseScore" },
  { minWidth: 140, prop: "policeScoreName" },
];
const FIXED_TABLE_HEADER = ['plateNo', 'deptName', 'totalScore', 'detail', 'date'];
/**
 * orderArray 指定排序属性，优先级依先后顺序 eg：["name", "num", "time"]
 * isReverse 是否倒序，默认 false
 */
Array.prototype.orderBy = Array.prototype.orderBy = function (
  orderArray,
  isReverse
) {
  if (typeof orderArray === "boolean" || typeof orderArray === "undefined") {
    isReverse = orderArray;
    orderArray = "";
  }
  if (typeof orderArray === "string") {
    let str = orderArray;
    orderArray = [];
    orderArray.push(str);
  }
  return this.sort((a, b) => {
    return compare(a, b, orderArray, isReverse);
  });
};

// 排序确认
function compare(a, b, orderArray, isReverse) {
  Array.prototype.orderBy = Array.prototype.orderBy = function (
    orderArray,
    isReverse
  ) {
    if (typeof orderArray === "boolean" || typeof orderArray === "undefined") {
      isReverse = orderArray;
      orderArray = "";
    }
    if (typeof orderArray === "string") {
      let str = orderArray;
      orderArray = [];
      orderArray.push(str);
    }
    return this.sort((a, b) => {
      return compare(a, b, orderArray, isReverse);
    });
  };

  // 排序确认
  function compare(a, b, orderArray, isReverse) {
    let c = orderArray[0];
    if (orderArray.length > 1 && a[c] === b[c]) {
      return compare(a, b, orderArray.slice(1), isReverse);
    } else {
      return a[c] == b[c] ? 0 : (isReverse ? a[c] > b[c] : a[c] < b[c]) ? -1 : 1;
    }
  }
}

export default {
  name: "car",
  components: {
    ScoreDetail,
    StartEndTime
  },
  data() {
    var checkScore = (rule, value, callback) => {
      if (value < 0 || value > 10) {
        callback(new Error("请输入0 到 10 以内的分值"));
      } else if (!value) {
        callback(new Error("请输入扣分值"));
      } else {
        callback();
      }
    };
    var checkVehicle = (rule, value, callback) => {
      if (value.length == 0) {
        callback(new Error("请输入0 到 10 以内的分值"));
      } else {
        callback();
      }
    };
    return {
      // flag: false,
      visible: false, // 弹窗显示与隐藏
      loading: false,
      labelPosition: "left", //dialog 中form左对齐
      activeTab2: "monitor", // 切换实时评分和历史评分
      sortList: [], // 排序弹窗里面的列表
      sort: [], // 存储排序条件
      recoverList: [], // 存储排序弹窗恢复的数据
      isReverse: true, // 排序弹窗升序和倒序
      deptId: "",
      hasDay: "",
      // jumpArr: '',
      tableHeader: {
        // 表头
        vehicle: { monitor: [], history: [] },
      },
      query: {
        vehicleIds: [],
      },
      tableMonitor: {
        query: {
          page: 1,
          size: 30,
        },
        data: [],
        sort: "default",
        sortKey: null,
        total: 0,
        loading: false,
      },
      tableHistory: {
        query: {
          page: 1,
          size: 30,
          hasDay: false,
          dateRange: [
            moment()
              .startOf("day")
              .subtract(1, "days")
              .toDate(),
            moment()
              .endOf("day")
              .subtract(1, "days")
              .toDate(),
          ],
        },
        data: [],
        sort: "default",
        sortKey: null,
        total: 0,
        loading: false,
      },
      // 自定义评分
      dealModal: {
        loading: false,
        show: false,
        form: {
          vehicleIdList: [],
          name: "",
          score: "",
        },
      },
      pickerOptions: {
        disabledDate: function (date) {
          return (
            moment()
              .endOf("day")
              .subtract(1, "days")
              .toDate() -
            date <
            0
          );
        },
      },
      rules: {
        vehicleIdList: [
          { required: true, validator: checkVehicle, trigger: 'blur' }
        ],
        score: [{ required: true, validator: checkScore, trigger: "blur" }],
        name: [
          { required: true, message: "请输入扣分项目名称", trigger: "blur" },
          {
            min: 1,
            max: 10,
            message: "长度在 1 到 10 个字符",
            trigger: "blur",
          },
        ],
      },
      exportLoading: false,
      exportHistoryLoading: false,
    };
  },
  watch: {
    // jumpArr: {
    //     handler(newVal) {
    //         this.change(newVal)
    //     },
    //     deep: true
    // }
  },
  computed: {
    ...mapState("vehicle", ["basicByVehicleId"]),

    // 实时评分分页
    filterTableMonitorData: function () {
      let arr = this.tableMonitor.data;
      let start =
        (this.tableMonitor.query.page - 1) * this.tableMonitor.query.size;
      let end = this.tableMonitor.query.page * this.tableMonitor.query.size;
      return arr.slice(start, end);
    },
    // 历史评分分页
    filterTableHistoryData: function () {
      let arr = this.tableHistory.data;
      let start =
        (this.tableHistory.query.page - 1) * this.tableHistory.query.size;
      let end = this.tableHistory.query.page * this.tableHistory.query.size;
      return arr.slice(start, end);
    },
    // 搜索框中结束时间
    endDatePickerOptions: function () {
      return {
        disabledDate: (date) => {
          return (
            date -
            moment()
              .endOf("day")
              .subtract(1, "days")
              .toDate() >
            0 || date - this.tableHistory.query.dateRange[0] < 0
          );
        },
      };
    },
    // 获取表头
    currentHeaderList: function () {
      this.$nextTick(() => {
        this.$refs["table"].doLayout();
      });
      return this.tableHeader["vehicle"][this.activeTab2].filter(
        (header) => !FIXED_TABLE_HEADER.includes(header.value)
      );
    },
  },
  async created() {
    try {
      this.pageLoading = true;
      let headerInfo = await Promise.all([
        this.$api.vehicleRealTimeDimensionScoreHead(),
        this.$api.vehicleHistoryDimensionScoreHead(),
        this.$api.fleetRealTimeDimensionScoreHead(),
        this.$api.fleetHistoryDimensionScoreHead(),
      ]);
      this.tableHeader = {
        vehicle: {
          monitor: headerInfo[0].data,
          history: headerInfo[1].data,
        },
      };
      // 深拷贝一份表头作为排序列表
      this.sortList = JSON.parse(JSON.stringify(this.currentHeaderList));
      // 排序列表中添加总分
      this.sortList.push({
        name: "总分",
        sortIndex: "1",
        isChecked: true,
        value: 'totalScore'
      });
      this.sort.push("totalScore");
    } catch (e) {
    } finally {
      this.pageLoading = false;
    }
  },
  methods: {
    getQueryMonitor(id) {
      this.$nextTick(async () => {
        await this.$refs["vehicleTree"].waitForInit
        this.$refs["vehicleTree"].$refs["tree"].setCurrentKey(id)
        this.$refs["vehicleTree"].$refs["tree"].setCheckedKeys([id]);
        let node = this.$refs["vehicleTree"].$refs["tree"].getNode(id);
        node.expand(null, true);
      });
      // 查询数据
      this.query.vehicleIds = [id];
      this.getTableMonitorData()
    },
    change(val) {
      this.$nextTick(() => {
        if (val.hasDay === 0) {
          this.$set(this.tableHistory.query, "hasDay", false);
        } else {
          this.$set(this.tableHistory.query, "hasDay", true);
        }
      });
      // if (this.flag === true) {
      this.tableHistory.query.dateRange[0] = val.dayStart;
      this.tableHistory.query.dateRange[1] = val.dayEnd;
      // }
      this.tableHistory.query.hasDay = val.hasDay;
      this.sort = val.sort;
      this.sortList = val.sortList;
      this.isReverse = val.isReverse;
      this.deptId = val.deptId;

      // 获取系统中当前账号下所有车辆
      let arr = this.basicByVehicleId;
      let carList = [];
      // 根据传入的值匹配车辆
      for (let item in arr) {
        if (
          arr[item].companyId === this.deptId ||
          arr[item].deptId === this.deptId
        ) {
          carList.push(arr[item].vehicleId.toString());
        }
      }
      // elementTree默认选中
      this.$nextTick(() => {
        this.$refs["vehicleTree"].$refs["tree"].setCheckedKeys(carList);
      });
      // 查询数据
      this.query.vehicleIds = carList;
      this.getTableHistoryData();
    },
    // 排序确认按钮
    sortListBtn() {
      // 深拷贝
      let list = JSON.parse(JSON.stringify(this.recoverList));
      this.tableHistory.data = list.orderBy(this.sort, this.isReverse);
      this.visible = false;
    },
    // 排序方向
    sortDirection() {
      this.isReverse = !this.isReverse;
    },
    // 排序
    changSort(data) {
      // 如果点击的排序选项已经选中，则取消选中
      if (data.isChecked === true) {
        // 获取当前序号
        let sortNum = data.sortIndex;
        // 在已选中的排序列表中删除
        let indexOf = this.sort.indexOf(data.value);
        this.sort.splice(indexOf, 1);

        // 点击选项取消选择并且大于当前序号的排序选项序号-1
        this.sortList.forEach((item, index) => {
          if (item.value === data.value) {
            this.$set(
              this.sortList[index],
              "isChecked",
              !this.sortList[index].isChecked
            );
            this.$set(this.sortList[index], "sortIndex", "");
          }

          if (item.sortIndex > sortNum) {
            this.$set(
              this.sortList[index],
              "sortIndex",
              this.sortList[index].sortIndex - 1
            );
          }
        });
      } else {
        // 如果点击的排序选项没有选中
        this.sortList.forEach((item, index) => {
          if (item.value === data.value) {
            this.$set(
              this.sortList[index],
              "isChecked",
              !this.sortList[index].isChecked
            );
            this.$set(this.sortList[index], "sortIndex", this.sort.length + 1);
            this.sort.push(this.sortList[index].value);
          }
        });
      }
    },
    // 排序弹窗恢复
    recover() {
      this.sort = [];
      // 排序列表全部取消选中
      this.sortList.forEach((item, index) => {
        this.$set(this.sortList[index], "isChecked", false);
        this.$set(this.sortList[index], "sortIndex", "");
      });
      this.tableHistory.data = this.recoverList;
    },
    // 树点击事件
    onCheckVehicle(data, { checkedNodes }) {
      let nodes = checkedNodes.filter((item) => item.type === 4);
      this.query.vehicleIds = nodes.map((item) => item.id);
    },
    // 查询实时评分
    async getTableMonitorData() {
      if (this.query.vehicleIds.length === 0) {
        this.$warning(this.$ct("messageInfo.0"));
        return;
      }
      let res;
      try {
        this.tableMonitor.loading = true;
        res = await this.$api.fleetVehicleDimensionScoreMonitoring({
          vehicleIds: this.query.vehicleIds,
        });

        if (res.status === 200) {
          res.data.forEach((item, index) => {
            item._originIndex = index;
          });
          this.tableMonitor.data = res.data;
          this.tableMonitor.total = res.data.length;
          this.$nextTick(() => {
            this.$refs.table.doLayout();
          });
        } else {
          this.$error(this.$ct("messageInfo.2"));
        }
      } catch (e) {
        this.$error(this.$ct("messageInfo.3"));
      } finally {
        this.tableMonitor.loading = false;
      }
    },
    // 查询历史评分
    async getTableHistoryData() {
      // this.flag = false

      let res;
      let params = {
        dayStart: this.DateFormat(this.tableHistory.query.dateRange[0]),
        dayEnd: this.DateFormat(this.tableHistory.query.dateRange[1]),
        hasDay: this.tableHistory.query.hasDay ? 1 : 0,
      };
      if (this.query.vehicleIds.length === 0) {
        this.$warning(this.$ct("messageInfo.0"));
        return;
      }
      try {
        this.tableHistory.loading = true;
        res = await this.$api.vehicleHistoricalDimensionScore({
          ...params,
          vehicleIds: this.query.vehicleIds,
        });
        if (res.status === 200) {
          res.data.forEach((item, index) => {
            item._originIndex = index;
          });
          this.tableHistory.data = res.data;
          this.tableHistory.total = res.data.length;
          this.recoverList = res.data;
          // 默认按照总分排序
          this.sortListBtn();
          this.$nextTick(() => {
            this.$refs.historyTable.doLayout();
          });
        } else {
          this.$error(res.data);
        }
      } catch (e) {
        this.$error(this.$ct("messageInfo.3"));
      } finally {
        this.tableHistory.loading = false;
      }
    },
    // 列表总分的宽度
    formatCeil(row, column, cellValue, index) {
      if (Number(cellValue) < 0) {
        return "-";
      } else if (typeof cellValue == "number") {
        return Math.round(Number(cellValue) * 100) / 100;
      } else {
        return cellValue;
      }
    },
    // 列表总分的样式
    getTotalScoreStyle(score) {
      let color;
      switch (true) {
        case score >= 0 && score < 40:
          color = "rgb(255, 87, 87)";
        case score >= 40 && score < 60:
          color = "rgb(252, 196, 25)";
          break;
        case score >= 60 && score < 80:
          color = "rgb(51, 133, 253)";
          break;
        case score >= 80:
          color = "rgb(52, 184, 67)";
          break;
      }
      return `background-color:${color}`;
    },
    dateStartChange(date) {
      if (this.tableHistory.query.dateRange[1] - date < 0) {
        this.$set(this.tableHistory.query.dateRange, 1, date);
      }
    },
    // 显示详情弹窗
    showDetail(row) {
      let loadingPromise = new Promise(async (resolve, reject) => {
        let res;
        let params = {
          dayStart: this.DateFormat(this.tableHistory.query.dateRange[0]),
          dayEnd: this.DateFormat(this.tableHistory.query.dateRange[1]),
        };
        switch (true) {
          case this.activeTab2 === "monitor":
            res = await this.$api.realTimeDetailsPage({
              vehicleId: row.vehicleId,
            });
            break;
          case this.activeTab2 === "history":
            res = await this.$api.historicalDetailsPage({
              vehicleId: row.vehicleId,
              date: row.date,
            });
            break;
        }
        if (!res || res.status != 200) {
          reject(this.$ct("messageInfo.2"));
        }
        resolve({
          ...res.data,
          dimension: this.$utils.assign(res.data.scoreContainer, row),
        });
      });
      this.$refs.scoreDetail.show(loadingPromise, "vehicle");
    },

    // 自定义扣分弹窗
    async showDealModal() {
      this.dealModal.show = true;
      this.dealModal.form.vehicleIdList = [];
      this.dealModal.form.name = "";
      this.dealModal.form.score = "";
    },
    // 扣分弹窗内容
    async batchAll() {
      this.dealModal.loading = true;
      // this.dealModal.show=true;
      if (this.dealModal.form.vehicleIdList.length == 0 || this.dealModal.form.name == "" || this.dealModal.form.score == "") {
        this.$warning("请将必填项补充完整")
        this.dealModal.loading = false;
      } else {
        let result = await this.$api.policeScoreYW({
          vehicleIdList: this.dealModal.form.vehicleIdList,
          name: this.dealModal.form.name,
          score: this.dealModal.form.score,
        });
        if (result.status === 200) {
          this.dealModal.loading = false;
          this.dealModal.show = false;
          this.$success("扣分成功");
          this.getTableMonitorData();
        } else {
          this.$error(result.message);
        }
      }
    },
    // Dialog里面树点击事件
    DialogonCheckVehicle(data, { checkedNodes }) {
      let nodes = checkedNodes.filter((item) => item.type === 4);
      this.dealModal.form.vehicleIdList = nodes.map((item) => item.id);
    },
    // 导出实时评分excel
    exportTableMonitorData() {
      let currentList = this.tableMonitor.data;
      if (!currentList.length) {
        this.$warning(this.$ct("messageInfo.4"));
        return;
      }
      this.exportLoading = true
      let excelBody = [];
      let f = (v) => {
        return this.formatCeil(null, null, v);
      };
      currentList.forEach((item, index) => {
        let array = [
          index + 1,
          item.plateNo,
          item.deptName,
          f(item.totalScore),
          // item.driveTime,
          // item.driveMile,
          // item.eventCount,
          // f(item.accScore),
          // f(item.brakeScore),
          // f(item.fcwScore),
          // f(item.hmwScore),
          // f(item.laneKeepScore),
          // f(item.pcwScore),
          // f(item.speedScore),
          // f(item.turnLightScore),
          // f(item.smokeScore),
          // f(item.distractScore),
          // f(item.lrreguarDriveScore),
          // f(item.speedNoAdasScore),
          // f(item.telScore),
          // f(item.tiredNoAdasScore),
          // f(item.tiredScore),
        ];
        this.currentHeaderList.forEach((header) => {
          array.push(f(item[header.value]));
        });
        excelBody.push(array);
      });
      const headers = [
        "index",
        "plateNo",
        "deptName",
        "totalScore",
        // "driveTime",
        // "driveMile",
        // "eventCount",
      ];
      this.currentHeaderList.forEach((item) => {
        headers.push(item.value);
      });
      let options = {
        fileName: `实时评分监控`,
        datas: [
          {
            sheetData: excelBody,
            sheetHeader: headers.map((item) => this.$ct(`label.${item}`)),
            columnWidths: [
              "3",
              "10",
              "10",

              "7",
              "7",
              "7",
              "7",
              "7",
              "7",
              "7",
              "7",
              "7",
              "7",
              "7",
              "7",
              "7",
              "7",
              "7",
              "7",
              "7",
              "7",
              "7",
            ],
          },
        ],
      };
      ExportJsonExcel(options).saveExcel();
      this.exportLoading = false
    },
    // 导出历史评分excel
    exportTableHistoryData() {
      let currentList = this.tableHistory.data;
      if (!currentList.length) {
        this.$warning(this.$ct("messageInfo.4"));
        return;
      }
      this.exportHistoryLoading = true
      let excelBody = [];
      let f = (v) => {
        return this.formatCeil(null, null, v);
      };
      currentList.forEach((item, index) => {
        let array = [
          index + 1,
          item.plateNo,
          item.deptName,
          item.date,
          f(item.totalScore),
          // item.driveTime,
          // item.driveMile,
          // item.eventCount,
          // f(item.accScore),
          // f(item.brakeScore),
          // f(item.fcwScore),
          // f(item.hmwScore),
          // f(item.laneKeepScore),
          // f(item.pcwScore),
          // f(item.speedScore),
          // f(item.turnLightScore),
          // f(item.smokeScore),
          // f(item.distractScore),
          // f(item.lrreguarDriveScore),
          // f(item.speedNoAdasScore),
          // f(item.telScore),
          // f(item.tiredNoAdasScore),
          // f(item.tiredScore),
        ];
        this.currentHeaderList.forEach((header) => {
          array.push(f(item[header.value]));
        });
        excelBody.push(array);
      });
      const headers = [
        "index",
        "plateNo",
        "deptName",
        "date",
        "totalScore",
        // "driveTime",
        // "driveMile",
        // "eventCount",
      ];
      this.currentHeaderList.forEach((item) => {
        headers.push(item.value);
      });
      let options = {
        fileName: this.$ct("reportName"),
        datas: [
          {
            sheetData: excelBody,
            sheetHeader: headers.map((item) => this.$ct(`label.${item}`)),
            columnWidths: [
              "3",
              "10",
              "10",
              "15",
              "7",
              "7",
              "7",
              "7",
              "7",
              "7",
              "7",
              "7",
              "7",
              "7",
              "7",
              "7",
              "7",
              "7",
              "7",
              "7",
              "7",
              "7",
              "7",
            ],
          },
        ],
      };
      ExportJsonExcel(options).saveExcel();
      this.exportHistoryLoading = true

    },
  },
};
</script>

<style scoped lang="scss">
.car {
  .query-top {
    height: 100%;
    overflow: auto;
  }

  .query-bottom {
    margin-top: 5px;
    padding: 10px;

    .query-item {
      display: flex;
      height: 40px;
      line-height: 40px;
      justify-content: space-between;
      align-items: center;

      >span {
        font-size: 12px;
        white-space: nowrap;
        margin-right: 5px;
      }
    }
  }

  .sort-btn {
    margin-left: 5px;

    .pony-iconv2 {
      font-size: 12px;
    }
  }

  .score-card {
    color: #ffffff;
    border-radius: 4px;
    text-align: center;
  }

  .score-aside {
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
  }

  .Dialogconcent {
    display: flex;
    text-align: center;

    .Dialogtree {
      width: 300px;
      height: 350px;
      display: inline-block;
      // background-color: #1d233b;
      margin: 10px;

    }

    .Dialogform {
      padding: 8px;
      width: 400px;
      height: 350px;
      display: inline-block;
      // background-color: #1d233b;
      margin: 10px;
      position: relative;
    }
  }
}
</style>
<style lang="scss">
.exportExcel {
  margin-left: 5px;
}

.my-el-popover {
  padding: 0;

  &:hover {}

  .top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 12px 0 12px;

    .sort-direction {
      display: flex;
      text-align: center;

      .up {
        width: 25px;
        border: 1px solid var(--border-color-base);
        border-radius: 4px 0 0 4px;
      }

      .down {
        width: 25px;
        border: 1px solid var(--border-color-base);
        margin-left: -1px;
        border-radius: 0 4px 4px 0;
      }

      .true {
        color: var(--color-primary);
        border: 1px solid var(--color-primary);
        z-index: 9999;
      }
    }

    .recover {
      color: var(--color-primary);
    }
  }

  .content {
    border-bottom: 1px solid var(--border-color-base);
    padding: 0 12px 12px 12px;

    ul {
      display: flex;
      flex-wrap: wrap;

      li {
        display: flex;
        align-items: center;
        width: 160px;
        height: 20px;
        font-size: 14px;
        margin-top: 10px;

        span {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 20px;
          height: 20px;
          border: 1px solid var(--border-color-base);
          margin-right: 5px;
          border-radius: 3px;
          color: #fff;
        }

        .select {
          background-color: var(--color-primary);
        }
      }
    }
  }

  .bottom {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding: 4px;
  }
}
</style>

<template>
  <Layout :content-loading="loading" class="car" :has-color="true">
    <template slot="aside" class="score-aside">
      <div class="query-top">
        <ElementTree ref="companyTree" type="department" :checkMode="true" @check="onCheckCompany"></ElementTree>
      </div>
      <div class="query-bottom bg bg--lighter" v-if="activeTab2 == 'monitor'">
        <div class="query-item">
          <span>企业/车队</span>
          <el-select style="width: 100%" v-model="query.monitordept" placeholder="请选择">
            <el-option :value="2" label="企业"></el-option>
            <el-option :value="3" label="车队"></el-option>
          </el-select>
        </div>
        <div class="query-item">
          <el-button size="mini" type="primary" style="width: 100%;" @click="getTableMonitorData" :loading="loading">{{
            $t("common.query") }}
          </el-button>
        </div>
      </div>
      <div class="query-bottom bg bg--lighter" v-else>
        <!-- <div class="query-item">
                  <span>开始日期</span>
                  <el-date-picker
                    v-model="tableHistory.query.dateRange[0]"
                    type="date"
                    :picker-options="pickerOptions"
                    @change="dateStartChange"
                  >
                  </el-date-picker>
                </div>
                <div class="query-item">
                  <span>结束日期</span>
                  <el-date-picker
                    v-model="tableHistory.query.dateRange[1]"
                    type="date"
                    :picker-options="endDatePickerOptions"
                  >
                  </el-date-picker>
                </div> -->
        <StartEndTime itemHeight="35" v-model="tableHistory.query.dateRange" valueFormat="timestamp" timeType="date"
          :isLimit="true">
        </StartEndTime>
        <div class="query-item">
          <span>按日显示</span>
          <el-switch style="margin-left: 3px" active-color="#13ce66" inactive-color="#1d233b"
            v-model="tableHistory.query.hasDay">
          </el-switch>
        </div>
        <div class="query-item">
          <span>企业/车队</span>
          <el-select style="width: 100%" v-model="query.historydept" placeholder="请选择">
            <el-option :value="2" label="企业"></el-option>
            <el-option :value="3" label="车队"></el-option>
          </el-select>
        </div>
        <div class="query-item">
          <el-button size="mini" type="primary" style="width: 100%;" @click="getTableHistoryData">{{ $t("common.query") }}
          </el-button>
        </div>
      </div>
    </template>

    <template slot="content">
      <el-tabs v-model="activeTab2" type="border-card">
        <el-tab-pane :label="$ct('monitor')" name="monitor">
          <Layout class="layout--no-padding-horizontal" :content-loading="tableMonitor.loading">
            <template slot="query">
              <el-button type="primary" style="margin-left:10px" @click="exportTableMonitorData" :loading="exportLoading">
                {{ $ct("export") }}
              </el-button>
              <span class="text text--danger" style="padding-left: 8px">{{ $ct("queryTip") }}</span>
              <div class="break-item"></div>
              <el-pagination small background :total="tableMonitor.total" :page-size="30"
                :current-page.sync="tableMonitor.query.page" layout="prev, pager, next, total">
              </el-pagination>
            </template>
            <template slot="content">
              <el-table ref="table" :data="filterTableMonitorData" border stripe highlight-current-row height="100%">
                <el-table-column type="index" align="center" :label="$ct('label.index')"
                  :index="(index) => (tableMonitor.query.page - 1) * tableMonitor.query.size + index + 1"></el-table-column>
                <el-table-column min-width="150" prop="deptName" :label="$ct('label.deptName')"
                  show-overflow-tooltip></el-table-column>
                <el-table-column prop="totalScore" :label="$ct('label.totalScore')">
                  <template slot-scope="scope">
                    <div :style="getTotalScoreStyle(scope.row.totalScore)" class="score-card">
                      {{ formatCeil(null, null, scope.row.totalScore) }}
                    </div>
                  </template>
                </el-table-column>
                <el-table-column :label="$ct('label.detail')">
                  <template slot-scope="scope">
                    <el-button type="text" icon="pony-iconv2 pony-xiangqing" size="mini"
                      @click="showDetail(scope.row)"></el-button>
                  </template>
                </el-table-column>
                <el-table-column min-width="100" prop="driveTime" :label="$ct('label.driveTime')"></el-table-column>
                <el-table-column min-width="100" prop="driveMile" :label="$ct('label.driveMile')"></el-table-column>
                <el-table-column min-width="130" prop="eventCount" :label="$ct('label.eventCount')"></el-table-column>
                <el-table-column v-for="item in currentHeaderList" :key="item.prop" :prop="item.prop"
                  :formatter="formatCeil" :min-width="item.minWidth" :label="$ct('label.' + item.prop)">
                </el-table-column>
              </el-table>
            </template>
          </Layout>
        </el-tab-pane>
        <el-tab-pane label="历史评分" name="history" lazy>
          <Layout class="layout--no-padding-horizontal" :content-loading="tableHistory.loading">
            <template slot="query">
              <el-button type="primary" @click="exportTableHistoryData" style="margin-left:10px"
                :loading="exportHistoryLoading">
                {{ $ct("export") }}
              </el-button>
              <el-popover popper-class="my-el-popover" placement="bottom-start" width="350" v-model="visible">
                <div class="top">
                  <div class="sort-direction">
                    <span class="up" :class="isReverse === false ? 'true' : ''" @click="sortDirection">
                      <i class="pony-iconv2 pony-shengxu"></i>
                    </span>
                    <span class="down" :class="isReverse === true ? 'true' : ''" @click="sortDirection">
                      <i class="pony-iconv2 pony-jiangxu"></i>
                    </span>
                  </div>
                  <div class="recover">
                    <span @click="recover">
                      <i class="el-icon-refresh-left"></i>恢复
                    </span>
                  </div>
                </div>
                <div class="content">
                  <ul>
                    <li class="item" v-for="(item, index) in sortList" :key="item.prop" @click="changSort(item)">
                      <span :class="item.isChecked === true ? 'select' : ''">{{
                        item.sortIndex
                      }}</span>{{ $ct("label." + item.prop) }}
                    </li>
                  </ul>
                </div>
                <div class="bottom">
                  <el-button @click="sortListBtn">确定</el-button>
                </div>
                <div slot="reference" style="display: flex; align-items: center">
                  <el-button type="primary"
                    :icon="isReverse === true ? 'pony-iconv2 pony-jiangxu' : 'pony-iconv2 pony-shengxu'"
                    style="height: 28px;width: 20px;display: flex;align-items: center;justify-content: center;margin-left: 5px"></el-button>
                </div>
              </el-popover>
              <span class="text text--danger" style="padding-left: 8px">{{ $ct("queryTip") }}</span>
              <div class="break-item"></div>
              <el-pagination small background :total="tableHistory.total" :page-size="30"
                :current-page.sync="tableHistory.query.page" layout="prev, pager, next, total">
              </el-pagination>
            </template>
            <template slot="content">
              <el-table ref="historyTable" :data="filterTableHistoryData" border stripe highlight-current-row
                height="100%">
                <el-table-column type="index" align="center" :label="$ct('label.index')"
                  :index="(index) => (tableMonitor.query.page - 1) * tableMonitor.query.size + index + 1"></el-table-column>
                <el-table-column min-width="150" prop="deptName" :label="$ct('label.deptName')"
                  show-overflow-tooltip></el-table-column>
                <el-table-column prop="date" min-width="180" :label="$ct('label.date')"></el-table-column>
                <el-table-column prop="totalScore" :label="$ct('label.totalScore')">
                  <template slot-scope="scope">
                    <div class="score-card" :style="getTotalScoreStyle(scope.row.totalScore)">
                      {{ formatCeil(null, null, scope.row.totalScore) }}
                    </div>
                  </template>
                </el-table-column>
                <el-table-column :label="$ct('label.detail')">
                  <template slot-scope="scope">
                    <div style="display: flex">
                      <el-button type="text" icon="pony-iconv2 pony-xiangqing" size="mini"
                        @click="showDetail(scope.row)"></el-button>
                      <el-button type="text" icon="pony-iconv2 pony-cheliangpingfen" size="mini"
                        @click="jump(scope.row)"></el-button>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column min-width="100" prop="driveTime" :label="$ct('label.driveTime')"></el-table-column>
                <el-table-column min-width="100" prop="driveMile" :label="$ct('label.driveMile')"></el-table-column>
                <el-table-column min-width="130" prop="eventCount" :label="$ct('label.eventCount')"></el-table-column>
                <el-table-column v-for="item in currentHeaderList" :key="item.prop" :prop="item.prop"
                  :formatter="formatCeil" :min-width="item.minWidth" :label="$ct('label.' + item.prop)">
                </el-table-column>
              </el-table>
            </template>
          </Layout>
        </el-tab-pane>
      </el-tabs>
      <ScoreDetail ref="scoreDetail"></ScoreDetail>
    </template>
  </Layout>
</template>

<script>
import ExportJsonExcel from "js-export-excel";
import ScoreDetail from "../../components/ScoreDetail";
import StartEndTime from "@/components/common/StartEedTime";

const TABLE_HEADER = [
  { minWidth: 140, prop: "nodriverScore" },
  { minWidth: 120, prop: "accScore" },
  { minWidth: 120, prop: "brakeScore" },
  { minWidth: 120, prop: "fcwScore" },
  { minWidth: 120, prop: "hmwScore" },
  { minWidth: 120, prop: "laneKeepScore" },
  { minWidth: 170, prop: "pcwScore" },
  { minWidth: 100, prop: "speedScore" },
  { minWidth: 120, prop: "crossTseScore" },
  { minWidth: 140, prop: "turnLightScore" },
  { minWidth: 100, prop: "turnScore" },
  { minWidth: 120, prop: "smokeScore" },
  { minWidth: 140, prop: "distractScore" },
  { minWidth: 140, prop: "lrreguarDriveScore" },
  { minWidth: 120, prop: "speedNoAdasScore" },
  { minWidth: 140, prop: "telScore" },
  { minWidth: 140, prop: "tiredNoAdasScore" },
  { minWidth: 140, prop: "tiredScore" },
  { minWidth: 140, prop: "crossLeftTseScore" },
  { minWidth: 140, prop: "crossRightTseScore" },
  { minWidth: 140, prop: "crossStrightTseScore" },
  { minWidth: 140, prop: "policeScoreName" },
];
export default {
  name: "enterprise",
  components: {
    ScoreDetail,
    StartEndTime
  },
  data() {
    return {
      visible: false, // 弹窗显示与隐藏
      loading: false,
      activeTab2: "monitor", // 切换实时评分和历史评分
      sortList: [], // 排序弹窗里面的列表
      sort: [], // 已选中的的排序条件
      recoverList: [], // 存储排序弹窗恢复的数据
      isReverse: true, // 排序弹窗升序和倒序
      tableHeader: {
        vehicle: { monitor: [], history: [] },
      },
      checkedNodesCompany: [],
      query: {
        vehicleIds: [],
        monitordept: 2,
        historydept: 2,
      },
      tableMonitor: {
        query: {
          page: 1,
          size: 30,
        },
        data: [],
        sort: "default",
        sortKey: null,
        total: 0,
        loading: false,
      },
      tableHistory: {
        query: {
          page: 1,
          size: 30,
          hasDay: false,
          dateRange: [
            moment()
              .startOf("day")
              .subtract(1, "days")
              .toDate(),
            moment()
              .endOf("day")
              .subtract(1, "days")
              .toDate(),
          ],
        },
        data: [],
        sort: "default",
        sortKey: null,
        total: 0,
        loading: false,
      },
      pickerOptions: {
        disabledDate: function (date) {
          return (
            date -
            moment()
              .endOf("day").subtract(1, "days")
              .toDate() >
            0
          );
        },
      },
      exportLoading: false,
      exportHistoryLoading: false,

    };
  },
  computed: {
    // 实时评分分页
    filterTableMonitorData: function () {
      let arr = this.tableMonitor.data;
      let start =
        (this.tableMonitor.query.page - 1) * this.tableMonitor.query.size;
      let end = this.tableMonitor.query.page * this.tableMonitor.query.size;
      return arr.slice(start, end);
    },
    // 历史评分分页
    filterTableHistoryData: function () {
      let arr = this.tableHistory.data;
      let start =
        (this.tableHistory.query.page - 1) * this.tableHistory.query.size;
      let end = this.tableHistory.query.page * this.tableHistory.query.size;
      return arr.slice(start, end);
    },
    // 搜索框中结束时间
    endDatePickerOptions: function () {
      return {
        disabledDate: (date) => {
          return (
            date -
            moment()
              .endOf("day").subtract(1, "days")
              .toDate() >
            0 || date - this.tableHistory.query.dateRange[0] < 0
          );
        },
      };
    },
    // 获取表头
    currentHeaderList: function () {
      this.$nextTick(() => {
        this.$refs["table"].doLayout();
      });
      console.log(this.tableHeader)
      return TABLE_HEADER.filter(
        (header) =>
          !!this.tableHeader["vehicle"][this.activeTab2].find(
            (item) => item.value === header.prop
          )
      );
    },
  },
  async created() {
    try {
      this.pageLoading = true;
      let headerInfo = await Promise.all([
        this.$api.vehicleRealTimeDimensionScoreHead(),
        this.$api.vehicleHistoryDimensionScoreHead(),
        this.$api.fleetRealTimeDimensionScoreHead(),
        this.$api.fleetHistoryDimensionScoreHead(),
      ]);
      this.tableHeader = {
        vehicle: {
          monitor: headerInfo[0].data,
          history: headerInfo[1].data,
        },
      };
      // 深拷贝一份表头作为排序列表
      this.sortList = JSON.parse(JSON.stringify(this.currentHeaderList));
      // 排序列表中添加总分列
      this.sortList.push({
        prop: "totalScore",
        sortIndex: "1",
        isChecked: true,
      });
      this.sort.push("totalScore");
    } catch (e) {
    } finally {
      this.pageLoading = false;
    }
  },
  methods: {
    // 跳转到车辆评分传的参数
    jump(row) {
      let arr = {
        deptId: row.deptId,
        dayStart: this.tableHistory.query.dateRange[0],
        dayEnd: this.tableHistory.query.dateRange[1],
        hasDay: this.tableHistory.query.hasDay ? 1 : 0,
        sort: this.sort,
        sortList: this.sortList,
        isReverse: this.isReverse,
      };
      this.$emit("jumpCar", arr);
    },
    // 排序确认按钮
    sortListBtn() {
      // 采用深拷贝
      let list = JSON.parse(JSON.stringify(this.recoverList));
      this.tableHistory.data = list.orderBy(this.sort, this.isReverse);
      this.visible = false;
    },
    // 排序方向
    sortDirection() {
      this.isReverse = !this.isReverse;
    },
    // 排序
    changSort(data) {
      // 如果点击的排序选项已经选中，则取消选中
      if (data.isChecked === true) {
        // 获取当前序号
        let sortNum = data.sortIndex;

        // 在已选中的排序列表中删除
        let indexOf = this.sort.indexOf(data.prop);
        this.sort.splice(indexOf, 1);

        // 点击选项取消选择并且大于当前序号的排序选项序号-1
        this.sortList.forEach((item, index) => {
          if (item.prop === data.prop) {
            this.$set(
              this.sortList[index],
              "isChecked",
              !this.sortList[index].isChecked
            );
            this.$set(this.sortList[index], "sortIndex", "");
          }

          if (item.sortIndex > sortNum) {
            this.$set(
              this.sortList[index],
              "sortIndex",
              this.sortList[index].sortIndex - 1
            );
          }
        });
      } else {
        // 如果点击的排序选项没有选中
        this.sortList.forEach((item, index) => {
          if (item.prop === data.prop) {
            this.$set(
              this.sortList[index],
              "isChecked",
              !this.sortList[index].isChecked
            );
            this.$set(this.sortList[index], "sortIndex", this.sort.length + 1);
            this.sort.push(this.sortList[index].prop);
          }
        });
      }
    },
    // 排序弹窗恢复
    recover() {
      this.sort = [];
      // 排序列表全部取消选中
      this.sortList.forEach((item, index) => {
        this.$set(this.sortList[index], "isChecked", false);
        this.$set(this.sortList[index], "sortIndex", "");
      });
      this.tableHistory.data = this.recoverList;
    },
    // 树点击事件
    onCheckCompany(data, { checkedNodes }) {
      let userType = [2, 3];
      this.checkedNodesCompany = checkedNodes.filter((item) =>
        userType.includes(item.type)
      );
    },
    // 查询实时评分
    async getTableMonitorData() {
      let deptIds = this.checkedNodesCompany
        .filter((item) => item.type == this.query.monitordept)
        .map((item) => item.id);

      if (deptIds.length === 0) {
        this.$warning(this.$ct("messageInfo.1"));
        return;
      }
      let res;
      try {
        this.tableMonitor.loading = true;
        res = await this.$api.fleetDimensionScore({
          deptIds: deptIds,
          deptType: this.query.monitordept,
        });

        if (res.status === 200) {
          res.data.forEach((item, index) => {
            item._originIndex = index;
          });
          this.tableMonitor.data = res.data;
          this.tableMonitor.total = res.data.length;
          this.$nextTick(() => {
            this.$refs.table.doLayout();
          });
        } else {
          this.$error(this.$ct("messageInfo.2"));
        }
      } catch (e) {
        this.$error(this.$ct("messageInfo.3"));
      } finally {
        this.tableMonitor.loading = false;
      }
    },
    // 查询历史评分
    async getTableHistoryData() {
      let res;
      let params = {
        dayStart: this.DateFormat(this.tableHistory.query.dateRange[0]),
        dayEnd: this.DateFormat(this.tableHistory.query.dateRange[1]),
        hasDay: this.tableHistory.query.hasDay ? 1 : 0,
        deptType: this.query.historydept,
      };

      let deptIds = this.checkedNodesCompany
        .filter((item) => item.type == this.query.historydept)
        .map((item) => item.id);

      if (deptIds.length === 0) {
        this.$warning(this.$ct("messageInfo.1"));
        return;
      }

      try {
        this.tableHistory.loading = true;
        res = await this.$api.fleetHistoryDimensionScore({
          ...params,
          deptIds: deptIds,
        });

        if (res.status === 200) {
          res.data.forEach((item, index) => {
            item._originIndex = index;
          });
          this.tableHistory.data = res.data;
          this.tableHistory.total = res.data.length;
          this.recoverList = res.data;
          // 默认按照总分排序
          this.sortListBtn();
          this.$nextTick(() => {
            this.$refs.historyTable.doLayout();
          });
        } else {
          this.$error(res.data);
        }
      } catch (e) {
        this.$error(this.$ct("messageInfo.3"));
      } finally {
        this.tableHistory.loading = false;
      }
    },
    // 百分比
    formatCeil(row, column, cellValue, index) {
      if (Number(cellValue) < 0) {
        return "-";
      } else if (typeof cellValue == "number") {
        return Math.round(Number(cellValue) * 100) / 100;
      } else {
        return cellValue
      }
    },
    // 列表总分的样式
    getTotalScoreStyle(score) {
      let color;
      switch (true) {
        case score >= 0 && score < 40:
          color = "rgb(255, 87, 87)";
          break;
        case score >= 40 && score < 60:
          color = "rgb(252, 196, 25)";
          break;
        case score >= 60 && score < 80:
          color = "rgb(51, 133, 253)";
          break;
        case score >= 80:
          color = "rgb(52, 184, 67)";
          break;
      }
      return `background-color:${color}`;
    },
    dateStartChange(date) {
      if (this.tableHistory.query.dateRange[1] - date < 0) {
        this.$set(this.tableHistory.query.dateRange, 1, date);
      }
    },
    // 显示详情弹窗
    showDetail(row) {
      let loadingPromise = new Promise(async (resolve, reject) => {
        let res;
        let params = {
          dayStart: this.DateFormat(this.tableHistory.query.dateRange[0]),
          dayEnd: this.DateFormat(this.tableHistory.query.dateRange[1]),
        };
        switch (true) {
          case this.activeTab2 === "monitor":
            res = await this.$api.realTimeDetailsPage({ deptId: row.deptId });
            break;
          case this.activeTab2 === "history":
            res = await this.$api.historicalDetailsPage({
              deptId: row.deptId,
              date: row.date,
            });
            break;
        }
        if (!res || res.status != 200) {
          reject(this.$ct("messageInfo.2"));
        }
        resolve({
          ...res.data,
          dimension: this.$utils.assign(res.data.scoreContainer, row),
        });
      });
      this.$refs.scoreDetail.show(loadingPromise, "vehicle");
    },
    // 导出实时评分excel
    exportTableMonitorData() {
      let currentList = this.tableMonitor.data;
      if (!currentList.length) {
        this.$warning(this.$ct("messageInfo.4"));
        return;
      }
      this.exportLoading = true;
      let excelBody = [];
      let f = (v) => {
        return this.formatCeil(null, null, v);
      };
      currentList.forEach((item, index) => {
        let array = [
          index + 1,
          item.plateNo,
          item.deptName,
          f(item.totalScore),
          item.driveTime,
          item.driveMile,
          item.eventCount,
          // f(item.accScore),
          // f(item.brakeScore),
          // f(item.fcwScore),
          // f(item.hmwScore),
          // f(item.laneKeepScore),
          // f(item.pcwScore),
          // f(item.speedScore),
          // f(item.turnLightScore),
          // f(item.smokeScore),
          // f(item.distractScore),
          // f(item.lrreguarDriveScore),
          // f(item.speedNoAdasScore),
          // f(item.telScore),
          // f(item.tiredNoAdasScore),
          // f(item.tiredScore),
        ];
        this.currentHeaderList.forEach((header) => {
          array.push(f(item[header.prop]));
        });
        excelBody.push(array);
      });
      const headers = [
        "index",
        "plateNo",
        "deptName",
        "totalScore",
        "driveTime",
        "driveMile",
        "eventCount",
      ];
      this.currentHeaderList.forEach((item) => {
        headers.push(item.prop);
      });
      let options = {
        fileName: `实时评分监控`,
        datas: [
          {
            sheetData: excelBody,
            sheetHeader: headers.map((item) => this.$ct(`label.${item}`)),
            columnWidths: [
              "3",
              "7",
              "10",
              "7",
              "7",
              "7",
              "7",
              "7",
              "7",
              "7",
              "7",
              "7",
              "7",
              "7",
              "7",
              "7",
              "7",
              "7",
              "7",
              "7",
              "7",
              "7",
            ],
          },
        ],
      };
      ExportJsonExcel(options).saveExcel();
      this.exportLoading = false;
    },
    // 导出历史评分excel
    exportTableHistoryData() {
      let currentList = this.tableHistory.data;
      if (!currentList.length) {
        this.$warning(this.$ct("messageInfo.4"));
        return;
      }
      this.exportHistoryLoading = true;
      let excelBody = [];
      let f = (v) => {
        return this.formatCeil(null, null, v);
      };
      currentList.forEach((item, index) => {
        let array = [
          index + 1,
          item.plateNo,
          item.deptName,
          item.date,
          f(item.totalScore),
          item.driveTime,
          item.driveMile,
          item.eventCount,
          // f(item.accScore),
          // f(item.brakeScore),
          // f(item.fcwScore),
          // f(item.hmwScore),
          // f(item.laneKeepScore),
          // f(item.pcwScore),
          // f(item.speedScore),
          // f(item.turnLightScore),
          // f(item.smokeScore),
          // f(item.distractScore),
          // f(item.lrreguarDriveScore),
          // f(item.speedNoAdasScore),
          // f(item.telScore),
          // f(item.tiredNoAdasScore),
          // f(item.tiredScore),
        ];
        this.currentHeaderList.forEach((header) => {
          array.push(f(item[header.prop]));
        });
        excelBody.push(array);
      });
      const headers = [
        "index",
        "plateNo",
        "deptName",
        "date",
        "totalScore",
        "driveTime",
        "driveMile",
        "eventCount",
      ];
      this.currentHeaderList.forEach((item) => {
        headers.push(item.prop);
      });
      let options = {
        fileName: this.$ct("reportName"),
        datas: [
          {
            sheetData: excelBody,
            sheetHeader: headers.map((item) => this.$ct(`label.${item}`)),
            columnWidths: [
              "3",
              "10",
              "10",
              "10",
              "7",
              "7",
              "7",
              "7",
              "7",
              "7",
              "7",
              "7",
              "7",
              "7",
              "7",
              "7",
              "7",
              "7",
              "7",
              "7",
              "7",
              "7",
              "7",
            ],
          },
        ],
      };
      ExportJsonExcel(options).saveExcel();
      this.exportHistoryLoading = false;

    },
  },
};
</script>

<style scoped lang="scss">
.car {
  .query-top {
    height: 100%;
    overflow: auto;
  }

  .query-bottom {
    margin-top: 5px;
    padding: 10px;

    .query-item {
      display: flex;
      height: 40px;
      line-height: 40px;
      justify-content: space-between;
      align-items: center;

      >span {
        font-size: 12px;
        white-space: nowrap;
        margin-right: 5px;
      }
    }
  }

  .sort-btn {
    margin-left: 5px;

    .pony-iconv2 {
      font-size: 12px;
    }
  }

  .score-card {
    color: #ffffff;
    border-radius: 4px;
    text-align: center;
  }

  .score-aside {
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
  }
}
</style>
<style lang="scss">
.my-el-popover {
  padding: 0;

  .top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 12px 0 12px;

    .sort-direction {
      display: flex;
      text-align: center;

      .up {
        width: 25px;
        border: 1px solid var(--border-color-base);
        border-radius: 4px 0 0 4px;
      }

      .down {
        width: 25px;
        border: 1px solid var(--border-color-base);
        margin-left: -1px;
        border-radius: 0 4px 4px 0;
      }

      .true {
        color: var(--color-primary);
        border: 1px solid var(--color-primary);
        z-index: 9999;
      }
    }

    .recover {
      color: var(--color-primary);
    }
  }

  .content {
    border-bottom: 1px solid var(--border-color-base);
    padding: 0 12px 12px 12px;

    ul {
      display: flex;
      flex-wrap: wrap;

      li {
        display: flex;
        align-items: center;
        width: 160px;
        height: 20px;
        font-size: 14px;
        margin-top: 10px;

        span {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 20px;
          height: 20px;
          border: 1px solid var(--border-color-base);
          margin-right: 5px;
          border-radius: 3px;
          color: #fff;
        }

        .select {
          background-color: var(--color-primary);
        }
      }
    }
  }

  .bottom {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding: 4px;
  }
}
</style>

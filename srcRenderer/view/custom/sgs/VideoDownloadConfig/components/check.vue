
<template>
  <PonyDialog v-model="show" :title="mode === 'add' ? '添加下载任务' : '查看下载任务'" :width="1000" :hasMask="true" @confirm="commit"
    :confirmDisabled="mode === 'modify'">
    <div style="display:flex;height:560px;width:100%;">
      <div style="height:100%;width:35%">
        <ElementTree type="vehicle" :checkMode="true" @check="selectNodes" ref="vehicleTree">
        </ElementTree>
      </div>
      <div style="height:100%;width:65%;overflow:auto;padding-right: 10px;">
        <el-form :model="modal" label-position="right" label-width="110px" :rules="rules" ref="form">
          <el-form-item label="任务名称" prop="taskName">
            <el-input v-model="modal.taskName" :disabled="mode === 'modify'"></el-input>
          </el-form-item>
          <el-form-item label="视频时长">
            <el-row>
              <el-col :span="11">
                <el-form-item label="视频周期">
                  <el-select v-model="modal.cycleType" style="width:80px"
                    :disabled="mode === 'modify' || modal.related == 2">
                    <el-option label="日" :value="1"></el-option>
                    <el-option label="周" :value="2"></el-option>
                    <el-option label="月" :value="3"></el-option>
                    <el-option label="运单" :value="0" v-if="modal.related == 2"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="11">
                <el-form-item label="颗粒度" prop="minTime">
                  <el-input v-model="modal.minTime" style="width: 60px" :disabled="mode === 'modify'"></el-input>分/段
                </el-form-item>

              </el-col>
            </el-row>
            <el-row>
              <el-col :span="11">
                <el-form-item v-show="modal.cycleType == 2" label="开始日期为每周">
                  <el-select v-model="modal.weeksTime" style="width: 80px" placeholder="请选择" @change="$forceUpdate()">
                    <el-option label="周一" :value="2"></el-option>
                    <el-option label="周二" :value="3"></el-option>
                    <el-option label="周三" :value="4"></el-option>
                    <el-option label="周四" :value="5"></el-option>
                    <el-option label="周五" :value="6"></el-option>
                    <el-option label="周六" :value="7"></el-option>
                    <el-option label="周日" :value="1"></el-option>

                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="11">

                <!-- <el-form-item label="设置录制时长" prop="durationTotal">
                                    <el-input v-model="modal.durationTotal" style="width: 60px" :disabled="mode==='modify'"></el-input>分
                                </el-form-item> -->
              </el-col>

            </el-row>
            <el-row v-show="modal.cycleType == 3">
              <el-col :span="11">
                <el-form-item label="开始日期" prop="startDateTEST">
                  <div style="display:flex">
                    <span>每月</span>
                    <el-input style="width:50px" v-model="modal.startDateTEST" :disabled="mode === 'modify'">

                    </el-input>
                    <span>日</span>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="11">
                <el-form-item label="结束日期" prop="endDateTEST">
                  <div style="display:flex">
                    <span>每月</span>
                    <el-input style="width:50px" v-model="modal.endDateTEST" disabled>

                    </el-input>
                    <span>日</span>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="11">
                <el-form-item label="周期录制时长" prop="cycleTime">
                  <el-input v-model="modal.cycleTime" style="width: 60px" :disabled="mode === 'modify'"></el-input>分
                </el-form-item>
              </el-col>
              <el-col :span="11">
                <el-form-item label="视频最小间隔" prop="minInterval">
                  <el-input v-model="modal.minInterval" style="width: 60px" :disabled="mode === 'modify'"></el-input>分
                </el-form-item>
              </el-col>

            </el-row>

          </el-form-item>
          <el-row>
            <el-col :span="13">
              <el-form-item label="最小速度">
                <el-input v-model="modal.minSpeed" style="width: 150px" :disabled="mode === 'modify'"></el-input>km/h
              </el-form-item>
            </el-col>
            <el-col :span="11">
              <el-form-item label="通道" prop="channels">
                <el-select v-model="modal.channels" multiple collapse-tags placeholder="请选择" @change="$forceUpdate()"
                  style="width: 175px">
                  <el-option v-for="(item, index) in 16" :key="index" :label="`通道${index + 1}`" :value="index + 1">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="有效周期" prop="weeks">
            <el-select v-model="modal.weeks" multiple collapse-tags style="width: 150px" placeholder="请选择"
              @change="$forceUpdate()">
              <el-option label="周一" :value="1"></el-option>
              <el-option label="周二" :value="2"></el-option>
              <el-option label="周三" :value="3"></el-option>
              <el-option label="周四" :value="4"></el-option>
              <el-option label="周五" :value="5"></el-option>
              <el-option label="周六" :value="6"></el-option>
              <el-option label="周日" :value="7"></el-option>

            </el-select>
          </el-form-item>
          <el-form-item label="有效日期">
            <el-col :span="9">
              <el-date-picker value-format="timestamp" placeholder="选择日期" v-model="modal.startDate" style="width: 100%"
                type="date" :disabled="mode === 'modify'">
              </el-date-picker>
            </el-col>
            <el-col style="text-align: center" :span="2">至</el-col>
            <el-col :span="9">
              <el-date-picker value-format="timestamp" placeholder="选择日期" v-model="modal.endDate" style="width: 100%"
                type="date" :picker-options="endPickerOptions" :disabled="mode === 'modify'">
              </el-date-picker>
            </el-col>
          </el-form-item>
          <el-form-item label="有效时间" >
            <el-checkbox v-model="billStatus" :disabled="mode === 'modify'">关联运单</el-checkbox>
            <el-checkbox v-model="diyStatus" :disabled="mode === 'modify'">自定义时间</el-checkbox>
            <el-checkbox v-model="fenceStatus" :disabled="mode === 'modify'" >关联围栏</el-checkbox>
          </el-form-item>
          <el-form-item label="" v-show="billStatus" style="margin: 10px">
            <el-radio v-model="modal.related" :label="1">赛科</el-radio>
            <el-radio v-model="modal.related" :label="2">中英天津</el-radio>

          </el-form-item>
          <el-row>
            <el-col :span="12">
              <el-form-item label="录制百分比" prop="percentage" v-show="modal.related == 2 && billStatus">
                <el-input v-model="modal.percentage" placeholder="请填写录制百分比" style="width: 150px"
                  :disabled="mode === 'modify'">
                </el-input>%
              </el-form-item>
            </el-col>
            <!-- <el-col :span="12">
              <el-form-item label="出发围栏" v-show="modal.related == 2 && billStatus && mode == 'add'">
                <SelectTreeInput v-model="fenceValue" ref="fenceInput" type="fenseTreeNew" :condition="treeFenseCondition"
                  placeholder="请选择围栏" title="请选择围栏" :checkMode='true' checkModeText='个' :extraKeys="['shape_type']"
                  :disable="mode === 'modify'" style="width: 150px" v-show="mode == 'add'">
                </SelectTreeInput>
              </el-form-item>
              <el-form-item label="出发围栏" v-show="modal.related == 2 && billStatus && mode == 'modify'">
                <el-select v-model="modal.sendFenseIdList" multiple collapse-tags style="width: 180px"
                  @change="$forceUpdate()">
                  <el-option v-for="(item, index) in sendFenseList" :key="index" :label="item.name" :value="item.id">
                    <div class="option-text">{{ item.name }}</div>
                  </el-option></el-select>
              </el-form-item>
            </el-col> -->
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="出发围栏" v-show="fenceStatus && mode == 'add'">
                <SelectTreeInput v-model="sendFenceValue" ref="fenceInput1" type="sendFenseTree"
                  :condition="treeFenseCondition" placeholder="请选择围栏" title="请选择围栏" :checkMode='true'
                  checkModeText='个' :extraKeys="['shape_type']" :disable="mode === 'modify'" style="width: 150px"
                  v-show="mode == 'add'&&fenceStatus">
                </SelectTreeInput>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="出发围栏" v-show="fenceStatus && mode == 'modify'">
                <el-select v-model="modal.sendFenseIdList" multiple collapse-tags style="width: 180px"
                  @change="$forceUpdate()">
                  <el-option v-for="(item, index) in sendFenseList" :key="index" :label="item.name" :value="item.id">
                    <div class="option-text">{{ item.name }}</div>
                  </el-option>
                </el-select>
                </el-form-item>
            </el-col>
          </el-row>
          <div class="approval_time">
            <el-row v-for="(item, index) in modal.validTime" :key="index" v-show="diyStatus">
              <el-col :span="9">
                <el-form-item>
                  <el-time-picker value-format="HH:mm:ss" placeholder="开始时间" v-model="item.start"
                    :picker-options="startTimePickerOption(item)" style="width: 100%;" :clearable="false"
                    :disabled="mode === 'modify'">
                  </el-time-picker>
                </el-form-item>
              </el-col>
              <el-col :span="2" class="dfc">至</el-col>
              <el-col :span="7">
                <el-form-item label-width="0">
                  <el-time-picker value-format="HH:mm:ss" placeholder="结束时间" v-model="item.end"
                    :picker-options="endTimePickerOption(item)" style="width: 130px;" :clearable="false"
                    :disabled="mode === 'modify'">
                  </el-time-picker>
                </el-form-item>
              </el-col>
              <el-col class="dfc" :span="4" style="height: 30px">
                <el-button type="text" @click="addPassTimeSelect(index)" :disabled="mode === 'modify'"><i
                    class="pony-iconv2 pony-xinzeng"></i></el-button>
                <el-button type="text" :disabled="modal.validTime.length == 1 || mode === 'modify'"
                  @click="removePassTimeSelect(index)">
                  <i class="pony-iconv2 pony-shanchu"></i>
                </el-button>

              </el-col>
            </el-row>
          </div>
          <el-form-item label="备注">
            <el-input type="textarea" :row="3" v-model="modal.remark" :disabled="mode === 'modify'"></el-input>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </PonyDialog>
</template>
<script>
import SelectTreeInput from '@/components/common/SelectTreeInput';

export default {
  name: 'check',
  components: { SelectTreeInput },
  data() {

    return {
      show: false,
      modal: {
        vehicleIdList: [],
        taskType: 1,
        cycleType: 1,
        cycleTime: 0,
        minInterval: 0,
        minTime: 0,
        minSpeed: 0,
        validWeekday: '',
        validChannel: [],
        startDate: moment().startOf(),
        endDate: moment().endOf(),
        validTime: [
          { start: '00:00:00', end: '23:59:59' }
        ],
        remark: '',
        channels: [],
        weeks: [],
        taskName: '',
        startDateTEST: 1,
        endDateTEST: 30,
        weeksTime: 2,
        cycleVal: 1,
        related: 0,
        sendFenseIdList: [],
        percentage: 0,
      },
      sendFenseList: [],
      fenceValue: null,
      mode: 'add',
      billStatus: false,
      diyStatus: false,
      fenceStatus: false,
      sendFenceValue: null
    }
  },
  watch: {
    'modal.startDateTEST': function (value) {
      this.modal.endDateTEST = value == 1 ? 30 : value - 1
    },
    'modal.related': function (val) {
      if (val == 2) {
        this.modal.cycleType = 0
      } else {
        this.modal.cycleType = 1
      }
    },
    'billStatus': function (val) {
      if (!val) {
        this.modal.related = 0
      }
    },
    'fenceValue': function (val) {
      if (val) {
        this.modal.sendFenseIdList = val.map(item => {
          return item.value
        })
      } 
    },
    'sendFenceValue': function (val) {
      if (val) {
        this.modal.sendFenseIdList = val.map(item => {
          return item.value
        })
      }
    }
  },
  computed: {
    startPickerOptions: function () {
      return {
        disabledDate: (date) => {
          moment(date).endOf("day") - this.modal.endDate > 0
        }
      }
    },
    endPickerOptions: function () {
      return {
        disabledDate: (date) => {
          return (
            moment(date).endOf("day") - this.modal.startDate < 0
          );
        },
      };
    },
    rules: function () {
      var validateDate = (rule, value, callback) => {
        if (value == 0) {
          callback(new Error('请输入大于0的数字'));
        } else if (value > 30) {
          callback(new Error('请输入1至30以内的数字'))
        } else {
          callback();
        }
      };
      var validatePass1 = (rule, value, callback) => {
        if (value == 0) {
          callback(new Error('请输入大于0的数字'));
        } else if (this.modal.cycleType == 1 && value > 1440) {
          callback(new Error('请输入1至1440以内的数字'))
        } else if (this.modal.cycleType == 2 && value > 10080) {
          callback(new Error('请输入1至10080以内的数字'))

        } else if (this.modal.cycleType == 3 && value > 43200) {
          callback(new Error('请输入1至43200以内的数字'))
        } else {

          callback();
        }
      };
      var validatePass2 = (rule, value, callback) => {
        if (this.modal.cycleType == 1 && value > 1440) {
          callback(new Error('请输入0至1440以内的数字'))
        } else if (this.modal.cycleType == 2 && value > 10080) {
          callback(new Error('请输入0至10080以内的数字'))

        } else if (this.modal.cycleType == 3 && value > 43200) {
          callback(new Error('请输入0至43200以内的数字'))
        }
        else {
          callback()
        }
      }
      return {
        taskName: [
          { required: true, message: '请填写任务名称', trigger: 'blur' }
        ],
        cycleTime: [
          { validator: validatePass1, trigger: 'blur' }
        ],
        minInterval: [
          { validator: validatePass2, trigger: 'blur' }
        ],
        minTime: [
          { validator: validatePass1, trigger: 'blur' }
        ],
        weeks: [
          { required: true, message: '请选择有效周期', trigger: 'change' }
        ],
        channels: [
          { required: true, message: '请选择通道', trigger: 'change' }
        ],
        startDateTEST: [{ validator: validateDate, trigger: 'blur' }],
        endDateTEST: [{ validator: validateDate, trigger: 'blur' }],
      }
    }
  },
  methods: {
    treeFenseCondition(treeNode) {
      if (treeNode.type == 2) {
        return true;
      }
    },
    selectNodes(current, { checkedNodes }) {
      let currentNodes = checkedNodes
        .filter((item) => item.type >= 4)
        .map((item) => item.id);
      this.modal.vehicleIdList = currentNodes

    },
    async showModal(row) {
      this.show = true;
      if (row) {
        this.mode = 'modify'
        this.modal.id = row.id
        this.modal.vehicleIdList = row.vehicleIdList
        this.modal.cycleType = row.cycleType;
        this.modal.cycleTime = row.cycleTime;
        this.modal.minInterval = row.minInterval;
        this.modal.minTime = row.minTime;
        this.modal.minSpeed = row.minSpeed;
        this.modal.taskName = row.taskName;
        this.modal.validChannel = row.validChannel;
        this.modal.validWeekday = row.validWeekday;
        this.modal.startDate = row.startDate;
        this.modal.endDate = row.endDate;
        this.modal.validTime = row.validTime;
        this.modal.remark = row.remark;
        this.modal.related = row.related;
        this.modal.percentage = row.percentage;
        this.modal.weeks = this.formatWeek(row.validWeekday, 1)
        this.modal.sendFenseIdList = row.sendFenseList.length ? row.sendFenseList.map(item => { return item.id }) : []
        this.sendFenseList = row.sendFenseList
        if(row.sendFenseList.length) this.fenceStatus = true
        this.modal.channels = this.formatChannel(row.validChannel, 1)
        if (row.taskType == 1 && !row.validTime.length && row.related) {
          this.billStatus = true
        } else if (row.taskType == 1 && row.validTime.length && row.related) {
          this.billStatus = true
          this.diyStatus = true
        } else {
          this.diyStatus = true
        }
        if (row.cycleType == 2) {
          this.modal.weeksTime = row.cycleVal
        }
        if (row.cycleType == 3) {
          this.modal.startDateTEST = row.cycleVal
        }
        await this.$nextTick()
        await this.$refs["vehicleTree"].waitForInit;
        this.$refs["vehicleTree"].$refs.tree.setCheckedKeys(row.vehicleIdList);
        if (row.vehicleIdList.length) {
          row.vehicleIdList.forEach((item) => {
            this.checkCurrentVehicle(item);
          });
        }
      } else {
        this.mode = 'add'
        this.modal = {
          vehicleIdList: [],
          taskType: 1,
          cycleType: 1,
          cycleTime: 0,
          minInterval: 0,
          minTime: 0,
          minSpeed: 0,
          validWeekday: '',
          validChannel: [],
          startDate: moment().startOf(),
          endDate: moment().endOf(),
          validTime: [
            { start: '00:00:00', end: '23:59:59' }
          ],
          remark: '',
          channels: [],
          weeks: [],
          taskName: '',
          startDateTEST: 1,
          endDateTEST: 30,
          weeksTime: 2,
          cycleVal: 1,
          related: 0,
          sendFenseList: [],
          percentage: 0,
          sendFenseIdList: []
        }
        this.sendFenseList = []
        this.billStatus = false
        this.diyStatus = false
        this.fenceStatus = false
        this.fenceValue = null
        this.sendFenceValue = null
      }
    },
    //展开多选树节点
    checkCurrentVehicle(Id) {
      this.$refs["vehicleTree"].$refs.tree.setCurrentKey(Id);
      this.$refs["vehicleTree"].$refs.tree.setChecked(Id, true);
      this.$refs["vehicleTree"].$refs.tree.getNode(Id).expand(null, true);
    },
    async commit() {
      if (!this.modal.vehicleIdList.length) {
        this.$warning('请选择下发任务车辆')
        return
      }
      if (!this.billStatus && !this.diyStatus && !this.fenceStatus) {
        this.$warning('请选择有效时间')
        return
      }
      if(this.fenceStatus&&!this.modal.sendFenseIdList.length){
        this.$warning('请选择出发围栏')
        return
      }
      if (!this.diyStatus && this.billStatus) {
        this.modal.validTime = []
      }
      if (this.diyStatus && !this.billStatus) {
        this.modal.related = 0
        this.modal.sendFenseIdList = []
      }

      this.$refs.form.validate(async (res) => {
        if (!res) return
        this.modal.validChannel = this.formatChannel(this.modal.channels, 0)
        this.modal.validWeekday = this.formatWeek(this.modal.weeks, 0)
        this.modal.startDate = moment(this.modal.startDate).valueOf()
        this.modal.endDate = moment(this.modal.endDate).valueOf()
        this.modal.cycleVal = this.modal.cycleType == 2 ? this.modal.weeksTime : this.modal.startDateTEST
        this.modal.related = this.billStatus ? this.modal.related : 0
        delete this.modal.weeksTime
        delete this.modal.endDateTEST
        delete this.modal.startDateTEST
        delete this.modal.channels
        delete this.modal.weeks
        if (this.mode == 'add') {
          let res = await this.$api.sgsTaskIns(this.modal)
          if (res.status !== 200) {
            this.$error(res.message || '新增失败')
            return
          } else {
            this.$success('新增成功')
            this.$emit('refresh')
            this.show = false
          }
        } else {
          let res = await this.$api.sgsTaskMod(this.modal)
          if (res.status !== 200) {
            this.$error(res.message || '修改失败')
            return
          } else {
            this.$success('修改成功')
            this.$emit('refresh')
            this.show = false
          }
        }
      })
    },
    //type 0:转字符串，1：转回数组
    formatWeek(value, type) {
      if (type) {
        let list = []
        value.split("").map((item, index) => {
          if (item == "1") {
            list.push(index + 1)
          }
        });
        return list
      } else {
        let weekStr = ''
        for (let i = 1; i <= 7; i++) {
          if (value.indexOf(i) !== -1) {
            weekStr += '1'
          } else {
            weekStr += '0'
          }
        }
        return weekStr;
      }
    },
    formatChannel(value, type) {
      if (type) {
        if (value.length == 1) {
          return [Number(value)]
        } else {
          return value.split(",").map((item, index) => {
            return Number(item)
          })
        }
      } else {
        let channelsStr = ''
        value.forEach(item => {
          channelsStr += item + ','
        })
        channelsStr = channelsStr.substring(0, channelsStr.length - 1);
        return channelsStr;

      }
    },
    addPassTimeSelect(index) {
      this.modal.validTime.splice(index + 1, 0, { start: '', end: '' })
    },
    removePassTimeSelect(index) {
      this.modal.validTime.splice(index, 1)
    },
    startTimePickerOption(item) {
      if (item.end) {
        return { selectableRange: `00:00:00 - ${item.end}` }
      } else {
        return { selectableRange: `00:00:00 - 23:59:59` }
      }
    },
    endTimePickerOption(item) {
      if (item.start) {
        return { selectableRange: `${item.start} - 23:59:59` }
      } else {
        return { selectableRange: `00:00:00 - 23:59:59` }
      }
    },
  }
}
</script>
<style lang="scss" scoped>
/deep/ .el-form-item__error {
  width: 150px;
}

/deep/ .el-select__tags-text {
  display: inline-block;
  max-width: 80px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/deep/ .el-select .el-tag__close.el-icon-close {
  top: -7px;
  right: -4px;
}

.option-text {
  display: inline-block;
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>

<template>
  <PonyDialog v-model="show" title="详情" :width="630" :hasMask="true" @confirm="commit"
    :confirmDisabled="mode === 'modify'">
    <div style="display:flex;height:560px;width:100%;">
      <el-form :model="modal" label-position="right" label-width="110px" :rules="rules" ref="form">
        <el-form-item label="任务名称">
          <el-input v-model="modal.taskName" :disabled="mode === 'modify'"></el-input>
        </el-form-item>
        <el-form-item label="视频时长">
          <el-row>
            <el-col :span="11">
              <el-form-item label="视频周期">
                <el-select v-model="modal.cycleType" style="width:80px" :disabled="mode === 'modify'">
                  <el-option label="日" :value="1"></el-option>
                  <el-option label="周" :value="2"></el-option>
                  <el-option label="月" :value="3"></el-option>
                  <el-option label="运单" :value="0"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="11">
              <el-form-item v-show="modal.cycleType == 2" label="开始日期为每周">
                <el-select v-model="modal.weeksTime" style="width: 150px" placeholder="请选择" @change="$forceUpdate()">
                  <el-option label="周一" :value="2"></el-option>
                  <el-option label="周二" :value="3"></el-option>
                  <el-option label="周三" :value="4"></el-option>
                  <el-option label="周四" :value="5"></el-option>
                  <el-option label="周五" :value="6"></el-option>
                  <el-option label="周六" :value="7"></el-option>
                  <el-option label="周日" :value="1"></el-option>

                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row v-show="modal.cycleType == 3">
            <el-col :span="11">
              <el-form-item label="开始日期" prop="startDateTEST">
                <div style="display:flex">
                  <span>每月</span>
                  <el-input style="width:50px" v-model="modal.startDateTEST" :disabled="mode === 'modify'">

                  </el-input>
                  <span>日</span>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="11">
              <el-form-item label="结束日期" prop="endDateTEST">
                <div style="display:flex">
                  <span>每月</span>
                  <el-input style="width:50px" v-model="modal.endDateTEST" disabled>

                  </el-input>
                  <span>日</span>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="11">
              <el-form-item label="周期录制时长" prop="cycleTime">
                <el-input v-model="modal.cycleTime" style="width: 60px" :disabled="mode === 'modify'"></el-input>分
              </el-form-item>
            </el-col>
            <el-col :span="11">
              <el-form-item label="视频最小间隔" prop="minInterval">
                <el-input v-model="modal.minInterval" style="width: 60px" :disabled="mode === 'modify'"></el-input>分
              </el-form-item>
            </el-col>

          </el-row>
          <el-row>
            <el-col :span="11">
              <el-form-item label="颗粒度" prop="minTime">
                <el-input v-model="modal.minTime" style="width: 60px" :disabled="mode === 'modify'"></el-input>分/段
              </el-form-item>
            </el-col>
            <!-- <el-col :span="11">

                            <el-form-item label="设置录制时长" prop="durationTotal">
                                <el-input v-model="modal.durationTotal" style="width: 60px" :disabled="mode==='modify'"></el-input>分
                            </el-form-item>
                        </el-col> -->

          </el-row>
        </el-form-item>
        <el-row>
          <el-col :span="13">
            <el-form-item label="最小速度">
              <el-input v-model="modal.minSpeed" style="width: 150px" :disabled="mode === 'modify'"></el-input>km/h
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label="通道" prop="channels">
              <el-select v-model="modal.channels" multiple collapse-tags placeholder="请选择" @change="$forceUpdate()"
                style="width: 175px">
                <el-option v-for="(item, index) in 16" :key="index" :label="`通道${index + 1}`" :value="index + 1">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="有效周期" prop="weeks">
          <el-select v-model="modal.weeks" multiple collapse-tags style="width: 150px" placeholder="请选择"
            @change="$forceUpdate()">
            <el-option label="周一" :value="1"></el-option>
            <el-option label="周二" :value="2"></el-option>
            <el-option label="周三" :value="3"></el-option>
            <el-option label="周四" :value="4"></el-option>
            <el-option label="周五" :value="5"></el-option>
            <el-option label="周六" :value="6"></el-option>
            <el-option label="周日" :value="7"></el-option>

          </el-select>
        </el-form-item>
        <el-form-item label="有效日期">

          <el-col :span="9">
            <el-date-picker value-format="timestamp" placeholder="选择日期" v-model="modal.startDate" style="width: 100%"
              type="date" :disabled="mode === 'modify'">
            </el-date-picker>
          </el-col>
          <el-col style="text-align: center" :span="2">至</el-col>
          <el-col :span="9">
            <el-date-picker value-format="timestamp" placeholder="选择日期" v-model="modal.endDate" style="width: 100%"
              type="date" :picker-options="endPickerOptions" :disabled="mode === 'modify'">
            </el-date-picker>
          </el-col>
        </el-form-item>

        <el-form-item label="有效时间" style="margin: 0">
          <div style="display:flex;margin: 0 0 7px 0;">
            <el-checkbox v-model="billStatus" :disabled="mode === 'modify'">关联运单</el-checkbox>
            <el-checkbox v-model="billTime" :disabled="mode === 'modify'">自定义时间</el-checkbox>
            <el-checkbox v-model="fenceStatus" :disabled="mode === 'modify'" v-if="hasPermission('sgs:videoDownloadConfig:fence')">关联围栏</el-checkbox>
          </div>

        </el-form-item>
        <el-form-item label="" v-show="billStatus" style="margin: 10px">
          <el-radio v-model="modal.related" :label="1">赛科</el-radio>
          <el-radio v-model="modal.related" :label="2">中海壳牌</el-radio>
        </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item label="录制百分比" prop="percentage" v-show="modal.related == 2 && billStatus">
              <el-input v-model="modal.percentage" placeholder="请填写录制百分比" style="width: 150px"
                :disabled="mode === 'modify'">
              </el-input>%
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="出发围栏" v-show="modal.related == 2 && billStatus && mode == 'modify'">
              <el-select v-model="modal.sendFenseIdList" multiple collapse-tags style="width: 180px"
                @change="$forceUpdate()">
                <el-option v-for="(item, index) in sendFenseList" :key="index" :label="item.name" :value="item.id">
                  <div class="option-text">{{ item.name }}</div>
                </el-option></el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="出发围栏" v-show="fenceStatus">
            <el-select v-model="modal.sendFenseIdList" multiple collapse-tags style="width: 180px"
                @change="$forceUpdate()">
                <el-option v-for="(item, index) in sendFenseList" :key="index" :label="item.name" :value="item.id">
                  <div class="option-text">{{ item.name }}</div>
                </el-option></el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <div class="approval_time" v-show="billTime">
          <el-row v-for="(item, index) in modal.validTime" :key="index">
            <el-col :span="10">
              <el-form-item>
                <el-time-picker value-format="HH:mm:ss" placeholder="开始时间" v-model="item.start"
                  :picker-options="startTimePickerOption(item)" style="width: 100%;" :clearable="false"
                  :disabled="mode === 'modify'">
                </el-time-picker>
              </el-form-item>
            </el-col>
            <el-col :span="2" class="dfc">至</el-col>
            <el-col :span="7">
              <el-form-item label-width="0">
                <el-time-picker value-format="HH:mm:ss" placeholder="结束时间" v-model="item.end"
                  :picker-options="endTimePickerOption(item)" style="width: 130px" :clearable="false"
                  :disabled="mode === 'modify'">
                </el-time-picker>
              </el-form-item>
            </el-col>

          </el-row>
        </div>
        <el-form-item label="备注">
          <el-input type="textarea" :row="3" v-model="modal.remark" :disabled="mode === 'modify'"></el-input>
        </el-form-item>
      </el-form>
    </div>
    <template slot="footer">
      <el-button type="primary" @click="show = false">取消
      </el-button>
    </template>
  </PonyDialog>
</template>
<script>
export default {
  name: 'check',
  data() {

    return {
      show: false,
      modal: {
        vehicleIdList: [],
        taskType: 1,
        cycleType: 1,
        cycleTime: 0,
        minInterval: 0,
        minTime: 0,
        minSpeed: 0,
        validWeekday: '',
        validChannel: [],
        startDate: moment().startOf(),
        endDate: moment().endOf(),
        validTime: [
          { start: '00:00:00', end: '23:59:59' }
        ],
        remark: '',
        channels: [],
        weeks: [],
        taskName: '',
        // durationTotal: 0,
        startDateTEST: 1,
        endDateTEST: 30,
        weeksTime: 2,
        cycleVal: 1,
        related: 0,
        sendFenseIdList: [],
        percentage: 0
      },
      mode: 'add',
      billTime: false,
      billStatus: false,
      fenceStatus: false,
      sendFenseList: [],

    }
  },
  watch: {
    'modal.startDateTEST': function (value) {

      this.modal.endDateTEST = value == 1 ? 30 : value - 1
    }
  },
  computed: {
    startPickerOptions: function () {
      return {
        disabledDate: (date) => {
          moment(date).endOf("day") - this.modal.endDate > 0
        }
      }

    },
    endPickerOptions: function () {
      return {
        disabledDate: (date) => {
          return (
            moment(date).endOf("day") - this.modal.startDate < 0
          );
        },
      };
    },
    rules: function () {
      var validateDate = (rule, value, callback) => {
        if (value == 0) {
          callback(new Error('请输入大于0的数字'));
        } else if (value > 30) {
          callback(new Error('请输入1至30以内的数字'))
        } else {
          callback();
        }
      };
      var validatePass1 = (rule, value, callback) => {
        if (value == 0) {
          callback(new Error('请输入大于0的数字'));
        } else if (this.modal.cycleType == 1 && value > 1440) {
          callback(new Error('请输入1至1440以内的数字'))
        } else if (this.modal.cycleType == 2 && value > 10080) {
          callback(new Error('请输入1至10080以内的数字'))

        } else if (this.modal.cycleType == 3 && value > 43200) {
          callback(new Error('请输入1至43200以内的数字'))
        } else {

          callback();
        }
      };
      var validatePass2 = (rule, value, callback) => {
        if (value == 0) {
          callback(new Error('请输入大于0的数字'));
        } else if (Number(this.modal.cycleTime) > Number(value)) {
          callback(new Error('不能低于周期录制时长'));

        }
        else {
          callback()
        }
      }
      return {
        cycleTime: [
          { validator: validatePass1, trigger: 'blur' }
        ],
        minInterval: [
          { validator: validatePass1, trigger: 'blur' }
        ],
        minTime: [
          { validator: validatePass1, trigger: 'blur' }
        ],
        weeks: [
          { required: true, message: '请选择有效周期', trigger: 'change' }
        ],
        channels: [
          { required: true, message: '请选择通道', trigger: 'change' }
        ],
        // durationTotal: [{ validator: validatePass2, trigger: 'blur' }],
        startDateTEST: [{ validator: validateDate, trigger: 'blur' }],
        endDateTEST: [{ validator: validateDate, trigger: 'blur' }],

      }
    }
  },
  methods: {
    selectNodes(current, { checkedNodes }) {
      let currentNodes = checkedNodes
        .filter((item) => item.type >= 4)
        .map((item) => item.id);
      this.modal.vehicleIdList = currentNodes

    },
    async showModal(row) {
      this.show = true;
      if (row) {
        this.mode = 'modify'
        this.modal.id = row.id
        this.modal.cycleType = row.cycleType;
        this.modal.cycleTime = row.cycleTime;
        this.modal.minInterval = row.minInterval;
        this.modal.minTime = row.minTime;
        this.modal.minSpeed = row.minSpeed;
        this.modal.taskName = row.taskName;
        this.modal.validChannel = row.validChannel;
        this.modal.validWeekday = row.validWeekday;
        this.modal.startDate = row.startDate;
        this.modal.endDate = row.endDate;
        this.modal.validTime = row.validTime;
        this.modal.remark = row.remark;
        this.modal.percentage = row.percentage;
        // this.modal.durationTotal = row.durationTotal / 60;
        this.modal.weeks = this.formatWeek(row.validWeekday, 1)
        this.modal.channels = this.formatChannel(row.validChannel, 1)
        this.modal.related = row.related
        this.modal.sendFenseIdList = row.sendFenseList ? row.sendFenseList.map(item => { return item.id }) : []
        this.sendFenseList = row.sendFenseList
        if(row.sendFenseList.length){
          this.fenceStatus = true
        }else{
          this.fenceStatus = false
        }
        if (row.cycleType == 2) {
          this.modal.weeksTime = row.cycleVal
        }
        if (row.cycleType == 3) {
          this.modal.startDateTEST = row.cycleVal
        }
        if (row.taskType == 1 && !row.validTime.length && row.related) {
          this.billStatus = true
        } else if (row.taskType == 1 && row.validTime.length && row.related) {
          this.billStatus = true
          this.billTime = true
        } else {
          this.billTime = true
        }

      }
    },
    async commit() {

    },
    //type 0:转字符串，1：转回数组
    formatWeek(value, type) {
      if (type) {
        let list = []
        value.split("").map((item, index) => {
          if (item == "1") {
            list.push(index + 1)
          }
        });
        return list
      } else {
        let weekStr = ''
        for (let i = 1; i <= 7; i++) {
          if (value.indexOf(i) !== -1) {
            weekStr += '1'
          } else {
            weekStr += '0'
          }
        }
        return weekStr;
      }
    },
    formatChannel(value, type) {
      if (type) {
        if (value.length == 1) {
          return [Number(value)]
        } else {
          return value.split(",").map((item, index) => {
            return Number(item)
          })
        }
      } else {
        let channelsStr = ''
        value.forEach(item => {
          channelsStr += item + ','
        })
        channelsStr = channelsStr.substring(0, channelsStr.length - 1);
        return channelsStr;

      }
    },
    addPassTimeSelect(index) {
      this.modal.validTime.splice(index + 1, 0, { start: '', end: '' })
    },
    removePassTimeSelect(index) {
      this.modal.validTime.splice(index, 1)
    },
    startTimePickerOption(item) {
      if (item.end) {
        return { selectableRange: `00:00:00 - ${item.end}` }
      } else {
        return { selectableRange: `00:00:00 - 23:59:59` }
      }
    },
    endTimePickerOption(item) {
      if (item.start) {
        return { selectableRange: `${item.start} - 23:59:59` }
      } else {
        return { selectableRange: `00:00:00 - 23:59:59` }
      }
    },
  }
}
</script>
<style lang="scss" scoped>
/deep/ .el-form-item__error {
  width: 150px;
}
</style>

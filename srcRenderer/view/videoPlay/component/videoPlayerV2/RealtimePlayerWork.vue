<template>
    <div
      class="realtime-player pony-player"
      v-context-menu="contextMenuOptions"
      @mousemove="controlShow = true"
      @mouseleave="controlShow = false"
    >
      <div class="pony-player__video-wrap wrap">
          <div
          class="video-title"
          v-show="videoTitle && sourceParams"
          :style="{ opacity: labelAlwaysShow || controlShow ? 1 : 0 }"
        >
          {{ videoTitle }}
        </div>
        <FlvVideo
          ref="player"
          :showDecodeInfo="showDecodeInfo"
          @decodeLog="log"
          @ready="_onPlayerMounted"
          @speedChange="_speedChange"
          @play="() => (this.isPlaying = true)"
          @pause="() => (this.isPlaying = false)"
          @ended="() => (this.isPlaying = false)"
        >
        </FlvVideo>
        <!-- <HlsVideo ref="player"
                        :showDecodeInfo="showDecodeInfo"
                        @decodeLog="log"
                        @ready="_onPlayerMounted"
                        @speedChange="_speedChange"
                        @play="()=>(this.isPlaying = true,this.sourceLoading = false,this.loaded = true)"
                        @pause="()=>this.isPlaying = false"
                        @ended="()=>this.isPlaying = false"
              >
              </HlsVideo> -->
      </div>
      <div class="pony-player__main-wrap wrap">
        <!--控制条-->
        <div class="pony-player__controller" v-if="loaded && player && player.videoElm" :style="{ opacity: controlShow ? 1 : 0 }">
          <!-- <span style="cursor: pointer;" @click="changeLiveStream">
                     {{ streamType?'子码流':'主码流' }}
                  </span> -->
          <i class="pony-iconv2 pony-shuaxin" v-if="loaded" @click="reload"></i>
          <i class="pony-iconv2 pony-tingzhi" v-if="!player.videoElm.paused" @click="stop"></i>
          <div class="flex-grow"></div>
          <span class="speed">{{ currentSpeed }}KB/s</span>
          <!-- <TalkBack :terminalId="terminalCode" :chn="chn" :inline="true" ref="talkBack" expandBackground="rgba(255,255,255,0.05)"
                  >
                      <i slot="icon" slot-scope="{allowClick,title}"
                         :class="['pony-iconv2 pony-yuyin',{'is-disabled':!allowClick }]" style="font-size: 16px"
                         :title="`${$ct('talkBack')}：` + title"></i>
                  </TalkBack> -->
  
          <i
            :class="[
              'pony-iconv2 ',
              { 'is-disabled': !loaded || !hasAudio },
              player.videoElm.muted ? 'pony-jingyin' : 'pony-shengyin',
            ]"
            @click="toggleSound"
            :title="volumeHint"
          ></i>
          <!-- <i class="pony-iconv2 pony-ziti" :title="$ct('tts')" @click="ttsSend"></i> -->
          <i :class="['pony-iconv2 pony-paizhao', { 'is-disabled': !loaded }]" :title="$ct('screenShot')" @click="screenshot()"></i>
          <i
            class="pony-iconv2"
            :class="[isFullScreen ? 'pony-suoxiao' : 'pony-quanping']"
            @click="toggleFullScreen"
            v-if="enableFullScreen"
            :title="(isFullScreen ? $ct('exit') : '') + $ct('fullscreen')"
          ></i>
        </div>
        <div class="pony-player__mask wrap" v-else>
          <i
            class="pony-iconv2 pony-sanjiaoxing"
            style="cursor: not-allowed; font-size: 40px"
            :title="$ct('notSupportVideo')"
            v-if="disabled"
          ></i>
          <i v-else-if="loading" class="el-icon-loading" style="font-size: 40px"></i>
          <i v-else-if="errorStatus" class="real-video" :title="label">{{ title }}</i>
          <i
            v-else
            :class="['pony-iconv2', firstLoad ? 'pony-sanjiaoxing' : 'pony-shuaxin']"
            style="font-size: 40px"
            :title="$ct('play')"
            @click="loadSource"
          ></i>
        </div>
      </div>
      <div class="pony-player__tip-wrap wrap">
        <!-- <div class="video-title" v-show="label && sourceParams" :style="{opacity: (labelAlwaysShow || controlShow)?1:0}">
              
              </div> -->
      </div>
      <div class="pony-player__log-wrap" v-if="showLog">
        <i class="pony-iconv2 pony-guanbi pointer" @click="showLog = false"></i>
        <div>
          <p v-for="row in logArr" :key="row">{{ row }}</p>
        </div>
      </div>
    </div>
</template>
  
  <script>
  import FlvVideo from "@/view/videoPlay/component/videoPlayerV2/component/FlvVideo";
  import HlsVideo from "@/view/videoPlay/component/videoPlayerV2/component/HlsVideo";
  
  // import TalkBack from "@/view/videoPlay/component/TalkBack/TalkBack";
  
  import strictEnd from "@/view/videoPlay/component/videoPlayerV2/mxin/strictEnd";
  import fullscreen from "@/view/videoPlay/component/videoPlayerV2/mxin/fullscreen";
  import log from "@/view/videoPlay/component/videoPlayerV2/mxin/log";
  import BstandardUtil from "@/util/bstandard";
  import { mapState } from "vuex";
  
  const API_URL = window.PONY.media.apiNewUrl;
//   const API_URL = "https://jh.ponytech.cn/api/gb28181";
  
  /**
   * @Author: yezy
   * @Email: <EMAIL>
   * @Date: 2020/7/22 16:15
   * @LastEditors: yezy
   * @LastEditTime: 2020/7/22 16:15
   * @Description:
   */
  export default {
    name: "realtimePlayer",
    _i18Name: "videoPlayerPony",
    mixins: [strictEnd, fullscreen, log],
    components: { HlsVideo, FlvVideo },
    props: {
      // vehicleId: {
      //     type: [String, Number],
      // },
      terminalCode: {
        type: String,
      },
      videoTitle:{
          type:String,
          default:''
      },
      // chnN: {
      //     type: [Number,Object],
      //     default: 0,
      // },
      symbol: {
        type: String,
      },
      label: {
        type: String,
      },
      autoPlay: {
        type: Boolean,
        default: false,
      },
      labelAlwaysShow: {
        type: Boolean,
        default: false,
      },
      showClose: {
        type: Boolean,
        default: false,
      },
      enableFullScreen: {
        type: Boolean,
        default: false,
      },
      disabled: {
        type: Boolean,
        default: false,
      },
    },
    data() {
      return {
        firstLoad: true, //该参数是否第一次加载，决定图标是三角按钮还是刷新按钮
        loaded: false, //当前是否有请求的加载的流
        //相关状态，不一定与内部video标签一致
        isPlaying: false,
        currentSpeed: 0,
        chn: this.chnN,
        player: null, //指向 $refs['player']
        sourceLoading: false,
        stopLoading: false,
        controlShow: false,
        loadAjax: null,
        url: "",
        id:'',
        hasAudio: false,
        //上一次的参数
        lastParams: {
          terminalCode: undefined,
          chn: undefined,
        },
  
        showLog: false,
        showDecodeInfo: false,
        contextMenuOptions: [
          {
            label: "显示日志",
            handler: () => {
              this.showLog = !this.showLog;
            },
          },
          {
            label: "显示解码信息",
            handler: () => {
              this.showDecodeInfo = !this.showDecodeInfo;
            },
          },
        ],
        controlTimeout: null,
        //打断加载行为
        interruptLoadSourceHandle: null,
        title: "点击播放实时视频",
        errorStatus: false, //播放失败状态
  
        // streamType: 1       // 0 主码流 1 子码流
      };
    },
    computed: {
      ...mapState("auth", ["token"]),
  
      sourceParams: function () {
        if (this.terminalCode !== undefined) {
          return this.terminalCode;
        } else {
          return undefined;
        }
      },
      loading: function () {
        return this.sourceLoading || this.stopLoading;
      },
      volumeHint: function () {
        if (this.loaded && !this.hasAudio) {
          return this.$ct("notSupportAudio");
        } else {
          return "";
        }
      },
    },
    watch: {
      showLog: function (val) {
        this.contextMenuOptions[0].label = `${val ? "隐藏" : "显示"}日志`;
      },
  
      showDecodeInfo: function (val) {
        this.contextMenuOptions[1].label = `${val ? "隐藏" : "显示"}解码信息`;
      },
      sourceParams: {
        handler: function (val, oldV) {
          if (oldV) {
            this.lastParams.terminalCode = oldV;
          }
          this.firstLoad = true;
  
          val && this.autoPlay && this._loadSource();
        },
        // immediate: true,//用onPlayerMounted替代
      },
      controlShow: function (val, oldV) {
        if (!oldV && val) {
          clearTimeout(this.controlTimeout);
          this.controlTimeout = setTimeout(() => {
            this.controlShow = false;
          }, 3000); //3s后自动隐藏
        }
      },
      disabled: function (val, oldV) {
        if (!oldV && val) {
          this.stop();
        }
      },
    },
    methods: {
      changeLiveStream() {
        // this.streamType = this.streamType?0:1
        this.reload();
      },
      async _speedChange(speed) {
        this.currentSpeed = speed;
      },
      async _loadSource() {
        await this._freeSource();
        await this._requestSource();
      },
      /*
          申请占用此设备通道，
          目前实时视频的推流在不同的客户端会复用,
          这是一个非常耗时的流程，需要实现其中任意阶段可暂停
          */
      timeout(delay) {
        return new Promise((resolve) => {
          setTimeout(resolve, delay, null);
        });
      },
      async _requestSource() {
        // console.log(this.chn);
        this.sourceLoading = true;
        try {
          this.log("正在请求视频资源...");
  
          let ajax = $.ajax({
            url: API_URL,
            type: "post",
            data: JSON.stringify({
              cmd: "live",
              channel_id: this.terminalCode,
            }),
            headers: {
              Authorization: "token " + this.token,
            },
          });
          this.loadAjax = ajax;
          let result = null;
          let res = await Promise.race([ajax, this.timeout(8000)]).then((value) => {
            if (value == null) {
              this.errorStatus = true
              this.title= "获取视频地址失败,超时"
              // this.$error("获取视频地址失败,超时");
              this.sourceLoading = false;
  
              return false;
            } else {
              result = value;
              this.errorStatus = false
              return true;
            }
          });
          if (!res) return;
  
          this.loadAjax = null;
          if (typeof result !== "object") {
            result = JSON.parse(result);
          }
          if (result.code == 0) {
            this.log("成功获取视频地址，开始缓冲...");
            this.url = result.data;
            this.id = result.id;

            this.hasAudio = result.hasAudio;
            this.firstLoad = false;
            this.errorStatus = false;
  
            await Promise.race([
              //允许中断操作
              new Promise((resolve, reject) => {
                this.interruptLoadSourceHandle = { resolve, reject };
              }),
  
              Promise.all([
                // (async () => {
                //     try {
                //         //向ws订阅流的相关信息 这一步骤不是必要的
                //         //有可能会得不到回复
                //         await this._subscribeVideoInfo(this.url);
                //     } catch (e) {
                //         //报错的话 就忽略这次 订阅推流结束事件的行为（忽略严格模式）
                //         this.videoStream._resolve()
                //     }
                // })(),
                //加载视频
                this.$refs["player"].load(this.url, this.hasAudio),
              ]),
            ]);
            this.interruptLoadSourceHandle.resolve();
            this.interruptLoadSourceHandle = null;
            this.loaded = true;
            // this.$emit('playing', this.loaded)
            this.log("开始播放视频.");
          } else {
            throw { from: "api", msg: result.message };
          }
        } catch (e) {
          this.sourceLoading = false;
          this.errorStatus = true;
          switch (true) {
            case e.statusText === "abort":
              break;
            case e.from === "flvjs" && e.error === "NetworkError":
              // this.$error('视频地址已失效.')
              this.title = "视频地址已失效.";
              this.log(`请求视频资源失败,原因：视频地址已失效.`);
              break;
            case e.from === "api":
              this.title = `${this.label}: ${e.msg}`;
              // this.$error(this.$ct("messageInfo.2") + `: ${e.msg}`);
              this.log(`请求视频资源失败,原因：${e.msg}`);
              break;
            case e.from === "interrupt":
              break;
            case e.data.type == "networkError":
              this.title='视频地址已失效.'
              // this.$error("视频地址已失效.");
              this.log(`请求视频资源失败,原因：视频地址已失效.`);
              break;
            default:
              // this.$error("加载失败");
              this.title="加载失败"
              // console.log(e);
          }
          //报错的话 就忽略这次 订阅推流结束事件的行为（忽略严格模式）
          this.videoStream._resolve instanceof Function && this.videoStream._resolve();
        } finally {
          this.sourceLoading = false;
        }
      },
      /*释放设备通道资源
       * strict= true 时采取严格模式，即明确得到设备断开提示才会结束*/
      async _freeSource(strict = true) {
        try {
          this.stopLoading = true;
          this.loadAjax && this.loadAjax.abort();
          if (this.interruptLoadSourceHandle) {
            this.interruptLoadSourceHandle.reject({ from: "interrupt", msg: "人工打断" });
            this.interruptLoadSourceHandle = null;
          }
          if (this.loaded) {
            this.loaded = false;
            this.player.unload();
            this.log("正在释放请求的资源...");
            // await $.post(API_URL, JSON.stringify({
            //     cmd: "stopLive",
            //     id:id
            // })).catch((e)=>{console.log(e);});
            let stopAjax = $.ajax({
              url: API_URL,
              type: "post",
              data: JSON.stringify({
                cmd: "stopLive",
                id: this.id,
              }),
              headers: {
                Authorization: "token " + this.token,
              },
            });
            // let result = null
            let res = await Promise.race([stopAjax, this.timeout(8000)]).then((value) => {
              if (value == null) {
                this.$error("停止视频失败,超时");
                return false;
              } else {
                return true;
              }
            });
            if (!res) return;
  
            this.url = "";
            this.id = ''
            this.hasAudio = false;
            this.lastParams = {
              terminalCode: undefined,
            };
            if (strict) {
              this.log("严格模式：等待设备停止推流...");
              let timeStart = Date.now();
              await Promise.race([
                new Promise(async (resolve) => {
                  await this.videoStream.waitForEnd;
                  this.log(`设备已断开,耗时${Date.now() - timeStart}ms.`);
                  resolve();
                }),
                new Promise(async (resolve) => {
                  await this.$utils.sleep(500);
                  if (this.videoStream.waitForEnd) {
                    this.log(`等待断开时间超过500ms,已忽略`);
                    this.videoStream._reject("忽略等待");
                  }
                  resolve();
                }),
              ]);
            }
          }
          // this.$refs['talkBack']?.clearWs();
        } catch (e) {
          throw e;
        } finally {
          this.stopLoading = false;
        }
      },
      //确保首次执行时$refs['player']已存在
      _onPlayerMounted() {
        this.player = this.$refs["player"];
        this.sourceParams && this.autoPlay && this._loadSource();
        this.log("播放器初始化完成");
      },
  
      loadSource() {
        if (this.sourceParams) {
          this._loadSource();
        }
      },
      reload() {
        //   console.log(this.chnN);
        //   this.chn=this.chnN
        return this._loadSource();
      },
      stop() {
        this.isPlaying = false;
        return this._freeSource();
      },
      toggleSound() {
        if (!this.loaded || !this.hasAudio) {
          return;
        }
        this.player.toggleMuted();
      },
      screenshot() {
        if (this.loaded) {
          this.player.screenShot(this.symbol);
        }
      },
      async ttsSend() {
        let { value } = await this.$prompt(this.$ct("ttsTip"), this.$ct("tts"));
        await BstandardUtil.init();
        let bits = Array.from({ length: 6 }).fill(0);
        bits[0] = 0;
        bits[2] = 1;
        bits[3] = 1;
        bits[4] = 0;
        bits[5] = 0;
        bits = bits.reverse();
        let res = await this.$api.sendMiniStandardCommon({
          vehicle_terminal_list: [
            {
              vehicle_id: this.vehicleId,
              terminal_no: this.terminalCode,
            },
          ],
          operate_key: "SendSMS",
          cmd_sms: {
            type: parseInt(bits.join(""), 2),
            text: value,
          },
        });
        let wsRes = await BstandardUtil.waitForResponse(res.data);
        if (wsRes && wsRes.task_state === 0) {
          this.$success(this.$ct("messageInfo.1"));
          return true;
        } else {
          this.$message(this.$ct("messageInfo.0"));
          return false;
        }
        await BstandardUtil.destroy();
      },
    },
    created() {
      window.addEventListener("beforeunload", this.stop);
    },
    beforeDestroy() {
      window.removeEventListener("beforeunload", this.stop);
      this._freeSource(false);
      clearTimeout(this.controlTimeout);
    },
    deactivated() {
      let isPlaying = !this.player.videoElm.paused;
      if (isPlaying) {
        this.log("切换至后台，断开设备链接");
        this._freeSource(false);
        this.$once("hook:activated", () => {
          this._loadSource();
        });
      }
    },
  };
  </script>
  
  <style lang="scss">
  @import "player";
  </style>
  <style scoped lang="scss">
  .realtime-player {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    flex-grow: 1;
    .real-video {
      font-size: 13px;
      font-weight: bold;
      font-style: normal;
    }
  }
  </style>
  
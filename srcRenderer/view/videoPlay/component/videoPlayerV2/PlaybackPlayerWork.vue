<template>
    <div class="playback-player pony-player"
         v-context-menu="contextMenuOptions"
         @mousemove="controlShow = true"
         @mouseleave="controlShow = false"
    >
        <div class="pony-player__video-wrap wrap ">
            <FlvVideo ref="player"
                      :showDecodeInfo="showDecodeInfo"
                      @decodeLog="log"
                      @ready="_onPlayerMounted"
                      @speedChange="_speedChange"
                      @play="()=>this.isPlaying = true"
                      @ended="_onEnded"
                      @canplay="_onCanPlay"
                      @timeupdate="_onTimeUpdate"
                      @progress="_setBufferProgress"
            >
            </FlvVideo>
            <!-- <HlsVideo ref="player"
                      :showDecodeInfo="showDecodeInfo"
                      @decodeLog="log"
                      @ready="_onPlayerMounted"
                      @speedChange="_speedChange"
                      @play="()=>this.isPlaying = true"
                      @ended="_onEnded"
                      @canplay="_onCanPlay"
                      @timeupdate="_onTimeUpdate"
                      @progress="_setBufferProgress"
            >
            </HlsVideo> -->
        </div>
    
        <div class="pony-player__main-wrap wrap">
            <!--控制条-->
            <div class="pony-player__controller" v-if="loaded && player && player.videoElm" :style="{opacity:controlShow?1:0}">
                <i class="pony-iconv2 pony-sanjiaoxing" :title="$ct('play')" v-if="player.videoElm.paused" @click="player.play"></i>
                <i class="pony-iconv2 pony-zanting" :title="$ct('pause')" v-else @click="player.pause"></i>
                <i class="pony-iconv2 pony-tingzhi" :title="$ct('stop')" @click="stop()"></i>
                <CustomSlider :percentage="playBack.progress" :subPercentage="playBack.bufferedProgress"
                              :disabled="loaded" :format-tooltip="formatTip" :offset="playBack.offset"
                              @change="setProgress" class="progress" rangeLimit>
                </CustomSlider>
                <span style="width: 65px;text-align: right">{{currentSpeed}}KB/s</span>
                <span class="time-tip">
                    {{formatTime(playBack.currentTime) + ' / ' + formatTime(playBack.totalTime)}}
                </span>
                <div :class="['collapse-item',{'is-disabled':!loaded}]">
                    <span>X{{playBack.speedValue[playBack.speed]}}</span>
                    <!-- playBack.speedValue.slice(1) -->
                    <ul class="pane">
                        <li v-for="(item,index) in playBack.speedValue.slice(1)"
                         :class="['text text--link',{'text--brand':playBack.speed === index+1}]" @click="fastForward(index+1)">
                            X{{item}}
                        </li>
                    </ul>
                </div>
                <i :class="['pony-iconv2 pony-paizhao',{'is-disabled':!loaded }]" :title="$ct('screenShot')"
                   @click="screenshot()"></i>
                <i :class="['pony-iconv2 ',{'is-disabled':!loaded || !hasAudio},player.videoElm.muted?'pony-jingyin':'pony-shengyin']"
                   @click="toggleSound" :title="volumeHint"></i>
                <i class="pony-iconv2" :class="[isFullScreen?'pony-lakuangsuoxiao':'pony-fangda1']" @click="toggleFullScreen"
                   v-if="enableFullScreen"
                   :title="(isFullScreen?$ct('exit'):'') + $ct('fullscreen')"></i>
            </div>
            <div class="pony-player__mask wrap" v-else>
                <i class="pony-iconv2 pony-sanjiaoxing" style="cursor: not-allowed;font-size:40px" :title="$ct('notSupportVideo')" v-if="disabled"></i>
                <i v-else-if="loading" class="el-icon-loading" ></i>
                <i v-else :class="['pony-iconv2',firstLoad?'pony-sanjiaoxing':'pony-shuaxin']" style="font-size:40px" :title="$ct('play')"
                   @click="loadSource()"></i>
            </div>
        </div>
        <div class="pony-player__tip-wrap wrap">
            <!-- <div class="video-title" v-show="label && sourceParams" :style="{opacity: (labelAlwaysShow || controlShow)?1:0}">
                {{label}}
            </div> -->
        </div>
        <div class="pony-player__log-wrap" v-if="showLog">
            <i class="pony-iconv2 pony-guanbi pointer" @click="showLog = false"></i>
            <div>
                <p v-for="row in logArr" :key="row">{{row}}</p>
            </div>
        </div>
    </div>
</template>

<script>
    import strictEnd from "@/view/videoPlay/component/videoPlayerV2/mxin/strictEnd";
    import fullscreen from "@/view/videoPlay/component/videoPlayerV2/mxin/fullscreen";
    import log from "@/view/videoPlay/component/videoPlayerV2/mxin/log";
    import CustomSlider from "@/components/common/CustomSlider";
    import HlsVideo from "@/view/videoPlay/component/videoPlayerV2/component/HlsVideo";
    import FlvVideo from "@/view/videoPlay/component/videoPlayerV2/component/FlvVideo";
    import {mapState} from 'vuex'

    const API_URL = window.PONY.media.apiNewUrlBack;
    // const API_URL = "http:*************:12410/api/gb28181"

    
    /**
     * @Author: yezy
     * @Email: <EMAIL>
     * @Date: 2020/7/22 16:14
     * @LastEditors: yezy
     * @LastEditTime: 2020/7/22 16:14
     * @Description:
     */
    export default {
        name: "playbackPlayer",
        _i18Name: 'videoPlayerPony',
        mixins: [strictEnd, fullscreen, log],
        components: {
            HlsVideo,
            CustomSlider,
            FlvVideo
        },
        props: {
            
            terminalCode: {type: String,},
           
            startTime: {type: String}, // YYYYMMDDHHmmss 格式
            endTime: {type: String},// YYYYMMDDHHmmss 格式

            // label: {type: String,},
            autoPlay: {type: Boolean, default: false,},
            labelAlwaysShow: {type: Boolean, default: false,},
            showClose: {type: Boolean, default: false,},
            enableFullScreen: {type: Boolean, default: false,},
            disabled: {type: Boolean, default: false,}
        },
        data() {
            return {
                firstLoad: true,//该参数是否第一次加载，决定图标是三角按钮还是刷新按钮
                loaded: false,//当前是否有请求的加载的流
                //相关状态，不一定与内部video标签一致
                isPlaying: false,

                currentSpeed: 0,
                playBack: {
                    offset: 0,
                    progress: 0,
                    bufferedProgress: 0,
                    currentTime: 0,
                    totalTime: 0,
                    loading: false,
                    timer: null,
                    speed: 1,
                    speedValue: [0, 1, 2, 4, 8, 16],
                },

                player: null,//指向 $refs['player']
                sourceLoading: false,
                stopLoading: false,
                controlShow: false,
                loadAjax: null,
                url: '',
                id:'',
                hasAudio: false,
                //上一次的参数
                lastParams: {
                    terminalCode: undefined,
                    chn: undefined,
                },

                showLog: false,
                showDecodeInfo: false,
                contextMenuOptions: [
                    {
                        label: '显示日志',
                        handler: () => {
                            this.showLog = !this.showLog;
                        }
                    },
                    {
                        label: '显示解码信息',
                        handler: () => {
                            this.showDecodeInfo = !this.showDecodeInfo;
                        }
                    }
                ],
                controlTimeout: null,
                //打断加载行为
                interruptLoadSourceHandle: null,

            }
        },
        computed: {
			...mapState("auth", ["token"]),

            startDate: function () {
                return moment(this.startTime, 'YYYYMMDDHHmmss').toDate();
            },
            endDate: function () {
                return moment(this.endTime, 'YYYYMMDDHHmmss').toDate();
            },
            
            diffTime: function () {
                return this.endDate - this.startDate;
            },
            // 计算快进时拖动进度条的时长
            offsetTime: function () {
                return this.diffTime * this.playBack.offset / 100 / 1000;
            },
            sourceParams: function () {
                if (this.terminalCode !== undefined && this.startTime && this.endTime) {
                    return this.terminalCode;
                } else {
                    return '';
                }
            },
            loading: function () {
                return this.sourceLoading || this.stopLoading
            },
            volumeHint: function () {
                if (this.loaded && !this.hasAudio) {
                    return this.$ct('notSupportAudio')
                } else {
                    return ''
                }
            },
        },
        watch: {
            showLog: function (val) {
                this.contextMenuOptions[0].label = `${val ? '隐藏' : '显示'}日志`;
            },
            showDecodeInfo:function(val){
                this.contextMenuOptions[1].label = `${val ? '隐藏' : '显示'}解码信息`;
            },
            sourceParams: {
                handler: function (val, oldV) {
                    if (oldV) {
                        this.lastParams.terminalCode = oldV;
                    }
                    this.firstLoad = true;
                    val && this.autoPlay && this._loadSource();
                },
                // immediate: true,//用onPlayerMounted替代
            },
            controlShow: function (val, oldV) {
                if (!oldV && val) {
                    clearTimeout(this.controlTimeout);
                    this.controlTimeout = setTimeout(() => {
                        this.controlShow = false;
                    }, 3000) //3s后自动隐藏
                }
            }
        },
        methods: {
            _onEnded() {
                this.isPlaying = false
                this.currentSpeed = 0;
                this.playBack.currentTime = this.offsetTime;
                this.playBack.progress = this.playBack.offset;
                // this.playBack.offset = 0;
                // this.playBack.bufferedProgress = 0;
            },
            timeout(delay) {
                return new Promise(resolve => {
                    setTimeout(resolve, delay,null)
                })
            },
            _onCanPlay() {
                this.playBack.progress = 0;
                this.playBack.currentTime = 0;
            },
            _onTimeUpdate(time, event) {
                
                let playspeed = this.playBack.speedValue[this.playBack.speed]
                this.playBack.currentTime = Math.min((time + this.offsetTime), this.playBack.totalTime);
                this.playBack.progress = (time + this.offsetTime) / this.playBack.totalTime * 100;
                this.$emit('currentTimeChange', this.playBack.currentTime);
            },
            _setBufferProgress(time, event) {
                // this.playBack.bufferedTime = Math.min((time + this.offsetTime), this.playBack.totalTime);
                this.playBack.bufferedProgress = (time + this.offsetTime) / this.playBack.totalTime * 100;
            },
            async _speedChange(speed) {
                
                this.currentSpeed = speed;
            },
            //确保首次执行时$refs['player']已存在
            _onPlayerMounted() {
                
                this.player = this.$refs['player'];
                this.sourceParams && this.autoPlay && this._loadSource();
                this.log('播放器初始化完成');
            },
            async _loadSource() {
                
                await this._freeSource();
                await this._requestSource();
            

            },
            /*
            申请占用此设备通道，
            目前实时视频的推流在不同的客户端会复用,
            这是一个非常耗时的流程，需要实现其中任意阶段可暂停
            */
            async _requestSource() {
                this.sourceLoading = true;
                try {
                    this.log('正在请求视频资源...');
                    const startTime = moment(this.startTime, 'YYYYMMDDHHmmss'),
                          offsetStartTime = !this.playBack.offset ?
                              this.startTime : startTime
                                  .add(moment(this.endTime, 'YYYYMMDDHHmmss').diff(startTime, 'seconds') * this.playBack.offset / 100, 'seconds')
                                  .format('YYYYMMDDHHmmss');
                   
                     let ajax = $.ajax({
                            url: API_URL,
                            type: 'post',
                            data: JSON.stringify({
                                cmd: "playback",
                                channel_id: this.terminalCode,
                                // terminal_no: '34020000001320000001',
                                start_time: offsetStartTime,
                                end_time: this.endTime,
                            }),
                            headers: {
                                Authorization: 'token ' + this.token,
                            },
                        })
                    this.loadAjax = ajax;
                    // let result = await ajax;
                    let result = null
                    let res = await Promise.race([ajax,this.timeout(8000)]).then((value)=>{
                        if(value == null){
                            this.$error('获取视频地址失败,超时')
                            this.sourceLoading = false;

                            return false
                        }else {
                            result = value
                            return true
                        }
                    })
                    if(!res)return
                    this.loadAjax = null;
                    if (typeof result !== 'object') {
                        result = JSON.parse(result)
                    }
                    if (result.code == 0) {
                        this.log('成功获取视频地址，开始缓冲...');
                        this.url = result.data;
                        this.id = result.id;
                        this.hasAudio = result.hasAudio;
                        this.firstLoad = false;

                        await Promise.race([
                            //允许中断操作
                            new Promise((resolve, reject) => {
                                this.interruptLoadSourceHandle = {resolve, reject};
                            }),
                            Promise.all([
                                // (async () => {
                                //     try {
                                //         //向ws订阅流的相关信息 这一步骤不是必要的
                                //         //有可能会得不到回复
                                //         await this._subscribeVideoInfo(this.url);
                                //     } catch (e) {
                                //         //报错的话 就忽略这次 订阅推流结束事件的行为（忽略严格模式）
                                //         // this.videoStream._resolve()
                                //     }
                                // })(),
                                //加载视频
                                this.$refs['player'].load(this.url, this.hasAudio),
                            ]),
                        ])
                        this.interruptLoadSourceHandle.resolve();
                        this.interruptLoadSourceHandle = null;
                        this.loaded = true;
                        this.log('开始播放视频.');
                        this.playBack.totalTime = Math.ceil(this.diffTime / 1000);
                        this.playBack.speed = 1;
                    } else {
                        throw {from: 'api', msg: result.message}
                    }
                } catch (e) {
                    switch (true) {
                        case e.statusText === 'abort':
                            break;
                        case e.from === 'flvjs' && e.error === 'NetworkError':
                            this.$error('视频地址已失效.')
                            this.log(`请求视频资源失败,原因：视频地址已失效.`);
                            break;
                        case e.from === 'api':
                            this.$error(this.$ct('messageInfo.2') + `: ${e.msg}`);
                            this.log(`请求视频资源失败,原因：${e.msg}`);
                            break;
                        case e.from === 'interrupt':
                            break;
                        default:
                            console.error(e);
                    }
                    //报错的话 就忽略这次 订阅推流结束事件的行为（忽略严格模式）
                    this.videoStream._resolve instanceof Function && this.videoStream._resolve();
                } finally {
                    this.sourceLoading = false;
                }
            },
            /*释放设备通道资源
            * strict= true 时采取严格模式，即明确得到设备断开提示才会结束*/
            async _freeSource(strict = true) {
                try {
                    this.stopLoading = true;
                    this.loadAjax && this.loadAjax.abort();
                    if (this.interruptLoadSourceHandle) {
                        this.interruptLoadSourceHandle.reject({from: 'interrupt', msg: '人工打断'});
                        this.interruptLoadSourceHandle = null;
                    }
                    if (this.loaded) {
                        this.loaded = false;
                        this.player.unload();
                        this.log('正在释放请求的资源...')
                        // await $.post(API_URL, JSON.stringify({
                        //     cmd: "control",
                        //     terminal_no: this.lastParams.terminalCode || this.terminalCode,
                            
                        //     // control_code: 2,
                        //     // time: '',
                        //     // level: 1,
                        // }));
                        // let id = this.url.split('/')[this.url.split('/').length-1].slice(0,-4)
                        // console.log(this.url);
                        // console.log(id);
                        // console.log("正在释放请求的资源...");
                         let stopAjax = $.ajax({
                            url: API_URL,
                            type: 'post',
                            data: JSON.stringify({
                                cmd: "controlPlayback",
                                // terminal_no: this.lastParams.terminalCode || this.terminalCode,
                                id:this.id,
                                action:'TEARDOWN'
                            }),
                            headers: {
                                Authorization: 'token ' + this.token,
                            },
                        })
                        // let result = null
                        let res = await Promise.race([stopAjax,this.timeout(8000)]).then((value)=>{
                            if(value == null){
                                this.$error('操作视频失败,超时')
                                return false
                            }else {
                                
                                return true
                            }
                        })
                        if(!res)return

                        this.url = '';
                        this.id = '';
                        this.hasAudio = false;
                        this.lastParams = {
                            terminalCode: undefined,
                        }
                        if (strict) {
                            this.log('严格模式：等待设备停止推流...')
                            let timeStart = Date.now();
                            await this.videoStream.waitForEnd;
                            this.log(`设备已断开,耗时${Date.now() - timeStart}ms.`)
                        }
                    }
                } catch (e) {
                    throw e
                } finally {
                    this.stopLoading = false;
                }
            },

            //特殊处理特殊实现
            //在缓冲区内就拖动跳转，在外就重新请求响应流地址
            async setProgress(percent,oldPercent) {
                const playBack      = this.playBack,
                      originPercent = percent;
                if (playBack.offset) {
                    percent = (percent - playBack.offset) / (100 - playBack.offset) * 100
                }
                playBack.progress = originPercent;
                if (percent >= 0 && originPercent <= playBack.bufferedProgress) {
                    let second = Math.ceil(this.diffTime * (originPercent - playBack.offset) / 100 / 1000);
                    this.player.seekTo(second);
                } else {
                    // this.playBack.offset = 0

                    // this.reload(originPercent);
                    let res = await this.processReload(originPercent)
                    if(!res){
                        //进度条回弹
                        this.reload(originPercent);
                        return 
                        // playBack.progress = oldPercent
                        // this.playBack.offset = oldPercent
                    }
                     this.sourceLoading = true;
                    this.loaded = false;
                    try{
                        await this.$refs['player'].load(this.url, this.hasAudio)
                        this.loaded = true;
                        this.sourceLoading = false;
                    }catch(e){
                        // this.$error('加载失败')
                        this.loaded = false;``
                        this.sourceLoading = false;
                        this.reload(originPercent);
                    }
                }
            },
            async processReload(offset = 0){
                this.playBack.offset = offset;
                const startTime = moment(this.startTime, 'YYYYMMDDHHmmss'),
                          offsetStartTime = !this.playBack.offset ?
                              this.startTime : startTime
                                  .add(moment(this.endTime, 'YYYYMMDDHHmmss').diff(startTime, 'seconds') * this.playBack.offset / 100, 'seconds')
                                  .format('YYYYMMDDHHmmss');
                let result = await this.sendVideoCommand(5,offsetStartTime)

                if (result.code != 0) {
                    // this.player.play()
                    this.$error(result.message|| '拖动失败')
                    return false
                }
                return true
            },
            //只需要控制码的控制命令
            async sendVideoCommand(type,time,level){
                let params = {
                    cmd: "controlPlayback",
                    id:this.id,
                    control_code: type,
                    action:'TEARDOWN'
                }
                if(time && time !== 0){
                    params.time = time
                }
                if(level && level !== 0){
                    params.level = level
                }
                return await $.ajax({
                        url:API_URL,
                        type: 'post',
                        data:JSON.stringify(params),
                        headers: {
                            Authorization:'token '+ this.token,
                        },
                    });
            },
            //格式化进度条文字
            formatTip(percent) {
                // return this.formatTime(Math.round(percent * this.playBack.totalTime / 100));
                return moment(this.startDate).add(this.diffTime * percent / 100, 'ms').format('HH:mm:ss');
            },
            //秒数格式化成 HH:mm:ss
            formatTime(second) {
                // let mTemp = Math.floor(second / 60);
                // let m = ('0' + mTemp).slice(-(mTemp > 10 ? mTemp.toString(10).length : 2));
                // let s = ('0' + Math.round(second) % 3600 % 60).slice(-2);
                // return `${m}:${s}`
                return moment(this.startDate).add(second, 'seconds').format('HH:mm:ss');
            },

            loadSource(offset = 0) {
                this.playBack.offset = offset;
                if (this.sourceParams) {
                    this._loadSource();
                }
            },
            reload(offset = 0) {
                this.playBack.offset = offset;
                return this._loadSource();
            },
            async stop() {
                this.isPlaying = false;
                this.$emit('stop', 0);
                await this._freeSource();
            },
            toggleSound() {
                if (!this.loaded || !this.hasAudio) {
                    return;
                }
                this.player.toggleMuted();
            },
            screenshot() {
                if (this.loaded) {
                    this.player.screenShot(this.symbol);
                }
            },
            //目前api只接受简单请求，故用jquery提交请求
            //todo 去除jquery依赖
            // async sendCommand(ctr, time, level) {
            //     return await $.post(API_URL, JSON.stringify({
            //         cmd: "control",
            //         terminal_no: this.videoInfo.terminalCode,
            //         channel_no: this.chn + 1,
            //         control_code: ctr,
            //         time,
            //         level,
            //     }));
            // },
            //倍数播放
            async fastForward(speed) {
                if (this.isStopped) return;
                //本地实现倍速
                if (speed === 5) return;

                this.player.setPlayBackRate(this.playBack.speedValue[speed])
                this.playBack.speed = speed;
                //简单低端机cpu性能不够 拖动后不能够继续播放的bug
                //ps: 目前能触发的机器有 任意配置的win7下的chrome  cpu <= i5-6400的win10（i5-7400就可以完美跳转）
                //高分辨率视频源下全机型都能触发
                await this.$utils.sleep(200);
                // console.log( this.player);
                this.player.$el.play();


                //推流实现倍速
                // let oldValue = this.player.speed;
                // let result = await this.sendCommand(3, '', speed)
                // if (result.code === 0) {
                //     this.playBack.speed = speed;
                // } else {
                //     this.playBack.speed = oldValue;
                //     this.$error(result.message)
                // }
            },
        },
        mounted(){
        },
        created() {
            window.addEventListener("beforeunload", this.stop);
        },
        beforeDestroy() {
            window.removeEventListener("beforeunload", this.stop);
            this._freeSource(false);
            clearTimeout(this.controlTimeout);
        },
    }
</script>

<style lang="scss">
    @import "player";
</style>
<style scoped lang="scss">
   
</style>

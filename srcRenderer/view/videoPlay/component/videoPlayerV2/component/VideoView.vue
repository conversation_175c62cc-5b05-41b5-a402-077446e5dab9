<template>
  <div class="video-view" ref="root">
    <div class="sim-disabled" v-if="loading">
      <i class="el-icon-loading text text--info" style="margin: 0 5px;font-size:40px"></i>
      <span style="font-size: 14px">{{ $ct('checkSimFlow') }}</span>
    </div>
    <div class="sim-disabled" v-else-if="simFlowOver">
      <i class="pony-iconv2 pony-cuowu text text--danger" style="margin: 0 5px;font-size:40px"></i>
      <span style="font-size: 14px">{{ $ct('simFlowOver') }}</span>
    </div>
    <div :class="['player-box',`multiple-${windowNum}`]" v-else ref="content">
      <div :class="['player',{'maximum':maximum === index},{'item-border':clickItemNum == index}]"
           @click="clickItem(index)"
           @dblclick="toggleMaximum(index)"
           v-for="(item,index) in windowNum" :key="item">
        <div class="noChannel" v-if="index >= chnNo && channelNoList.length || channelNoList[index] === 0">
          <div class="noChannel-des">
            <i class="pony-iconv2 pony-zanwushipinicon"></i>
            <p>无此通道</p>
          </div>
        </div>
        <RealtimePlayer ref="player"
                        v-else-if="playType == 'flv'"
                        :notSupportVideo="notSupportVideo"
                        :inputType="inputType"
                        :vehicleId="currentVehicle.vehicle_id"
                        :terminalCode="currentVehicle.code"
                        :channelTalkback="channelTalkback"
                        :label="channelNoList?channelNoList[index]?channelNoNameObj[channelNoList[index]] ? channelNoNameObj[channelNoList[index]] : '通道'+channelNoList[index]:'':item"
                        :symbol="currentVehicle.plate_no"
                        :chnN="channelNoList?channelNoList[index]:item"
                        :autoPlay="index < chnNo && autoPlay"
                        :disabled="index >= chnNo || !currentVehicle.code"
                        :enableFullScreen="true"
                        :labelAlwaysShow="true"
                        @playing="playing"
        ></RealtimePlayer>
        <RealtimePlayerJessibuca ref="player"
                                 v-else-if="playType==='jessibuca'"
                                 :notSupportVideo="notSupportVideo"
                                 :inputType="inputType"
                                 :vehicleId="currentVehicle.vehicle_id"
                                 :terminalCode="currentVehicle.code"
                                 :channelTalkback="channelTalkback"
                                 :label="channelNoList?channelNoList[index]?channelNoNameObj[channelNoList[index]] ? channelNoNameObj[channelNoList[index]] : '通道'+channelNoList[index]:'':item"
                                 :symbol="currentVehicle.plate_no"
                                 :chnN="channelNoList?channelNoList[index]:item"
                                 :autoPlay="index < chnNo && autoPlay"
                                 :disabled="index >= chnNo || !currentVehicle.code"
                                 :enableFullScreen="true"
                                 :labelAlwaysShow="true"
                                 @playing="playing"
        ></RealtimePlayerJessibuca>
        <RealtimePlayerHls ref="player"
                           v-else
                           :notSupportVideo="notSupportVideo"
                           :inputType="inputType"
                           :vehicleId="currentVehicle.vehicle_id"
                           :terminalCode="currentVehicle.code"
                           :channelTalkback="channelTalkback"
                           :label="channelNoList?channelNoList[index]?channelNoNameObj[channelNoList[index]] ? channelNoNameObj[channelNoList[index]] : '通道'+channelNoList[index]:'':item"
                           :symbol="currentVehicle.plate_no"
                           :chnN="channelNoList?channelNoList[index]:item"
                           :autoPlay="index < chnNo && autoPlay"
                           :disabled="index >= chnNo || !currentVehicle.code"
                           :enableFullScreen="true"
                           :labelAlwaysShow="true"
                           @playing="playing"
        ></RealtimePlayerHls>
      </div>
    </div>
    <div v-show="hasVideoPlaying && autoStop" v-stop-mouse-move="stopMouseMoveOptions"></div>

    <PonyDialog width="450" @confirm="ttsModalConfirm" @close="closeTTsModal"
                title="请输语音下发内容" hasMask
                v-model="ttsmodal.show">
      <el-form ref="ttsform" :model="ttsmodal.data" :rules="ttsmodal.rules" label-width="80px">
        <el-form-item label="模板">
          <el-select style="width: 100%" v-model="ttsmodal.data.type"
                     @change="ttsmodal.data.text = JSON.parse(JSON.stringify(ttsmodal.data.type))">
            <el-option v-for="(item, index) in ttsmodal.modellist" :key="index"
                       :label="item.configDesc" :value="item.configValue">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="信息" prop="text">
          <el-input type="textarea" :rows="5" v-model="ttsmodal.data.text"></el-input>
        </el-form-item>
      </el-form>
    </PonyDialog>
  </div>
</template>

<script>

import RealtimePlayer from "@/view/videoPlay/component/videoPlayerV2/RealtimePlayer";
import RealtimePlayerHls from "@/view/videoPlay/component/videoPlayerV2/component/RealtimePlayerHls";
import RealtimePlayerJessibuca from "./RealtimePlayerJessibuca.vue";


/**
 * @Author: yezy
 * @Email: <EMAIL>
 * @Date: 2019-05-08 13:45:42
 * @LastEditors: yezy
 * @LastEditTime: 2019-05-08 13:45:42
 * @Description: 重构的视频组件
 */

export default {
  name: "videoView",
  components: {
    RealtimePlayerJessibuca,
    RealtimePlayer,
    RealtimePlayerHls
  },
  props: {
    playType: {
      type: String,
      default: 'flv',
    },
    inputType: {
      type: Number,
      default: 1,
    },
    //显示的通道数目
    windowNum: {
      type: [Number, Array],
      default: 4,
      // validator: function (value) {
      //     return [1, 4, 6, 9].indexOf(value) !== -1
      // }
    },
    //车辆id   终端号 简化参数，终端号在组件内获得，
    vehicleId: {
      type: [Number, String],
      required: false,
    },
    chnNo: {
      type: Number,
      default: 4,
    },
    channelTalkback: {
      type: [Number, String],
      default: 1,

    },
    channelNoList: {
      type: Array,
    },
    channelNoNameObj: {
      type: Object,
      default: () => ({})
    },
    autoPlay: {
      type: Boolean,
    },
    autoStop: {
      type: Number,
    },
    notSupportVideo: {
      type: Boolean,
      default: false,
    }
  },
  data() {
    return {
      loading: false,
      simFlowOver: false,
      maximum: -1,
      currentVehicle: {
        plate_no: '',
        vehicle_id: '',
        code: ''
      },
      initialized: false,
      stopMouseMoveOptions: {
        target: document,
        time: this.autoStop,
        handler: this.stopAll,
        stateChange: this.onRemainingTimeChange
      },
      players: [],
      ttsmodal: {
        modellist: [],
        show: false,
        _reject: null,
        _resolve: null,
        data: {
          type: '',
          text: ''
        },
        rules: {
          text: [
            {required: true, message: '请输入消息内容', trigger: 'blur'},
          ]
        }
      },
      // 选中
      clickItemNum: 0,
    }
  },
  watch: {
    vehicleId: {
      handler: 'initVehicleData',
      immediate: true,
    },
  },
  computed: {
    hasVideoPlaying() {
      if (this.players) {
        return this.players.some(item => {
          return item.isPlaying;
        })
      } else {
        return false
      }
    },
    label() {
      return
    },
  },
  mounted() {
    this.getTTSModelList()
    this.$emit('clickItemIndex', 0)
  },
  methods: {
    playing(val) {
      this.$emit('playing', val)
    },
    //  showModal() {
    //     this.ttsmodal.show = true
    //     return new Promise((resolve, reject) => {
    //         this.ttsmodal._reject = reject;
    //         this.ttsmodal._resolve = resolve;
    //     })
    // },
    clickItem(index) {
      this.clickItemNum = index
      this.$emit('clickItemIndex', index)
    },
    onRemainingTimeChange(isStop, remaining) {
      this.$emit('remainingTimeChange', isStop, remaining);
    },
    toggleMaximum(index) {
      if (index === this.maximum) {
        this.maximum = -1;
      } else {
        this.maximum = index;
      }
    },
    async stopAll(type = true) {
      if (type && this.chnNo) {
        // let alertBox = this.$notify({
        //     title: '警告',
        //     message: '您已长时间不操作系统，视频已经自动关闭！',
        //     type: 'warning',
        //     duration:0,
        //     offset:50
        // });
        // document.onmousemove = function(){
        //     alertBox.close()
        // }
      }
      let temp = [];
      for (let i = 0; i < this.windowNum; i++) {
        if (this.$refs['player'][i]) {
          temp.push(this.$refs['player'][i].stop());
          // if (this.playType === 'flv') {
          //     temp.push(this.$refs['player'][i].stop());
          // } else {
          //     temp.push(this.$refs['playerHls'][i].stop());
          // }
        }
      }
      await Promise.all(temp);
      this.$emit('stopPlay');
    },
    async refresh() {
      if (this.simFlowOver) return;
      let temp = [];
      for (let i = 0; i < this.windowNum; i++) {
        if (this.$refs['player'][i]) {
          temp.push(this.$refs['player'][i].reload());
        }

        // if (this.playType === 'flv') {
        //     temp.push(this.$refs['player'][i].reload());
        // } else {
        //     temp.push(this.$refs['playerHls'][i].reload());
        // }
      }
      await Promise.all(temp);
    },
    async checkSimFlowOver() {
      if (!this.$store.getters['main/need2CheckSim'] || !this.vehicleId || !this.currentVehicle.code) return false
      try {
        this.loading = true;
        let res = await this.$api.getSimFlowUsedInfo({
          vehicle_id: this.vehicleId,
          terminal_no: this.currentVehicle.code,
        })
        if (res.status === 200) {
          this.simFlowOver = res.data.is_over;
        } else {
          this.simFlowOver = false;
        }
      } catch (e) {
        this.$error('查询流量使用情况失败')
        this.simFlowOver = false;
      } finally {
        this.loading = false;
      }
    },
    async initVehicleData(val) {
      if (!val) {
        this.initialized = false;
        return;
      }
      try {
        Object.assign(this.currentVehicle, await this.$store.dispatch('vehicle/getVehicleInfo', {
          key: 'id',
          value: val
        }))
        await this.checkSimFlowOver();
        this.initialized = true;
        await this.$nextTick();
        this.players = this.$refs['player'];
      } catch (e) {
        this.$info(this.$ct('messageInfo'))
      }
    },
    async getTTSModelList() {
      let result = await this.$api.getTTS()
      if (!result || result.status != 200 || !result.data || !result.data.length) return
      this.ttsmodal.modellist = result.data
      this.ttsmodal.data.type = result.data[0].configValue
      this.ttsmodal.data.text = result.data[0].configValue
    },

    showModal() {
      this.ttsmodal.show = true
      return new Promise((resolve, reject) => {
        this.ttsmodal._reject = reject;
        this.ttsmodal._resolve = resolve;
      })
    },

    closeTTsModal() {
      this.ttsmodal._reject = false;
      this.ttsmodal._resolve = false;
    },

    ttsModalConfirm() {
      this.$refs['ttsform'].validate(async (valid) => {
        if (!valid) return
        this.ttsmodal.show = false
        this.ttsmodal._resolve(this.ttsmodal.data.text);
      })
    },
  },
}
</script>

<style scoped lang="scss">
.video-view {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;

  .player-box {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    flex-wrap: wrap;


    .player {
      position: relative;
      width: 50%;
      height: 50%;
      border: 1px solid transparent;
      z-index: 1;
      box-sizing: border-box;

      &.item-border {
        border: 2px solid var(--color-primary);
      }

      /deep/ video {
        object-fit: fill
      }

      &.maximum {
        position: absolute;
        top: 0;
        left: 0;
        width: 100% !important;
        height: 100% !important;
        display: block;
        z-index: 999;
      }

      .noChannel {
        width: 100%;
        height: 100%;
        background-color: var(--background-color-lighter);
        display: flex;
        justify-content: center;
        align-items: center;

        .noChannel-des {
          width: 60px;

          i {
            font-size: 58px;
            color: var(--color-text-channeldisabled);
          }

          p {
            margin-top: 15px;
            color: var(--color-text-channeldisabled);

          }
        }
      }

    }

    &.multiple-1 {
      .player {
        width: 100%;
        height: 100%;
      }
    }

    &.multiple-6 {
      .player {
        width: 33.333333%;
        height: 50%;
      }
    }

    &.multiple-9 {
      .player {
        width: 33.333333%;
        height: 33.333333%;
      }
    }

    &.multiple-16 {
      .player {
        width: 25%;
        height: 25%;
      }
    }
  }

  .sim-disabled {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #fff;
    font-size: .4rem;
    cursor: pointer;
    z-index: 2;
    background: #18191a;
  }
}
</style>

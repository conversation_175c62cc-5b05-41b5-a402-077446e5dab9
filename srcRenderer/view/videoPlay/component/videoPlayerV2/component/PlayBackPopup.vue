<template>
    <div class="playback-player pony-player"
        @mousemove="controlShow = true"
        @mouseleave="controlShow = false"
    >
        <div class="pony-player__video-wrap wrap " id="warp">
            <FlvVideo ref="player"
                    @decodeLog="log"
                    @ready="_onPlayerMounted"
                    @play="()=>this.isPlaying = true"
                    @ended="_onEnded"
                    @canplay="_onCanPlay"
                    @timeupdate="_onTimeUpdate"
                    @progress="_setBufferProgress"
            >
            </FlvVideo>
        </div>
        <div class="pony-player__main-wrap wrap">
            <!--控制条-->
            <div class="pony-player__controller" v-if="loaded && player && player.videoElm" :style="{opacity:controlShow?1:0}">
                <i class="pony-iconv2 pony-sanjiaoxing" title="播放" v-if="player.videoElm.paused" @click="player.play"></i>
                <i class="pony-iconv2 pony-zanting" title="暂停" v-else @click="player.pause"></i>
                <CustomSlider :percentage="playBack.progress" :subPercentage="playBack.bufferedProgress"
                            :disabled="loaded" :format-tooltip="formatTip" :offset="playBack.offset"
                            @change="setProgress" class="progress" rangeLimit>
                </CustomSlider>
                <span style="width: 65px;text-align: right">{{currentSpeed}}KB/s</span>
                <span class="time-tip">
                    {{formatTime(playBack.currentTime) + ' / ' + formatTime(playBack.totalTime)}}
                </span>
                
                <i :class="['pony-iconv2 pony-paizhao',{'is-disabled':!loaded }]" title="截图"
                @click="screenshot()"></i>
                <i :class="['pony-iconv2 ',{'is-disabled':!loaded || !hasAudio},player.videoElm.muted?'pony-jingyin':'pony-shengyin']"
                @click="toggleSound" :title="volumeHint"></i>
                <i class="pony-iconv2" :class="[isFullScreen?'pony-suoxiao':'pony-quanping']" @click="toggleFullScreen"
                v-if="enableFullScreen"
                :title="(isFullScreen?'退出':'') + '全屏'"></i>
            </div>
            <div class="pony-player__mask wrap" v-else>
                <i v-if="loading" class="el-icon-loading" ></i>
                <i v-else :class="['pony-iconv2',firstLoad?'pony-sanjiaoxing':'pony-shuaxin']" style="font-size:40px" title="播放"
                @click="loadSource()"></i>
            </div>
        </div>
    </div>
</template>

<script>
 /**
 * @Author: xieyj
 * @Email: 
 * @Date: 2021/11/17 14:29
 * @LastEditors: xieyj
 * @LastEditTime: 
 * @Description:
 */
import strictEnd from "@/view/videoPlay/component/videoPlayerV2/mxin/strictEnd";
import fullscreen from "@/view/videoPlay/component/videoPlayerV2/mxin/fullscreen";
import log from "@/view/videoPlay/component/videoPlayerV2/mxin/log";
import CustomSlider from "@/components/common/CustomSlider";
import FlvVideo from "@/view/videoPlay/component/videoPlayerV2/component/FlvVideo";
import {mapState} from 'vuex';
const API_URL = window.PONY.media.apiUrl;
export default {
    name: 'playBackPopup',
    mixins: [strictEnd, fullscreen, log],
    components: {
        FlvVideo,
        CustomSlider
    },
    props: {
        vehicleId: {type: [String, Number],},
        terminalCode: {type: String,},
        chn: {type: Number, default: 0},// start at 0
        startTime: {type: String}, // YYYYMMDDHHmmss 格式
        endTime: {type: String},// YYYYMMDDHHmmss 格式
        autoPlay: {type: Boolean, default: true,},
        // showClose: {type: Boolean, default: false,},
        enableFullScreen: {type: Boolean, default: true,},
    },
    data () {
        return {
            
            // show:false,
            firstLoad: true,//该参数是否第一次加载，决定图标是三角按钮还是刷新按钮
            loaded: false,//当前是否有请求的加载的流
            //相关状态，不一定与内部video标签一致
            isPlaying: false,
            title:'',
            currentSpeed: 0,
            playBack: {
                offset: 0,
                progress: 0,
                bufferedProgress: 0,
                currentTime: 0,
                totalTime: 0,
                loading: false,
                timer: null,
            },

            player: null,//指向 $refs['player']
            sourceLoading: false,
            stopLoading: false,
            controlShow: false,
            loadAjax: null,
            url: '',
            id: '',
            hasAudio: false,
            controlTimeout: null,
            //打断加载行为
            interruptLoadSourceHandle: null

        };
    },

    computed: {
        ...mapState('vehicle', ['basicByVehicleId']),
		...mapState("auth", ["token"]),

        startDate: function () {
            return moment(this.startTime, 'YYYYMMDDHHmmss').toDate();
        },
        endDate: function () {
            return moment(this.endTime, 'YYYYMMDDHHmmss').toDate();
        },
        diffTime: function () {
            return this.endDate - this.startDate;
        },
        offsetTime: function () {
            return this.diffTime * this.playBack.offset / 100 / 1000;
        },

        sourceParams: function () {
            if (this.vehicleId && this.terminalCode && this.chn !== undefined && this.startTime && this.endTime) {
                return `${this.vehicleId}-${this.terminalCode}-${this.chn}-${this.startTime}-${this.endTime}`;
            } else {
                return '';
            }
        },
        loading: function () {
            return this.sourceLoading || this.stopLoading
        },
        volumeHint: function () {
            if (this.loaded && !this.hasAudio) {
                return '该设备不支持音频输出'
            } else {
                return ''
            }
        },
    },

    mounted() {

    },
    watch:{
        sourceParams: {
            handler: function (val, oldV) {
                this.firstLoad = true;
                val && this.autoPlay && this._loadSource();
            },
        },
        controlShow: function (val, oldV) {
            if (!oldV && val) {
                clearTimeout(this.controlTimeout);
                this.controlTimeout = setTimeout(() => {
                    this.controlShow = false;
                }, 3000) //3s后自动隐藏
            }
        }
    },

    methods: {
        _onEnded() {
            this.isPlaying = false
            this.currentSpeed = 0;
            this.playBack.currentTime = this.offsetTime;
            this.playBack.progress = this.playBack.offset;
            // this.playBack.offset = 0;
            // this.playBack.bufferedProgress = 0;
        },
        _onCanPlay() {
            this.playBack.progress = 0;
            this.playBack.currentTime = 0;
        },
        _onTimeUpdate(time, event) {
            this.playBack.currentTime = Math.min((time + this.offsetTime), this.playBack.totalTime);
            this.playBack.progress = (time + this.offsetTime) / this.playBack.totalTime * 100;
            this.$emit('currentTimeChange', this.playBack.currentTime);
        },
        _setBufferProgress(time, event) {
            // this.playBack.bufferedTime = Math.min((time + this.offsetTime), this.playBack.totalTime);
            this.playBack.bufferedProgress = (time + this.offsetTime) / this.playBack.totalTime * 100;
        },
        // async _speedChange(speed) {
        //     this.currentSpeed = speed;
        // },
        //确保首次执行时$refs['player']已存在
        // closeVideo(){
        //     // this.show = false
        //     this.stop()
        //     this.$emit('close')
        // },
        _onPlayerMounted() {
            this.player = this.$refs['player'];
            this.sourceParams && this.autoPlay && this._loadSource();
            this.log('播放器初始化完成');
        },
        async _loadSource() {
            await this._freeSource();
            await this._requestSource();
        },
        
        /*
        申请占用此设备通道，
        目前实时视频的推流在不同的客户端会复用,
        这是一个非常耗时的流程，需要实现其中任意阶段可暂停
        */
        async _requestSource() {

            this.sourceLoading = true;
            try {
                this.log('正在请求视频资源...');
                const startTime = moment(this.startTime, 'YYYYMMDDHHmmss'),
                        offsetStartTime = !this.playBack.offset ?
                            this.startTime : startTime
                                .add(moment(this.endTime, 'YYYYMMDDHHmmss').diff(startTime, 'seconds') * this.playBack.offset / 100, 'seconds')
                                .format('YYYYMMDDHHmmss');
                let ajax = $.ajax({
                    url:API_URL,
                    type: 'post',
                    data:JSON.stringify({
                        cmd: "playback",
                        terminal_no: this.terminalCode,
                        channel_no: this.chn,
                        play_code: 0,
                        level: 0,
                        start_time: offsetStartTime,
                        end_time: this.endTime,
                    }),
                    headers: {
                        Authorization:'token '+this.token,
                    },
                 });

                this.loadAjax = ajax;
                let result = await ajax;
                this.loadAjax = null;
                if (typeof result !== 'object') {
                    result = JSON.parse(result)
                }
                if (result.code == 0) {
                    this.log('成功获取视频地址，开始缓冲...');
                    this.url = result.data.split('?')[0];
                    this.id = result.id
                    this.hasAudio = result.hasAudio;
                    this.firstLoad = false;
                    // await this.$api.setVideoLog({
                    //     url: this.url, // 视频接口地址
                    //     type: 502, // 日志类型(501: 实时视频; 502: 录像回放)
                    //     vehicleId: this.vehicleId // 车辆id
                    // })
                    await Promise.race([
                        //允许中断操作
                        new Promise((resolve, reject) => {
                            this.interruptLoadSourceHandle = {resolve, reject};
                        }),
                        Promise.all([
                            // (async () => {
                            //     try {
                            //         //向ws订阅流的相关信息 这一步骤不是必要的
                            //         //有可能会得不到回复
                            //         await this._subscribeVideoInfo(this.url);
                            //     } catch (e) {
                            //         //报错的话 就忽略这次 订阅推流结束事件的行为（忽略严格模式）
                            //         this.videoStream._resolve()
                            //     }
                            // })(),
                            //加载视频
                            this.$refs['player'].load(this.url, this.hasAudio),
                        ]),
                    ])
                    this.interruptLoadSourceHandle.resolve();
                    this.interruptLoadSourceHandle = null;
                    this.loaded = true;
                    this.log('开始播放视频.');
                    this.playBack.totalTime = Math.ceil(this.diffTime / 1000);
                } else {
                    throw {from: 'api', msg: result.message}
                }
            } catch (e) {
                switch (true) {
                    case e.statusText === 'abort':
                        break;
                    case e.from === 'flvjs' && e.error === 'NetworkError':
                        this.$error('视频地址已失效.')
                        this.log(`请求视频资源失败,原因：视频地址已失效.`);
                        break;
                    case e.from === 'api':
                        this.$error('获取视频地址失败' + `: ${e.msg}`);
                        this.log(`请求视频资源失败,原因：${e.msg}`);
                        break;
                    case e.from === 'interrupt':
                        break;
                    default:
                        console.error(e);
                }
                //报错的话 就忽略这次 订阅推流结束事件的行为（忽略严格模式）
                this.videoStream._resolve instanceof Function && this.videoStream._resolve();
            } finally {
                this.sourceLoading = false;
            }
        },
        /*释放设备通道资源
        * strict= true 时采取严格模式，即明确得到设备断开提示才会结束*/
        async _freeSource(strict = true) {
            try {
                this.stopLoading = true;

                this.loadAjax && this.loadAjax.abort();
                if (this.interruptLoadSourceHandle) {
                    this.interruptLoadSourceHandle.reject({from: 'interrupt', msg: '人工打断'});
                    this.interruptLoadSourceHandle = null;
                }
                if (this.loaded) {
                    this.loaded = false;
                    this.player.unload();
                    this.log('正在释放请求的资源...')
                    await $.ajax({
                        url:API_URL,
                        type: 'post',
                        data:JSON.stringify({
                            cmd: "controlPlayback",
                            id:this.id,
                            control_code: 2,
                        }),
                        headers: {
                            Authorization:'token '+this.token,
                        },
                 });
                    this.stopLoading = false;
                    this.url = '';
                    this.id = ''
                    this.hasAudio = false;
                    
                    if (strict) {
                        this.log('严格模式：等待设备停止推流...')
                        let timeStart = Date.now();
                        await this.videoStream.waitForEnd;
                        this.log(`设备已断开,耗时${Date.now() - timeStart}ms.`)
                    }
                }
            } catch (e) {
                throw e
            } finally {
                this.stopLoading = false;

            }
        },
        //特殊处理特殊实现
        //在缓冲区内就拖动跳转，在外就重新请求响应流地址
        setProgress(percent) {
            const playBack = this.playBack,
                  originPercent = percent;
            if (playBack.offset) {
                percent = (percent - playBack.offset) / (100 - playBack.offset) * 100
            }
            playBack.progress = originPercent;
            if (percent >= 0 && originPercent <= playBack.bufferedProgress) {
                let second = Math.ceil(this.diffTime * (originPercent - playBack.offset) / 100 / 1000);
                this.player.seekTo(second);
            } else {
                this.reload(originPercent);
            }
        },
        //格式化进度条文字
        formatTip(percent) {
            // return this.formatTime(Math.round(percent * this.playBack.totalTime / 100));
            return moment(this.startDate).add(this.diffTime * percent / 100, 'ms').format('HH:mm:ss');
        },
        //秒数格式化成 HH:mm:ss
        formatTime(second) {
            return moment(this.startDate).add(second, 'seconds').format('HH:mm:ss');
        },
        loadSource(offset = 0) {
            this.playBack.offset = offset;
            if (this.sourceParams) {
                this._loadSource();
            }
        },
        reload(offset = 0) {
            this.playBack.offset = offset;
            return this._loadSource();
        },
        async stop() {
            this.isPlaying = false;
            // this.$emit('stop', this.chn);
            await this._freeSource();
        },
        toggleSound() {
            if (!this.loaded || !this.hasAudio) {
                return;
            }
            this.player.toggleMuted();
        },
        screenshot() {
            if (this.loaded) {
                this.player.screenShot(this.basicByVehicleId[this.vehicleId].plate);
            }
        },
    },
    created() {
        window.addEventListener("beforeunload", this.stop);
    },
    beforeDestroy() {
        window.removeEventListener("beforeunload", this.stop);
        this._freeSource(false);
        clearTimeout(this.controlTimeout);
    },
}

</script>
<style lang="scss">
    @import "../player";
</style>
<style lang='scss' scoped>

</style>

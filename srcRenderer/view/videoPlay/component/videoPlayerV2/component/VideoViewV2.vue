<template>
    <div class="video-view-v2">
        <!-- v-for="(item,index) in videoDataTemp" :key="item._key"> -->
        <!-- v-for="(item,index) in videoDataTemp" :key="index"> -->
        <div :class="['player-box']" ref="content">
            <div :class="[
          'player',
          {'maximum': maximum === index},
          { 'item-border': clickItemNum == index && videoDataList.length },
        ]" 
        :style="{width:boxWidth + '%',height: boxHeight + '%', display: (index+ 1)>windowNum?'none':'block'}" 
        @dblclick="toggleMaximum(index)" 
        @click="clickItem(index)" 
        v-for="(item, index) in videoDataTemp" :key="item._key">
                <!-- { maximum: maximum === index }, -->
                <CheckSimOverFlow :vehicleId="item.vehicleId" :terminalCode="item.terminalCode">
                    <RealtimePlayer ref="player" 
                        :vehicleId="item.vehicleId" 
                        :terminalCode="item.terminalCode" 
                        :channelTalkback="channelTalkback" 
                        :label="getLabel(item)" :symbol="item.plateNo" 
                        :chnN="item.channelNo" :labelAlwaysShow="true" 
                        :enableFullScreen="true" :useName="useName"
                         @useChange="useChange" :autoPlay="autoPlay" 
                         :disabled="item.disabled" @playing="playing" 
                         :inputType="item.inputType"
                         />
                </CheckSimOverFlow>
                <div class="close-btn" v-if="!item.disabled" @click="remove(index)">
                    <i class="pony-iconv2 pony-guanbi"></i>
                </div>
            </div>
        </div>

        <PonyDialog width="450" @confirm="ttsModalConfirm" title="请填写下发信息" hasMask v-model="ttsmodal.show">
            <el-form ref="ttsform" :model="ttsmodal.data" :rules="ttsmodal.rules" label-width="80px">
                <el-form-item label="模板">
                    <el-select style="width: 100%" v-model="ttsmodal.data.type" @change="
              ttsmodal.data.text = JSON.parse(
                JSON.stringify(ttsmodal.data.type)
              )
            ">
                        <el-option v-for="(item, index) in ttsmodal.modellist" :key="index" :label="item.configDesc" :value="item.configValue">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="信息" prop="text">
                    <el-input type="textarea" :rows="5" v-model="ttsmodal.data.text"></el-input>
                </el-form-item>
            </el-form>
        </PonyDialog>
    </div>
</template>

<script>
/**
 * @Author: yezy
 * @Email: <EMAIL>
 * @Date: 2020/3/31 13:40
 * @LastEditors: yezy
 * @LastEditTime: 2020/3/31 13:40
 * @Description:
 * 生成的_key很重要，影响组件复用
 */
import UUID from "@/assets/lib/uuid";
import CheckSimOverFlow from "@/view/videoPlay/component/videoPlayerV2/component/CheckSimOverFlow";
// import RealtimePlayer from "@/view/videoPlay/component/videoPlayerV2/component/RealtimePlayerHls";
import RealtimePlayer from "@/view/videoPlay/component/videoPlayerV2/RealtimePlayer";


export default {
    name: "videoView",
    _i18Name: "realTime1078",
    components: {
        RealtimePlayer,
        CheckSimOverFlow,
    },
    props: {
       
        windowNum: {
            type: Number,
            default: 4,
        },
        line: {
            type: Number
        },
        column: {
            type: Number
        },
        autoPlay: {
            type: Boolean,
            default: true,
        },
        channelTalkback: {
            type: [Number, String],
            default: 1,

        },
        channelNameList: {
            type: Object,
        },
        value: {
            type: String,
            default: "1",
        },
    },
    data() {
        return {
            loading: false,
            simFlowOver: false,
            maximum: -1,
            useName: false,
            // 选中
            clickItemNum: 0,
            videoDataList: [],
            defaultData: {
                plateNo: "",
                vehicleId: "",
                terminalCode: "",
                channelNo: 1,
                disabled: true,
                inputType:1,
                label:''
            },
            maxBoxNum:16,
            isFullScreen: false,
            ttsmodal: {
                modellist: [],

                show: false,
                _reject: null,
                _resolve: null,
                data: {
                    type: "",
                    text: "",
                },
                rules: {
                    text: [
                        { required: true, message: "请输入消息内容", trigger: "blur" },
                    ],
                },
            },
            clientWidth: '', // 总宽度
            clientHeight: '', // 总高度
        };
    },

    computed: {
        videoDataTemp() {
            let temp = [];
            if (this.videoDataList.length < this.maxBoxNum) {
                let defaultDataStr = JSON.stringify(this.defaultData);
                temp = new Array(this.maxBoxNum - this.videoDataList.length)
                    .fill(0)
                    .map((item) => {
                        return {
                            ...JSON.parse(defaultDataStr),
                            _key: UUID.genV4().toString(),
                        };
                    });
            }
            // this.videoDataList = this.videoDataList.concat(temp)
            return this.videoDataList.concat(temp).slice(0, this.maxBoxNum);
        },
        hasVideoPlaying() {
            return this.$refs["player"].some((item) => {
                return !item.isPaused;
            });
        },
        boxWidth() {
            return 100 / this.column
        },
        boxHeight() {
            return this.height = 100 / this.line
        }
    },

    mounted() {
        this.getTTSModelList();
        this.$emit("clickItemIndex", 0);
        this.clientHeight = this.$refs.content.clientHeight
        this.clientWidth = this.$refs.content.clientWidth
    },

    watch: {
        maxBoxNum:function (val) {
            this.videoDataList = this.videoDataList.slice(0, val);
            // for (let i = 0; i < this.videoDataList.length; i++) {
            //     let Str = this.value;
            //     let winNum = this.windowNum;
            //     this.$nextTick(() => {
            //         let BoxWidth = this.$refs["player"][i].$refs["playerBox"].offsetWidth;
            //         let BoxHeight = this.$refs["player"][i].$refs["playerBox"]
            //             .offsetHeight;
            //         this.$refs["player"][i].changeWidth(Str, BoxWidth, BoxHeight, winNum);
            //     });
            // }
        },

        //   监听这五个参数的变化，为了确保各种状态下切换宽高比，
        // 切换全屏状态，高度偶尔会缺失几十像素，退出全屏，再次进入就没问题。
        windowNum: function (val) {
            if(val>this.maxBoxNum){
                this.maxBoxNum = val;
            }
            let arr = JSON.parse(JSON.stringify(this.videoDataList)).slice(0,val);
            for (let i = 0; i < arr.length; i++) {
                let Str = this.value;
                let winNum = this.maxBoxNum;
                  let BoxWidth = this.$refs["player"][0].$refs["playerBox"].offsetWidth;
                    let BoxHeight = this.$refs["player"][0].$refs["playerBox"]
                        .offsetHeight;
                this.$nextTick(() => {
                  
                    this.$refs["player"][i].changeWidth(Str, BoxWidth, BoxHeight, winNum);
                });
            }
        },
        value: function (val) {
            let arr = JSON.parse(JSON.stringify(this.videoDataList)).slice(0,this.windowNum);
            // this.videoDataList = this.videoDataList.slice(0, this.windowNum);
            for (let i = 0; i < arr.length; i++) {
                let Str = this.value;
                let winNum = this.maxBoxNum;
                this.$nextTick(() => {
                    let BoxWidth = this.$refs["player"][i].$refs["playerBox"].offsetWidth;
                    let BoxHeight = this.$refs["player"][i].$refs["playerBox"]
                        .offsetHeight;
                    this.$refs["player"][i].changeWidth(Str, BoxWidth, BoxHeight, winNum);
                });
            }
        },
        maximum: function (val) {
            // 监听这个值判断是否双击视频，获取盒子的宽高
            let arr = JSON.parse(JSON.stringify(this.videoDataList)).slice(0,this.windowNum);
            let Str = this.value;
            let winNum = this.maxBoxNum;
            if (val != -1) {
                for (let i = 0; i < arr.length; i++) {
                    this.$nextTick(() => {
                        let BoxWidth = this.$refs["content"].offsetWidth;
                        let BoxHeight = this.$refs["content"].offsetHeight;
                        this.$refs["player"][i].changeWidth(
                            Str,
                            BoxWidth,
                            BoxHeight,
                            winNum
                        );
                    });
                }
            } else {
                for (let i = 0; i < arr.length; i++) {
                    this.$nextTick(() => {
                        let BoxWidth = this.$refs["player"][i].$refs["playerBox"]
                            .offsetWidth;
                        let BoxHeight = this.$refs["player"][i].$refs["playerBox"]
                            .offsetHeight;
                        this.$refs["player"][i].changeWidth(
                            Str,
                            BoxWidth,
                            BoxHeight,
                            winNum
                        );
                    });
                }
            }
        },
        isFullScreen: function (val) {
            let arr = JSON.parse(JSON.stringify(this.videoDataList)).slice(0,this.windowNum);
            // 监听这个值是判断是否点击全屏，获取全屏的宽高
            let Str = this.value;
            let winNum = this.maxBoxNum;
            if (val == true) {
                for (let i = 0; i < arr.length; i++) {
                    let BoxWidth = this.$refs["player"][i].$refs["playerBox"].offsetWidth;
                    let BoxHeight = this.$refs["player"][i].$refs["playerBox"]
                        .offsetHeight;
                    this.$nextTick(() => {
                        this.$refs["player"][i].changeWidth(
                            Str,
                            BoxWidth,
                            BoxHeight,
                            winNum
                        );
                    });
                }
            } else {
                for (let i = 0; i < arr.length; i++) {
                    let BoxWidth = this.$refs["player"][i].$refs["playerBox"].offsetWidth;
                    let BoxHeight = this.$refs["player"][i].$refs["playerBox"]
                        .offsetHeight;
                    this.$nextTick(() => {
                        this.$refs["player"][i].changeWidth(
                            Str,
                            BoxWidth,
                            BoxHeight,
                            winNum
                        );
                    });
                }
            }
        },
        useName: function (val) {
            let arr = JSON.parse(JSON.stringify(this.videoDataList)).slice(0,this.windowNum);
            // 监听这个值是判断每个盒子内是否点击全屏，获取全屏的宽高
            let Str = this.value;
            let winNum = this.maxBoxNum;
            if (val == true) {
                for (let i = 0; i < arr.length; i++) {
                    let BoxWidth = this.$refs["player"][i].$refs["playerBox"].offsetWidth;
                    let BoxHeight = this.$refs["player"][i].$refs["playerBox"]
                        .offsetHeight;
                    this.$nextTick(() => {
                        this.$refs["player"][i].changeWidth(
                            Str,
                            BoxWidth,
                            BoxHeight,
                            winNum
                        );
                    });
                }
            } else {
                for (let i = 0; i < arr.length; i++) {
                    let BoxWidth = this.$refs["player"][i].$refs["playerBox"].offsetWidth;
                    let BoxHeight = this.$refs["player"][i].$refs["playerBox"]
                        .offsetHeight;
                    this.$nextTick(() => {
                        this.$refs["player"][i].changeWidth(
                            Str,
                            BoxWidth,
                            BoxHeight,
                            winNum
                        );
                    });
                }
            }
        },
    },
    methods: {
        playing(val) {
            this.$emit('playing', val)
        },
        useChange() {
            this.useName = true;
            //   let target = this.$el;
        },
        clickItem(index) {
            this.clickItemNum = index;
            this.$emit("clickItemIndex", index);
            this.$emit("changeVehicle", this.videoDataTemp[index])
        },
        getLabel(data) {
            return data.plateNo + data.label
            return data.plateNo + " " + this.$t("common.chnName") + data.channelNo;
        },
        toggleMaximum(index) {
            this.maximum = index === this.maximum ? -1 : index;
        },
        videoEqual(obj, oth) {
            return (
                obj.vehicleId === oth.vehicleId &&
                obj.terminalCode === oth.terminalCode &&
                obj.channelNo === oth.channelNo
            );
        },
        async append(dataArray) {
            //检查相同视频
            let sameFlag = false;
            dataArray = dataArray.filter((item) => {
                let flag = true;
                for (let data of this.videoDataList) {
                    if (this.videoEqual(data, item)) {
                        flag = false;
                        sameFlag = true;
                        break;
                    }
                }
                return flag;
            });
            if (!dataArray.length) {
                if (sameFlag) {
                    this.$info(this.$ct("messageInfo[0]"));
                }
                return;
            }
            if (sameFlag) {
                this.$info(this.$ct("messageInfo[0]"));
            }

            // //检查是否有空余位置
            // let siteCount = this.windowNum - this.videoDataList.length;
            // if (siteCount < dataArray.length) {
            //     if (siteCount > 0) {
            //         dataArray = dataArray.slice(0, siteCount)
            //         this.$info('空余窗口不足，已忽略多余请求')
            //     } else {
            //         this.$info('没有空余的位置了')
            //         return;
            //     }
            // }

            //添加最终数据
            dataArray.forEach((item) => {
                item._key = UUID.genV4().toString();
            });
            let start = this.videoDataList.length;
            // this.videoDataList.push(...dataArray);
            this.videoDataList = this.videoDataList
                .concat(dataArray)
                .slice(-1 * this.windowNum);
            let end = this.videoDataList.length;

            if (start === this.windowNum) {
                end = this.windowNum;
                start = this.windowNum - dataArray.length;
            }
            // console.log('this.videoDataList 老夫怀疑你有毒', this.videoDataList);
        },
        remove(index) {
            this.videoDataList.splice(index, 1);
            this.$emit('removeMarker', this.videoDataList);
            // console.log(this.videoDataList);
        },
        removeChannel(id, terminalCode,) {
            this.videoDataList = this.videoDataList.filter(item => item.vehicleId != id && item.terminalCode != terminalCode)
        },
        async stopAll() {
            // console.log("自信自信");
            // console.log(this.videoDataList);
            // console.log(this.$refs['player']);

            // return Promise.all(this.videoDataList.map((item, index) => {
            //     return this.$refs['player'][index].stop();
            // }))
            // console.log(this.videoDataList);
            let temp = [];
            for (let i = 0; i < this.windowNum; i++) {
                // this.$refs['player'][i].terminalCode=this.videoDataList[i].terminalCode
                // this.$refs['player'][i].chn=this.videoDataList[i].channelNo

                temp.push(this.$refs["player"][i].stop());
            }
            // console.log(temp);
            await Promise.all(temp);
        },
        async playAll() {
            let temp = [];
            for (let i = 0; i < this.windowNum; i++) {
                temp.push(this.$refs["player"][i].reload());
            }
            await Promise.all(temp);
            // return Promise.all(this.videoDataList.map((item, index) => {
            //     return this.$refs['player'][index].reload();
            // }))
        },
        clear() {
            this.videoDataList = [];
        },
        // 全屏事件
        toggleFullScreen() {
            let target = this.$el;
            if (this.isFullScreen) {
                if (document.exitFullscreen) {
                    document.exitFullscreen();
                } else if (document.mozCancelFullScreen) {
                    document.mozCancelFullScreen();
                } else if (document.webkitCancelFullScreen) {
                    document.webkitCancelFullScreen();
                } else if (document.msExitFullscreen) {
                    document.msExitFullscreen();
                }
            } else {
                if (target.requestFullscreen) {
                    target.requestFullscreen();
                } else if (target.mozRequestFullScreen) {
                    target.mozRequestFullScreen();
                } else if (target.webkitRequestFullScreen) {
                    target.webkitRequestFullScreen();
                } else if (target.msRequestFullscreen) {
                    target.msRequestFullscreen();
                }
            }
            //   this.isFullScreen = !this.isFullScreen;
            //   console.log(this.isFullScreen, "this.isFullScreen");
        },
        async getTTSModelList() {
            let result = await this.$api.getTTS();
            if (
                !result ||
                result.status != 200 ||
                !result.data ||
                !result.data.length
            )
                return;
            this.ttsmodal.modellist = result.data;
            this.ttsmodal.data.type = result.data[0].configValue;
            this.ttsmodal.data.text = result.data[0].configValue;
        },

        showModal() {
            this.ttsmodal.show = true;
            return new Promise((resolve, reject) => {
                this.ttsmodal._reject = reject;
                this.ttsmodal._resolve = resolve;
            });
        },

        // 无语的结果 东西还要自己拼接的说！！！！！
        ttsModalConfirm() {
            this.$refs["ttsform"].validate(async (valid) => {
                if (!valid) return;
                this.ttsmodal.show = false;
                this.ttsmodal._resolve(this.ttsmodal.data.text);
            });
        },
    },
    created() {
        const toggleFullscreen = () => {
            this.isFullScreen = !this.isFullScreen;
            setTimeout(() => {
                if (
                    this.useName &&
                    this.$refs["player"].every((item) => !item.isFullScreen)
                ) {
                    this.useName = false;
                    this.isFullScreen = false;
                }
            }, 800);
        };
        document.addEventListener("fullscreenchange", toggleFullscreen);
        document.addEventListener("webkitfullscreenchange", toggleFullscreen);
    },
};
</script>

<style scoped lang="scss">
.video-view-v2 {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;

    .player-box {
        position: relative;
        width: 100%;
        height: 100%;
        display: flex;
        flex-wrap: wrap;

        .close-btn {
            position: absolute;
            right: 4px;
            top: 4px;
            line-height: 30px;
            height: 30px;
            width: 30px;
            text-align: center;
            border-radius: 50%;
            color: #e9eaef;
            background: rgba(0, 0, 0, 0.4);
            font-size: 16px;
            cursor: pointer;
            transition: opacity 0.8s;
            z-index: 3;
        }

        .player {
            position: relative;
            width: 50%;
            height: 50%;
            border: 1px solid transparent;
            z-index: 1;
            box-sizing: border-box;
            &.item-border {
                border: 1px solid #66ff66;
            }

            /deep/ video {
                object-fit: fill;
            }

            &.maximum {
                position: absolute !important;
                top: 0;
                left: 0;
                width: 100% !important;
                height: 100% !important;
                display: block;
                z-index: 999 !important;
            }
        }

        &.multiple-1 {
            .player {
                width: 100%;
                height: 100%;
            }
        }

        &.multiple-6 {
            .player {
                width: 33.333333%;
                height: 50%;
            }
        }

        &.multiple-9 {
            .player {
                width: 33.333333%;
                height: 33.333333%;
            }
        }
        &.multiple-10 {
            .player {
                width: 20%;
                height: 50%;
            }
        }

        &.multiple-16 {
            .player {
                width: 25%;
                height: 25%;
            }
        }
    }
}
</style>

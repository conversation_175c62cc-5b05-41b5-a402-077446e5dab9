<template>
    <div class="flv-video" ref="video"></div>
</template>

<script>
    /**
     * @Author: yezy
     * @Email: <EMAIL>
     * @Date: 2020/7/22 15:57
     * @LastEditors: yezy
     * @LastEditTime: 2020/7/22 15:57
     * @Description:
     */
    import UUID from '@/assets/lib/uuid';
    // import flvjs from '../lib/flv'
    import flvjs from 'flv-h265.js'

    import { sleep } from '../../../../../util/common';

    flvjs.LoggingControl.enableAll = false;
    export default {
        name: "flvVideo",
        props:{
            showDecodeInfo:{type:Boolean}
        },
        data() {
            return {
                _playerId: null,
                videoElm: null,
                flvPlayer: null,

                //提取一些video tag的状态，并让其响应式
            }
        },
        methods: {
            _onPlay(ev) {
                this.$emit('play', ev);
            },
            _onEnded(ev) {
                this.$emit('ended', ev);
            },
            _onLoadedData(ev) {
                this.$emit('loadeddata', ev);
            },
            _onProgress(ev) {
                this.$emit('progress', ev.target.buffered.length ? ev.target.buffered.end(0) : 0, ev);
            },
            _onTimeUpdate(ev) {
                this.$emit('timeupdate', ev.target.currentTime, ev);
            },
            _onPause(ev) {
                this.$emit('pause', true);
            },
            _onWaiting(ev) {
                this.$emit('waiting', ev);
                this.$emit('loading', true);//没有数据足以播放时触发
            },
            _onCanPlayThrough(ev) {
                this.$emit('canplaythrough', ev);
                this.videoElm.readyState === 4 && this.$emit('loading', false);//缓冲足够的数据时触发
            },
            _onSeeked(ev) {
                this.$emit('seeked', ev);
                this.$emit('loading', false);
            },
            _onError(error) {
                this.$emit('error', error);
            },
            _downloadFile(fileName, blob) {
                let aLink = document.createElement('a');
                let evt = document.createEvent("HTMLEvents");
                evt.initEvent("click", true, true);//initEvent 不加后两个参数在FF下会报错  事件类型，是否冒泡，是否阻止浏览器的默认行为
                aLink.download = fileName;
                aLink.href = URL.createObjectURL(blob);

                // aLink.dispatchEvent(evt);
                //aLink.click()
                aLink.dispatchEvent(new MouseEvent('click', {bubbles: true, cancelable: true, view: window}));//兼容火狐
            },
            _initDOM() {
                let videoElm = document.createElement('video');
                videoElm.style.width = '100%';
                videoElm.style.height = '100%';
                videoElm.style.objectFit = 'fill';
                videoElm.id = this.playerId;

                //bind events
                Object.entries(this.$options.methods).filter(([key]) => key.startsWith('_on')).forEach(([key, handler]) => {
                    let eventName = key.slice(3).toLowerCase();
                    this[key] = handler.bind(this);
                    videoElm.addEventListener(eventName, this[key]);
                })

                this.videoElm = videoElm;
                this.$el.appendChild(videoElm);
            },
            _destroy() {
                this.flvPlayer?.destroy();

                if (!this.videoElm) return;
                this.$el.removeChild(this.videoElm);
                this.videoElm = null;
            },
            init() {
                this._destroy();
                this._initDOM();
            },
            unload() {
                this.flvPlayer?.unload();
            },

            //加载资源
            async load(videoUrl, hasAudio) {
                if (!this.videoElm) {
                    throw new Error('the video element is not mounted');
                }
                this.flvPlayer?.destroy();
                let flvPlayer = this.flvPlayer = flvjs.createPlayer({
                    type: 'flv',
                    isLive: true,
                    hasAudio: hasAudio,//flvjs必须指定有无音轨才能解码对应视频
                    hasVideo: true,
                    enableStashBuffer: false,
                    url: videoUrl
                }, {
                    // enableWorker:true,
                    autoCleanupSourceBuffer: false,
                    enableStashBuffer: false,
                    stashInitialSize: 128,
                });
                flvPlayer.attachMediaElement(this.videoElm);
                flvPlayer.volume = 1;
                flvPlayer.muted = true;
                flvPlayer.on(flvjs.Events.STATISTICS_INFO, (arg) => {
                    if (this.showDecodeInfo) {
                        let decoded = arg.decodedFrames,
                            dropped = arg.droppedFrames;
                        this.$emit('decodeLog', `总帧数${decoded + dropped}, 已解码：${decoded}, 已丢弃：${dropped}${
                            dropped >= decoded / 10 ? ', 解码异常！' : '.'
                        }`)
                    }
                    this.$emit('speedChange', arg.speed.toFixed(1));
                });
                let successLoadP = new Promise((resolve, reject) => {
                    flvPlayer.on(flvjs.Events.ERROR, (error, type, detail) => {
                        reject({from: 'flvjs', error, type, detail})
                    });
                    flvPlayer.on(flvjs.Events.MEDIA_INFO, () => {
                        resolve()
                    });
                })
                //加载资源
                await flvPlayer.load();
                //如果加载资源出错（例如流地址404了)，那么下面这个promise就不会结束
                //await flvPlayer.play();
                //所以特殊处理
                // await successLoadP;
                // var playPromise = flvPlayer.play();
                // if (playPromise !== undefined) {
                //     playPromise.then(_ => {
                //         // Automatic playback started!
                //         // Show playing UI.
                //     })
                //      .catch(error => {
                //          // Auto-play was prevented
                //          // Show paused UI.
                //      });
                // }
                // await flvPlayer.play();
                await sleep(500)
                await flvPlayer.play();

                // setTimeout(() => {
                //     await flvPlayer.play();

                // },500)
            },
            /**
             * @param {Number} volume  0-1*/
            setVolume(volume) {
                this.flvPlayer.volume = volume;
            },

            toggleMuted(muted) {
                this.flvPlayer.muted = muted !== undefined ? muted : !this.flvPlayer.muted;
            },

            screenShot(filename) {
                let videoE = this.videoElm;
                let canvas = document.createElement('canvas');
                canvas.width = videoE.videoWidth;
                canvas.height = videoE.videoHeight;
                let ctx = canvas.getContext('2d');
                ctx.drawImage(videoE, 0, 0, canvas.width, canvas.height);
                canvas.toBlob((blob) => {
                    this._downloadFile(`${filename}_${moment().format('YYYY_MM_DD_HH_mm_SS')}.jpg`, blob);
                }, 'image/jpg', 1)
            },

            /**
             * @param {Number} time unit:second*/
            seekTo(time) {
                this.videoElm.currentTime = time;
            },

            playOrPause() {
                this.videoElm.paused ? this.flvPlayer?.play() : this.flvPlayer?.pause();
            },

            play() {
                this.flvPlayer?.play()
            },

            pause() {
                this.flvPlayer?.pause()
            },

            setPlayBackRate(value) {
                this.videoElm.playbackRate = value;
            },

        },
        mounted() {
            this.playerId = UUID.genV4();
            this.init();
            this.$emit('ready')
        },
        beforeDestroy() {
            this._destroy();
        }
    }
</script>

<style scoped lang="scss">
    .flv-video {
        width: 100%;
        height: 100%;
    }
</style>

<template>
		<div class="video-view-v2 resize-element">
				<div class='player-box' ref="content" >
						<div class="player"
						     :style="{width:boxWidth + 'px',height: boxHeight + 'px'}"
						     v-for="(item, index) in videoDataTemp"
						     :key="item._key"
						>
								<CheckSimOverFlow
										:vehicleId="item.vehicleId"
										:terminalCode="item.terminalCode"
								>
										<RealtimePlayer
												:boxComponents="[]"
												:inputType="item.inputType"
												ref="player"
												:vehicleId="item.vehicleId"
												:terminalCode="item.terminalCode"
												:label="getLabel(item)"
												:symbol="item.plateNo"
												:chnN="item.channelNo"
												:labelAlwaysShow="true"
												:enableFullScreen="true"
												:autoPlay="autoPlay"
												:disabled="item.disabled"
										/>
								</CheckSimOverFlow>
						</div>
				</div>
		</div>
</template>

<script>

import UUID from "@/assets/lib/uuid";
import CheckSimOverFlow from "@/view/videoPlay/component/videoPlayerV2/component/CheckSimOverFlow";
import RealtimePlayer from "@/view/videoPlay/component/videoPlayerV2/RealtimePlayer";

export default {
		name: "VideoViewPolling",
		components: {
				RealtimePlayer,
				CheckSimOverFlow,
		},
		props: {
				windowNum: {
						type: Number,
						default: 4,
				},
				line: {
						type: Number
				},
				column: {
						type: Number
				},
				autoPlay: {
						type: Boolean,
						default: true,
				},
				value: {
						type: String,
						default: "1",
				},
		},
		data() {
				return {
						loading: false,
						simFlowOver: false,
						// 选中
						clickItemNum: 0,
						videoDataList: [],
						clientWidth: '', // 总宽度
						clientHeight: '', // 总高度
						defaultData: {
								plateNo: "",
								vehicleId: "",
								terminalCode: "",
								channelNo: 1,
								disabled: true,
								inputType:1
						}
				};
		},

		computed: {
				videoDataTemp() {
						let temp = [];
						if (this.videoDataList.length < this.windowNum) {
								let defaultDataStr = JSON.stringify(this.defaultData);
								temp = new Array(this.windowNum - this.videoDataList.length)
										.fill(0)
										.map((item) => {
												return {
														...JSON.parse(defaultDataStr),
												};
										});
						}
            return this.videoDataList.concat(temp).map(item => {
                return {
                    ...item,
                    _key: UUID.genV4().toString(),
                }
            })
				},
				boxWidth() {
						return this.clientWidth / this.column
				},
				boxHeight() {
						return this.height = this.clientHeight / this.line
				}
		},
		mounted() {
				this.clientHeight = this.$refs.content.clientHeight
				this.clientWidth = this.$refs.content.clientWidth
				window.addEventListener('resize', this.handleResize);
		},
		methods: {
			handleResize(){
				this.clientHeight = this.$refs.content.clientHeight
				this.clientWidth = this.$refs.content.clientWidth
			},
				getLabel(data) {
						return data.plateNo + " " + this.$t("common.chnName") + data.channelNo;
				},
				// 添加视频通道
				async append(dataArray) {
						this.videoDataList = dataArray
				},
				async stopAll() {
						let temp = [];
						for (let i = 0; i < this.videoDataList.length; i++) {
								temp.push(this.$refs["player"][i].stop());
						}
						await Promise.all(temp);
						this.videoDataList = [];
				},
				async playAll() {
						let temp = [];
						for (let i = 0; i < this.videoDataList.length; i++) {
								temp.push(this.$refs["player"][i]._loadSource());
						}
						await Promise.all(temp);
				}
		},
		beforeDestroy() {
    // 移除事件监听器
    window.removeEventListener('resize', this.handleResize);
  },
};
</script>

<style scoped lang="scss">
.video-view-v2 {
		position: relative;
		width: 100%;
		height: 100%;
		overflow: hidden;

		.player-box {
				position: relative;
				width: 100%;
				height: 100%;
				display: flex;
				flex-wrap: wrap;

				.player {
						position: relative;
						width: 50%;
						height: 50%;
						border: 1px solid transparent;
						z-index: 1;
						box-sizing: border-box;

						&.item-border {
								border: 1px solid #66ff66;
						}

						/deep/ video {
								object-fit: fill;
						}
				}
		}
}
</style>

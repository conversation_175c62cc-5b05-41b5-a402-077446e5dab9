<template>
    <PonyDialog :show="show" :title="'实时视频  - '+currentVehicle.plate_no" :hasFooter="false" @close="(value)=>{$emit('close',value)}" @change="(value)=>{$emit('change',value)}" :is-fullscreen="true" :content-style="{
                    padding:0,
                    height:(channelNo > 6?610.2:420) + 'px',
                    'overflow-y':'auto'
                }" :resize="resizeOptions" :width="channelNo>4?884.8:563.2">
        <VideoView :vehicleId="vehicleId" :playType="playType" :inputType="currentVehicle.inputType" ref="videoView" :autoPlay="true" 
        :windowNum="channelNo>4?channelNo>6?channelNo>9?16:9:6:4" :channelNoList="channelNoList" :channelNoNameObj="channelNoNameObj" :chnNo="channelNo" @playing="isplaying"></VideoView>
        <template slot="headerContent">
            <span v-if="isStopMove && hasVideoPlaying">{{remaining}}秒后自动停止播放</span>
            <i class="pony-iconv2 pony-shuaxin pointer" style="margin-left: 10px" title="刷新" @click="refresh"></i>
        </template>
    </PonyDialog>
</template>

<script>
// height:(channelNo > 6?619.2:469.8) + 'px'
import VideoView from './VideoView'
import { mapState } from 'vuex';

export default {
    name: "popup-video",
    model: {
        prop: 'show',
        event: 'change'
    },
    props: {
        show: {
            type: Boolean,
            default: false,
        },
        playType: {
            type: String,
            default: 'flv',
        },
        vehicleId: {
            type: [String, Number],

        }
    },
    components: {
        VideoView,

    },
    computed: {
        ...mapState('auth', ['userInfo']),

    },
    data() {
        return {
            channelNo: 0,
            resizeOptions: {
                min: [563.2, 469.8],
                triggerHint: true,
                triggerPosition: ['bottomRight']
            },
            isStop: false,
            remaining: 300,
            channelNoList: [],
            channelNoNameObj:{},
            mouseMonitor: null,
            oldPageX: '',
            oldPageY: '',
            pageX: '',
            pageY: '',
            configTime: 300,
            hasVideoPlaying: false,
            isStopMove: false,
            currentVehicle: {
                    plate_no: '',
                    vehicle_id: '',
                    code: '',
                    inputType:1
            }
        }
    },
    watch: {
        vehicleId: {
            handler: 'init',
            immediate: true,
        },

    },
    async mounted() {
        // let res = await this.$api.operateMini1078Config({
        //     operate_type: 2,
        //     config_type: 70
        // })
        // if (res && res.status == 200) {
        //     this.configTime = res.data[70] ? res.data[70] * 60 : 300
        //     this.remaining = this.configTime
        // }
        window.addEventListener('mousemove', (e) => {
            this.pageX = e.pageX
            this.pageY = e.pageY
        })
    },
    methods: {
        isplaying(val) {
            let flag = false
            let list = this.$refs.videoView.$children
            for (let i = 0; i < list.length; i++) {
                if (list[i].isPlaying === true) {
                    flag = true
                    this.hasVideoPlaying = true
                    this.setMouseInterval()
                }
            }
            if (!flag) {
                this.hasVideoPlaying = false
                this.clearInterval()
            }
        },
        setMouseInterval() {
            if (this.mouseMonitor) {
                clearInterval(this.mouseMonitor)
            }
            this.mouseMonitor = setInterval(async () => {
                if (this.oldPageX === this.pageX && this.oldPageY === this.pageY) {
                    this.remaining -= 1
                    if (this.remaining <= 0) {
                        await this.$refs['videoView'].stopAll()
                        clearTimeout(this.mouseMonitor)
                        this.mouseMonitor = null
                    }
                    this.isStopMove = true
                } else {
                    this.remaining = this.configTime
                    this.isStopMove = false
                }
                this.oldPageY = this.pageY
                this.oldPageX = this.pageX
            }, 1000)
        },
        clearInterval() {
            if (this.mouseMonitor) {
                clearTimeout(this.mouseMonitor)
                this.mouseMonitor = null
            }
        },
        async checkSimFlowOver() {
                if (!this.$store.getters['main/need2CheckSim'] || !this.vehicleId || !this.currentVehicle.code) return false
                try {
                    let res = await this.$api.getSimFlowUsedInfo({
                        vehicle_id: this.vehicleId,
                        terminal_no: this.currentVehicle.code,
                    })
                    if (res.status === 200) {
		                    this.configTime = res.data.time_over * 60
		                    this.remaining = this.configTime
                    } else {
                        this.$error(res.message)
                    }
                } catch (e) {
                    this.$error('查询流量使用情况失败')
                } finally {
                }
            },
        async init() {
            if (!this.vehicleId) return
            Object.assign(this.currentVehicle, await this.$store.dispatch('vehicle/getVehicleInfo', {key: 'id', value: this.vehicleId}))
            await this.checkSimFlowOver()
            let result = await this.$api.getChannelNoByVehicleId({
                vehicle_id: this.vehicleId + ":V2"
            })

            this.channelNo = result.data.channel_count;
            this.currentVehicle.inputType = result.data.inputType
            // this.channelNoList=result.data.channel_valid_v2
            this.channelNoList = []
            result.data.channel_valid_name && result.data.channel_valid_name.length && result.data.channel_valid_name.forEach((item, index) => {
                    this.channelNoNameObj[item.no] = item.name
            })
            result.data.channel_valid_v2.forEach((item, index) => {
                if (item) {
                    this.channelNoList.push(index + 1)
                }
            })
		        if (!this.channelNoList.length) {
				        this.channelNoList = [0, 0, 0, 0]
		        }
            //用户配置的通道顺序
            if (this.channelNoList.length >= 4) {
                let res = await this.operateChannelSortQuery(2, this.vehicleId, this.channelNoList.length)
                if (res) {
                    this.channelNoList = this.sortByAnotherArray(this.channelNoList,res)
                    // this.channelNoList = res
                }
            }

        },
        sortByAnotherArray(arr1, arr2) {
            // 创建一个副本避免修改原数组
            const sortedArr = [...arr1];
            
            sortedArr.sort((a, b) => {
                const indexA = arr2.indexOf(a);
                const indexB = arr2.indexOf(b);
                
                // 如果两个元素都在arr2中，按arr2的顺序排序
                if (indexA !== -1 && indexB !== -1) {
                return indexA - indexB;
                }
                // 如果只有a在arr2中，a排在前面
                else if (indexA !== -1) {
                return -1;
                }
                // 如果只有b在arr2中，b排在前面
                else if (indexB !== -1) {
                return 1;
                }
                // 如果都不在arr2中，保持原顺序
                else {
                return 0;
                }
            });
            return sortedArr;
        },
        refresh() {
            this.$refs['videoView'].refresh();
        },
        /**
         * @operateType :2:查询, 1:新增或修改, -2:删除恢复默认
         * @vehicleid
         */
        async operateChannelSortQuery(operateType, objId, channelNum) {
            let res = await this.$api.operateChannelSort({
                operateType,
                objId,
            })
            if (!res || res.status != 200 || !res.data.length || !res.data.find(item => item.size == channelNum)) {
                return false
            }
            return res.data.find(item => item.size == channelNum).sort
        },
    },
    beforeDestroy() {
        this.clearInterval()
        window.removeEventListener('mousemove', () => {
        })
    },
}
</script>

<style scoped lang="scss">
</style>

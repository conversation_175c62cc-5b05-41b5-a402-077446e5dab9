<template>
		<div
				class="realtime-player pony-player"
				v-context-menu="contextMenuOptions"
				@mousemove="controlShow = true"
				@mouseleave="controlShow = false"
				ref="playerBox"
		>
				<div class="pony-player__video-wrap wrap " :id="'warp' + chnN" ref="flag">
						<div
								class="video-title"
								v-show="label && sourceParams"
								:style="{ opacity: labelAlwaysShow || controlShow ? 1 : 0 }"
						>
								{{ label }}
						</div>
						<div class="pony-player__log-wrap" v-if="showLog">
								<i class="pony-iconv2 pony-guanbi pointer" @click="showLog = false"></i>
						</div>
						<FlvVideo
								class="video"
								ref="player"
								:showDecodeInfo="showDecodeInfo"
								@decodeLog="log"
								@ready="_onPlayerMounted"
								@speedChange="_speedChange"
								@play="() => (this.isPlaying = true)"
								@pause="() => (this.isPlaying = false)"
								@ended="() => (this.isPlaying = false)"
						>
						</FlvVideo>
				</div>
				<div class="pony-player__main-wrap wrap">
						<!--控制条-->
						<div
								class="pony-player__controller"
								v-if="loaded && player && player.videoElm"
								:style="{ opacity: controlShow ? 1 : 0 }"
						>
								<span style="cursor: pointer;" @click="changeLiveStream" v-if="boxComponents.includes('stream') && inputType != 3">
										{{ streamType ? "子码流" : "主码流" }}
								</span>
								<i class="pony-iconv2 pony-shuaxin" v-if="loaded && boxComponents.includes('refresh')"
								   @click="reload"></i>
								<i

										class="pony-iconv2 pony-tingzhi"
										v-if="!player.videoElm.paused && boxComponents.includes('stop')"
										@click="stop"
								></i>
								<div class="flex-grow"></div>
								<span class="speed" v-if="boxComponents.includes('speed')">{{ currentSpeed }}KB/s</span>
								<TalkBack
										v-if="boxComponents.includes('recording') && inputType != 3"
										:terminalId="terminalCode"
                    :vehicleId="vehicleId"
										:chn="chnN"
										:inline="true"
										ref="talkBack"
										v-on="$listeners"
										expandBackground="rgba(255,255,255,0.05)"
								>
										<i
												slot="icon"
												slot-scope="{ allowClick, title }"
												:class="['pony-iconv2 pony-yuyin', { 'is-disabled': !allowClick }]"
												style="font-size: 16px"
												:title="`${$ct('talkBack')}：` + title"
										></i>
								</TalkBack>
								<div :class="['collapse-item',{'is-disabled':!loaded}]" v-if="boxComponents.includes('sound')">
										<i :class="['pony-iconv2 ',{'is-disabled':!loaded || !hasAudio},player.videoElm.muted?'pony-jingyin':'pony-shengyin']"
										   @click="toggleSound" :title="volumeHint"></i>
										<div class="pane" v-if="hasAudio">
												<el-slider
														v-model="volumeProcess"
														vertical
														@change="changeVolume"
														height="100px">
												</el-slider>
										</div>
								</div>
								<i
										v-if="boxComponents.includes('tts')"
										class="pony-iconv2 pony-ziti"
										:title="$ct('tts')"
										@click="ttsSend"
								></i>
								<i
										v-if="boxComponents.includes('photo')"
										:class="['pony-iconv2 pony-paizhao', { 'is-disabled': !loaded }]"
										:title="$ct('screenShot')"
										@click="screenshot()"
								></i>
								<i class="videoTime" v-show="recordStatus"><i class="el-icon-loading" v-if="recordLoading" style="font-size:16px"></i><i v-else>{{ videoTime }}</i></i>
								<i
										v-if="boxComponents.includes('video')"
										class="pony-iconv2 pony-shipin"
										:title="recordStatus ? '结束录制' : '开始录制'"
										@click="startRecord"
								></i>
								<i
										class="pony-iconv2"
										:class="[isFullScreen ? 'pony-suoxiao' : 'pony-quanping']"
										@click="toggleChange"
										v-if="enableFullScreen && boxComponents.includes('full')"
										:title="(isFullScreen ? $ct('exit') : '') + $ct('fullscreen')"
								></i>
								<el-popover
										v-if="boxComponents.includes('setting')"
										placement="top"
										width="320"
										trigger="click">
										<div class="block">
												<span class="demonstration">亮度</span>
												<el-slider v-model="settingValue.brightness" :min="0" :max="1" :step="0.1" :format-tooltip="formatter"></el-slider>
										</div>
										<div class="block">
												<span class="demonstration">对比度</span>
												<el-slider v-model="settingValue.contrast" :min="0" :max="1" :step="0.1" :format-tooltip="formatter"></el-slider>
										</div>
										<div class="block">
												<span class="demonstration">饱和度</span>
												<el-slider v-model="settingValue.saturate" :min="0" :max="1" :step="0.1" :format-tooltip="formatter"></el-slider>
										</div>
										<div class="block">
												<span class="demonstration">色度</span>
												<el-slider v-model="settingValue.rotate" :min="0" :max="360" :step="1" :format-tooltip="formatterRotate"></el-slider>
										</div>
										<i class="pony-iconv2 pony-shezhi" slot="reference"></i>
								</el-popover>
						</div>
						<div class="pony-player__mask wrap" v-else @click="loadSource">
								<i
										class="real-video"
										style="cursor: not-allowed;"
										:title="$ct('notSupportVideo')"
										v-if="notSupportVideo && disabled"
								>{{$ct('notSupportVideo')}}
								</i>
								<i
										class="real-video"
										style="cursor: not-allowed;"
										:title="$ct('notSupportVideo')"
										v-else-if="disabled"
								>点击播放实时视频
								</i>
								<i
										v-else-if="loading"
										class="el-icon-loading"
										style="font-size:40px"
								></i>
								<i
										v-else
										class="real-video"
										:title="$ct('play')"
								>{{ title }}</i>
						</div>
				</div>
			<!-- <template slot="allPony" v-if="downloadObj.show"> -->
				<PonyDialog :show="downloadObj.show" width="380" title="下载说明" class="pony-warp" :allowClose="false" id="ponyWarp">
					<p>请输入内容:</p>
					<el-input v-model="downloadObj.remark" type="textarea" placeholder="请输入内容" resize="none" style="margin:10px 0"></el-input>
					<template slot="footer">
						<el-button type="primary" @click="handleConfirmDownload">确认
						</el-button>
					</template>
				</PonyDialog>
			<!-- </template> -->
		</div>
</template>

<script>
import FlvVideo from "@/view/videoPlay/component/videoPlayerV2/component/FlvVideo";
import TalkBack from "@/view/videoPlay/component/TalkBack/TalkBack";

import strictEnd from "@/view/videoPlay/component/videoPlayerV2/mxin/strictEnd";
import fullscreen from "@/view/videoPlay/component/videoPlayerV2/mxin/fullscreen";
import log from "@/view/videoPlay/component/videoPlayerV2/mxin/log";
import BstandardUtil from "@/util/bstandard";
import {mapState} from "vuex";
// import flvjs from '@/assets/lib/flv'
    import flvjs from 'flv-h265.js'


import {sleep} from "@/util/common";
const API_URL = window.PONY.media.apiUrl;

/**
 * @Author: yezy
 * @Email: <EMAIL>
 * @Date: 2020/7/22 16:15
 * @LastEditors: xieyj
 * @LastEditTime: 2021/11/15 16:15
 * @Description:
 */
export default {
		name: "realtimePlayer",
		_i18Name: "videoPlayerPony",
		mixins: [strictEnd, fullscreen, log],
		components: {TalkBack, FlvVideo},
		props: {
				inputType:{
					type: [String, Number],
					default:1
				},
				vehicleId: {
						type: [String, Number],
				},
				terminalCode: {
						type: String,
				},
				channelTalkback:{
					type: [String, Number],
					default:1
				},
				chnN: {
						type: [Number, Object],
						default: 0,
				},
				symbol: {
						type: String,
				},
				label: {
						type: String,
						default:''
				},
				autoPlay: {
						type: Boolean,
						default: false,
				},
				labelAlwaysShow: {
						type: Boolean,
						default: false,
				},
				showClose: {
						type: Boolean,
						default: false,
				},
				enableFullScreen: {
						type: Boolean,
						default: false,
				},
				notSupportVideo:{
						type: Boolean,
						default: false,
				},
				disabled: {
						type: Boolean,
						default: false,
				},
				boxComponents: {
						type: [String, Array],
						default: function () {
								return ['stream', 'refresh', 'stop', 'speed', 'recording', 'sound', 'tts', 'photo', 'video', 'full', 'setting']
						}
				}
		},
		data() {
				return {
					downloadObj:{
						show:false,
						remark:'',
						resObj:null,
					},
						//录像的状态
						recordStatus: false,
						startTime: null,
						endTime: null,

						recorder: null,
						videoElement: null,
						chunks: [],
						videoTime: "00:00:00",
						timeRecord: null,
						recordLoading:false,
						flvPlayer:null,
						loaded: false, //当前是否有请求的加载的流
						//相关状态，不一定与内部video标签一致
						isPlaying: false,
						currentSpeed: 0,
						chn: this.chnN,
						player: null, //指向 $refs['player']
						sourceLoading: false,
						stopLoading: false,
						controlShow: false,
						loadAjax: null,
						url: "",
            id:null,
						hasAudio: false,
						//上一次的参数
						lastParams: {
								url: undefined,
						},
						timeOut: null,
						showLog: false,
						showDecodeInfo: false,
						contextMenuOptions: [
								{
										label: "显示日志",
										handler: () => {
												this.showLog = !this.showLog;
										},
								},
								{
										label: "显示解码信息",
										handler: () => {
												this.showDecodeInfo = !this.showDecodeInfo;
										},
								},
						],
						controlTimeout: null,
						//打断加载行为
						interruptLoadSourceHandle: null,

						streamType: 1, // 0 主码流 1 子码流
						title: '点击播放实时视频',
						settingValue: {
								brightness: 1,
								contrast: 1,
								saturate: 1,
								rotate: 0,
						},
						volumeProcess: 0
				};
		},
		computed: {
				...mapState("vehicle", ["basicByVehicleId"]),
				...mapState("auth", ["token"]),

				sourceParams: function () {
						if (this.vehicleId && this.terminalCode && this.chn !== undefined) {
								return `${this.vehicleId}-${this.terminalCode}-${this.chn}`;
						} else {
								return undefined;
						}
				},
				loading: function () {
						return this.sourceLoading || this.stopLoading;
				},
				volumeHint: function () {
						if (this.loaded && !this.hasAudio) {
								return this.$ct("notSupportAudio");
						} else {
								return "";
						}
				},

		},
		watch: {
				// 对比度，色度，饱和度，亮度
				settingValue: {
						handler: function (val) {
								let box = this.$refs.player.$refs.video
								let video = box.getElementsByTagName('video')[0]
								video.style.filter = `brightness(${val.brightness}) contrast(${val.contrast}) saturate(${val.saturate}) hue-rotate(${val.rotate}deg)`
						},deep: true
				},
				showLog: function (val) {
						this.contextMenuOptions[0].label = `${val ? "隐藏" : "显示"}日志`;
				},
				chnN: function (val) {
						this.chn = val;
				},
				showDecodeInfo: function (val) {
						this.contextMenuOptions[1].label = `${val ? "隐藏" : "显示"}解码信息`;
				},
				sourceParams: {
						handler: function (val, oldV) {
								// if (oldV) {
								//     let [vehicleId, terminalCode, chn] = oldV.split('-');
								//     this.lastParams.terminalCode = terminalCode;
								//     this.lastParams.chn = parseInt(chn);
								// }
								this.title = '点击播放实时视频'

								val && this.autoPlay && this._loadSource();
						},
						// immediate: true,//用onPlayerMounted替代
				},
				id: function (val, oldV) {
						this.lastParams.url = oldV;
				},
				controlShow: function (val, oldV) {
						if (!oldV && val) {
								clearTimeout(this.controlTimeout);
								this.controlTimeout = setTimeout(() => {
										this.controlShow = false;
								}, 3000); //3s后自动隐藏
						}
				},
				disabled: function (val, oldV) {
						if (!oldV && val) {
								this.stop();
						}
				},
		},
		methods: {
			async downloadConfirm(){
				this.downloadObj.resObj = null
				this.downloadObj.show = true
				let warp = this.$refs.playerBox
				let lay = document.querySelector('.layout') || document.querySelector('.mapLayOut__map') || document.querySelector('.el-main')
				await this.$nextTick(()=>{})
				let dom = warp.querySelector('#ponyWarp')
				lay.appendChild(dom)
				this.downloadObj.remark = ''
				return new Promise((res,rej)=>{
					this.downloadObj.resObj = res
				})
				
				
			},
			handleConfirmDownload(){
				this.downloadObj.show = false
				this.downloadObj.resObj(this.downloadObj.remark)
			},
				changeVolume(val){
						if(val == 0){
								this.$refs.player.toggleMuted(true);
						}else {
								this.$refs.player.setVolume(val/100)
								this.$refs.player.toggleMuted(false);
						}

				},
				formatterRotate(val) {
						return val + '°'
				},
				formatter(val) {
						return val * 100 + '%'
				},
				//   每个盒子内的全屏事件
				toggleChange() {
						this.$emit("useChange");
						this.toggleFullScreen();
				},
				// 宽高比改变
				changeWidth(row, BoxWidth, BoxHeight, winNum) {
					return
						if (winNum != 6 && winNum != 10) {
								if (row == "1") {
										this.$refs.flag.style.width = BoxWidth + "px";
										this.$refs.flag.style.height = BoxHeight + "px";
										this.$refs.flag.style.marginTop = 0 + "px";
										this.$refs.flag.style.marginLeft = 0 + "px";
								}
								if (row == "2") {
										let videoWidth = BoxWidth;
										let videoHeight = videoWidth / 2;
										let marginLeft = (BoxWidth - videoWidth) / 2;
										let marginTop = (BoxHeight - videoHeight) / 2;
										this.$refs.flag.style.width = videoWidth + "px";
										this.$refs.flag.style.height = videoHeight + "px";
										this.$refs.flag.style.marginLeft = marginLeft + "px";
										this.$refs.flag.style.marginTop = marginTop + "px";
								}
								if (row == "3") {
										let videoHeight = BoxHeight;
										let videoWidth = (videoHeight / 9) * 16;
										let marginLeft = (BoxWidth - videoWidth) / 2;
										let marginTop = (BoxHeight - videoHeight) / 2;

										this.$refs.flag.style.width = videoWidth + "px";
										this.$refs.flag.style.height = videoHeight + "px";
										this.$refs.flag.style.marginLeft = marginLeft + "px";
										this.$refs.flag.style.marginTop = marginTop + "px";
								}
								if (row == "4") {
										let videoWidth = (BoxHeight / 3) * 4;
										let videoHeight = BoxHeight;
										let marginLeft = (BoxWidth - videoWidth) / 2;
										this.$refs.flag.style.width = videoWidth + "px";
										this.$refs.flag.style.height = videoHeight + "px";
										this.$refs.flag.style.marginLeft = marginLeft + "px";
										this.$refs.flag.style.marginTop = 0 + "px";
								}
								if (row == "5") {
										let videoWidth = BoxHeight;
										let videoHeight = BoxHeight;
										let marginLeft = (BoxWidth - videoWidth) / 2;
										this.$refs.flag.style.width = videoWidth + "px";
										this.$refs.flag.style.height = videoHeight + "px";
										this.$refs.flag.style.marginLeft = marginLeft + "px";
										this.$refs.flag.style.marginTop = 0 + "px";
								}
						} else if (winNum == 10) {
								if (row == "1") {
										this.$refs.flag.style.width = BoxWidth + "px";
										this.$refs.flag.style.height = BoxHeight + "px";
										this.$refs.flag.style.marginTop = 0 + "px";
										this.$refs.flag.style.marginLeft = 0 + "px";
								}
								if (row == "2") {
										let videoWidth = BoxWidth;
										let videoHeight = videoWidth / 2;
										let marginLeft = (BoxWidth - videoWidth) / 2;
										let marginTop = (BoxHeight - videoHeight) / 2;
										this.$refs.flag.style.width = videoWidth + "px";
										this.$refs.flag.style.height = videoHeight + "px";
										this.$refs.flag.style.marginLeft = marginLeft + "px";
										this.$refs.flag.style.marginTop = marginTop + "px";
								}
								if (row == "3") {
										let videoWidth = BoxWidth;
										let videoHeight = (videoWidth / 16) * 9;

										let marginLeft = (BoxWidth - videoWidth) / 2;
										let marginTop = (BoxHeight - videoHeight) / 2;
										this.$refs.flag.style.width = videoWidth + "px";
										this.$refs.flag.style.height = videoHeight + "px";
										this.$refs.flag.style.marginLeft = marginLeft + "px";
										this.$refs.flag.style.marginTop = marginTop + "px";
								}
								if (row == "4") {
										let videoWidth = BoxWidth;
										let videoHeight = (videoWidth / 4) * 3;
										let marginLeft = (BoxWidth - videoWidth) / 2;
										let marginTop = (BoxHeight - videoHeight) / 2;
										this.$refs.flag.style.width = videoWidth + "px";
										this.$refs.flag.style.height = videoHeight + "px";
										this.$refs.flag.style.marginLeft = marginLeft + "px";
										this.$refs.flag.style.marginTop = marginTop + "px";
								}
								if (row == "5") {
										let videoWidth = BoxWidth;
										let videoHeight = BoxWidth;
										let marginLeft = (BoxWidth - videoWidth) / 2;
										let marginTop = (BoxHeight - BoxWidth) / 2;
										this.$refs.flag.style.width = videoWidth + "px";
										this.$refs.flag.style.height = videoHeight + "px";
										this.$refs.flag.style.marginLeft = marginLeft + "px";
										this.$refs.flag.style.marginTop = marginTop + "px";
								}
						} else {
								if (row == "1") {
										this.$refs.flag.style.width = BoxWidth + "px";
										this.$refs.flag.style.height = BoxHeight + "px";
										this.$refs.flag.style.marginTop = 0 + "px";
										this.$refs.flag.style.marginLeft = 0 + "px";
								}
								if (row == "2") {
										let videoWidth = BoxWidth;
										let videoHeight = videoWidth / 2;
										let marginLeft = (BoxWidth - videoWidth) / 2;
										let marginTop = (BoxHeight - videoHeight) / 2;
										this.$refs.flag.style.width = videoWidth + "px";
										this.$refs.flag.style.height = videoHeight + "px";
										this.$refs.flag.style.marginLeft = marginLeft + "px";
										this.$refs.flag.style.marginTop = marginTop + "px";
								}
								if (row == "3") {
										let videoWidth = BoxWidth;
										let videoHeight = (videoWidth / 16) * 9;
										let marginLeft = (BoxWidth - videoWidth) / 2;
										let marginTop = (BoxHeight - videoHeight) / 2;
										this.$refs.flag.style.width = videoWidth + "px";
										this.$refs.flag.style.height = videoHeight + "px";
										this.$refs.flag.style.marginLeft = marginLeft + "px";
										this.$refs.flag.style.marginTop = marginTop + "px";
								}
								if (row == "4") {
										let videoWidth = BoxWidth;
										let videoHeight = (videoWidth / 4) * 3;
										let marginLeft = (BoxWidth - videoWidth) / 2;
										let marginTop = (BoxHeight - videoHeight) / 2;
										this.$refs.flag.style.width = videoWidth + "px";
										this.$refs.flag.style.height = videoHeight + "px";
										this.$refs.flag.style.marginLeft = marginLeft + "px";
										this.$refs.flag.style.marginTop = marginTop + "px";
								}
								if (row == "5") {
										let videoWidth = BoxWidth - 30;
										let videoHeight = BoxWidth - 30;
										let marginLeft = (BoxWidth - videoWidth) / 2;
										let marginTop = (BoxHeight - BoxWidth) / 2;
										this.$refs.flag.style.width = videoWidth + "px";
										this.$refs.flag.style.height = videoHeight + "px";
										this.$refs.flag.style.marginLeft = marginLeft + "px";
										this.$refs.flag.style.marginTop = marginTop + "px";
								}
						}
				},
				//录像有声道的解码有问题,我加载一个没声道的(我t母亲的也不知道为什么有声道的有问题.暂时只能用这么鸡肋的方法)
				async initNoAudio(){
					this.videoElement = document.createElement('video')
					try{
						this.recordLoading = true
						this.flvPlayer = flvjs.createPlayer({
							type: 'flv',
							isLive: true,
							hasAudio: false,//flvjs必须指定有无音轨才能解码对应视频
							hasVideo: true,
							enableStashBuffer: false,
							url: this.url
						}, {
                    // enableWorker:true,
                    autoCleanupSourceBuffer: false,
                    enableStashBuffer: false,
                    stashInitialSize: 128,
                })
						this.flvPlayer.attachMediaElement(this.videoElement);
						//加载资源
						await this.flvPlayer.load();
            await sleep(500)
						//如果加载资源出错（例如流地址404了)，那么下面这个promise就不会结束
						await this.flvPlayer.play();
						this.recordLoading = false
					}catch(e){
						this.recordLoading = false
					}
					
				},
				//录像
				startRecord() {
						//已经在录像的
						if (this.recordStatus) {
								this.stopScreen();
						} else {
								this.startScreen();
						}
				},
				//开始录像
				async startScreen() {
						this.recordStatus = true;
						if(!this.hasAudio){
							this.videoElement = this.player.videoElm
						}else {
							await this.initNoAudio()
						}
						this.chunks = [];
						//创建MediaRecorder，设置媒体参数
						const stream = this.videoElement.captureStream(25);
						// MediaRecorder  API
						this.recorder = new MediaRecorder(stream, {
								mimeType: "video/webm;codecs=vp8",
								videoBitsPerSecond: 6500000, //默认是2500000，说是越高越清晰，但是感觉没什么用
						});
						//收集录制数据
						this.recorder.ondataavailable = (e) => {
								this.chunks.push(e.data);
						};
						this.startTime = new Date().getTime();
						this.recorder.start(10);
						this.timeDuration();
				},
				
				async stopScreen() {
						this.recorder.stop();
						if(this.flvPlayer){
							this.flvPlayer.unload()
						}
						this.endTime = new Date().getTime()
						this.recorder = null;
						clearInterval(this.timeRecord);
						this.timeRecord = null;
						await this.downloadConfirm()
						this.recordStatus = false;
					
						this.download();
						
						this.videoTime = '00:00:00'
				},
				// 录像的时间记录
				timeDuration() {
						this.timeRecord = setInterval(() => {
								let second = new Date().getTime() - this.startTime;
								this.videoTime = this.SecondFormat(second);
						}, 1000);
				},
				async toSaveVideo(file){
					var formData = new FormData();
						formData.append("key", 'mediac');
						formData.append("file", file);
						formData.append("absolute", 1);

					let resUrl = await $.ajax({
						type: "post",
						url: '/ponysafety2/a/fileupload/common',
						data: formData,
						dataType: "json",
						contentType: false,
						processData: false,
						cache: false,
					});
					
					let res = await this.$api.videoCutSave({
						url:resUrl.url,
						row:{
							vehicle_id:this.vehicleId,
							terminal_no:this.terminalCode,
							channel_no:this.chnN,
							remark:this.downloadObj.remark,
							start_time:this.startTime,
							end_time:this.endTime
						}
					})
					if(res && res.status == 200){
						this.$success('录制成功')
					}
				},
				download() {
						let blob = new Blob(this.chunks);
						let url = window.URL.createObjectURL(blob);
						let myFile = new File([blob], "demo.mp4", { type: 'video/mp4' });
						let link = document.createElement("a");
						link.href = url;
						this.toSaveVideo(myFile)
						link.download =
								this.basicByVehicleId[this.vehicleId].plate +
								"通道" +
								this.chnN +
								".mp4";
						link.style.display = "none";
						document.body.appendChild(link);
						link.click();
						link.remove();
				},
				changeLiveStream() {
						this.streamType = this.streamType ? 0 : 1;
						this.reload();
				},
				async _speedChange(speed) {
						this.currentSpeed = speed;
				},
				async _loadSource() {
						await this._freeSource();
						await this._requestSource();
						
				},
				/*
						申请占用此设备通道，
						目前实时视频的推流在不同的客户端会复用,
						这是一个非常耗时的流程，需要实现其中任意阶段可暂停
						*/
				async _requestSource() {
						// console.log(this.chn);
						
						this.sourceLoading = true;
						try {
								this.log("正在请求视频资源...");
								if (typeof this.chn !== "number") return;
								let ajaxObj = {}
								// if(this.inputType == 3){
								// 	ajaxObj = {
								// 			url: API_URL809,
								// 			type: "post",
								// 			data: JSON.stringify({
								// 					cmd: "downRealvideoMsgStartup",
								// 					vehicleNo:this.basicByVehicleId[this.vehicleId].plate,
								// 					channelId: this.chn,
								// 					avItemType: 0,
								// 			}),
								// 	}
								// }else {
									ajaxObj = {
											url: API_URL,
											type: "post",
											data: JSON.stringify({
													cmd: "live",
													terminal_no: this.terminalCode,
                          vehicle_id:this.vehicleId,
													channel_no: this.chn,
													stream_type: this.streamType,
													media_type: 0,
											}),
											headers: {
													Authorization: "token " + this.token,
											},
									}
								// }
								let ajax = $.ajax(ajaxObj);
								this.loadAjax = ajax;
								let result
								if (this.timeOut) {
										clearTimeout(this.timeOut)
								}
								this.timeOut = setTimeout(() => {
										if (!result) {
												// this.$error("获取视频地址失败,超时!");
												if (this.label && this.label.split(' ')[1] !== undefined) {
														this.title = this.label.split(' ')[0] + '-' + this.label.split(' ')[1] + '获取视频地址失败,超时!'
												} else {
														this.title = this.label + '获取视频地址失败,超时!'
												}
												ajax.abort()
												this.loadAjax = null;
												return
										}
								}, 20 * 1000)
								result = await ajax;
								if (!result || result.code != 0) {
										clearTimeout(this.timeOut)
										this.loadAjax = null;
										// this.$error("获取视频地址失败");
										if (this.label && this.label.split(' ')[1] !== undefined) {
											// if(this.inputType == 3){
											// 	if(result.status != 200 && result.code == 500){
											// 		this.title = this.label.split(' ')[0] + '-' + this.label.split(' ')[1] + result.message || '获取视频地址失败'
											// 	}else {
											// 		this.title = this.label.split(' ')[0] + '-' + this.label.split(' ')[1] + result.data || '获取视频地址失败'
											// 	}
											// }else {
											// 	this.title = this.label.split(' ')[0] + '-' + this.label.split(' ')[1] + result.message || '获取视频地址失败'
											// }
												this.title = this.label.split(' ')[0] + '-' + this.label.split(' ')[1] + result.message || '获取视频地址失败'

										} else {
											// if(this.inputType == 3){
											// 	if(result.status != 200 && result.code == 500){
											// 		this.title = this.label + result.message|| '获取视频地址失败'
											// 	}else {
											// 		this.title = this.label + result.data|| '获取视频地址失败'
											// 	}
											// }else {
											// 	this.title = this.label + result.message||'获取视频地址失败'

											// }
												this.title = this.label + result.message||'获取视频地址失败'

										}
										return
								}

								this.loadAjax = null;
								if (typeof result !== "object") {
										result = JSON.parse(result);
								}
								if (result.code == 0) {
										clearTimeout(this.timeOut)

										this.log("成功获取视频地址，开始缓冲...");
										this.url = result.data;
                    this.id = result.id
										this.hasAudio = result.hasAudio;
										this.title = '重新加载实时视频'
										await this.$api.setVideoLog({
												uri: this.url, // 视频接口地址
												type: 501, // 日志类型(501: 实时视频; 502: 录像回放)
												vehicleId: this.vehicleId, // 车辆id
										});
										await Promise.race([
												//允许中断操作
												new Promise((resolve, reject) => {
														this.interruptLoadSourceHandle = {resolve, reject};
												}),
												Promise.all([
														//   (async () => {
														//     try {
														//       //向ws订阅流的相关信息 这一步骤不是必要的
														//       //有可能会得不到回复
														//       await this._subscribeVideoInfo(this.url);
														//     } catch (e) {
														//       //报错的话 就忽略这次 订阅推流结束事件的行为（忽略严格模式）
														//       this.videoStream._resolve();
														//     }
														//   })(),
														//加载视频
														this.$refs["player"]?.load(this.url, this.hasAudio),
												]),
										]);
										this.interruptLoadSourceHandle.resolve();
										this.interruptLoadSourceHandle = null;
										this.loaded = true;
										// 向父组件暴露一个方法，用于判断视频是否正在播放
										this.$emit('playing', this.loaded)
										this.log("开始播放视频.");
								}
								// else {
								// 		clearTimeout(this.timeOut)
								// 		throw {from: "api", msg: result.message || '失败888'};
								// }
						} catch (e) {
								clearTimeout(this.timeOut)
								switch (true) {
										case e.statusText === "abort":
												break;
										case e.from === "flvjs" && e.error === "NetworkError":
												// this.$error("视频地址已失效.");
												this.title = '视频地址已失效'
												this.log(`请求视频资源失败,原因：视频地址已失效.`);
												break;
										case e.from === "api":
												// this.$error(this.$ct("messageInfo.2") + `: ${e.msg}`);
												if (this.label && this.label.split(' ')[1] !== undefined) {
														this.title = this.label.split(' ')[0] + '-' + this.label.split(' ')[1] + ` ${e.msg}`
												} else {
														this.title = this.label + ` ${e.msg}`
												}
												this.log(`请求视频资源失败,原因：${e.msg}`);
												break;
										case e.from === "interrupt":
												break;
										default:
												console.error(e);
								}
								//报错的话 就忽略这次 订阅推流结束事件的行为（忽略严格模式）
								this.videoStream._resolve instanceof Function &&
								this.videoStream._resolve();
						} finally {
								this.sourceLoading = false;
						}
				},
				/*释放设备通道资源
				 * strict= true 时采取严格模式，即明确得到设备断开提示才会结束*/
				async _freeSource(strict = true) {
					if (this.recordStatus) {
							this.stopScreen();
					}
						try {
								this.stopLoading = true;
								this.loadAjax && this.loadAjax.abort();
								if (this.interruptLoadSourceHandle) {
										this.interruptLoadSourceHandle.reject({
												from: "interrupt",
												msg: "人工打断",
										});
										this.interruptLoadSourceHandle = null;
								}
								if (this.loaded) {
										this.loaded = false;
										this.player.unload();
										this.log("正在释放请求的资源...");
										let ajaxObj = {}
										// if(this.inputType == 3){
										// 	ajaxObj = {
										// 		url: API_URL809,
										// 		type: "post",
										// 		data: JSON.stringify({
										// 				cmd: "downRealvideoMsgEnd",
										// 				vehicleNo:this.basicByVehicleId[this.vehicleId].plate,
										// 				channelId: this.chn,
										// 				avItemType: 0,
										// 		}),
										// 	}
										// }else {
											ajaxObj = {
												url: API_URL,
												type: "post",
												data: JSON.stringify({
														cmd: "controlLive",
														id: this.lastParams.url ? this.lastParams.url : this.id,
														control_code: 0,
												}),
												headers: {
														Authorization: "token " + this.token,
												},
											}
										// }
										await $.ajax(ajaxObj);
										this.url = "";
										this.id = "";
										this.hasAudio = false;
										this.lastParams = {
												url: undefined,
										};
										this.$emit('playing', this.loaded)
										if (strict) {
												this.log("严格模式：等待设备停止推流...");
												let timeStart = Date.now();
												await Promise.race([
														new Promise(async (resolve) => {
																await this.videoStream.waitForEnd;
																this.log(`设备已断开,耗时${Date.now() - timeStart}ms.`);
																resolve();
														}),
														new Promise(async (resolve) => {
																await this.$utils.sleep(500);
																if (this.videoStream.waitForEnd) {
																		this.log(`等待断开时间超过500ms,已忽略`);
																		this.videoStream._reject("忽略等待");
																}
																resolve();
														}),
												]);
										}
								}
								this.$refs["talkBack"]?.clearWs();
						} catch (e) {
								throw e;
						} finally {
								this.stopLoading = false;
						}
				},
				//确保首次执行时$refs['player']已存在
				_onPlayerMounted() {
						this.player = this.$refs["player"];
						this.sourceParams && this.autoPlay && this._loadSource();
						this.log("播放器初始化完成");
				},

				loadSource() {
						if (typeof this.chn !== "number") {
								this.chn = this.chn.value;
						}

						if (this.sourceParams) {
								this._loadSource();
						}
				},
				reload() {
						//   console.log(this.chnN);
						//   this.chn=this.chnN
						return this._loadSource();
				},
				stop() {
						this.isPlaying = false;
						return this._freeSource();
				},
				toggleSound() {
						if (!this.loaded || !this.hasAudio) {
								return;
						}
						this.player.toggleMuted();
				},
				screenshot() {
						if (this.loaded) {
								this.player.screenShot(this.symbol);
						}
				},
				async ttsSend() {
						let value = "";
						if (this.$parent.showModal) {
								value = await this.$parent.showModal();
						} else if (this.$parent.$parent.showModal) {
								value = await this.$parent.$parent.showModal();
						} else if (this.$parent.$parent.$parent.showModal) {
								value = await this.$parent.$parent.$parent.showModalTTS();
						} else {
								return
						}
						// let {value} = await this.$prompt(this.$ct('ttsTip'), this.$ct('tts'))
						await BstandardUtil.init();
						let bits = Array.from({length: 6}).fill(0);
						bits[0] = 0;
						bits[2] = 1;
						bits[3] = 1;
						bits[4] = 0;
						bits[5] = 0;
						bits = bits.reverse();
						let res = await this.$api.sendMiniStandardCommon({
								vehicle_terminal_list: [
										{
												vehicle_id: this.vehicleId,
												terminal_no: this.terminalCode,
										},
								],
								operate_key: "SendSMS",
								cmd_sms: {
										type: parseInt(bits.join(""), 2),
										text: value,
								},
						});
						let wsRes = await BstandardUtil.waitForResponse(res.data);
						if (wsRes && wsRes.task_state === 0) {
								this.$success(this.$ct("messageInfo.1"));
								return true;
						} else {
								this.$message(this.$ct("messageInfo.0"));
								return false;
						}
						await BstandardUtil.destroy();
				},
		},
		created() {
				window.addEventListener("beforeunload", this.stop);
				// this.$nextTick(() => {
				// 		this.changeWidth(
				// 				this.$parent.$parent.value,
				// 				this.$refs["playerBox"].offsetWidth,
				// 				this.$refs["playerBox"].offsetHeight,
				// 				this.$parent.$parent.windowNum
				// 		);
				// });
		},
		beforeDestroy() {
				window.removeEventListener("beforeunload", this.stop);
				this._freeSource(false);
				clearTimeout(this.controlTimeout);
		},
		deactivated() {
				let isPlaying = !this.player.videoElm.paused;
				if (isPlaying) {
						this.log("切换至后台，断开设备链接");
						this._freeSource(false);
						this.$once("hook:activated", () => {
								this._loadSource();
						});
				}
		},
};
</script>

<style lang="scss">
@import "player";
</style>
<style scoped lang="scss">
.realtime-player {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		flex-grow: 1;

		#canvas {
				width: 100%;
				height: 100%;
		}

		.real-video {
				font-size: 13px;
				font-weight: bold;
				font-style: normal;
		}

		.videoTime {
				position: absolute;
				right: 0;
				top: -5px;
				background-color: black;
				color: red;
				display: inline-block;
				line-height: 20px;
				font-style: normal;
				font-size: 12px;
				padding: 3px 8px;
				border-radius: 3px;
				i {
					display: inline-block;
					width: 46px;
					color: red;
					font-size: 12px;
					font-style: normal;
					text-align: center;
				}
		}

		.video {
				height: calc(100% - 20px);
		}

		.pony-player__controller > span {
				margin: 0;
		}
}
</style>

<template>
    <PonyDialog v-model="show" :hasFooter="false" :hasMask="false" :width="400"
                @close="handleClose" class="popup-talk-back-video"
                contentStyle="height:300px;padding:0"
    >
        <TalkBack :terminalId="terminalId" :chn="1" :inline="true" ref="talkBack" slot="headerContent" :vehicleId="vehicleId"></TalkBack>
        <VideoView type="realTime" :windowNum="1" :code="terminalId" :symbol="plateNo" :vehicleId="vehicleId"
                   :chn="1" ref="videoPlayer" :autoPlay="false"
        ></VideoView>
    </PonyDialog>
</template>

<script>
    /**
     * @Author: yezy
     * @Email: <EMAIL>
     * @Date: 2019/9/3 11:07
     * @LastEditors: yezy
     * @LastEditTime: 2019/9/3 11:07
     * @Description: 建德对讲方案弹窗
     */
    import TalkBack from './TalkBack';
    import VideoView from '../videoPlayer/VideoView'

    export default {
        name: "popup-talk-back-video",
        components: {
            TalkBack,
            VideoView
        },
        data() {
            return {
                show: false,
                terminalId: '48184159',
                plateNo: '沪AUT615（21）',
                vehicleId: 4301,
            }
        },
        methods: {
            handleClose() {
                this.$refs['talkBack'].clearWs();
                this.show = false;
            },
            async showModal(vehicleId, plateNo) {
                let terminalId = (await this.$store.dispatch('mediaPlatform/getVehicleTerminalInfo', {
                    id: this.vehicleId
                })).data;
                this.terminalId = terminalId;
                this.vehicleId = vehicleId;
                this.plateNo = plateNo;
                this.show = true;
            },
            closeModal() {
                this.show = false;
            }
        }
    }
</script>

<style scoped lang="scss">
    .popup-talk-back-video {
    }
</style>

<template>
    <PonyDialog v-model="show" :title="title" :hasFooter="false" :hasMask="false" :width="300" @close="handleClose">
        <TalkBack :terminalId="terminalId" :chn="chn" ref="talkBack" :alwaysExpand="true" expandWidth="100%" :vehicleId="vehicleId"></TalkBack>
    </PonyDialog>
</template>

<script>
    /**
     * @Author: yezy
     * @Email: <EMAIL>
     * @Date: 2019-07-15 16:12:59
     * @LastEditors: yezy
     * @LastEditTime: 2019-07-15 16:12:59
     * @Description: 对讲功能实现，全局对讲弹出框 //18461145184
     */
    import {mapState, mapMutations} from 'vuex';
    import TalkBack from './TalkBack';

    export default {
        name: "popup-talk-back",
        components: {
            TalkBack,
        },
        data() {
            return {
                show: false,
                terminalId: null,
                vehicleId:null,
                chn:1,
                title: ''
            }
        },
        methods: {
            handleClose() {
                this.$refs['talkBack'].clearWs();
                this.show = false;
            },
            showModal(terminalId, chn,title = '车辆对讲',vehicleId) {
                this.terminalId = terminalId;
                this.chn = chn
                this.title = title;
                this.vehicleId = vehicleId;
                this.show = true;
            }
        }
    }
</script>

<style scoped lang="scss">

</style>

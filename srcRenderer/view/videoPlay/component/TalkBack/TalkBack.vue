<template>
		<div class="talkBack" :data-state="step" :title="StepMsg[step] || $ct('unknowStep')" :style="boxStyle">
				<div :class="['info-content text',{'text--danger':isErrorInfo}]" :style="infoStyle">
						<p>{{StepMsg[step] || $ct('unknowStep')}}</p>
				</div>
				<el-button v-if="!inline" icon="pony-iconv2 pony-maikefeng" circle type="primary" plain style="margin-left: 5px" :title="StepMsg[step]"
				           :disabled="!allowClick"
				           @click="getTalkBackLink()"></el-button>
				<el-button v-else type="text" :title="StepMsg[step]" :disabled="!allowClick"
				           @click="getTalkBackLink()">
						<slot name="icon" v-bind="{allowClick,title:StepMsg[step]}">
								<i class="pony-iconv2 pony-maikefeng"></i>
						</slot>
				</el-button>
		</div>
</template>

<script>
/**
 * @Author: yezy
 * @Email: <EMAIL>
 * @Date: 2019-07-15 17:15:03
 * @LastEditors: yezy
 * @LastEditTime: 2019-07-15 17:15:03
 * @Description: 双向对讲
 */
const BASE_URL_API = window.PONY.media.apiUrl;
import Recorder from './Recoder'
import {mapState} from 'vuex'

const StepState = {
		DEFAULT: 0,
		WAITING_MICROPHONE_PERMISSION: 1,
		NOT_SUPPORTED: 2,
		GET_MICROPHONE_PERMISSION_FAILURE: 3,
		GET_MICROPHONE_PERMISSION_SUCCESS: 4,
		CLICK_TO_CONNECT_DEVICE: 5,
		CONNECTING_DEVICE: 6,
		CONNECTED_DEVICE_FAILURE: 7,
		CONNECTED_DEVICE_SUCCESS: 8,
		DEVICE_DISCONNECT: 9,
    RECEIVE_MESSAGE:10
}
export default {
		name: "talkBack",
		props: {
				terminalId: {
						required: false,
						type: [Number, String],
				},
        vehicleId: {
						required: false,
						type: [Number, String],
				},
				//没用的，本来为了区分车辆和场地的，后来发现两个对讲用的是一个地址，先留着
				type:{
						type: String,
						default: 'vehicle',
				},
				chn: {
						type: Number,
						default: 1,
				},
				inline: {
						type: Boolean,
						default: false,
				},
				autoLink: {
						type: Boolean,
						default: false,
				},
				expandWidth: {
						type: [Number, String],
						default: 'auto'
				},
				expandBackground: {
						type: String,
						default: 'transparent'
				},
				alwaysExpand: {
						type: Boolean,
						default: false
				}
		},
		data() {
				return {
						StepState,
						StepMsg:{
								[StepState.DEFAULT]:'点击开始对讲',
								[StepState.WAITING_MICROPHONE_PERMISSION]:'正在请求麦克风权限',      
								[StepState.NOT_SUPPORTED]:'浏览器不支持，请检查相关设置',                      
								[StepState.GET_MICROPHONE_PERMISSION_SUCCESS]:'请求麦克风权限成功',  
								[StepState.GET_MICROPHONE_PERMISSION_FAILURE]:'请求麦克风权限失败，请确认开启麦克风权限或检查音频输入设备。', 
								[StepState.CLICK_TO_CONNECT_DEVICE]:'点击以连接设备',            
								[StepState.CONNECTING_DEVICE]:'正在连接设备',                  
								[StepState.CONNECTED_DEVICE_SUCCESS]:'连接设备成功,正在对讲',          
								[StepState.CONNECTED_DEVICE_FAILURE]:'连接设备失败，请重试',          
								[StepState.DEVICE_DISCONNECT]:'设备已断开，请重新连接',
								[StepState.RECEIVE_MESSAGE]:'',//如果接口返回message有值,赋值为message
						},
						step: 0,
						recorder: null,
						linked: false,
						ws: null,
						wsUrl:'',
						temp: [],
						replyBuffer: [],
						audioCtx: null,
						delayTemp: [],
						isPlaying: false,
						initialized: false,
						commandTrue:false,
				}
		},
		computed: {
			...mapState("auth", ["token"]),

				infoStyle: function () {
						let temp = {
								'flex-grow': (!this.expanded ? 0 : 1),
						}
						if (this.alwaysExpand) {
								temp['justify-content'] = 'center';
						}
						return temp
				},
				boxStyle: function () {
						let style;

						if (this.expanded) {
								style = {
										background: this.expandBackground,
								}
								if (this.expandWidth === 'auto') {
										Object.assign(style, {
												flexGrow: 99999,
												width: this.expandWidth
										})
								} else {
										Object.assign(style, {
												width: this.expandWidth + (typeof this.expandWidth === 'number' ? 'px' : ''),
										})
								}
						}
						return style;
				},
				expanded: function () {
						return this.alwaysExpand || (this.step >= StepState.CONNECTING_DEVICE && this.step < StepState.RECEIVE_MESSAGE);
				},
				allowClick: function () {
						const whiteList = [
								StepState.DEFAULT,
								// StepState.WAITING_MICROPHONE_PERMISSION,
								// StepState.NOT_SUPPORTED,
								// StepState.GET_MICROPHONE_PERMISSION_FAILURE,
								StepState.GET_MICROPHONE_PERMISSION_SUCCESS,
								// StepState.CONNECTING_DEVICE,
								StepState.CONNECTED_DEVICE_FAILURE,
								StepState.CONNECTED_DEVICE_SUCCESS,
								StepState.DEVICE_DISCONNECT,
								StepState.CLICK_TO_CONNECT_DEVICE,
                StepState.RECEIVE_MESSAGE
						];
						return whiteList.includes(this.step);
				},
				isErrorInfo: function () {
						const whiteList = [
								StepState.NOT_SUPPORTED,
								StepState.GET_MICROPHONE_PERMISSION_FAILURE,
								StepState.CONNECTED_DEVICE_FAILURE,
                StepState.RECEIVE_MESSAGE

						];
						return whiteList.includes(this.step);
				}
		},
		watch: {
				terminalId: {
						handler: async function (val, oldV) {
								if (!val) return;
								if (this.autoLink) {
										await this.$nextTick()
										this.getTalkBackLink()
								}
						},
						immediate: true,
				},
				step: function (val) {
						this.$emit('stateChange', {
								msg: this.StepMsg[val] || this.$ct('unknowStep'),
								isError: this.isErrorInfo,
								allowClick: this.allowClick,
						})
				}
		},
		methods: {
				async getTalkBackLink() {
						if (!this.initialized) {
								return;
						}
						if (this.ws) {
								// this.ws.close();
								this.stop()
								return;
						}
						if (!this.audioCtx) {
								this.audioCtx = new AudioContext();
						}

						this.step = StepState.CONNECTING_DEVICE;
						try {
								let res = await $.ajax({
										url: BASE_URL_API,
										type: "post",
										data: JSON.stringify({
												cmd: "live",
												terminal_no: this.terminalId.toString(),
                        vehicle_id:Number(this.vehicleId),
												media_type:2,
												//2020-06-28 18:01:03 因设备原因固定成通道1
												channel_no: this.chn,
										}),
										headers: {
												Authorization: "token " + this.token,
										},
								});

								if (res.code === 0) {
										this.commandTrue = true
										this.setStateDelay(StepState.CONNECTED_DEVICE_SUCCESS, 500);
										this.wsUrl = res.data
										let ws = new WebSocket(res.data);
										ws.binaryType = 'arraybuffer'  //WebSocket.binaryType 返回websocket连接所传输二进制数据的类型。(Blob 类型的数据/ArrayBuffer 类型的数据(ArrayBuffer 对象用来表示通用的、固定长度的原始二进制数据缓冲区。))
										ws.onopen = this.onWsOpen.bind(this);
										ws.onmessage = this.onWsMessage.bind(this);
										ws.onclose = this.onWsClose.bind(this);
										ws.onerror = this.onWsError.bind(this);
										this.ws = ws;
								} else {
										this.commandTrue = false
                    if(res && res.message){
                      this.StepMsg[StepState.RECEIVE_MESSAGE] = res.message
                    }else {
                      this.StepMsg[StepState.RECEIVE_MESSAGE] = this.StepMsg[StepState.CONNECTED_DEVICE_FAILURE]
                    }
										this.setStateDelay(StepState.RECEIVE_MESSAGE, 500);
								}
						} catch (e) {
								this.setStateDelay(StepState.CONNECTED_DEVICE_FAILURE, 500);
						}

				},
				async initDevice() {
						this.step = StepState.WAITING_MICROPHONE_PERMISSION;
						navigator.getUserMedia = navigator.getUserMedia || navigator.webkitGetUserMedia;
						if (!navigator.getUserMedia) {
								this.step = StepState.NOT_SUPPORTED;
								return;
						}
						let stream;
						try {
								stream = await navigator.mediaDevices.getUserMedia({audio: true});
								this.recorder = new Recorder(stream);
								this.recorder.onAudioData = this.onAudioDataHandle.bind(this);
								this.step = StepState.GET_MICROPHONE_PERMISSION_SUCCESS;
								this.initialized = true;
								this.setStateDelay(StepState.CLICK_TO_CONNECT_DEVICE, 500);
						} catch (e) {
								console.error(e);
								this.step = StepState.GET_MICROPHONE_PERMISSION_FAILURE;
								// this.$error('申请麦克风权限失败，请确认开启麦克风权限或检查音频输入设备。')
						}
				},
				async startTalkBack() {
						if (this.recorder) {
								this.recorder.start();
								this.$emit('talkStart')
						}
				},
				stopTalkBack() {
						if (this.recorder) {
								this.recorder.stop();
						}
				},
				onAudioDataHandle(audioData) {
						if (this.linked) {
								this.ws.send(audioData);
								// this.ws.send(new ArrayBuffer(1364));
						}
				},
				clearWs() {
						this.stopTalkBack();
						this.linked = false;
						if (!this.ws) return;
						let ws = this.ws;
						ws.onopen = null;
						ws.onmessage = null;
						ws.onclose = null;
						ws.onerror = null;
						this.ws = null;
				},
				onWsClose() {
						console.log('ws已断开' + Date.now());
						this.setStateDelay(StepState.DEVICE_DISCONNECT, 500);
						this.clearWs();
				},
				onWsOpen() {
						console.log('ws已连接' + Date.now());
						this.linked = true;
						this.startTalkBack();


				},
				playReply() {
						this.isPlaying = false;
						let buffer = this.replyBuffer.shift();
						if (buffer) {
								let source = this.audioCtx.createBufferSource();
								source.buffer = buffer;
								source.connect(this.audioCtx.destination);
								source.onended = this.playReply;
								source.start();
								this.isPlaying = true;
						}
				},
				onWsError(err) {
						console.error(err);
						this.clearWs();
				},
				async onWsMessage(msg) {
						//aac
						//凑够一定的大小再解码以防止解码失败 和 和频繁播放引起的杂音
						//1个768b 持续0.128s 8个一组 所以至少延迟播放1.024秒
						// if (this.temp.length === 8) {
						//     this.temp = this.temp.map(item => new Int16Array(item));
						//     let size = this.temp.reduce((a, b) => a + b.length, 0)
						//     let totalArray = new Int16Array(size);
						//     let offset = 0;
						//     for (let i = 0; i < this.temp.length; i++) {
						//         totalArray.set(this.temp[i], offset)
						//         offset += this.temp[i].length;
						//     }
						//     let audioBuffer = await this.audioCtx.decodeAudioData(totalArray.buffer)
						//     this.replyBuffer.push(audioBuffer);
						//     this.temp = [];
						//     //起码缓冲2组数据才播放
						//     if (this.replyBuffer.length >= 2) {
						//         this.playReply()
						//     }
						// } else {
						//     this.temp.push(msg.data)
						// }

						//测出ws两个样本传输间隔150~170ms左右，单样本播放时长128ms，这就造成了每播放几组样本就要等待缓冲
						// let sampleNum = 20
						// if(this.delayTemp.length === sampleNum){
						//     let delays = this.delayTemp.map((item,index,arr)=>{
						//         if(index === 0){
						//             return 0;
						//         }else{
						//             return item - arr[index-1]
						//         }
						//     })
						//     console.log(delays.reduce((a,b)=>a+b,0)/(sampleNum-1));
						//     this.delayTemp = []
						// }else{
						//     this.delayTemp.push(Date.now())
						// }
						//pcm
						//0.04s*10 一个了
						if (this.temp.length === 20) {
								this.temp = this.temp.map(item => new Int16Array(item));
								let size = this.temp.reduce((a, b) => a + b.length, 0)
								let totalArray = new Int16Array(size);
								let offset = 0;
								for (let i = 0; i < this.temp.length; i++) {
										totalArray.set(this.temp[i], offset)
										offset += this.temp[i].length;
								}
								this.temp = [];
								let audioBuffer = await this.audioCtx.decodeAudioData(this.buildWAV(totalArray))
								this.replyBuffer.push(audioBuffer);
								if (!this.isPlaying) {
										this.playReply()
								}
						} else {
								this.temp.push(msg.data)
						}

				},
				/**
				 * @param {Int16Array} input*/
				buildWAV(input) {
						let sampleRate = 8000;
						let sampleBits = 16;
						let bytes = input;
						let dataLength = bytes.length * 2;
						let buffer = new ArrayBuffer(44 + dataLength);
						let data = new DataView(buffer);
						let channelCount = 1;//单声道
						let offset = 0;

						let writeString = function (str) {
								for (let i = 0; i < str.length; i++) {
										data.setUint8(offset + i, str.charCodeAt(i));
								}
						};

						// 资源交换文件标识符
						writeString('RIFF');
						offset += 4;
						// 下个地址开始到文件尾总字节数,即文件大小-8
						data.setUint32(offset, 36 + dataLength, true);
						offset += 4;
						// WAV文件标志
						writeString('WAVE');
						offset += 4;
						// 波形格式标志
						writeString('fmt ');
						offset += 4;
						// 过滤字节,一般为 0x10 = 16
						data.setUint32(offset, 16, true);
						offset += 4;
						// 格式类别 (PCM形式采样数据)
						data.setUint16(offset, 1, true);
						offset += 2;
						// 通道数
						data.setUint16(offset, channelCount, true);
						offset += 2;
						// 采样率,每秒样本数,表示每个通道的播放速度
						data.setUint32(offset, sampleRate, true);
						offset += 4;
						// 波形数据传输率 (每秒平均字节数) 单声道×每秒数据位数×每样本数据位/8
						data.setUint32(offset, channelCount * sampleRate * (sampleBits / 8), true);
						offset += 4;
						// 快数据调整数 采样一次占用字节数 单声道×每样本的数据位数/8
						data.setUint16(offset, channelCount * (sampleBits / 8), true);
						offset += 2;
						// 每样本数据位数
						data.setUint16(offset, sampleBits, true);
						offset += 2;
						// 数据标识符
						writeString('data');
						offset += 4;
						// 采样数据总数,即数据总大小-44
						data.setUint32(offset, dataLength, true);
						offset += 4;
						for (let i = 0; i < bytes.length; i++, offset += 2) {
								data.setInt16(offset, bytes[i], true);
						}
						return data.buffer;
				},
				setStateDelay(step, delay) {
						setTimeout(() => {
								this.step = step;
						}, delay)
				},
				stop() {
						this.ws && this.ws.close();
						this.step = StepState.CLICK_TO_CONNECT_DEVICE;
						if(!this.commandTrue)return
						$.ajax({
								url: BASE_URL_API,
								type: "post",
								data: JSON.stringify({
										cmd: "controlLive",
										control_code:4,
										id: this.wsUrl.split("/")[this.wsUrl.split("/").length - 1].split("?")[0].slice(0, -4),

								}),
								headers: {
										Authorization: "token " + this.token,
								},
						});
				}

		},
		async mounted() {
				// let ws = new WebSocket('ws://127.0.0.1:8888');
				// ws.binaryType = 'arraybuffer'
				// ws.onopen = () => {
				//     ws.send(JSON.stringify({cmd: 'name', name: 'xxxx.wav'}))
				// };
				// ws.onclose = () => {
				//     // ws.send(JSON.stringify({cmd: 'name', name: 'xxxx.wav'}))
				// };
				// this._ws = ws;
				// let res = await fetch('http://127.0.0.1/lethergo.pcm',{
				//     responseType: 'arraybuffer'
				// })
				// let buffer = await res.arrayBuffer()
				// console.log(buffer);
				// this.temp = buffer;
				this.initDevice();
		},
		async beforeDestroy() {

				this.stop();
				this.clearWs();
				this.recorder = null;
				this.initialized = false;
				this.audioCtx = null;
		}
}
</script>

<style scoped lang="scss">
.talkBack {
		display: inline-flex;
		height: 30px;
		line-height: 30px;
		justify-content: flex-end;
		align-items: center;

		.el-button {
				/deep/ .pony-iconv2 {
						margin-right: 0;
				}
		}

		.info-content {
				width: 0;
				display: flex;
				align-items: center;
				text-indent: 10px;
				transition: all .5s ease-in-out;
				flex-direction: row-reverse;

				p {
						margin: 0;
						overflow: hidden;
						text-overflow: ellipsis;
						white-space: nowrap;
				}
		}
}
</style>

<template>
  <div class="realtime-video-v2">
    <Layout :contentLoading="loading" :hasColor="true">
      <!--						<template slot="aside">-->
      <!--								<ElementTree-->
      <!--										ref="vehicleTree"-->
      <!--										type="vehicle"-->
      <!--										state-->
      <!--										onlineFilter-->
      <!--										onlineCountTip-->
      <!--										:checkMode="true"-->
      <!--								></ElementTree>-->
      <!--						</template>-->
      <template slot="query">
        <div class="query-item">
          <span class="title" style="width: 38px">车辆: </span>
          <SelectTreeInput
            :width="300"
            ref="modalVehicle"
            v-model="selectCarlList"
            type="vehicle"
            placeholder="请选择车辆"
            title="请选择车辆"
            :checkMode="true"
            :disable="playing"
            :condition="vehicleCondition"
            :onlineCountTip="true"
            :onlineFilter="true"
            :state="true"
            :extraKeys="['channelCount','name','terminalNo','id', 'acc','channelValid']">
          </SelectTreeInput>
        </div>
        <div class="query-item">
          <VideoNumSelect :line="4" :column="4" :disabled="playing" @changeNum="changeNum"></VideoNumSelect>
        </div>
        <div class="query-item">
          <span class="title">时长: </span>
          <el-input v-model.number="videoLength" :disabled="playing">
            <template slot="append">秒</template>
          </el-input>
        </div>
        <div class="query-item">
          <span class="title">总时长: </span>
          <el-select v-model="allVideoLength" placeholder="请选择" :disabled="playing">
            <el-option
              v-for="item in allVideoLengthList"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </div>
        <div class="query-item">
          <span class="title">通道: </span>
          <el-select v-model="channel" placeholder="请选择" :disabled="playing" multiple collapse-tags>
            <el-option
              v-for="item in channelList"
              :key="item"
              :label="'通道'+item"
              :value="item">
            </el-option>
          </el-select>
        </div>
        <div class="query-item" v-if="!playing">
          <el-button type="primary" :disabled="playing" @click="play">开始轮询</el-button>
        </div>
        <div class="query-item" v-else>
          <el-button type="primary" @click="stop">结束轮询</el-button>
        </div>
        <div class="query-item" v-if="!pause">
          <el-button type="primary" :disabled="!playing" @click="clearAll">暂停轮询</el-button>
        </div>
        <div class="query-item" v-else>
          <el-button type="primary" :disabled="!playing" @click="play">继续轮询</el-button>
        </div>
        <div class="query-item">
          <el-button type="primary" :disabled="!playing || page === 1 || pause" @click="changePage(-1)">上一页</el-button>
        </div>
        <div class="query-item">
          <el-button type="primary" :disabled="!playing || page === pageTotal || pause" @click="changePage(1)">下一页</el-button>
          <!--										总页数{{pageTotal}}当前页{{page}}-->
        </div>
        <span style="color: red">(仅显示ACC在线车辆)</span>
      </template>
      <template slot="content">
        <VideoViewPolling
          ref="videoView"
          :value="value"
          :autoPlay="true"
          :windowNum="windowNum"
          :line="line"
          :column="column"
        ></VideoViewPolling>
      </template>
    </Layout>
  </div>
</template>

<script>

import VideoViewPolling from "./component/videoPlayerV2/component/VideoViewPolling";
import VideoNumSelect from "./component/videoPlayerV2/component/VideoNumSelect";
import SelectTreeInput from '@/components/common/SelectTreeInput';
import {sleep} from "../../util/common";


export default {
  name: "pollingVideo",
  components: {
    VideoViewPolling,
    VideoNumSelect,
    SelectTreeInput
  },
  data() {
    return {
      loading: false,
      windowNum: 16,// 显示视频通道数量
      remaining: 300,
      isStopMove: false,
      playing: false, // 播放
      pause: false, // 暂停
      value: "1",
      line: 4, // 行
      column: 4, // 列
      videoLength: 10, //视频时长
      allVideoLength: '5', //总播放时长
      selectCarlList: [], // 选中车辆列表
      allVideoChannelList: [], //选中车辆所有视频通道列表
      page: 1, // 当前页数
      // pageStart: 0, // 分页起始数值
      allVideoLengthList: [{
        value: '5',
        label: '5分钟'
      }],
      total: null,
      interval: null,
      channel: [1, 2, 3, 4, 5, 6, 7, 8, 9],
      channelList: []
    };
  },
  computed: {
    filterChannelList() {
      return this.allVideoChannelList.filter(item => this.channel.includes(item.channelNo))
    },
    pageTotal() {
      let rowCount = this.filterChannelList.length
      let pageSize = Number(this.windowNum)
      if (rowCount == null || rowCount == "") {
        return 0;
      } else {
        if (pageSize != 0 && rowCount % pageSize == 0) {
          return parseInt(rowCount / pageSize)
        }
        if (pageSize != 0 && rowCount % pageSize != 0) {
          return parseInt(rowCount / pageSize) + 1
        }
      }
    },
    pageStart() {
      return (this.page - 1) * this.windowNum
    },
    videoChannelList() {
      return this.filterChannelList.slice(this.pageStart, this.pageStart + this.windowNum)
    }
  },
  watch: {
    // 获取所有视频通道
    selectCarlList: function (val) {
      this.allVideoChannelList = []
      val.forEach(data => {
        let channelList = []
        data.channelValid.split('').map((chanl, index) => {
          if (+chanl == 1) {
            channelList.push(index + 1)
          }
        })
        let list = []
        channelList.forEach(item => {
          list.push({
            plateNo: data.name,
            vehicleId: parseInt(data.id),
            terminalCode: data.terminalNo,
            channelNo: item,
            inputType: data.inputType
          })
        })
        this.allVideoChannelList = this.allVideoChannelList.concat(list)
        let num = this.allVideoChannelList.map(item => item.channelNo)
        this.channelList = num.filter(function (item, index) {
          return num.indexOf(item) === index
        })
        this.channel = this.channelList
      })
    }
  },
  mounted() {
    if(this.hasPermission('pollingVideo:permanentPlayVideo')) {
      this.allVideoLengthList.push({
        value: '0',
        label: '永久'
      })
    }
  },
  methods: {
    // 选择车辆
    vehicleCondition(treeNode) {
      return treeNode.type === 4 && treeNode.acc === '开'
    },
    // 改变播放通道数量
    changeNum(line, column) {
      this.windowNum = line * column
      this.line = line
      this.column = column
    },
    // 开始轮询
    async play() {
      if (this.selectCarlList.length === 0) {
        return this.$warning('请选择车辆！')
      }
      this.totalDuration()
      this.timeInterval()
      await this.playAll()
    },
    // 开始播放
    async playAll() {
      try {
        await this.$refs["videoView"].append(this.videoChannelList)
        this.pause = false
        this.playing = true
      } catch (e) {
        console.log(e)
      } finally {
      }
    },
    // 停止轮询
    stop() {
      this.playing = false
      this.pause = false
      // this.selectCarlList = []
      // this.allVideoChannelList = []
      this.page = 1
      this.clearAll()
    },
    // 暂停轮询
    async clearAll() {
      try {
        clearTimeout(this.total)
        this.total = null
        clearTimeout(this.interval)
        this.interval = null
        this.pause = true
        await this.$refs["videoView"].stopAll()
      } catch (e) {
        console.log(e)
      } finally {
      }
    },
    async changePage(val) {
      this.page += val
      await this.clearAll()
      await this.play()
    },
    // 总时长
    totalDuration() {
      if(this.allVideoLength == 0) return
      this.total = setTimeout(() => {
        this.stop()
      }, 1000 * 60 * this.allVideoLength)
    },
    // 时长
    timeInterval() {
      this.interval = setInterval(async () => {
        if (this.page === this.pageTotal) {
          this.page = 1
        } else {
          this.page++
        }
        await this.$refs["videoView"].append(this.videoChannelList)
      }, this.videoLength * 1000)
    }
  },
  beforeDestroy() {
    this.stop()
  },
  deactivated() {
    this.stop()
  }
};
</script>

<style lang="scss">
.realtime-video-v2 {
  width: 100%;
  height: 100%;

  .title {
    margin-right: 5px;
  }
}
</style>

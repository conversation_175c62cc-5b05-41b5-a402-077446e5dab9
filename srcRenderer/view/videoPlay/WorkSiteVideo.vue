<template>
    <Layout id="workSiteVideo" :has-color="true">
        <template slot="aside">
            <div class="query-top">
                <ElementTree :type="treeType" :alwaysNew="true" ref="tree" v-if="treeType"
                                 @node-click="playbackTreeClick" @node-dbl-click="playbackTreeDblClick"
                                 :elementTypeList="elementTypeList" :elementType="true" @change="treeChange"
                                 class="flex-grow bg bg--light box-shadow" style="height:calc(100% - 100px)"
                                 ></ElementTree>
            </div>
            
            
           
            <!-- <el-tabs v-model="activeTab" style="height:calc(100% - 100px)"
                     stretch type="border-card">
                <el-tab-pane label="工地" name="workSite">
                    <ElementTree type="workSiteVideo" :alwaysNew="true" ref="tree"
                                 @node-click="playbackTreeClick" @node-dbl-click="playbackTreeDblClick"
                                 class="flex-grow bg bg--light box-shadow" style="height:calc(100% - 100px)"
                                 ></ElementTree>
                </el-tab-pane>
                <el-tab-pane label="消纳场" name="xiaoNa">
                    <ElementTree type="xiaoNaVideo" :alwaysNew="true" ref="tree" @node-click="playbackTreeClick"
                                 @node-dbl-click="playbackTreeDblClick"
                                 class="flex-grow bg bg--light box-shadow" style="height:calc(100% - 100px)"
                                 ></ElementTree>
                </el-tab-pane>
            </el-tabs> -->
            <div class="search-wrap flex-not-shrink bg bg--light box-shadow">
                <div class="query-item">
                    <label>{{ $ct('date') }} ：</label>
                    <el-date-picker
                        v-model="query.date" class="query-input"
                        type="date" @change="dateChange"
                        placeholder="选择日期">

                    </el-date-picker>
                </div>
                <div class="query-item">
                    <el-button type="primary" @click="search()" class="w100">查询</el-button>
                </div>
            </div>
        </template>
        <template slot="content">
            <div class="video-right-wrap" :class="{'multiple-4':chn4Mode,'multiple-9':chn9Mode}">
                <div class="video-table-wrap">
                  <div class="video-wrap" v-if="downOption.options">
                        <div class="video-player">
                            <VideoPlayerDown ref="playerVideo" :addButton="['record']" @ready="eventEmitter('ready', $event)"
                              :options="downOption" @closeVideo="closeVideo" :autoplay="true">
                            </VideoPlayerDown>
                        </div>
                    </div>
                    <div class="video-wrap" v-else>
                        <div class="video-player"
                             :class="{selected:index===window.selected,maximize:index===window.maximize}"
                             v-for="(item,index) in (chn9Mode?9:chn4Mode?4:1)" :key="index"
                             @dblclick="toggleMaximize(index)"
                        >
                            <PlaybackPlayerWork ref="player" type="playBack"
                                                :terminalCode="currentVehicle.code"
                                                :startTime="getVideoParams(index).startTime"
                                                :endTime="getVideoParams(index).endTime"
                                                @currentTimeChange="(time)=>currentTimeChange(index,time)"
                                                @stop="onVideoStop"
                            ></PlaybackPlayerWork>

                        </div>
                    </div>
                    
                    <div class="fixed-button">
                        <i class="pony-iconv2 pony-shuaxin text text--link" :title="$ct('refresh')" @click="search"></i>
                    </div>
                </div>
                <div class="extra-wrap flex-grow">
                    <!-- <GPSRecordMap ref="recordMap"
                                          :start-time="timeToDate(currentVideo.startTime)"
                                          :end-time="timeToDate(currentVideo.endTime)"
                                          :current-time="currentVideo.currentTime"></GPSRecordMap> -->
                    <SimpleMap mapConfig="justmap" :mapLittleTool="['scale']" ref="simplemap"
                               @mapReady="generateMapObj"></SimpleMap>
                </div>
            </div>
            <el-tabs class="special-border-bottom video-list" :value="'axis'" v-loading="recordTable.loading" v-model="tabName"
                     type="border-card" style="border: none;  user-select: none;">
                <el-tab-pane :label="$ct('timeAxis')" name="axis">
                    <VideoListAxisWork :timeData="timeData" ref="videoAxis"
                                       @do="cmdAdapter"></VideoListAxisWork>
                </el-tab-pane>
                <!-- recordTable.data -->
                <el-tab-pane :label="$ct('recordList')" name="table">
                    <el-table :empty-text="recordTable.loading? $ct('searching'):$ct('noResult')"
                              ref="record" :data="recordTable.data" border stripe height="100%" style="margin-top: -1px"
                              highlight-current-row @row-dblclick="realPlay">
                        <el-table-column type="index" :label="$ct('index')" width="60">
                        </el-table-column>
                        <el-table-column :label="$ct('control')" width="70">
                            <template slot-scope="scope">
                                <i class="pony-iconv2 pony-sanjiaoxing" :title="$ct('play')"
                                   @click="realPlay(scope.row)"
                                   style="margin-right: 5px;"></i>
                            </template>
                        </el-table-column>
                        <el-table-column :label="$ct('state')" width="120">
                            <template slot-scope="scope">
                                <span>{{ scope.row.status !== undefined ? $ct(`fileState[${scope.row.status}]`) : $ct('loading') }}</span>
                            </template>
                        </el-table-column>

                        <el-table-column :label="$ct('progress')" width="150">
                            <div class="video-control" slot-scope="scope">
                                <span v-if="scope.row.loading"><i
                                    class="el-icon-loading"></i>{{ $ct('loading') }}</span>
                                <template v-else-if="scope.row.status === 0">
                                    <span v-if="scope.row.progress === -1">
                                        {{ $ct('clickHint2') }}
                                    </span>
                                    <el-progress v-else :percentage="scope.row.progress" :text-inside="true"
                                                 style="flex-grow: 1"
                                                 :stroke-width="14"></el-progress>
                                    <i class="pony-iconv2 pony-guanbi" :title="$ct('cancelDownload')"
                                       @click="cancelDownload(scope.row)"></i>
                                </template>

                                <template v-else-if="scope.row.status == 4 || scope.row.status == 1">
                                    <span class="font-blue" style="color:#67C23A"
                                          @click="download(toFileName(scope.row),scope.row.url)">
                                        {{ $ct('clickForDownload') }}  <i class="pony-iconv2 pony-xiazai"
                                                                          style="cursor: pointer;margin-left: 5px"></i>
                                    </span>
                                    <i class="pony-iconv2 pony-shuaxin" :title="$ct('reUpload')"
                                       @click="downloadSelectTime(scope.row,true)"></i>
                                </template>

                                <span v-else class="font-blue" @click="downloadSelectTime(scope.row,false)">
                                    {{ $ct('clickForUpload') }}
                                    <i class="pony-iconv2 pony-xiazai" style="cursor: pointer;margin-left: 5px"></i>
                                </span>
                            </div>
                        </el-table-column>
                        <el-table-column prop="start_time" :label="$ct('startTime')" width="200">
                            <template slot-scope="scope">
                                <span>{{ scope.row | toStart }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column prop="end_time" :label="$ct('endTime')" width="200">
                            <template slot-scope="scope">
                                <span>{{ scope.row | toEnd }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column :label="$ct('timeLength')" width="120">
                            <template slot-scope="scope">
                                <span>{{ scope.row| toTime }}</span>
                            </template>
                        </el-table-column>
                        <!-- <el-table-column prop="len" :label="`${$ct('fileSize')}(MB)`" width="120">
                            <template slot-scope="scope">
                                <span>{{ scope.row.size | toOmen }}</span>
                            </template>
                        </el-table-column> -->
                        <el-table-column prop="name" :label="$ct('fileName')" show-overflow-tooltip>
                            <!-- <template slot-scope="scope" v-once>
                                <span>{{ toFileName(scope.row )}}</span>
                            </template> -->
                        </el-table-column>
                        

                        
                    </el-table>
                </el-tab-pane>
                <!-- 下载列表 -->
                <el-tab-pane :label="$ct('downloadList')" name="download">
                    <el-table :empty-text="recordTable.loading? $ct('searching') : $ct('noResult')"
                              ref="record" :data="downloadTable.data" border stripe height="100%"
                              style="margin-top: -1px"
                              highlight-current-row @row-dblclick="realPlay">
                        <el-table-column type="index" :label="$ct('index')" width="60">
                        </el-table-column>
                        <el-table-column label="操作" width="60">
                          <template slot-scope="{ row }">
                            <i class="pony-iconv2 pony-sanjiaoxing" title="播放" @click="realPlayDown(row)" :disabled="!row.url"
                              style="margin-right: 5px"></i>
                          </template>
                        </el-table-column>
                        <el-table-column :label="$ct('state')" width="120">
                            <template slot-scope="scope">
                                <span>{{ scope.row.status !== undefined ? $ct(`fileState[${scope.row.status}]`) : $ct('loading') }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column :label="$ct('progress')" width="150">
                            <div class="video-control" slot-scope="scope">
                                <span v-if="scope.row.loading"><i
                                    class="el-icon-loading"></i>{{ $ct('loading') }}</span>
                                <template v-else-if="scope.row.status === 0">
                                    <span v-if="scope.row.progress === -1">
                                        {{ $ct('clickHint2') }}
                                    </span>
                                    <template v-else>
                                        <el-progress :percentage="scope.row.progress" :text-inside="true"
                                                     style="flex-grow: 1"
                                                     :stroke-width="14"></el-progress>
                                    </template>
                                    <i class="pony-iconv2 pony-guanbi" title="取消下载"
                                       @click="cancelDownload(scope.row)"></i>
                                </template>

                                <template v-else-if="scope.row.status === 4 || scope.row.status === 1">
                                <span class="font-blue" style="color:#67C23A"
                                      @click="download(toFileName(scope.row),scope.row.url)">
                                    {{ $ct('clickForDownload') }}  <i class="pony-iconv2 pony-xiazai"
                                                                      style="cursor: pointer;margin-left: 5px"></i>
                            </span>
                                    <i class="pony-iconv2 pony-shuaxin" title="重新上传"
                                       @click="downloadSelectTime(scope.row,true)"></i>
                                </template>

                                <span v-else class="font-blue" @click="downloadSelectTime(scope.row,false)">
                                    {{ $ct('clickForUpload') }}  <i class="pony-iconv2 pony-xiazai"
                                                                    style="cursor: pointer;margin-left: 5px"></i>
                            </span>
                            </div>
                        </el-table-column>
                        <el-table-column :label="$ct('startTime')" width="200">
                            <template slot-scope="scope">
                                <span>{{ scope.row | toStart }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column :label="$ct('endTime')" width="200">
                            <template slot-scope="scope">
                                <span>{{ scope.row | toEnd }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column :label="$ct('timeLength')" width="120">
                            <template slot-scope="scope">
                                <span>{{ scope.row| toTime }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column prop="file" :label="$ct('fileName')" show-overflow-tooltip>
                            <template slot-scope="scope" v-once>
                                <span>{{ toFileName(scope.row) }}</span>
                            </template>
                        </el-table-column>
                        
                    </el-table>
                </el-tab-pane>
            </el-tabs>
        </template>
        <PonyDialog
              v-model="timeShow.show"
              width="330"
              okText="确认上传"
              @close="timeShow.show = false"
              @confirm="confirmUpload"
              title="上传视频"
            >
                <div class="search-wrap">
                    <div class="query-item">
                        <label>开始时间：</label>
                        <el-date-picker
                          v-model="timeShow.startTime"
                          class="query-input"
                          type="datetime"
                          @change="dateChange"
                          value-format="yyyy-MM-dd HH:mm:ss"
                          :picker-options="pickeroptions1"
                        >
                        </el-date-picker>
                    </div>
                    <div class="query-item">
                        <label>结束时间：</label>
                        <el-date-picker
                          v-model="timeShow.endTime"
                          class="query-input"
                          type="datetime"
                          @change="dateChange"
                          value-format="yyyy-MM-dd HH:mm:ss"
                          :picker-options="pickeroptions"
                        >
                        </el-date-picker>
                    </div>
                </div>
            </PonyDialog>
            <PonyDialog :show="downloadObj.show" width="380" title="下载说明" class="pony-warp" :allowClose="false">
              <p>请输入内容:</p>
              <el-input v-model="downloadObj.remark" type="textarea" placeholder="请输入内容" resize="none"
                style="margin:10px 0"></el-input>
              <template slot="footer">
                <el-button type="primary" @click="handleConfirmDownload">确认
                </el-button>
              </template>
            </PonyDialog>
    </Layout>
</template>

<script>
/**
 * @Author: xieyj
 * @Email:
 * @Date: 2020/3/17 13:34
 * @LastEditors: xieyj
 * @LastEditTime: 2020/3/17 13:34
 * @Description:
 */
import L from '@/assets/lib/leaflet-bmap'
import PlaybackPlayerWork from './component/videoPlayerV2/PlaybackPlayerWork'
import GPSRecordMap from './component/GPSRecordMap'
import videoWS from './component/videoPlayerV2/lib/ws';
import VideoListAxisWork from './component/VideoListAxis/VideoListAxisWork'
import SimpleMap from '@/view/monitor/leaflet/leafletMap/MaticsMapV2'
import {generateCurrentFenseLayer} from '@/view/monitor/util/generateUtil'
import {mapState} from 'vuex'
import VideoPlayerDown from "@/view/custom/sgs/VideoDownloadView/components/VideoDownloadViewPlayer";


const API_URL = window.PONY.media.apiNewUrlBack;
// const API_URL = "http://*************:12410/api/gb28181"
export default {
    name: "workSiteVideo",
    _i18Name: 'playback1078',
    components: {
        PlaybackPlayerWork,
        GPSRecordMap,
        VideoListAxisWork,
        SimpleMap,
        VideoPlayerDown
    },
    data() {
        return {
          downloadObj: {
            show: false,
            remark: '',
            resObj: null,
          },
            activeTab: 'workSite',
            query: {
                date: moment().startOf('day'),
                startTime: moment().startOf('day').toDate(),
                endTime: moment().endOf('day').toDate(),
                // chn: 0,
            },
            treeType:'',
            renderDataType:5,
            timeShow: {
                show: false,
                startTime: '',
                endTime: '',
                rowData: null,
                reupload: false
            }, 
            downOption:{},
            elementTypeList:[
                {
                    type:'workSiteVideo',
                    value:0,
                    name:'工地',
                    renderDataType:5
                },
                {
                    type:'xiaoNaVideo',
                    value:1,
                    name:'消纳场',
                    renderDataType:5

                },
                {
                    type:'pointTerminalWorksite',
                    value:2,
                    name:'收集点',
                    renderDataType:6

                },
                {
                    type:'stopTerminalWorksite',
                    value:3,
                    name:'停车场',
                    renderDataType:6

                },
            ],
            currentVehicle: {
                // plate_no: '',
                // vehicle_id: '',
                code: '',
                codeName:''
                // chnNo: 0,
            },
            currentVideo: {
                startTime: null,
                endTime: null,
                currentTime: null,
            },
            videoPlayList: [
                {
                    // chn:0,
                    startTime: null,
                    endTime: null,
                }
            ],
            recordTable: {
                loading: false,
                data: []
            },
            downloadTable: {
                data: []
            },

            tabName: 'axis',
            chn4Mode: false,
            chn9Mode: false,
            currentRecordAjax: null,
            ws: null,
            // plateNoTemp: null,
            chnList: [1, 2, 3, 4],//标识设备的前4个通道号是什么 不一定是1,2,3,4
            timeData: undefined,
            window: {
                selected: -1,
                maximize: -1,
            },
            //设备名称
            terminal_name: "",
            _gdLayerGroup: null,
            _gdBindLayerObj: null,
            fenseDetailQuery: [],
            pickeroptions: {
                disabledDate: (time) => {
                    return time.getTime() <= new Date(this.timeShow.startTime).getTime();
                },
            },
        }
    },
    filters: {
        toStart(value) {
            if (value) {
                let startTime = value.start_time;
                return moment(startTime, 'YYYYMMDDHHmmss').format('YYYY-MM-DD HH:mm:ss');
            }
        },
        toEnd(value) {
            if (value) {
                let endTime = value.end_time;
                return moment(endTime, 'YYYYMMDDHHmmss').format('YYYY-MM-DD HH:mm:ss');
            }
        },
        toOmen(value) {
            if (value) {
                return (value / 1024 / 1024).toFixed(2);
            }
        },
        toTime(value) {
            if (value != undefined) {
                let startTime = moment(value.start_time, 'YYYYMMDDHHmmss').toDate();
                let endTime = moment(value.end_time, 'YYYYMMDDHHmmss').toDate();
                let ms = (endTime - startTime) / 1000;
                let h = ('0' + (Math.floor(ms / 3600))).slice(-2);
                let m = ('0' + (Math.floor(ms % 3600 / 60))).slice(-2);
                let s = ('0' + ms % 3600 % 60).slice(-2);
                return `${h}:${m}:${s}`;
            }
        },
    },

    computed: {
		...mapState("auth", ["token"]),

        // tableFilter: function () {
        //     return this.chnList.map(item => {
        //         return {
        //             text: this.toCHN(item),
        //             value: item,
        //         }
        //     })
        // },
        pickeroptions1: function () {
            let strStart = moment(this.timeShow.startTime).format("HH:mm:ss");
            let strEnd = moment(this.timeShow.endTime).format("HH:mm:ss");
            return {
                selectableRange: strStart + "-" + strEnd,
            };

        },
    },

    methods: {
      downloadConfirm() {
        this.download.resObj = null
        this.download.show = true
        this.download.remark = ''
        return new Promise((res, rej) => {
          this.download.resObj = res
        })
      },
      handleConfirmDownload() {
        this.downloadObj.show = false
        this.downloadObj.resObj(this.downloadObj.remark)
      },
        async treeChange(val){
            this.$refs['tree'].loading = true
            this.treeType = this.elementTypeList[val].type
            this.renderDataType = this.elementTypeList[val].renderDataType

            this.$refs['tree'].initTree(this.treeType)
        },
        async getSelectTreeList(){
            let res = await this.$api.areaTerminalType({
                page:'video/workSite'
            })
            if(!res || res.status != 200){
                return this.$warning(res.message)
            }
            this.elementTypeList = res.data
            this.renderDataType = this.elementTypeList[0].renderDataType
            this.treeType = this.elementTypeList[0].type
            // this.$refs['tree'].initTree(this.treeType)

        },
        nodeRender(h, {node, data, store}) {
            let extra = []
            if (data.type === 5 || data.type === 6) {
                return h('div', {class: 'custom-tree-node'}, [
                    h('i', {
                        class: ['tree-icon', data.iconSkin],
                    }),
                    h('span', {
                        class: 'custom-tree-node__label',
                        attrs: {
                            title: data.name,
                        },
                    }, node.label),
                    ...extra
                ])
            } else {
                let allArr = this.$utils.flatTree(data.children).filter(it => it.type == 5)
                let unAble = allArr.filter(it => it.business_value)
                return h('div', {class: 'custom-tree-node'}, [
                    h('i', {
                        class: ['tree-icon', data.iconSkin],
                    }),
                    h('span', {
                        class: 'custom-tree-node__label',
                        attrs: {
                            title: data.name,
                        },
                    }, node.label + `( ${unAble.length} / ${allArr.length} ) `),
                    ...extra
                ])
            }
        },
        closeVideo(){
          this.downOption = {}
        },
        eventEmitter(type, target, index) {
          console.log(target,'target');
          if (target) {
            target.play();
          }
        },
        generateMapObj() {
            this._gdBindLayerObj = {}
            this._gdLayerGroup = new L.FeatureGroup().addTo(this.$refs['simplemap']._map)
        },
        getVideoParams(index) {
            return this.videoPlayList[index] || {};
        },
        onVideoStop(chn) {
            this.$set(this.videoPlayList, chn, null);
        },
        // toggleMultiple(chnNo) {
        //     let other = chnNo === 4 ? 9 : 4;
        //     this[`chn${chnNo}Mode`] = !this[`chn${chnNo}Mode`];
        //     this[`chn${other}Mode`] = false;
        // },
        toggleMaximize(index) {
            this.window.maximize = this.window.maximize === index ? -1 : index;
        },
        async selectWindow(index) {
            // console.log(index);
            this.window.selected = index;
            if (!this.videoPlayList[index]) return
            Object.assign(this.currentVideo, {
                startTime: this.videoPlayList[index].startTime,
                endTime: this.videoPlayList[index].endTime,
            })
            await this.$nextTick()
            // this.$refs['recordMap'].getGPSData();
        },
        // toCHN(value) {
        //     return this.$t('common.chnName') + value;
        // },
        timeToDate(str) {
            return moment(str, 'YYYYMMDDHHmmss').toDate();
        },
        toFileName(value) {

            if (value) {
                if (value.name) {
                    return value.name;
                } else {
                    return this.terminal_name
                }
            }
        },
        async playbackTreeClick(data, node, $node) {
            if (data.type === (this.renderDataType+1)) {
                this.fenseDetailQuery = new Array(node.parent.data.id)
                this.currentVehicle.code = data.hrefName;  //获取终端号
                this.currentVehicle.codeName = data.name;  //获取终端名字

            } else if (data.type == this.renderDataType) {
                this.$warning('请选择终端')
                this.fenseDetailQuery = new Array(node.data.id)
            } else {
                this.$warning('请选择终端')
            }

        },
        async playbackTreeDblClick(data, node, $node) {
            if (data.type == (this.renderDataType+1)) {
                this.fenseDetailQuery = new Array(node.parent.data.id)
                this.currentVehicle.code = data.hrefName;
                this.currentVehicle.codeName = data.name;
                this.search();
            } else if (data.type == this.renderDataType) {
                this.$warning('请选择终端')
                this.fenseDetailQuery = new Array(node.data.id)
                this.getFenseDetail(this.fenseDetailQuery)
            } else {
                this.$warning('请选择终端')
            }


        },
        async getFenseDetail(list) {
            let result = await this.$api.queryFenseInfoAssets({fence_id_list: list})
            if (!result || result.status != 200) {
                this.$error(result.message || '查询出错');
                return
            }
            if (this._gdLayerGroup) {
                this._gdLayerGroup.clearLayers()
            }
            // console.log(result);
            result.data.forEach(fense => {
                let layerList
                if (this.activeTab == 'workSite') {
                    layerList = generateCurrentFenseLayer(fense, 'gongdi', 'newBlue', true)
                } else {
                    layerList = generateCurrentFenseLayer(fense, 'xnc', 'newgreen', true)
                }
                layerList[1].detail = fense
                //详情的点击事件
                // layerList[1].on('click', (e) => {
                //     console.log(e.target.detail);
                //     this.$emit('layerClick', e.target.detail)
                // })
                layerList.forEach(item => {
                    this._gdLayerGroup.addLayer(item)
                })
                this._gdBindLayerObj[fense.id] = layerList
                // console.log(this._gdBindLayerObj[fense.id]);
            })
            if (list.length == 1) {
                this.fitCurrentBounds(list[0])
            }
        },
        fitCurrentBounds(id) {
            let currentLayerList = this._gdBindLayerObj[id]
            if (!currentLayerList || !currentLayerList.length) return
            this.$refs['simplemap']._map.fitBounds(currentLayerList[0].getBounds())
        },
        realPlay(rowData) {
          this.downOption = {}
            this.play(rowData.start_time, rowData.end_time);
        },

        async play(startTime, endTime) {
            const params = {
                startTime: startTime,
                endTime: endTime
            }
            let changeIndex = 0;
            //单窗口模式 直接替换单前视频
            // if (!this.chn4Mode && !this.chn9Mode) {
            //     changeIndex = 0;
            // } else {
            //     const arrLength = this.chn4Mode ? 4 : 9;
            //     const index = this.videoPlayList.slice(0, arrLength).findIndex(item => item === null);
            //     const sameChnIndex = this.videoPlayList.findIndex(item => item && item.chn === chn);
            //     if (sameChnIndex !== -1) {
            //         changeIndex = sameChnIndex
            //     } else if (index === -1) {
            //         if (this.videoPlayList.length < arrLength) {//说明非空item的length 不足4或9
            //             changeIndex = this.videoPlayList.length;
            //         } else { //或者刚好等于4或9;
            //             //如果处于4通道模式并且通道数量大于4的情况  则自动切换到9通道模式
            //             if (this.chn4Mode && this.chnList.length > 4) {
            //                 changeIndex = 4;
            //                 this.toggleMultiple(9);
            //                 await this.$nextTick();
            //             }
            //             //逻辑上永远不会触发else
            //             // else {
            //             //     this.$warning('没有空余的窗口了，将替换同一通道的窗口');
            //             //     changeIndex = this.videoPlayList.findIndex(item => item.chn === chn);
            //             // }
            //         }
            //     } else {
            //         changeIndex = index;
            //     }
            // }
            //找到第一个空位塞进去
            const target = this.videoPlayList[changeIndex]
            if (target) {
                await this.$confirm('同一通道同时只能播放一个视频，是否替换', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                })
            }
            this.$set(this.videoPlayList, changeIndex, params);
            await this.$nextTick();
            let item = this.$refs['player'][changeIndex];
            item.loadSource()
            this.selectWindow(changeIndex)
        },
        realPlayDown(row) {
          this.videoPlayList = []
          if (this.downOption.id == row.id) {
            this.$warning("已添加该视频!");
            return;
          }
          this.downOption = {
            options: {
              sources: [
                {
                  type: "video/mp4",
                  src: row.url,
                },
              ],
            },
            id:row.id,
            startTime: moment(row.start_time).valueOf(),
            endTime: moment(row.end_time).valueOf(),
            name: this.currentVehicle.codeName + row.start_time
          }
        },
        // allPlay() {
        //     this.$nextTick(() => {
        //         this.$refs['player'].slice(0, this.chnList.length).forEach(item => {
        //             item.refresh()
        //         })
        //     })
        // },
        allStop() {
            this.$nextTick(() => {
                this.$refs['player'].forEach(item => {
                    item.stop()
                })
            })
        },
        currentTimeChange(index, time) {
            if (index === this.window.selected) {
                this.currentVideo.currentTime = moment(this.timeToDate(this.currentVideo.startTime)).add(time, 'seconds').toDate();
            }
        },
        downloadSelectTime(row, reupload = false) {
            this.timeShow.show = true;
            this.ftpMode = false;
            this.timeShow.startTime = moment(row.start_time, "YYYYMMDDHHmmss").format(
              "YYYY-MM-DD HH:mm:ss"
            );
            this.timeShow.endTime = moment(row.end_time, "YYYYMMDDHHmmss").format(
              "YYYY-MM-DD HH:mm:ss"
            );
            this.timeShow.rowData = row;
            this.timeShow.reupload = reupload;
        },
        confirmUpload() {
            this.$warning(
              "下载" +
              this.timeShow.startTime +
              "到" +
              this.timeShow.endTime +
              "的视频"
            );
            this.downloadQueue(this.timeShow,this.timeShow.reupload);
            this.timeShow.show = false;
        },
        //从真实地址下载
        download(name, url) {
            let aLink = document.createElement('a');
            let evt = document.createEvent("HTMLEvents");
            evt.initEvent("click", true, true);//initEvent 不加后两个参数在FF下会报错  事件类型，是否冒泡，是否阻止浏览器的默认行为
            aLink.download = name;
            aLink.href = url;
            aLink.target = '_blank';
            // aLink.dispatchEvent(evt);
            //aLink.click()
            aLink.dispatchEvent(new MouseEvent('click', {bubbles: true, cancelable: true, view: window}));//兼容火狐
        },
        //创建下载任务
        async downloadQueue(row, reupload = false) {
            row.loading = true
            try {
                // let ajax = $.post(API_URL, JSON.stringify({
                //     cmd: "upload",
                //     // terminal_no:'34020000001320000001',

                //     terminal_no: this.currentVehicle.code,
                //     start_time: row.start_time,
                //     end_time: row.end_time,
                //     reupload: reupload
                // }))
                let ajax = $.ajax({
                    url: API_URL,
                        type: 'post',
                        data: JSON.stringify({
                            cmd: "upload",
                        // terminal_no:'34020000001320000001',
                        channel_id: this.currentVehicle.code,
                        start_time: moment(row.startTime, 'YYYY-MM-DD HH:mm:ss').format('YYYYMMDDHHmmss'),
                        end_time: moment(row.endTime, 'YYYY-MM-DD HH:mm:ss').format('YYYYMMDDHHmmss'),
                        reupload: reupload
                        }),
                        headers: {
                            Authorization: 'token ' + this.token,
                        },
                })
                let res = null
                let result = await Promise.race([ajax, this.timeout(15000)]).then((value) => {
                    if (value == null) {
                        this.$error('上传失败,超时')
                        // this.sourceLoading = false;

                        return false
                    } else {
                        res = value
                        return true
                    }
                })
                if (!result) return
                if (res.code === 4) {
                    this.download(this.toFileName(row.rowData), res.data)
                } else if (res.code != 0) {
                    this.$error(this.$ct('messageInfo[4]'))
                } else {
                    let id = res.data

                    Object.assign(row.rowData, {
                        status: 0,
                        progress: 0,
                        id: res.data,
                        upload_start: row.startTime,
                        upload_end: row.endTime,
                    });
                    let uploadedObj = this.downloadTable.data.find(item => item.id == id)
                    //避免重复推入
                    if (uploadedObj) {
                        Object.assign(uploadedObj, {
                            ...JSON.parse(JSON.stringify(row.rowData)),
                            loading: false,
                        });
                    } else {
                        let downObj = JSON.parse(JSON.stringify(row.rowData));
                        this.downloadTable.data.push(
                          Object.assign(downObj, {
                              start_time: moment(row.startTime, "YYYY-MM-DD HH:mm:ss").format(
                                "YYYYMMDDHHmmss"
                              ),
                              end_time: moment(row.endTime, "YYYY-MM-DD HH:mm:ss").format(
                                "YYYYMMDDHHmmss"
                              ),
                              loading: false,
                          })
                        );
                    }
                    // let index = this.downloadTable.data.findIndex(item => item.id == row.id)
                    // if (index != -1) {
                    //     this.downloadTable.data.slice(index, 1, {
                    //         ...JSON.parse(JSON.stringify(row)),
                    //         loading: false,
                    //     })
                    // } else {
                    //     this.downloadTable.data.push({
                    //         ...JSON.parse(JSON.stringify(row)),
                    //         loading: false,
                    //     });
                    // }
                    this.$success(this.$ct('messageInfo[5]'));

                    clearInterval(this.timeInter);
                this.timeInter = null;
                this.intervalReflash()
                    // videoWS.subscribeProgressInfo([res.data])
                }
            } finally {
                row.rowData.loading = false;
                this.$forceUpdate();
            }
        },
        async downloadSelect(startTime, endTime) {
            let ajax = $.ajax({
                    url: API_URL,
                        type: 'post',
                        data: JSON.stringify({
                            cmd: 'upload',
                            // terminal_no: '34020000001320000001',
                            channel_id: this.currentVehicle.code,
                            start_time: startTime,
                            end_time: endTime,
                        }),
                        headers: {
                            Authorization: 'token ' + this.token,
                        },
                })
            let res = null
            let result = await Promise.race([ajax, this.timeout(15000)]).then((value) => {
                if (value == null) {
                    this.$error('获取上传列表失败,超时')
                    // this.sourceLoading = false;
                    return false
                } else {
                    res = value
                    return true
                }
            })
            if (!result) return
            if (res.code === 4) {
                this.download(this.terminal_name, res.data);
            } else if (res.code === 1) {
                this.$error(this.$ct('messageInfo[4]'));
            } else {

                this.$success(this.$ct('messageInfo[5]'));
                let index = this.downloadTable.data.findIndex(item => item.id == res.data)
                if (index != -1) {
                    this.downloadTable.data.slice(index, 1, {
                        start_time: startTime,
                        end_time: endTime,
                        status: 0,
                        progress: 0,
                        loading: false,
                        id: res.data,
                    })
                } else {
                    this.downloadTable.data.push({
                        start_time: startTime,
                        end_time: endTime,
                        status: 0,
                        progress: 0,
                        loading: false,
                        id: res.data,
                    });
                }
                clearInterval(this.timeInter);
                this.timeInter = null;
                this.intervalReflash()
                // videoWS.subscribeProgressInfo([res.data])

            }
        },
        async getDownListStatus() {
            
            let ids = [];
            ids = this.downloadTable.data
              .filter((item) => item.status == 0)
              .map((item) => item.id);
            if (!ids.length) {
                clearInterval(this.timeInter)
                return 
            };
            let params = {
                cmd: 'status',
                id:ids
            }
            let res = await $.ajax({
                type: "POST",
                url: API_URL,
                data: JSON.stringify(params),
                headers: {
                    Authorization: 'token ' + this.token,
                },
            });
            // let res = await $.get(API_URL_STATUS, {ids:["0341fb92-38f7-48b4-b9be-c7b02192e5f9"]})
            if (res.code != 0) {
                this.$error(res.message || "状态查询出错！");
                return;
            }
            res.data.forEach((data) => {
                const rows = [];
                const recordItem = this.recordTable.data.find(
                  (item) => item.id === data.id
                );
                recordItem && rows.push(recordItem);
                const downloadItem = this.downloadTable.data.find(
                  (item) => item.id === data.id
                );
                downloadItem && rows.push(downloadItem);
                rows.length &&
                rows.forEach((row) => {
                    switch (data.status) {
                        case 0: //正在上传
                            Object.assign(row, {
                                status: 0,

                                progress: Math.min(
                                  100,
                                  row.size
                                    ? row.ftp
                                      ? Number(((data.size / row.size) * 100).toFixed(1))
                                      : Number(data.percent.toFixed(1))
                                    : data.percent
                                      ? Number(data.percent.toFixed(1))
                                      : 0
                                ),
                                currentSize: data.size,
                            });
                            break;
                        case 1: //失败
                            Object.assign(row, {
                                status: 1,
                                loading: false,
                                currentSize: data.size,
                            });
                            break;
                        case 4: //上传完成
                            Object.assign(row, {
                                status: 4,
                                url: data.url,
                                loading: false,
                                currentSize: data.size,
                            });
                            break;
                    }
                });
                this.$forceUpdate();
            });
        },
        //定时器，定时刷新
        intervalReflash() {
            if(this.timeInter){
                clearInterval(this.timeInter);
                this.timeInter = null;
            }
            this.timeInter = setInterval(() => {
                this.getDownListStatus()
            }, 3500)
        },
        async search() {
          if(this.downOption.options){
            this.$refs.playerVideo.rePlay()
            return 
          }
            this.allStop();
            // if (!this.currentVehicle.code) {
            //     this.$warning('请选择场地设备')
            //     return;
            // }
            // this.getFenseDetail(this.fenseDetailQuery)
            await this.getRecordList();
        },

        onProgressHandle(data) {
            const rows = [];
            const recordItem = this.recordTable.data.find(item => item.id === data.id);
            recordItem && rows.push(recordItem);
            const downloadItem = this.downloadTable.data.find(item => item.id === data.id);
            downloadItem && rows.push(downloadItem);
            rows.length && rows.forEach(row => {
                Object.assign(row, {
                    status: 0,
                    progress: Math.min(
                        100, Number(data.percent.toFixed(1))
                    ),
                    // currentSize: data.size,
                })
            })
        },
        onStreamSavedHandle(data) {
            const rows = [];
            const recordItem = this.recordTable.data.find(item => item.id === data.id);
            recordItem && rows.push(recordItem);
            const downloadItem = this.downloadTable.data.find(item => item.id === data.id);
            downloadItem && rows.push(downloadItem);
            rows.length && rows.forEach(row => {
                Object.assign(row, {
                    status: 4,
                    url: data.url,
                    loading: false,
                    // currentSize: data.size,
                })
            });
        },
        async onStreamStopHandle(data) {
            const rows = [];
            const recordItem = this.recordTable.data.find(item => item.id === data.id);
            recordItem && rows.push(recordItem);
            const downloadItem = this.downloadTable.data.find(item => item.id === data.id);
            downloadItem && rows.push(downloadItem);
            if (rows.length) {
                let temp = rows[rows.length - 1];
                //steamStop事件有可能在uploadResult事件后
                if (temp.status === 4) return;
                //steamStop事件发生在uploadResult前并且1000ms内status没发生变化 则说明设备意外中断上传，需要显示上传失败
                await this.$utils.sleep(1000);
                if (temp.status === 4) return;
                rows.length && rows.forEach(row => {
                    Object.assign(row, {
                        status: 1,
                        loading: false,
                        // currentSize: data.size,
                    })
                });
            }
        },
        async getDownloadList(params) {
            try {
                let ajax = $.ajax({
                    url: API_URL,
                        type: 'post',
                        data: JSON.stringify({
                            cmd: 'queryUploadRecord',
                            ...params,
                        }),
                        headers: {
                            Authorization: 'token ' + this.token,
                        },
                })
                // console.log(res);
                let res = null
                let result = await Promise.race([ajax, this.timeout(15000)]).then((value) => {
                    if (value == null) {
                        this.$error('获取上传列表失败,超时')
                        // this.sourceLoading = false;

                        return false
                    } else {
                        res = value
                        return true
                    }
                })
                if (!result) return

                const downloadList = [];
                this.recordTable.data.forEach((item, index, array) => {
                    let temp = res.data.find((row) => {
                        return item.start_time === row.start_time &&
                            item.end_time === row.end_time;
                    })

                    Object.assign(item, {
                        status: temp ? temp.status : 3,
                        url: temp ? temp.url : '',
                        progress: -1,
                        id: temp ? temp.id : undefined,
                        loading: false,

                    });
                    if (temp) {

                        downloadList.push(JSON.parse(JSON.stringify(item)))
                    }
                });
                this.downloadTable.data = res.data.map(item => {

                    let temp = this.recordTable.data.find((row) => {
                        item.name = row.name
                        return item.start_time === row.start_time &&
                            item.end_time === row.end_time;
                    })


                    return {
                        ...item,
                        status: item ? item.status : 3,
                        url: item ? item.url : '',
                        progress: -1,
                        id: item ? item.id : undefined,
                        loading: false,

                    }
                });
                clearInterval(this.timeInter);

                this.timeInter = null;
                this.intervalReflash();
                //重新订阅进度信息
                // videoWS.subscribeProgressInfo(res.data.filter(item => item.status !== 4).map(item => item.id));
            } catch (e) {
                console.log(e);
                throw new Error(this.$ct('messageInfo[3]'))
            }
        },

        // positionFilter(value, row) {
        //     return row.channel_no === value;
        // },
        dateChange(date) {
            this.query.startTime = moment(date).startOf('day').toDate();
            this.query.endTime = moment(date).endOf('day').toDate();
        },
        timeout(delay) {
            return new Promise(resolve => {
                setTimeout(resolve, delay, null)
            })
        },
        async getRecordListAll() {
            let params = {
                channel_id: this.currentVehicle.code,
                // terminal_no: '34020000001320000001',

                start_time: moment(this.query.startTime).format('YYYYMMDDHHmmss'),
                end_time: moment(this.query.endTime).format('YYYYMMDDHHmmss'),
            }
            let ajax = $.ajax({
                    url: API_URL,
                        type: 'post',
                        data: JSON.stringify({
                            cmd: 'index',
                            ...params,
                        }),
                        headers: {
                            Authorization: 'token ' + this.token,
                        },
                })
            this.currentRecordAjax = ajax;
            // let result=await ajax
            let result = null
            let res = await Promise.race([ajax, this.timeout(15000)]).then((value) => {
                if (value == null) {
                    this.$error('获取视频列表失败,超时')
                    // this.sourceLoading = false;

                    return false
                } else {
                    result = value
                    return true
                }
            })
            if (!res) return
            this.currentRecordAjax = null;

            if (result.code === 0) {
                if (result.data.length == 0) {
                    this.$warning('没有录像列表')
                    return;
                }

                this.recordTable.data = result.data.map(item => {
                    return Object.assign(item, {
                        status: 3,
                        url: '',
                        progress: -1,
                        // fileName: '',
                        loading: false,
                    })
                });
                this.terminal_name = this.recordTable.data[0].name
                // console.log( this.recordTable.data);

                //分析结果得出 通道数量
                let chn = [...new Set(this.recordTable.data.map(item => item.channel_no))].sort((a, b) => a - b);
                //裁剪
                this.chnList = [1];
                // this.recordTable.data = this.recordTable.data
                //     .filter(item => this.chnList.includes(item.channel_no))
                //     .sort((a, b) => parseInt(a.start_time) - parseInt(b.start_time));
            } else {
                throw new Error('获取列表失败')
            }
            //等待ws连接  查询下载列表相关数据
            await this.getDownloadList(params);
        },
        abortRecordAjax() {
            if (!this.currentRecordAjax) return;
            if (this.currentRecordAjax instanceof Array) {
                this.currentRecordAjax.forEach(ajax => ajax.abort());
            } else {
                this.currentRecordAjax.abort();
            }
        },

        async getRecordList() {
            try {
                this.abortRecordAjax();
                if (!this.currentVehicle.code) {
                    this.$warning('请选择场地设备')
                    // this.$warning(this.$ct('messageInfo[1]'))
                    return;
                }
                this.getFenseDetail(this.fenseDetailQuery)
                this.setAxisData([]);
                //查询所有通道的录像列表
                // this.plateNoTemp = this.currentVehicle.plate_no;
                this.recordTable.data = [];
                this.recordTable.loading = true;
                //获取录像列表
                await this.getRecordListAll();
                //生成时间轴数据
                this.setAxisData(this.recordTable.data);
                //重新订阅进度信息
                // this.getProgressInfo();
            } catch (e) {
                if (e && e.readyState === 0) return;//ajax abort导致的出错就不提示
                this.$error(e)
            } finally {
                this.recordTable.loading = false;
            }
        },
        async cancelDownload(row) {
            let result = await $.ajax({
                    url: API_URL,
                        type: 'post',
                        data: JSON.stringify({
                            cmd: "cancelUpload",
                            id: row.id,
                        }),
                        headers: {
                            Authorization: 'token ' + this.token,
                        },
                })
            if (result.code === 0) {
                Object.assign(row, {
                    status: 3,
                    url: '',
                    progress: -1,
                    // fileName: '',
                    loading: false,
                    id: null,
                })
            } else {
                this.$error('取消下载失败');
            }
        },
        setAxisData(tableData) {
            // console.log(tableData);

            const startOfDay = moment(this.query.date).startOf('day');
            const timeData = new Array(this.chnList.length)
            // console.log(timeData);
            tableData.forEach(item => {
                // console.log(item);
                const startTime = (moment(item.start_time, 'YYYYMMDDHHmmss').diff(startOfDay)) / 1000;
                const endTime = (moment(item.end_time, 'YYYYMMDDHHmmss').diff(startOfDay)) / 1000;
                // console.log(item.start_time);
                const chn = 0;
                if (timeData[chn]) {

                    timeData[chn].push([startTime, endTime]);

                } else {
                    timeData[chn] = [[startTime, endTime]];
                }
            })
            //将时间轴约束在00:00 到 24:00
            timeData.forEach(chnData => {
                if (chnData[0][0] < 0) chnData[0][0] = 0;
                if (chnData[chnData.length - 1][1] > 86400) chnData[chnData.length - 1][1] = 86400;
            })
            // console.log(timeData);
            this.timeData = timeData;
        },

        async cmdAdapter(data) {
            // console.log(data);
            try {
                switch (data.cmd) {
                    case 'play': {
                        const {rangeList} = data;
                        if (rangeList.length === 0) {
                            this.$warning('该时间段内没有可用的录像！');
                            return;
                        }
                        if (!data.firstRange && rangeList.length >= 2) {
                            this.$warning('暂不支持播放多段视频，为您播放选区的第一部分！')
                        }
                        this.$refs['videoAxis'].selectRange(rangeList[0][0], rangeList[0][1]);
                        // console.log(rangeList);
                        const startTime = moment(this.query.date).startOf('day')
                            .add(rangeList[0][0], 'seconds').format('YYYYMMDDHHmmss');
                        const endTime = moment(this.query.date).startOf('day')
                            .add(rangeList[0][1], 'seconds').format('YYYYMMDDHHmmss');

                        this.play(startTime, endTime);
                        break;
                    }
                    case 'download': {
                        const {rangeList} = data;
                        if (rangeList.length === 0) {
                            this.$warning('该时间段内没有可用的录像！');
                            return;
                        }
                        if (!data.firstRange && rangeList.length >= 2) {
                            this.$warning('暂不支持下载多段视频，为您下载选区的第一部分！')
                        }
                        this.$refs['videoAxis'].selectRange(rangeList[0][0], rangeList[0][1]);
                        const startTime = moment(this.query.date).startOf('day')
                            .add(rangeList[0][0], 'seconds').format('YYYYMMDDHHmmss');
                        const endTime = moment(this.query.date).startOf('day')
                            .add(rangeList[0][1], 'seconds').format('YYYYMMDDHHmmss');

                        await this.downloadSelect(startTime, endTime);
                        break;
                    }

                }
            } catch (e) {
                this.$error(e);
            }
        },

    },
    mounted(){
        this.getSelectTreeList()
    },
    async created() {
        // this.getSelectTreeList()

        // let ws = videoWS.getInstance();
        // ws.on('progress', this.onProgressHandle);
        // ws.on('streamSave', this.onStreamSavedHandle);
        // ws.on('streamEnd', this.onStreamStopHandle);
        // // console.log(PONY.media.websocketChangdiUrl);
        // ws.init(PONY.media.websocketChangdiUrl);
        // let tryCount = 5;//剩余重试次数
        // const fn = async () => {
        //     if (!tryCount) return;
        //     await this.$utils.sleep(500);
        //     ws.init();
        //     console.log('ws重新链接');
        //     tryCount--;
        // }
        // ws.on('close', fn)
        // this.ws = ws;
    },
    beforeDestroy() {
        this.abortRecordAjax();
        this.ws?.off('progress', this.onProgressHandle);
        this.ws?.off('streamSave', this.onStreamSavedHandle);
        this.ws?.off('streamEnd', this.onStreamStopHandle);
        this.ws?.close();
        clearInterval(this.timeInter)
        this.timeInter = null
    }
}
</script>

<style scoped lang="scss">

.element-tree {
    height: 100% !important;
}

#workSiteVideo {
    $video-height: '100vh - 96px - 257px' ; //视屏区域的高度
    .query-top {
            height: calc(100% - 100px);
        }
        .query-bottom {
            margin-top: 5px;
            padding: 10px;

            .query-item {
                display: flex;
                height: 40px;
                line-height: 40px;
                justify-content: space-between;
                align-items: center;

                > span {
                    font-size: 12px;
                    white-space: nowrap;
                    margin-right: 5px;
                }
            }
        }
    .search-wrap {
        overflow: hidden;
        padding: 10px;

        .query-item {
            display: flex;
            align-items: center;
            padding: 5px;

            label {
                font-size: 12px;
                line-height: 32px;
                width: 65px;
            }

            .query-input {
                font-size: 12px;
                width: calc(100% - 50px);
            }
        }
    }

    .video-right-wrap {
        position: relative;
        display: flex;

        &[class*=multiple-] {
            .video-table-wrap .video-wrap {
                display: flex;
                flex-wrap: wrap;
                justify-content: space-between;
                padding: 1px 0 0 1px;

                .video-player {
                    border: 1px solid transparent;
                    margin-left: -1px;
                    margin-top: -1px;
                }
            }
        }

        &.multiple-4 .video-table-wrap .video-wrap .video-player {
            height: calc(50% + 1px);
            width: calc(50% + 1px);
        }

        &.multiple-9 .video-table-wrap .video-wrap .video-player {
            height: calc(33.333333% + 1px);
            width: calc(33.333333% + 1px);
        }

        .video-table-wrap {

            position: relative;
            height: 100%;
            width: calc((#{$video-height}) * 16 / 9);
            flex-shrink: 0;
            z-index: 1;

            .video-wrap {
                position: relative;
                flex-shrink: 0;
                float: left;
                height: calc(#{$video-height});
                width: 100%;

                .video-player {
                    position: relative;
                    height: 100%;
                    width: 100%;
                    float: left;
                    z-index: 1;

                    &.selected {
                        z-index: 2;
                        border-color: var(--color-primary);
                    }

                    &.maximize {
                        position: absolute;
                        top: 1px;
                        left: 1px;
                        z-index: 3;
                        width: 100% !important;
                        height: 100% !important;
                    }
                }

            }

            .fixed-button {
                position: absolute;
                right: 0;
                top: calc(#{$video-height});
                z-index: 1;

                > i {
                    top: 0;
                    font-size: 14px;
                    width: 29px;
                    height: 29px;
                    line-height: 29px;
                    text-align: center;
                    border-bottom: 1px solid transparent;

                    &:hover {
                        border-bottom-color: var(--border-color-lighter);
                        background: var(--background-color-light);
                    }

                }

            }
        }

        .extra-wrap {
            margin-left: 10px;
            overflow: hidden;
        }

    }

    .video-list {
        position: absolute;
        top: calc(#{$video-height});
        left: 0;
        height: 257px;
        width: 100%;
        z-index: 0;

        /deep/ .el-tabs__content {
            overflow: inherit;
        }
    }
}

.video-control {
    display: flex;
    justify-content: center;
    align-items: center;

    i[class^=el-icon-] {
        font-size: 14px;
    }

    i {
        cursor: pointer;
        margin-left: 5px;
    }
}

/deep/ .el-tree-node__content {
    font-size: 12px;
}
</style>

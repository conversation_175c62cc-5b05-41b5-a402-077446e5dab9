<template>
		<div class="realtime-1078">
				<Layout :contentLoading="loading" :hasColor="true">
						<template slot="aside">
								<div class="aside">
										<ElementTree :class="isShowControl? 'showCon': 'unShowCon'" type="vehicle" state onlineFilter
										             onlineCountTip @node-click="monitorTreeClick"></ElementTree>
										<HolderControl :terminal_no="terminal_no" :vehicle_id="vehicle_id" :clickItemNum="clickItemNum"
										               @changHeight="changHeight"></HolderControl>
								</div>
						</template>
						<template slot="query">
								<div class="query-item">
										<label>{{ $ct('currentSelect') }}：</label>
										<span class="font-blue">
												{{ currentVehicle ? currentVehicle.plateNo : $ct('none') }}
										</span>
								</div>
								<div class="break-item"></div>
								<div class="query-item" v-if="isStopMove && hasVideoPlaying">
										将于&nbsp;
										<span class="text text--brand"> {{ remaining }} </span>
										&nbsp;秒后停止播放
								</div>
								<div class="query-item">
										<el-button @click="vehicleChannelConfig" type="primary">视频通道配置</el-button>
								</div>
								<div class="query-item">
										<el-button :disabled="!currentVehicle || currentVehicle.inputType == 3" icon="pony-iconv2 pony-yuyin" :title="$ct('talkBack')"
										           @click="showTalkBack"></el-button>
										<el-button :disabled="!currentVehicle" icon="pony-iconv2 pony-zanting" :title="$ct('stopAll')"
										           @click="stopAll" :loading="stopping"></el-button>
										<el-button :disabled="!currentVehicle" icon="pony-iconv2 pony-shuaxin" :title="$ct('refresh')"
										           @click="playAll" :loading="playing"></el-button>
								</div>
						</template>
						<template slot="content">
								<VideoView :vehicleId="currentVehicle ?currentVehicle.id :''" ref="videoView" :autoPlay="true"
											playType="flv"
								           :windowNum="channelNo>4?channelNo>6?9:6:4"
										   :inputType="currentVehicle?currentVehicle.inputType:1"
								           :chnNo="channelNo"
										   :channelTalkback="currentVehicle ?currentVehicle.channelTalkback :1"
								           :channelNoList="channelNoList"
										   :channelNoNameObj="channelNoNameObj"
								           @clickItemIndex="changItem"
								           @playing="isplaying"
								></VideoView>
						</template>
						<TalkBack ref="talkBack"></TalkBack>
						<PonyDialog
								:show="show"
								title="车辆通道配置"
								:width="590"
								content-style="max-height:500px"
								@close="show = false"
								@confirm="confirmConfig"
						>
								<div class="leftVehicle">
										<el-button @click="vehicleChannelConfigOverAll" type="primary">部门通道配置</el-button>

										<ElementTree type="vehicle" @node-click="monitorTreeClickChannel"
										             @mounted="treeMounted"></ElementTree>
								</div>
								<div class="main">
										<el-select placeholder="请选择通道个数" v-model="channelNum" @change="changeChannelNum">
												<el-option :label="item+'个通道'" :value="item" v-for="item in [4,5,6,7,8,9]"
												           :key="item"></el-option>
										</el-select>
										<ul v-if="channelRort.length">
												<li v-for="setting in channelRort" :key="setting">
														<span>{{ channelDes[setting - 1] }}</span>
														<div class="icon">
																<i class="operate el-icon-sort-down" @click="changePosition(setting, true)"></i>
																<i class="operate el-icon-sort-up" @click="changePosition(setting, false)"></i>
														</div>

												</li>
										</ul>
								</div>
								<PonyDialog
										:show="overAll.show"
										title="部门通道配置"
										:width="590"
										@close="overAll.show = false"
										@confirm="confirmConfigOverAll"
								>
										<div class="leftVehicle">
												<!-- @mounted="treeMounted" -->
												<ElementTree type="department" ref="tree" @node-click="nodeClick"
												             :checkMode="false"></ElementTree>
										</div>
										<div class="main overall">
												<el-select placeholder="请选择通道个数" v-model="overAll.channelNum" @change="changeChannelNumOverAll">
														<el-option :label="item+'个通道'" :value="item" v-for="item in [4,5,6,7,8,9]"
														           :key="item"></el-option>
												</el-select>
												<ul v-if="overAll.channelRort.length">
														<li v-for="setting in overAll.channelRort" :key="setting">
																<span>{{ channelDes[setting - 1] }}</span>
																<div class="icon">
																		<i class="operate el-icon-sort-down"
																		   @click="changePositionOverAll(setting, true)"></i>
																		<i class="operate el-icon-sort-up"
																		   @click="changePositionOverAll(setting, false)"></i>
																</div>
														</li>
												</ul>
										</div>
								</PonyDialog>
						</PonyDialog>
				</Layout>
		</div>
</template>

<script>
/**
 * @Author: yezy
 * @Email: <EMAIL>
 * @Date: 2020-05-11 15:23:08
 * @LastEditors: xieyj
 * @LastEditTime: 2021-11-4 15:23:08
 * @Description:
 */
import VideoView from './component/videoPlayerV2/component/VideoView'
import TalkBack from './component/TalkBack/PopupTalkBack';
import HolderControl from "./component/videoPlayerV2/component/HolderControl";
import {mapState} from 'vuex';


export default {
		name: "realTime1078",
		components: {
				VideoView,
				TalkBack,
				HolderControl
		},
		data() {
				return {
						overAll: {
								show: false,
								loading: false,
								deptId: null,
								channelNum: 9,
								channelRort: [1, 2, 3, 4, 5, 6, 7, 8, 9],
						},
						show: false,
						channelNum: 9,
						channelDes: ['通道1', '通道2', '通道3', '通道4', '通道5', '通道6', '通道7', '通道8', '通道9'],
						channelRort: [1, 2, 3, 4, 5, 6, 7, 8, 9],
						contentHeight: 0,
						currentVehicle: null,
						loading: false,
						channelNo: 0,
						channelNoPony: 9,
						selectId: null,
						channelNoList: [],
						channelNoNameObj: {},
						remaining: 300,
						configTime: 300,
						isStopMove: false,
						playing: false,
						stopping: false,
						vehicle_id: '',
						terminal_no: '',
						clickItemNum: '',
						isShowControl: false,
						index: '',
						// 播放时间
						hasVideoPlaying: false,
						mouseMonitor: null,
						oldPageX: '',
						oldPageY: '',
						pageX: '',
						pageY: '',
				}
		},
		computed: {
				...mapState('auth', ['userInfo']),

		},
		mounted() {
				window.addEventListener('mousemove', (e) => {
						this.pageX = e.pageX
						this.pageY = e.pageY
				})
		},
		methods: {
				async nodeClick(data, node) {
						this.overAll.deptId = data.id
						this.defaultChannelOverAll(data.id)
				},
				// 云景控制是否显示，改变侧边栏高度
				changHeight(val) {
						this.isShowControl = val === true;
				},
				// 选中通道显示边框
				changItem(index) {
						this.index = index
						this.clickItemNum = this.channelNoList[index]
				},
				// async defaultChannel(){
				//     this.channelNum = 9
				//     let res = await this.operateChannelSortQuery(2,-1,9)
				//     if(res){
				//         this.channelRort = res
				//         return
				//     }
				//     this.channelRort = [1,2,3,4,5,6,7,8,9]
				// },
				async defaultChannelOverAll(id) {
						this.overAll.channelNum = 9
						let res = await this.operateChannelSortQuery(2, id, 9)
						if (res) {
								this.overAll.channelRort = res
								return
						}
						this.overAll.channelRort = [1, 2, 3, 4, 5, 6, 7, 8, 9]
				},
				changeChannelNum(val) {
						if (this.selectId) {
								let saveObj = JSON.parse(localStorage.getItem('channelConfig' + this.userInfo.id + this.selectId + val))
								if (saveObj) {
										this.channelRort = saveObj
								} else {
										let arr = []
										for (let i = 0; i < val; i++) {
												arr.push(i + 1)
										}
										this.channelRort = arr
								}
						} else {
								let arr = []
								for (let i = 0; i < val; i++) {
										arr.push(i + 1)
								}
								this.channelRort = arr
						}
				},
				/**
				 * @operateType :2:查询, 1:新增或修改, -2:删除恢复默认
				 * @vehicleid
				 */
				async operateChannelSortQuery(operateType, objId, channelNum) {
						let res = await this.$api.operateChannelSort({
								operateType,
								objId,
						})
						if (!res || res.status != 200 || !res.data.length || !res.data.find(item => item.size == channelNum)) {
								return false
						}
						return res.data.find(item => item.size == channelNum).sort
				},
				/**
				 * @operateType :2:查询, 1:新增或修改, -2:删除恢复默认
				 * @vehicleid
				 * @config: 配置的通道数和通道顺序
				 */
				async operateChannelSortConfig(operateType, objId, config = null) {
						let res = await this.$api.operateChannelSort({
								operateType,
								objId,
								config,
						})
						if (!res || res.status != 200) {
								return false
						}
						return res
				},
				async changeChannelNumOverAll(val) {
						if (this.overAll.deptId) {
								let res = await this.operateChannelSortQuery(2, this.overAll.deptId, val)
								if (res) {
										this.overAll.channelRort = res
										return
								}
						}

						let arr = []
						for (let i = 0; i < val; i++) {
								arr.push(i + 1)
						}
						this.overAll.channelRort = arr
				},
				vehicleChannelConfig() {
						// this.defaultChannel()
						// this.channelNum = 9
						this.show = true
				},
				vehicleChannelConfigOverAll() {
						this.deptId = null
						this.overAll.channelNum = 9
						this.overAll.channelRort = [1, 2, 3, 4, 5, 6, 7, 8, 9],
								this.overAll.show = true
				},
				// 确认配置
				async confirmConfig() {
						if (!this.selectId) {
								this.$warning('请选择车辆！')
								return
						}
						let res = await this.operateChannelSortConfig(1, this.selectId, [{
								size: this.channelNum,
								sort: this.channelRort
						}])
						if (res) {
								this.$success('配置成功！')
								this.show = false
								if (this.currentVehicle && this.currentVehicle.id == this.selectId && this.channelNoList.length == this.channelNum) {
										this.channelNoList = this.channelRort
								}
						} else {
								this.$error('配置失败！')

						}
				},
				// 确认配置全局
				async confirmConfigOverAll() {
						this.overAll.loading = true
						if (!this.overAll.deptId) {
								this.$warning('请选择部门！')
								return
						}
						let res = await this.operateChannelSortConfig(1, this.overAll.deptId, [{
								size: this.overAll.channelNum,
								sort: this.overAll.channelRort
						}])
						if (res) {
								this.overAll.loading = false
								this.$success('配置成功！')
								this.overAll.show = false
						}
						if (this.currentVehicle && this.currentVehicle.id) {
								let res = await this.operateChannelSortQuery(2, this.currentVehicle.id, this.channelNoList.length)
								if (res) {
										this.channelNoList = this.overAll.channelRort
								}
						}
				},
				// 右侧改变顺序
				changePosition(current, type) {
						let index = this.channelRort.findIndex(item => item == current)
						let changeIndex
						if (type) {
								if ((index + 1) == this.channelRort.length) return
								changeIndex = index + 1
						} else {
								if ((index - 1) < 0) return
								changeIndex = index - 1
						}
						this.channelRort[index] = this.channelRort.splice(changeIndex, 1, this.channelRort[index])[0];
				},
				//全局
				changePositionOverAll(current, type) {
						let index = this.overAll.channelRort.findIndex(item => item == current)
						let changeIndex
						if (type) {
								if ((index + 1) == this.overAll.channelRort.length) return
								changeIndex = index + 1
						} else {
								if ((index - 1) < 0) return
								changeIndex = index - 1
						}
						this.overAll.channelRort[index] = this.overAll.channelRort.splice(changeIndex, 1, this.overAll.channelRort[index])[0];
				},
				showTalkBack() {
						if (!this.currentVehicle) return;
						this.$refs['talkBack'].showModal(this.currentVehicle.code, this.currentVehicle.channelTalkback,this.currentVehicle.plateNo + ' 对讲',this.currentVehicle.id)
				},
				async checkSimFlowOver() {
						if (!this.vehicle_id || !this.terminal_no) return false
						let res = await this.$api.getSimFlowUsedInfo({
								vehicle_id: this.vehicle_id,
								terminal_no: this.terminal_no,
						})
						if (res.status === 200) {
								this.remaining = res.data.time_over * 60
								this.configTime = res.data.time_over * 60
						} else {
								this.$error(res.message)
						}

				},
				async monitorTreeClick(data, node, $node) {
					
						this.terminal_no = data.terminalNo
						this.vehicle_id = data.id
						if (data.type === 4) {
							if(!data.isVideoSupport){
								this.$info('该车辆不支持视频相关服务,请刷新页面或咨询运维')
								return
							}
								await this.checkSimFlowOver();
								if (this.currentVehicle) {
										if (this.currentVehicle.id == data.id) {
												if (this.$refs['videoView'].hasVideoPlaying) return
												this.playAll()
										} else {
												this.stopAll()
										}
								}

								let result = await this.$api.getChannelNoByVehicleId({
										vehicle_id: data.id + ":V2"
								})
								this.channelNo = result.data.channel_count;
								this.channelNoList = []
								this.channelNoNameObj = {}

								result.data.channel_valid_v2.forEach((item, index) => {
										if (item) {
												this.channelNoList.push(index + 1)
										}
								})
								result.data.channel_valid_name && result.data.channel_valid_name.length && result.data.channel_valid_name.forEach((item, index) => {
										this.channelNoNameObj[item.no] = item.name
								})
								if (!this.channelNoList.length) {
										this.channelNoList = [0, 0, 0, 0]
								}
								if (this.channelNoList.length >= 4) {
										let res = await this.operateChannelSortQuery(2, data.id, this.channelNoList.length)
										if (res) {
											this.channelNoList = this.sortByAnotherArray(this.channelNoList,res)

												// this.channelNoList = res
										}
								}
								this.currentVehicle = {
										id: data.id,
										plateNo: data.name,
										code: data.terminalNo,
										channelTalkback:data.channelTalkback,
										inputType:data.inputType
								};

								//this.playAll() //再次点击重新发请求
						}
						this.clickItemNum = this.channelNoList[this.index]
				},
				sortByAnotherArray(arr1, arr2) {
					// 创建一个副本避免修改原数组
					const sortedArr = [...arr1];
					
					sortedArr.sort((a, b) => {
						const indexA = arr2.indexOf(a);
						const indexB = arr2.indexOf(b);
						
						// 如果两个元素都在arr2中，按arr2的顺序排序
						if (indexA !== -1 && indexB !== -1) {
						return indexA - indexB;
						}
						// 如果只有a在arr2中，a排在前面
						else if (indexA !== -1) {
						return -1;
						}
						// 如果只有b在arr2中，b排在前面
						else if (indexB !== -1) {
						return 1;
						}
						// 如果都不在arr2中，保持原顺序
						else {
						return 0;
						}
					});
					
						return sortedArr;
					},
				treeMounted($elTree) {
						if (!this.vehicle_id) return
						const node = $elTree.getNode(this.vehicle_id);
						node && node.expand(null, true);
						$elTree.setCurrentKey(this.vehicle_id)
						this.monitorTreeClickChannel({
								type: 4,
								id: this.vehicle_id
						})
				},
				async monitorTreeClickChannel(data, node, $node) {
						if (data.type === 4) {
								this.selectId = data.id

								let result = await this.$api.getChannelNoByVehicleId({
										vehicle_id: data.id + ":V2"
								})
								// this.channelNoPony = result.data.channel_count;
								this.channelNum = result.data.channel_count
								if (this.channelNum >= 4) {
										let res = await this.operateChannelSortQuery(2, data.id, this.channelNum)
										if (res) {
												this.channelRort = res
										} else {
												// 有单车辆的就用但车辆的，没有的话用默认的
												let arr = []
												result.data.channel_valid_v2.forEach((item, index) => {
														if (item) {
																arr.push(index + 1)
														}
												})
												this.channelRort = arr

										}
								}
						} else {
								this.$warning('请选择车辆！')
						}
				},
				isplaying() {
						let flag = false
						let list = this.$refs.videoView.$refs.player
						for (let i = 0; i < list.length; i++) {
								if (list[i].isPlaying === true) {
										flag = true
										this.hasVideoPlaying = true
										this.setMouseInterval()
								}
						}
						if (!flag) {
								this.hasVideoPlaying = false
								this.clearInterval()
						}
				},
				clearInterval() {
						if (this.mouseMonitor) {
								clearTimeout(this.mouseMonitor)
								this.mouseMonitor = null
						}
				},
				setMouseInterval() {
						if (this.mouseMonitor) {
								clearInterval(this.mouseMonitor)
						}
						this.mouseMonitor = setInterval(async () => {
								if (this.oldPageX === this.pageX && this.oldPageY === this.pageY) {
										this.remaining -= 1
										if (this.remaining <= 0) {
												await this.$refs['videoView'].stopAll()
												setTimeout(() => {
														alert('您已长时间不操作系统，视频已经自动关闭！')
												}, 500)
												clearTimeout(this.mouseMonitor)
												this.mouseMonitor = null
										}
										this.isStopMove = true
								} else {
										this.remaining = this.configTime
										this.isStopMove = false
								}
								this.oldPageY = this.pageY
								this.oldPageX = this.pageX
						}, 1000)
				},
				async stopAll() {
						try {
								this.stopping = true;
								await this.$refs['videoView'].stopAll(false);
						} finally {
								this.stopping = false;
						}
				},
				async playAll() {
						try {
								this.playing = true;
								await this.$refs['videoView'].refresh();
						} finally {
								this.playing = false;
						}
				}
		},
		beforeDestroy() {
				this.clearInterval()
				window.removeEventListener('mousemove', () => {
				})
		},
    deactivated() {
        this.clearInterval()
        this.stopAll()
    }
}
</script>

<style scoped lang="scss">
.realtime-1078 {
		height: 100%;
		width: 100%;

		.leftVehicle {
				float: left;
				height: calc(100% - 18px);
				width: calc(100% - 210px);
		}

		.main {
				padding-top: 38px;
				padding-left: 20px;
				float: left;
				overflow: scroll;
				width: 210px;
				.operate {
						font-size: 16px;
						cursor: pointer;
				}

				ul {
						height: 100%;
				}

				li {
						width: 100%;
						line-height: 30px;

						span {
								font-size: 14px;
								display: inline-block;
						}

						.icon {
								float: right;
						}
				}
		}

		.overall {
				padding-top: 10px;
		}

		.aside {
				height: 100%;
				width: 100%;
				display: flex;
				flex-direction: column;
				position: absolute;
				top: 0;
				left: 0;
				bottom: 0;
				right: 0;
				overflow: hidden;

				.showCon {
						max-height: calc(100% - 160px) !important;
				}

				.unShowCon {
						height: calc(100% - 33px) !important;
				}
		}
}
</style>

<template>
  <div class="realtime-video-v2">
    <Layout :contentLoading="loading" :hasColor="true">
      <template slot="aside">
        <div class="aside" ref="aside">
          <div ref="main" class="unShowCon">
            <ElementTree ref="tree" type="vehicleWithChannel" state onlineFilter onlineCountTip :renderContent="true"
              @renderContentClick="renderContentClick" :checkMode="false" @node-click="nodeClick" v-show="!checkMode"
              @node-expand="nodeExpand" :videoShow="true" @channelStatus="changeChannels"></ElementTree>
            <ElementTree ref="tree1" type="vehicle" state onlineFilter onlineCountTip :renderContent="true"
              @renderContentClick="renderContentClick" :checkMode="true" @check="selectNodes" v-show="checkMode">
            </ElementTree>
          </div>

          <HolderControl :terminal_no="terminal_no" :vehicle_id="vehicle_id" :clickItemNum="clickItemNum"
            :currentVehicle="currentClick" :selectNodesList="selectNodesList" @changHeight="changHeight"
            @changeTree="changeTree"></HolderControl>
        </div>
      </template>
      <template slot="query">
        <div class="query-item">
          <!-- <el-button
												icon="pony-iconv2 pony-sifenping"
												:class="{ active: windowNum === 4 }"
												:title="
              `${$ct('switchTo')}${windowNum === 4 ? defaultNum : 4}${$ct(
                'windowMode'
              )}`
            "
												@click="setWindowNum(4)"
										></el-button>
										<el-button
												icon="pony-iconv2 pony-jiufenping"
												:class="{ active: windowNum === 9 }"
												:title="
              `${$ct('switchTo')}${windowNum === 9 ? defaultNum : 9}${$ct(
                'windowMode'
              )}`
            "
												@click="setWindowNum(9)"
										></el-button>
										<el-button
												icon="pony-iconv2 pony-liufenping"
												:class="{ active: windowNum === 6 }"
												:title="
              `${$ct('switchTo')}${windowNum === 6 ? defaultNum : 6}${$ct(
                'windowMode'
              )}`
            "
												@click="setWindowNum(6)"
										></el-button
										>
										<el-button
												icon="pony-iconv2 pony-shifenping"
												:class="{ active: windowNum === 10 }"
												:title="
              `${$ct('switchTo')}${windowNum === 10 ? defaultNum : 10}${$ct(
                'windowMode'
              )}`
            "
												@click="setWindowNum(10)"
										></el-button> -->
          <VideoNumSelect :line="4" :column="4" :disabled="playing" @changeNum="changeNum" :title="false">
          </VideoNumSelect>
          <!-- <el-select v-model="value" placeholder="请选择" style="width: 100px;margin-left:10px">
                        <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
                        </el-option>
                    </el-select> -->
          <el-button icon="pony-iconv2 pony-quanping" title="全屏" @click="$refs['videoView'].toggleFullScreen()"
            style="margin-left:10px; height:28px;"></el-button>
        </div>
        <div class="break-item"></div>
        <div class="query-item" v-if="isStopMove && hasVideoPlaying">
          {{ $ct("will") }}&nbsp;
          <span class="text text--brand"> {{ remaining }} </span>
          &nbsp;{{ $ct("stopPlay") }}
        </div>
        <div class="query-item">
          <el-button icon="pony-iconv2 pony-sanjiaoxing" :title="$ct('play') + $ct('all')" @click="playAll"
            :loading="playing"></el-button>
          <el-button icon="pony-iconv2 pony-zanting" :title="$ct('stop') + $ct('all')" @click="stopAll"
            :loading="stopping"></el-button>
          <el-button icon="pony-iconv2 pony-tingzhi" :title="$ct('clearSelected')" @click="clearAll"></el-button>
        </div>
      </template>
      <template slot="content">
        <div class="videoContent">
          <div class="videos">
            <VideoViewV2 ref="videoView" :value="value" :channelTalkback="currentClick ? currentClick.channelTalkback : 1"
              :autoPlay="true" :windowNum="windowNum" @clickItemIndex="changItem" @changeVehicle="openVehiclePopup"
              @playing="isplaying" :line="line" :column="column" @removeMarker="removeVehicleMarker"></VideoViewV2>
          </div>
          <div class="vehicleMap">
            <MaticsMap ref="maticMap" @changeClear="clearMap" @mapReady="generateMapObj">
            </MaticsMap>
          </div>
        </div>
      </template>
      <Popup ref='vehicle' :minWidth="350" :maxWidth="400" :auto-close='false' :close-on-click='false'>
        <MonitorPopup :setting="table.popupSetting" :vehicleInfo="currentState.popupResult" ref="videosPopup"
          :button="false" v-if="currentState.popupResult">
        </MonitorPopup>
      </Popup>
    </Layout>
  </div>
</template>

<script>
/**
 * @Author: yezy
 * @Email: <EMAIL>
 * @Date: 2020-05-11 15:23:20
 * @LastEditors: yezy
 * @LastEditTime: 2020-05-11 15:23:20
 * @Description: 九宫格模式
 */
import VideoViewV2 from "./component/videoPlayerV2/component/VideoViewV2";
import ElementTree from "@/components/tree/ElementTree";
// import HolderControl from "./component/videoPlayerV2/component/HolderControl";
import HolderControl from "../videoPlayStandard/component/videoPlayerStandard/component/HolderControl";
import MaticsMap from '@/view/monitor/leaflet/leafletMap/MaticsMapV2';
import MonitorPopup from '@/view/monitor/page/Monitor/components/MonitorPopup'

import VideoNumSelect from './component/videoPlayerV2/component/VideoNumSelect';
import Popup from '@/view/monitor/components/Popup'
import { mapState } from 'vuex'
import * as MONITOR from '@/view/monitor/page/Monitor/util/monitor'
import { rollerStatusValue } from '@/view/monitor/page/Monitor/util/monitor'
import * as PUBLICUTIL from '@/view/monitor/util/monitorUtil'
import { transVehicleBoxState } from '@/view/monitor/page/Monitor/util/hqMonitorHandle'
import { getVehicleLastHumidity, getVehicleLastTemp, stateDesc } from '@/view/monitor/page/Monitor/util/ftMonitorHandle'
import { ftDefaultData } from '@/view/monitor/page/Monitor/util/ftDetail'

import {
  ALARM,
  // CanDataUpload,
  DangerEvent,
  DataDriverInfo,
  DataWayBill,
  GNSS,
  GPS,
  LockStatus,
  MotorInfo,
  ObdInfo,
  StatsInfo,
  TempEvent,
  TempUpload,
  TerminalLink,
  TrailStateChange
} from '@/service/wsService'




export default {
  name: "realTime1078V2",
  _i18Name: "realTime1078",
  components: {
    VideoViewV2,
    ElementTree,
    HolderControl,
    MaticsMap,
    MonitorPopup,
    VideoNumSelect,
    Popup
  },
  data() {
    const getPopupSetting = [
      // { name: '部门', key: 'format_dept', size: 'all' },
      { name: '司机', key: 'driver_name', size: 'half' },
      { name: '电话', key: 'driver_phone', size: 'half' },
      { name: '速度', key: 'gpsSpeed', size: 'half', tip: 'km/h' },
      { name: 'ACC状态', key: 'format_acc', size: 'half' },
      { name: '里程', key: 'mile', size: 'half' },
      { name: '设备', key: 'terminalNo', size: 'half' },
      // { name: '位置', key: 'location', size: 'all' },
    ]
    return {
      currentClick: null,
      checkMode: false,
      loading: false,
      windowNum: 16,
      defaultNum: 16,
      line: 4, // 行
      column: 4, // 列
      remaining: 300,
      configTime: 300,
      isStopMove: false,
      selectNodesList: [],
      channelNameObj: {},
      playing: false,
      stopping: false,
      vehicle_id: "",
      terminal_no: "",
      clickItemNum: "",
      isShowControl: false,
      getPopupSetting,
      options: [
        {
          value: "1",
          label: "满画面",
        },
        {
          value: "2",
          label: "2:1显示",
        },
        {
          value: "3",
          label: "16:9显示",
        },
        {
          value: "4",
          label: "4:3显示",
        },
        {
          value: "5",
          label: "1:1显示",
        },
      ],
      value: "1",
      mouseMonitor: null,
      oldPageX: '',
      oldPageY: '',
      pageX: '',
      pageY: '',
      hasVideoPlaying: false,
      table: {
        popupSetting: [],
        vehicleInfo: null,
      },
      activeCar: null,
      _markerCluster: null,
      _vehicleInfoData: {},
      _vehicleMarkerInfo: {},
      _map: null,
      checkedVehicleIds: [], //车辆树选中的ID存一下
      onLineVehicleIds: [],
      offLineVehicleIds: [],
      workVehicleIds: [],
      clusterVehicleList: [],
      orderStateDes: ['正供', '出厂', '在工地', '回厂', '供毕'],
      source: {
        subscriptList: [],
        interVaList: [],
        gpsList: [],
        gsixList: [],
        tempList: [],
        zaizList: [],
        terminalLinkList: [],
        ftList: [],
        lockList: [],
        gnssList: [],
        wayBillList: [],
        dataDriverInfo: [],
      },
      currentState: {  //车辆卡片信息内容，参考实时监控
        realTime: false,
        lock: null,
        popup: null,
        popupResult: null, //默认车牌为空不然组件提示报错
        currenthgdxnc: null
      },
      channelNoCheckedVehicleIds: [], //通道树选中的车辆id存一下
      vehicleVideoStatus: [],  //记录播放中的车辆
      channels: [], //
    };
  },
  async mounted() {
    window.addEventListener('mousemove', (e) => {
      this.pageX = e.pageX
      this.pageY = e.pageY
    })
    // 加载底下表格和车辆卡片上得显示配置
    let interFace = [
      this.$api.operateUserTableConfig({ operate_type: 2, user_id: "-1", code: 'monitor_popup' })      //地图配置已经配置的列表（地图卡片上的显示的配置）
    ]
    let result = await Promise.all(interFace)
    let response = result && result.length
    // this.table.setting = response?JSON.parse(result[0].data.config_value):MONITOR.getTableSetting
    this.table.popupSetting = response ? JSON.parse(result[0].data.config_value) : this.getPopupSetting
    // 订阅需要得数据信息
    this.beginSubScribe()
    // 开启定时器处理订阅和需要刷新得信息
    this.startInterVal()
    await this.readyMonitorDataInfo()
    this.$refs.main.style.height = this.$refs.aside.offsetHeight - 980 + 'px'
  },
  computed: {
    ...mapState('main', ['company']),
    ...mapState('vehicle', ['vehicleOnlineJudge']),
  },
  methods: {
    changeChannels(list) {
      this.channels = list
    },
    startInterVal() {
      this.source.interVaList.push(
        setInterval(this.handleGpsRefresh, 1000),
        setInterval(this.handleTerminalLinkRefresh, 1000),
        setInterval(this.handleGsixRefresh, 15 * 1000),
        setInterval(this.handleOnlineRefresh, 30 * 1000),
        setInterval(this.handleTempRefresh, 10 * 1000),
        setInterval(this.handleZaizRefresh, 10 * 1000),
        setInterval(this.handleFtRefresh, 10 * 1000),
        setInterval(this.handleLockRefresh, 1 * 1000),
        setInterval(this.handleGNSSRefresh, 10 * 1000),
        setInterval(this.handleDataWayBillRefresh, 10 * 1000),
        setInterval(this.handleDataDriverInfoRefresh, 10 * 1000),
      )
    },

    /**
     * 如果说每次来的数据都立刻处理得话 CPU 占用情况会很大，
     * 这里是先将从他存储起来通过定时器定时处理每一笔数据
     *
     * 如果说是一些不常见得数据则是立刻处理得
     */
    beginSubScribe() {
      this.source.subscriptList.push(
        GPS.subscribe(msg => this.source.gpsList.push(msg)),
        TerminalLink.subscribe(msg => this.source.terminalLinkList.push(msg)),
        ObdInfo.subscribe(msg => this.source.gsixList.push(msg)),
        //国六发动机
        MotorInfo.subscribe(msg => this.source.gsixList.push(msg)),
        // 温度上报
        TempUpload.subscribe(msg => this.source.tempList.push(msg)),
        TempEvent.subscribe(msg => this.source.zaizList.push(msg)),
        //福田数据查看
        // CanDataUpload.subscribe(msg => this.source.ftList.push(msg)),
        //茅台锁状态
        LockStatus.subscribe(msg => this.source.lockList.push(msg)),
        // 拖车状态
        TrailStateChange.subscribe(msg => {
          let vehicle = this._vehicleInfoData[msg.vehicleId]
          Object.assign(vehicle, { trailState: msg.trailState })
          if (msg.trailState == 2) {
            this.workVehicleIds.add(msg.vehicleId)
          } else {
            this.workVehicleIds.remove(msg.vehicleId)
          }
        }),
        StatsInfo.subscribe(msg => { this.handleVehicleMileState(msg) }),
        GNSS.subscribe(msg => { this.source.gnssList.push(msg) }),
        DataWayBill.subscribe(msg => { this.source.wayBillList.push(msg) }),
        DataDriverInfo.subscribe(msg => { this.source.dataDriverInfo.push(msg) }),
      )

    },
    /**
*  为了确保数据合并得是想要得数据，我这里都对数据重新处理了下
*  这里得数据来源有点广泛四通八达
*/
    async readyMonitorDataInfo() {
      this._vehicleInfoData = {}
      this._vehicleMarkerInfo = {}

      let collectVehicle = await MONITOR.initCollectFormat()
      let timeVideoVehicle = MONITOR.initVideoFormat()
      let driverBindVehicle = MONITOR.initDriverFormat()
      let interFace = [
        MONITOR.initGsixData(),
        MONITOR.initClzclCustomFormat(),
        this.$api.getCarInfoAndGpsAlarm(),
        getVehicleLastTemp(),
        getVehicleLastHumidity(),
        this.$api.deviceOnline({
          vehicleIdList: []
        })
      ]
      const [gsixVehicleInfo, clzclCustomInfo, lastVehicleInfo, lastTempObj, lastHumidityObj, lastDeviceRes] = await Promise.all(interFace)
      //  2021 01 29 庆丰得接口速度莫名变快，导致树都还没处理好
      // await this.$refs.tree.checkTreeOk()

      if (!lastVehicleInfo || lastVehicleInfo.status != 200) {
        this.$error(lastVehicleInfo.message || '查询最后位置信息出错')
        return
      }
      let lastDeviceOnline = {}
      if (!lastDeviceRes || lastDeviceRes.status != 200) {
        this.$error(lastDeviceRes.message || '查询最后链路状态出错')
        return
      }
      lastDeviceRes.data.length && lastDeviceRes.data.forEach(item => {
        lastDeviceOnline[item.vehicleId] = item
      })
      lastVehicleInfo.data.lastGps.forEach((item) => {
        item.terminalLinkStatus = lastDeviceOnline[item.id].status
        MONITOR.initVehicleBasicData(item)
        if (timeVideoVehicle && timeVideoVehicle[item.id]) {
          Object.assign(item, timeVideoVehicle[item.id])
        }
        if (driverBindVehicle && driverBindVehicle[item.id]) {
          Object.assign(item, driverBindVehicle[item.id])
        }
        if (collectVehicle && collectVehicle[item.id]) {
          item.collected = true
        }
        if (clzclCustomInfo && clzclCustomInfo[item.id]) {
          Object.assign(item, clzclCustomInfo[item.id])
          this.workVehicleIds.add(item.id)
        }
        if (gsixVehicleInfo && gsixVehicleInfo[item.id]) {
          item.GSIX = gsixVehicleInfo[item.id]
        }
        if (lastTempObj && lastTempObj[item.id]) {
          Object.assign(item, lastTempObj[item.id])
        }
        if (lastHumidityObj && lastHumidityObj[item.id]) {
          Object.assign(item, lastHumidityObj[item.id])
        }

        Object.assign(item, ftDefaultData)
        // if(item.latlng && item.latlng[0] && item.latlng[1]) {
        //     let marker = this.generateVehicleMarker(item)
        //     this._vehicleMarkerInfo[item.id] = marker
        // }
        // console.log('vehicle', JSON.stringify(item))
        // this.$refs.tree.changeNodeView(item.id, item.state)
        this._vehicleInfoData[item.id] = item
        this.checkedVehicleOnlineState(item)
      })

      // console.log('this._vehicleInfoData', this._vehicleInfoData)

      // 重新统计
    },
    /**
* 在车辆判断在线离线分配之后，需要把一些值进行制空，不然让人家看到车辆离线但是还是有速的就不好了
*/
    checkedVehicleOnlineState(vehicle) {
      if (vehicle.state == vehicle._state) return
      vehicle._state = vehicle.state
      if (PUBLICUTIL.checkCarOffLine(vehicle.state)) {
        if (this.vehicleOnlineJudge[vehicle.id] == 1) {
          vehicle.acc = false
        }
        vehicle.gpsSpeed = 0
        vehicle.gpsSinglep = 0
        vehicle.gsmSinglep = 0
        this.offLineVehicleIds.add(vehicle.id)
        this.onLineVehicleIds.remove(vehicle.id)
      } else {
        this.onLineVehicleIds.add(vehicle.id)
        this.offLineVehicleIds.remove(vehicle.id)
      }
    },
    /**
* 为了方式数据污染不得已采用深拷贝得方式重新对卡片得内容进行手动刷新
* 催得紧，就这么改了，这样改东西就少了，最大限度的减少了效率方面的影响 纯手工刷新 奥里给
*/
    reflashVehiclePopup(id) {
      let vehicle = JSON.parse(JSON.stringify(this._vehicleInfoData[id]))
      // console.log(vehicle,'vehicle')
      // 只有打开的时候才去读取这个，还有要求加的这几个都是一个类别的，只用单独处理一个就好了 2020 10 29  杨涵
      // 巧了，接口那里刚好 有这些字段，不用自己创建假的字段了，真香
      if (vehicle.extand_detail) {
        let extand = transVehicleBoxState(vehicle.extand_detail)
        Object.assign(vehicle, extand)

        if (vehicle.extand_detail['T59']) {
          let arr = vehicle.extand_detail['T59'].split("|")
          vehicle.rollerState = rollerStatusValue[arr[0]] || '-'
          vehicle.dischargingNumber = rollerStatusValue[arr[2]] || '-'
        }
      }
      this.currentState.popupResult = vehicle
      if (this.currentState.lock) {
        let marker = this._vehicleMarkerInfo[this.currentState.popup]
        if (marker && this._markerCluster.hasLayer(marker)) {
          this.$refs['maticMap']._map.setView(marker.getLatLng(), 19)
        }
      }
    },
    /**
* 生成marker 没有什么水准得就是拿捏得死一些就好了
*/
    generateVehicleMarker(vehicle) {
      let marker = null
      if (vehicle && vehicle.latlng && vehicle.latlng[0] && vehicle.latlng[1]) {
        let rotationAngle = 0
        let content = `<div>${vehicle.plate}</div>`
        if (PUBLICUTIL.checkCarRunState(vehicle.state)) {
          rotationAngle = vehicle.dire
          content = content + `<div>${vehicle.gpsSpeed}km/h</div>`
        }
        let icon = PUBLICUTIL.getVehicleMapIcon(vehicle.state)
        let zIndexOffset = vehicle.isFeeEnd ? 10000 : 1000
        marker = new L.Marker(vehicle.latlng,
          { icon: icon, rotationAngle: rotationAngle, zIndexOffset: zIndexOffset })
          .bindTooltip(content, { permanent: true, offset: [11, 0], direction: 'right' })
          .openTooltip()
        marker.vehicle_id = vehicle.id
        marker.on('click', (e) => {
          this.openCurrentVehicleInfo(+vehicle.id)
        })
      }

      return marker
    },
    /**
*  打开车辆得信息卡片
*/
    openCurrentVehicleInfo(id) {
      const vehicle = this._vehicleInfoData[id]
      const marker = this._vehicleMarkerInfo[id]
      if (!vehicle || !vehicle.gpsTime) {
        this.$message({ showClose: true, message: "车辆从未上线！", type: 'warning' })
        return
      }
      if (!marker) {
        if (!vehicle.latlng || !vehicle.latlng[0] || !vehicle.latlng[1]) {
          this.$message({ showClose: true, message: '车辆定位异常', type: 'warning' })
          return
        } else {
          // this.$refs.tree.checkCurrentVehicle(id)
        }
      }
      this.reflashVehiclePopup(id)
      if (marker && this._markerCluster.hasLayer(marker)) {
        this.currentState.popup = id
        this.$nextTick(() => {
          this.$refs.vehicle.open(marker)
          this.$refs['maticMap']._map.setView(marker.getLatLng(), 19, { paddingBottomRight: [10, 3] })
        })
      } else {
        // this.$refs.tree.checkCurrentVehicle(id)
      }
      // this.$refs.cluster.close()
      this.$nextTick(() => {
        this.$refs.videosPopup.init(this.$refs['maticMap']._map, marker._latlng, id)
      })
    },
    handleVehicleMileState(msgList) {
      if (!msgList || !msgList.b.length) return
      msgList.b.forEach(item => {
        let vehicle = this._vehicleInfoData[item.a]
        if (!vehicle) return
        let stopType = item.c
        let stopTime = item.d
        Object.assign(vehicle, { stop_time: stopTime, stop_type: stopType })
        if (vehicle.acc) {
          let transLateTime = PUBLICUTIL.transSecondToHMSCN(item.b)
          Object.assign(vehicle, { working_time: transLateTime, })
        }
      })
    },
    handleTerminalLinkRefresh() {
      if (!this.source.terminalLinkList.length) return
      let handleList = this.source.terminalLinkList.slice(0, 200)
      handleList.sort((a, b) => +(a.times) - +(b.times))
      // console.log(this._vehicleInfoData)
      handleList.forEach(item => {
        let vehicle = this._vehicleInfoData[item.vehicleId]
        if (!vehicle) return
        if (this.vehicleOnlineJudge[item.vehicleId] != 2) return
        vehicle.terminalLinkStatus = item.status
        MONITOR.initWsVehicleBasicData(vehicle)
        this.handleMarkerRefresh(vehicle)
        this.checkedVehicleOnlineState(vehicle)
        if (this.currentState.popup == vehicle.id) {
          this.reflashVehiclePopup(vehicle.id)
        }
      })
      this.source.terminalLinkList = this.source.terminalLinkList.slice(handleList.length, this.source.terminalLinkList.length)
      // this.$refs['tree'].handleTipSuffix()
    },
    handleGpsRefresh() {
      if (!this.source.gpsList.length) return
      let handleList = this.source.gpsList.slice(0, 600)
      handleList.sort((a, b) => +(a.gpsTime) - +(b.gpsTime))
      handleList.forEach(item => {
        // console.time('time span');
        let vehicle = this._vehicleInfoData[item.id]
        if (!vehicle) return
        if (item.orderNo) {
          vehicle.concrete_state = this.orderStateDes[item.orderState - 1]
        }
        Object.assign(vehicle, item)
        MONITOR.initWsVehicleBasicData(vehicle, item)
        this.handleMarkerRefresh(vehicle)
        // this.checkedVehicleOnlineStateSimple(vehicle)

        // this.checkedVehicleOnlineState(vehicle)
        if (this.currentState.popup == vehicle.id) {
          this.reflashVehiclePopup(vehicle.id)
        }
      })
      this.source.gpsList = this.source.gpsList.slice(handleList.length, this.source.gpsList.length)
      // this.$refs['tree'].handleTipSuffix()
    },
    handleTempRefresh() {
      if (!this.source.tempList.length) return
      this.source.tempList.forEach(item => {
        let vehicle = this._vehicleInfoData[item.id]
        if (!vehicle) {
          this.source.tempList.remove(item)
          return
        }
        delete item.terminalNo
        Object.assign(vehicle, item, {
          ftt_onState: stateDesc[item.ftt_onState],
          ftt_ysj: stateDesc[item.ftt_ysj],
          ftt_doorstate: stateDesc[item.ftt_doorstate],
          moutai_door_magnetism: stateDesc[item.ftt_doorstate]
        })
        this.source.tempList.remove(item)
        if (this.currentState.popup == item.id) {
          this.reflashVehiclePopup(item.id)
        }
      })
    },
    handleZaizRefresh() {
      if (!this.source.zaizList.length) return
      this.source.zaizList.forEach(item => {
        let vehicle = this._vehicleInfoData[item.id]
        delete item.terminalNo
        Object.assign(vehicle, item)
        this.source.zaizList.remove(item)
        if (this.currentState.popup == item.id) {
          this.reflashVehiclePopup(item.id)
        }
      })
    },
    handleFtRefresh() {
      if (!this.source.ftList.length) return
      this.source.ftList.forEach(item => {
        let vehicle = this._vehicleInfoData[item.vehicleId]
        if (!vehicle) {
          this.source.ftList.remove(item)
          return
        }
        // delete item.terminalNo
        Object.assign(vehicle, item)
        this.source.ftList.remove(item)
        if (this.currentState.popup == item.vehicleId) {
          this.reflashVehiclePopup(item.vehicleId)
        }
      })
    },
    handleLockRefresh() {
      if (!this.source.lockList.length) return
      this.source.lockList.forEach(item => {
        let vehicle = this._vehicleInfoData[item.id]
        if (!vehicle) {
          this.source.lockList.remove(item)
          return
        }
        vehicle.moutai_lock_brokenStatus = item.antiBroken
        vehicle.moutai_lock_lockStatus = item.lock
        vehicle.moutai_lock_onStatus = item.statusView
        vehicle.moutai_lock_power = item.power
        vehicle.moutai_lock_password = item.password
        this.source.lockList.remove(item)
        if (this.currentState.popup == item.id) {
          this.reflashVehiclePopup(item.id)
        }
      })
    },
    handleMarkerRefresh(vehicle) {
      // 如果有报警，不更新
      if (vehicle.alarmTime) return
      let marker = this._vehicleMarkerInfo[vehicle.id]
      if (marker && vehicle.latlng[0] && vehicle.latlng[1]) {
        if (this._markerCluster.hasLayer(marker)) {
          let rotationAngle = 0
          // let content = `<div>${ vehicle.plate }</div>`
          if (PUBLICUTIL.checkCarRunState(vehicle.state)) {
            rotationAngle = vehicle.dire
            // content = content + `<div>${ vehicle.gpsSpeed }km/h</div>`
          }
          let icon = PUBLICUTIL.getVehicleMapIcon(vehicle.state)
          marker.setTooltipContent(MONITOR.getCustomVehicleStyle(vehicle))
          marker.setIcon(icon)
          marker.setRotationAngle(rotationAngle)
          marker.setLatLng(vehicle.latlng)
        }
      } else {                                            // 安排重新上线
        let marker = this.generateVehicleMarker(vehicle)
        this._vehicleMarkerInfo[vehicle.id] = marker
      }
      // this.$refs.tree.changeNodeView(vehicle.id, vehicle.state)
    },
    handleGsixRefresh() {
      if (!this.source.gsixList.length) return
      this.source.gsixList.forEach(item => {
        let vehicle = this._vehicleInfoData[item.id]
        delete item.id
        Object.assign(vehicle.GSIX, item)
        this.source.gsixList.remove(item)
      })
    },
    async handleOnlineRefresh() {
      let result = await this.$api.getVehicleslastonlinetime()
      if (!result || result.status != 200 || !result.data.length) return
      result.data.forEach(item => {
        let vehicle = this._vehicleInfoData[item.vehicle_id]
        if (!vehicle) return
        if (!vehicle.acc) {
          Object.assign(vehicle, { last_time_online: item.last_time_online })
        }
        // 如果报警时间大于60s恢复图标
        if (!vehicle.alarmTime || ((new Date().getTime() - vehicle.alarmTime) / 1000) > 60) {
          vehicle.state = PUBLICUTIL.getVehicleState(vehicle)
          vehicle.alarmTime = null

        } else {
          let state = PUBLICUTIL.getVehicleState(vehicle)
          vehicleState.changeCarIcon(vehicle.id, state)
        }
        this.handleMarkerRefresh(vehicle)
        // this.checkedVehicleOnlineStateSimple(vehicle)
        // this.checkedVehicleOnlineState(vehicle)
      })
    },
    handleGNSSRefresh() {
      if (!this.source.gnssList.length) return
      this.source.gnssList.forEach(item => {
        let vehicle = this._vehicleInfoData[item.vehicleId]
        if (!vehicle) {
          this.source.gnssList.remove(item)
          return
        }

        delete item.vehicleId
        Object.assign(vehicle.satellite, item)
        this.currentState.popupResult = vehicle
        this.source.gnssList.remove(item)
        // if(this.currentState.popup == item.vehicleId) {
        //         this.reflashVehiclePopup(item.vehicleId)
        // }
      })
    },
    handleDataWayBillRefresh() {
      if (!this.source.wayBillList.length) return
      this.source.wayBillList.forEach(item => {
        let vehicle = this._vehicleInfoData[item.vehicleId]
        if (!vehicle) {
          this.source.wayBillList.remove(item)
          return
        }

        delete item.vehicleId
        if (vehicle.mini_waybill) {
          Object.assign(vehicle.mini_waybill, item.data)
        }
        this.source.wayBillList.remove(item)
        if (this.currentState.popup == item.vehicleId) {
          this.reflashVehiclePopup(item.vehicleId)
        }
      })
    },
    handleDataDriverInfoRefresh() {
      if (!this.source.dataDriverInfo.length) return
      this.source.dataDriverInfo.forEach(item => {
        let vehicle = this._vehicleInfoData[item.vehicleId]
        if (!vehicle) {
          this.source.dataDriverInfo.remove(item)
          return
        }
        let arr = {
          on_driver_name: item.driverName ? item.driverName : '', // 名称
          on_driver_idcardno: item.idNumber ? item.idNumber : '', // 身份证
          on_driver_issOrg: item.organization ? item.organization : '', // 发证机构
          on_driver_licNum: item.certId ? item.certId : '', // 从业资格编码
          on_driver_expirationDate: item.expirationDate ? item.expirationDate : '' // 证件有效期
        }
        Object.assign(vehicle, arr)
        this.source.dataDriverInfo.remove(item)
        if (this.currentState.popup == item.vehicleId) {
          this.reflashVehiclePopup(item.vehicleId)
        }
      })
    },
    // handleAlarmRefresh(msg) {
    //     if (this.userAlarmHeight[Number(msg.vehicleId)] && this.userAlarmHeight[Number(msg.vehicleId)].includes(Number(msg.alarmType))) {
    //         this.changeCarStatus(msg)
    //     }
    // },
    // handleDangerRefresh(msg) {
    //     if (this.userDangerHeight[Number(msg.vehicleId)] && this.userDangerHeight[Number(msg.vehicleId)].includes(Number(msg.eventType))) {
    //         this.changeCarStatus(msg)
    //     }
    // },
    // 报警改变车辆图标
    // changeCarStatus(msg) {
    //     let vehicle = this._vehicleInfoData[msg.vehicleId]
    //     if (!vehicle) return
    //     let arr = vehicle.state.split('_')
    //     vehicle.state = arr[0] + '_' + arr[1] + '_' + '2' + '_' + arr[3]
    //     vehicle.alarmTime = moment().valueOf()
    //     // 生成图标
    //     this._vehicleMarkerInfo[vehicle.id] = this.generateVehicleMarker(vehicle)
    //     vehicleState.changeCarIcon(vehicle.id, vehicle.state)
    //     // this.$refs.tree.changeNodeView(vehicle.id, vehicle.state)

    // },

    //点击视频窗口切换车辆卡片
    openVehiclePopup(obj) {
      let vehicle = this._vehicleInfoData[obj.vehicleId]
      if (vehicle) {
        this.currentState.lock = vehicle.id ? vehicle.id : null;
      }
      this.handleVehicleMarkerRefresh(vehicle)
    },
    //移除无视频的地图车辆
    removeVehicleMarker(list) {
      let array = list.map(item => {
        let value = item.vehicleId
        return value.toString()
      })
      let arr = array.filter((item, index) => {
        return array.indexOf(item) === index
      })
      let diffent = PUBLICUTIL.getArrayDifference(this.vehicleVideoStatus, arr)
      if (!diffent.length) return
      this.vehicleVideoStatus = PUBLICUTIL.getArrayDifference(this.vehicleVideoStatus, diffent)
      let marker = this._vehicleMarkerInfo[diffent[0]]
      if (marker) {
        this._markerCluster.removeLayers([marker])
        this.$refs.vehicle.close()
      }
    },
    // 改变播放通道数量
    changeNum(line, column) {
      this.windowNum = line * column
      this.line = line
      this.column = column
    },
    async generateMapObj(map) {
      let $matcismap = this.$refs['maticMap']._map
      this.$refs.vehicle.init($matcismap)
      let result = await this.$api.operateUserTableConfig({ operate_type: 2, user_id: "-1", code: 'monitor_other' })
      this.table.juheRange = result ? result.data.config_value : 0
      this.handleRadiusChange(this.table.juheRange)
    },
    handleRadiusChange(value = 0) {
      let $matcismap = this.$refs['maticMap']._map
      let layersList = []
      if (this._markerCluster) {
        this._markerCluster.clearLayers()
        $matcismap.removeLayer(this._markerCluster)
        this._markerCluster = null
        // layersList = this.checkedVehicleIds.filter(curr => this._vehicleMarkerInfo[curr]).map(item => this._vehicleMarkerInfo[item])
      }
      this._markerCluster = new L.MarkerClusterGroup({
        showCoverageOnHover: false,
        maxClusterRadius: value
      }).addTo($matcismap)

      this._markerCluster.on('clustermouseover', (e) => {
        if ($matcismap.getMaxZoom() != $matcismap.getZoom()) return
        let layers = e.layer.getAllChildMarkers()
        if (layers.length) {
          this.clusterVehicleList = layers.map(item => item.vehicle_id)
        }
        this.$nextTick(() => {
          this.$refs.cluster.open(L.latLng(e.latlng))
        })
      })

      // if (layersList.length) {
      //     this._markerCluster.addLayers(layersList)
      // }
    },
    clearMap() {
      this.$refs.vehicle.close()
    },

    /**
*  处理选中车辆让他在地图上显示
*  小小得优化一下
*  区辆此选中得差值
*  以当前选中得 checkedVehicleIds 为基准
*  包含代表是新加得
*  不包含代表是移出得
*/
    handleVehicleChecked(list, obj) {
      if (!list.length) {
        if (this._markerCluster) {
          this._markerCluster.clearLayers()
        }
        this.checkedVehicleIds = []
        this.$refs.vehicle.close()
        return
      }
      let listArr
      if (obj) {
        listArr = list.map(item => item.id)
      } else {
        listArr = list
      }
      let diffent = PUBLICUTIL.getArrayDifference(this.checkedVehicleIds, listArr)
      if (!diffent.length) return
      let addLayerList = []
      let removeLayerList = []
      diffent.forEach(item => {
        //新增的车辆_vehicleInfoData这个对象里面没有它的数据,把接口里的数据(挂树上了)放进去
        if (obj && !this._vehicleInfoData[item]) {
          this._vehicleInfoData[item] = list.find(it => it.id == item)
          this._vehicleInfoData[item].plate = list.find(it => it.id == item).name
        }
        let marker = this._vehicleMarkerInfo[item]
        if (!this.checkedVehicleIds.includes(item)) {
          if (!marker) {
            let dataInfo = this._vehicleInfoData[item]
            // if(!dataInfo || !dataInfo.gpsTime) {
            //     this.$message({ showClose: true, message: this.$ct('message.neverOnline'), type: 'warning' })
            //     return
            // }
            marker = this.generateVehicleMarker(dataInfo)
            this._vehicleMarkerInfo[item] = marker
          }
          if (marker) addLayerList.push(marker)
        } else {
          if (marker) {
            removeLayerList.push(marker)
          }
        }
      })
      //  这里 于 2020 12 -23 16：05 介于初始化生成后有可能用户会出现切换地图得可能行，所以这里处理   与  682 行 左右得注释代码联动得

      // let addLayerList = diffent.filter(item => !this.checkedVehicleIds.includes(item) && this._vehicleMarkerInfo[item]).map(curr => {
      //     return this._vehicleMarkerInfo[curr]
      // })
      // let removeLayerList = diffent.filter(item => this.checkedVehicleIds.includes(item) && this._vehicleMarkerInfo[item]).map(curr => {
      //     return this._vehicleMarkerInfo[curr]
      // })
      /**
       * 如果移除了当前观察的车辆，关闭popup 弹框
       */
      if (removeLayerList.length) {
        if (this.checkedVehicleIds.includes(this.currentState.popup)) {
          this.$refs.vehicle.close()
        }
      }
      this._markerCluster.addLayers(addLayerList)
      this._markerCluster.removeLayers(removeLayerList)
      /**
       * 新增的上线车辆要刷新位置
       */
      if (addLayerList.length) {
        let newAddVehicleList = diffent.filter(item => !this.checkedVehicleIds.includes(item)).map(curr => this._vehicleInfoData[curr])
        newAddVehicleList.forEach(item => {
          this.handleMarkerRefresh(item)
        })
        /**
         * 如果本次操作之前前没有车辆显示在地图上，那么本次操作聚合
         */
        if (!this.checkedVehicleIds.length) {
          this.$refs['maticMap']._map.fitBounds(this._markerCluster.getBounds())
        }
      }
      this.checkedVehicleIds = JSON.parse(JSON.stringify(listArr))

      if (diffent.length == 1 && this.checkedVehicleIds.includes(diffent[0])) {
        this.openCurrentVehicleInfo(diffent[0])
      }
    },
    async nodeExpand(data, node, $node) {
      if (data.type != 3) return
      let vehicleIds = data.children.filter(item => !item.children.length).map(item => item.id)
      if (!vehicleIds.length) return
      let res = await this.$api.getCommonListByKey({
        key: 'channel_valid_name',
        list: vehicleIds
      })
      let nameList = res && res.status == 200 ? res.data : []
      nameList.forEach(it => {
        if (it.channel_valid_name.length) {
          this.channelNameObj[it.vehicle_id] = {}
          it.channel_valid_name.forEach(val => {
            this.channelNameObj[it.vehicle_id][val.no] = val.name
          })
        }
      })
      data.children.forEach(item => {
        if (item.children.length) return
        let channelList = []
        let obj = nameList.find(it => it.vehicle_id == item.id)
        obj.channel_valid_v2.forEach((chanl, index) => {
          if (chanl) {
            channelList.push(index + 1)
          }
        })

        let dataList = new Array(item.channelCount)
          .fill(0)
          .map((child, index) => {
            // let chn = index + 1;
            return {
              id: item.id + "chn" + channelList[index],
              name: this.channelNameObj[item.id] && this.channelNameObj[item.id][channelList[index]] ? this.channelNameObj[item.id][channelList[index]] : "通道" + channelList[index],
              chn: channelList[index],
              type: 5,
              iconSkin: "channl",
            };
          });
        dataList.forEach(it => {
          this.$refs['tree'].$refs['tree'].append(it, item)

        })
        this.$refs['tree'].filterTree()

      })

    },
    async nodeClick(data, node, $node) {

      if (data.type == 4) {
        // let status
        // if (data.iconSkin.split('_')[1] == 0) {
        // 		status = false
        // } else {
        // 		status = true
        // }
        this.currentClick = {
          plateNo: data.name,
          chn: 1,
          terminal_no: data.terminalNo,
          vehicle_id: data.id,
          channelTalkback: data.channelTalkback,
          channelBroadcast: data.channelBroadcast,

          // status,
        }
        if (!this.channelNoCheckedVehicleIds.includes(this.currentClick.vehicle_id)) {
          this.channelNoCheckedVehicleIds.push(this.currentClick.vehicle_id)
        }
        await this.checkSimFlowOver(this.currentClick.vehicle_id, this.currentClick.terminal_no)
        if (this.vehicleVideoStatus.includes(this.currentClick.vehicle_id)) {
          let dataInfo = this._vehicleInfoData[this.currentClick.vehicle_id]
          this.handleVehicleMarkerRefresh(dataInfo)
        }
      } else if (data.type == 5) {
        this.currentClick = {
          plateNo: node.parent.data.name,
          chn: data.chn,
          terminal_no: node.parent.data.terminalNo,
          vehicle_id: node.parent.data.id,
          channelTalkback: node.parent.data.channelTalkback,
          channelBroadcast: node.parent.data.channelBroadcast
        }

        if (!this.channelNoCheckedVehicleIds.includes(this.currentClick.vehicle_id)) {
          this.channelNoCheckedVehicleIds.push(this.currentClick.vehicle_id)
        }
        await this.checkSimFlowOver(this.currentClick.vehicle_id, this.currentClick.terminal_no)
        if (this.vehicleVideoStatus.includes(this.currentClick.vehicle_id)) {
          let dataInfo = this._vehicleInfoData[this.currentClick.vehicle_id]
          this.handleVehicleMarkerRefresh(dataInfo)
        }
        // let dataInfo = this._vehicleInfoData[this.currentClick.vehicle_id]
        // let marker = this.generateVehicleMarker(dataInfo)
        // this._vehicleMarkerInfo[this.currentClick.vehicle_id] = marker
        // addLayerList.push(marker)
        // this._markerCluster.addLayers(addLayerList)
        // this.openCurrentVehicleInfo(this.currentClick.vehicle_id)
      }
    },
    // 关闭时间按照配置
    async checkSimFlowOver(vehicle_id, terminal_no) {
      if (!vehicle_id || !terminal_no) return false
      let res = await this.$api.getSimFlowUsedInfo({
        vehicle_id: vehicle_id,
        terminal_no: terminal_no,
      })
      if (res.status === 200) {
        this.configTime = res.data.time_over * 60
        this.remaining = this.configTime
      } else {
        this.$error(res.message)
      }
    },
    selectNodes(data, { checkedNodes }) {
      this.selectNodesList = checkedNodes.filter(item => item.type == 4 && item.inputType != 3)
    },
    // 控制树是否为单选 //树类型
    changeTree(val) {
      this.checkMode = val
    },
    // 云景控制是否显示，改变侧边栏高度
    changHeight(val) {
      this.$refs.main.style.height = this.$refs.aside.offsetHeight - val + 'px'
      this.$refs.main.style.maxHeight = this.$refs.aside.offsetHeight + 'px'
      this.isShowControl = val === true;
    },
    changItem(index) {
      this.clickItemNum = index + 1;
    },
    //点击加号打开弹窗
    handleVehicleMarkerRefresh(vehicle) {
      if (!vehicle) return;
      let addLayerList = [];
      let removeLayerList = [];
      let marker = this._vehicleMarkerInfo[vehicle.id];
      if (!this.channelNoCheckedVehicleIds.includes(vehicle.id)) {
        if (!marker) {
          let dataInfo = this._vehicleInfoData[vehicle.id];
          marker = this.generateVehicleMarker(dataInfo)
          this._vehicleMarkerInfo[vehicle.id] = marker
        }
        if (marker) addLayerList.push(marker)
      } else {
        if (marker) {
          removeLayerList.push(marker)
        }
      }
      if (removeLayerList.length) {
        if (this.channelNoCheckedVehicleIds.includes(this.currentState.popup)) {
          this.$refs.vehicle.close()
        }
      }
      this._markerCluster.addLayers(addLayerList)
      this._markerCluster.removeLayers(removeLayerList)
      if (addLayerList.length) {
        this.handleMarkerRefresh(vehicle)
      }
      if (!this.channelNoCheckedVehicleIds.length) {
        this.$refs['maticMap']._map.fitBounds(this._markerCluster.getBounds())
      }
      this.openCurrentVehicleInfo(vehicle.id)

    },
    renderContentClick({ node, data, type }) {
      if (type == 'jia') {
        if (data.type === 5) {
          this.addVideo(node.parent.data, data.chn);
          this.terminal_no = node.parent.data.terminalNo;
          this.vehicle_id = node.parent.data.id;
          this.checkSimFlowOver(this.vehicle_id, this.terminal_no)
          let vehicle = this._vehicleInfoData[node.parent.data.id]
          this.handleVehicleMarkerRefresh(vehicle)
        } else {
          this.addVideo(data);
          this.terminal_no = data.terminalNo;
          this.vehicle_id = data.id;
          this.checkSimFlowOver(this.vehicle_id, this.terminal_no)
          let vehicle = this._vehicleInfoData[data.id]
          this.handleVehicleMarkerRefresh(vehicle)
        }
      } else {
        this.reduceVideo(data);
      }
    },
    reduceVideo(data) {
      this.$refs["videoView"].removeChannel(parseInt(data.id), data.terminalNo)
      let marker = this._vehicleMarkerInfo[data.id]
      this._markerCluster.removeLayers([marker])
      this.$refs.vehicle.close()
    },
    addVideo(data, channelNo) {
      if (!this.vehicleVideoStatus.includes(data.id)) {
        this.vehicleVideoStatus.push(data.id)
      }
      if (channelNo) {
        this.$refs["videoView"].append([
          {
            plateNo: data.name,
            vehicleId: parseInt(data.id),
            terminalCode: data.terminalNo,
            channelNo,
            inputType: data.inputType,
            label: this.channelNameObj[data.id] && this.channelNameObj[data.id][channelNo] ? this.channelNameObj[data.id][channelNo] : '通道' + channelNo
          },
        ]);
      } else {
        let channelList = []
        data.channelValid.split('').forEach((chanl, index) => {
          if (+chanl == 1) {
            channelList.push(index + 1)
          }
        })
        let temp = this.channels.length ? this.channels.map((item, index) => {
          return {
            plateNo: data.name,
            vehicleId: parseInt(data.id),
            terminalCode: data.terminalNo,
            channelNo: item,
            inputType: data.inputType,
            label: this.channelNameObj[data.id] && this.channelNameObj[data.id][item] ? this.channelNameObj[data.id][item] : '通道' + item

          };
        }) : new Array(data.channelCount)
          .fill(0)
          .map((item, index) => {
            return {
              plateNo: data.name,
              vehicleId: parseInt(data.id),
              terminalCode: data.terminalNo,
              channelNo: channelList[index],
              inputType: data.inputType,
              label: this.channelNameObj[data.id] && this.channelNameObj[data.id][channelList[index]] ? this.channelNameObj[data.id][channelList[index]] : '通道' + channelList[index]

            };
          });
        this.$refs["videoView"].append(temp);
      }
    },
    async stopAll() {
      try {
        this.stopping = true;
        await this.$refs["videoView"].stopAll();
      } finally {
        this.stopping = false;
      }
    },
    async playAll() {
      try {
        this.playing = true;
        await this.$refs["videoView"].playAll();
      } finally {
        this.playing = false;
      }
    },
    isplaying(val) {
      let flag = false
      let list = this.$refs.videoView.$refs.player
      for (let i = 0; i < list.length; i++) {
        if (list[i].isPlaying === true) {
          flag = true
          this.hasVideoPlaying = true
          this.setMouseInterval()
        }
      }
      if (!flag) {
        this.hasVideoPlaying = false
        this.clearInterval()
      }
    },
    clearAll() {
      this.$refs['videoView'].clear()
      this.hasVideoPlaying = false
      this.vehicleVideoStatus.forEach(item => {
        let marker = this._vehicleMarkerInfo[item]
        this._markerCluster.removeLayers([marker])
      })
      this.$refs.vehicle.close()

    },
    setMouseInterval() {
      if (this.mouseMonitor) {
        clearInterval(this.mouseMonitor)
      }
      this.mouseMonitor = setInterval(async () => {
        if (!this.hasVideoPlaying) return
        if (this.oldPageX === this.pageX && this.oldPageY === this.pageY) {
          this.remaining -= 1
          if (this.remaining <= 0) {
            await this.$refs['videoView'].clear()
            this.vehicleVideoStatus.forEach(item => {
              let marker = this._vehicleMarkerInfo[item]
              this._markerCluster.removeLayers([marker])
            })
            this.$refs.vehicle.close()
            setTimeout(() => {
              alert('您已长时间不操作系统，视频已经自动关闭！')
            }, 500)
            clearTimeout(this.mouseMonitor)
            this.mouseMonitor = null
          }
          this.isStopMove = true
        } else {
          this.remaining = this.configTime
          this.isStopMove = false
        }
        this.oldPageY = this.pageY
        this.oldPageX = this.pageX
      }, 1000)
    },
    clearInterval() {
      if (this.mouseMonitor) {
        clearTimeout(this.mouseMonitor)
        this.mouseMonitor = null
      }
    }
  },
  beforeDestroy() {
    this.clearInterval()
    window.removeEventListener('mousemove', () => {
    })
  },
};
</script>

<style lang="scss">
.realtime-video-v2 {
  width: 100%;
  height: 100%;

  .custom-tree-node {
    display: flex;
    align-items: center;
    height: 24px;
    flex-grow: 1;
    padding-right: 5px;
    overflow: hidden;
    text-overflow: ellipsis;

    i {
      display: inline-block;
      width: 24px;
      height: 24px;
      text-align: center;
      line-height: 24px;
      flex-shrink: 0;
    }

    &__label {
      font-size: 12px;
    }

    &__extra {
      flex-grow: 1;
      display: flex;
      flex-direction: row-reverse;
    }
  }

  .aside {
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    overflow: hidden;

    //.showCon {
    //    max-height: 685px !important;
    //}
    //
    .unShowCon {
      height: calc(100% - 30px);
    }
  }

  .videoContent {
    display: flex;
    flex-direction: row;
    height: 100%;
    width: 100%;

    .videos {
      height: 100%;
      width: 73%;
    }

    .vehicleMap {
      height: 100%;
      width: 30%;
    }
  }
}
</style>

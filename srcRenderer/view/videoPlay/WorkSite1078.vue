<template>
    <div class="workSiteVideo">
      <Layout id="workSiteVideo" :has-color="true" :loading="loading">
        <template slot="aside">
          <div class="query-top">
            <ElementTree
              :type="treeType"
              ref="treeWork"
              v-if="treeType"
              :renderContent="true"
              :jian="false"
              :renderDataType="renderDataType"
              :num="true"
              @renderContentClick="renderContentClick"
              :elementTypeList="elementTypeList"
              :elementType="true"
              @change="treeChange"
            ></ElementTree>
          </div>
  
          <!-- <el-tabs v-model="activeTab" 
                       stretch type="border-card">
                  <el-tab-pane label="工地" name="workSite">
                      <ElementTree type="workSiteVideo" :alwaysNew="true" ref="treeWork" :renderContent="true" :jian="false" :renderDataType="5" :num="true" @renderContentClick="renderContentClick"></ElementTree>
                  </el-tab-pane>
                  <el-tab-pane label="消纳场" name="xiaoNa">
                      <ElementTree type="xiaoNaVideo" :alwaysNew="true" ref="treeXiaoNa" :renderContent="true" :jian="false" :renderDataType="5" @renderContentClick="renderContentClick"></ElementTree>
                  </el-tab-pane>
              </el-tabs> -->
        </template>
        <template slot="query">
          <div class="query-item">
            <el-button
              icon="pony-iconv2 pony-sifenping"
              :class="{ active: windowNum === 4 }"
              :title="`切换${windowNum === 4 ? defaultNum : 4}窗口模式`"
              @click="setWindowNum(4)"
            ></el-button>
            <el-button
              icon="pony-iconv2 pony-jiufenping"
              :class="{ active: windowNum === 9 }"
              :title="`切换${windowNum === 9 ? defaultNum : 9}窗口模式`"
              @click="setWindowNum(9)"
            ></el-button>
          </div>
          <div class="break-item"></div>
          <div class="query-item" v-if="isStopMove && $refs['videoView'].hasVideoPlaying">
            将于&nbsp;
            <span class="text text--brand"> {{ remaining }} </span>
            &nbsp;秒后停止播放
          </div>
          <div class="query-item">
            <el-button icon="pony-iconv2 pony-bofangquan" title="播放全部" @click="playAll" :loading="playing"></el-button>
            <el-button icon="pony-iconv2 pony-zanting" title="停止全部" @click="stopAll" :loading="stopping"></el-button>
            <el-button icon="pony-iconv2 pony-tingzhi" title="清空已选择" @click="$refs['videoView'].clear()"></el-button>
          </div>
        </template>
        <template slot="content">
          <VideoViewWork ref="videoView" :autoPlay="true" :windowNum="windowNum"></VideoViewWork>
        </template>
      </Layout>
    </div>
</template>
  
  <script>
  /**
   * @Author: yezy
   * @Email: <EMAIL>
   * @Date: 2020/3/17 13:34
   * @LastEditors: yezy
   * @LastEditTime: 2020/3/17 13:34
   * @Description:
   */
  import VideoViewWork from "./component/videoPlayerV2/component/VideoViewWork";
  import ElementTree from "@/components/tree/ElementTree";
  
  export default {
    name: "workSite1078",
    components: {
      VideoViewWork,
      ElementTree,
    },
    data() {
      return {
        activeTab: "workSite",
        currentTerminal: null,
        loading: false,
        windowNum: 16,
        defaultNum: 16,
        remaining: 300,
        isStopMove: false,
        playing: false,
        stopping: false,
        count: 0,
        treeType: "",
        renderDataType: 5,
        terminalCode: null,
        terminalLabel: null,
        videoTitle: null,
        elementTypeList: [
          {
            type: "workSiteVideo",
            value: 0,
            name: "工地",
            renderDataType: 5,
          },
          {
            type: "xiaoNaVideo",
            value: 1,
            name: "消纳场",
            renderDataType: 5,
          },
          {
            type: "pointTerminalWorksite",
            value: 2,
            name: "收集点",
            renderDataType: 6,
          },
          {
            type: "stopTerminalWorksite",
            value: 3,
            name: "停车场",
            renderDataType: 6,
          },
        ],
      };
    },
    methods: {
      async treeChange(val) {
        this.$refs["treeWork"].loading = true;
        this.treeType = this.elementTypeList[val].type;
        this.renderDataType = this.elementTypeList[val].renderDataType;
        this.$refs["treeWork"].initTree(this.treeType);
      },
      async getSelectTreeList() {
        let res = await this.$api.areaTerminalType({
          page: "video/worklive",
        });
        if (!res || res.status != 200) {
          return this.$warning(res.message);
        }
        this.elementTypeList = res.data;
        this.renderDataType = this.elementTypeList[0].renderDataType;
        this.treeType = this.elementTypeList[0].type;
        // this.$refs['tree'].initTree(this.treeType)
      },
      renderContentClick({ node, data, type }) {
        if (type == "jia") {
          if (data.type == this.renderDataType + 1) {
            this.terminalLabel = data.name;
            this.terminalCode = data.hrefName;
            this.videoTitle = node.parent.data.name+'-'+data.name;
            this.addVideo(node.parent.data, 1);
  
            // this.addVideo(node.parent.data, data.chn);
            // this.terminal_no = node.parent.data.terminalNo;
            // this.vehicle_id = node.parent.data.id;
            // this.checkSimFlowOver(this.vehicle_id, this.terminal_no)
            // let vehicle = this._vehicleInfoData[node.parent.data.id]
            // this.handleVehicleMarkerRefresh(vehicle)
          } else if (data.type == this.renderDataType) {
            this.addVideo(data);
            // this.terminal_no = data.terminalNo;
            // this.vehicle_id = data.id;
            // this.checkSimFlowOver(this.vehicle_id, this.terminal_no)
            // let vehicle = this._vehicleInfoData[data.id]
            // this.handleVehicleMarkerRefresh(vehicle)
          }
        } else {
          this.reduceVideo(data);
        }
      },
      // 树中+号以及事件的添加
      nodeRender(h, { node, data, store }) {
        let extra = [];
        if (data.type === 5 || data.type === 6) {
          extra.push(
            h(
              "div",
              {
                class: "custom-tree-node__extra",
              },
              [
                h("i", {
                  class: "pony-iconv2 pony-jia pointer text text--link",
                  on: {
                    click: (ev) => {
                      if (data.type === 6) {
                        this.videoTitle = node.parent.data.name+'-'+data.name;
                        this.terminalCode = data.hrefName;
                        this.terminalLabel = data.name;
                        this.addVideo(node.parent.data, 1);
                      } else {
                        this.addVideo(data);
                      }
                      ev.stopPropagation();
                    },
                  },
                }),
              ]
            )
          );
          return h("div", { class: "custom-tree-node" }, [
            h("i", {
              class: ["tree-icon", data.iconSkin],
            }),
            h(
              "span",
              {
                class: "custom-tree-node__label",
                attrs: {
                  title: data.name,
                },
              },
              node.label
            ),
            ...extra,
          ]);
        } else {
          let allArr = this.$utils.flatTree(data.children).filter((it) => it.type == 5);
          let unAble = allArr.filter((it) => it.business_value);
          return h("div", { class: "custom-tree-node" }, [
            h("i", {
              class: ["tree-icon", data.iconSkin],
            }),
            h(
              "span",
              {
                class: "custom-tree-node__label",
                attrs: {
                  title: data.name,
                },
              },
              node.label + `( ${unAble.length} / ${allArr.length} ) `
            ),
            ...extra,
          ]);
        }
      },
      setWindowNum(num) {
        this.windowNum = this.windowNum === num ? this.defaultNum : num;
      },
      addVideo(data, n) {
        // if第六层树
        if (n) {
          this.$refs["videoView"].append([
            {
              terminalCode: this.terminalCode,
              label: this.terminalLabel,
              videoTitle: this.videoTitle,
            },
          ]);
        } else {
          if (!data.children || !data.children.length) return;
          let temp = data.children.map((item, index) => {
            return {
              terminalCode: item.hrefName,
              label: item.name,
              videoTitle:data.name+'-'+item.name,
            };
          });
          this.$refs["videoView"].append(temp);
        }
      },
      onStateChange(isStop, remaining) {
        this.isStopMove = isStop;
        this.remaining = remaining;
      },
      async stopAll() {
        try {
          this.stopping = true;
          await this.$refs["videoView"].stopAll();
        } finally {
          this.stopping = false;
        }
      },
      async playAll() {
        try {
          this.playing = true;
          await this.$refs["videoView"].playAll();
        } finally {
          this.playing = false;
        }
      },
      changeTreeName(val) {
        let data = val.data;
      },
    },
    created() {
      this.getSelectTreeList();
    },
    activated() {
      if (!this.playing) {
        this.playAll();
      }
    },
  };
  </script>
  
  <style lang="scss">
  .workSiteVideo {
    width: 100%;
    height: 100%;
    .query-top {
      height: 100%;
    }
    .query-bottom {
      margin-top: 5px;
      padding: 10px;
  
      .query-item {
        display: flex;
        height: 40px;
        line-height: 40px;
        justify-content: space-between;
        align-items: center;
  
        > span {
          font-size: 12px;
          white-space: nowrap;
          margin-right: 5px;
        }
      }
    }
    .custom-tree-node {
      display: flex;
      align-items: center;
      height: 24px;
      flex-grow: 1;
      padding-right: 5px;
      overflow: hidden;
      text-overflow: ellipsis;
  
      i {
        display: inline-block;
        width: 24px;
        height: 24px;
        text-align: center;
        line-height: 24px;
        font-size: 16px;
        flex-shrink: 0;
      }
  
      &__label {
        font-size: 12px;
      }
  
      &__extra {
        flex-grow: 1;
        display: flex;
        flex-direction: row-reverse;
      }
      .custom-tree-node__label {
        width: 135px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
  </style>
  
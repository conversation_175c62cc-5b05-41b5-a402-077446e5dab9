<template>
  <Layout class="car-repair" :hasColor="true" :contentLoading="table.loading">
    <template slot="aside">
      <ElementTree type="vehicle" :checkMode="true" ref="tree" @check="selectNodes"></ElementTree>

    </template>
    <template slot="query">
      <div class="query-item">
        <span>类别：</span>
        <el-select v-model="query.type">
          <el-option label="全部" :value="null"></el-option>
          <el-option v-for="item in repairTypeList" :value="item.value" :label="item.label" :key="item.value"></el-option>
        </el-select>
      </div>
      <div class="query-item">
        <span>送修时间：</span>
        <el-date-picker v-model="query.startTime" type="date" clearable :picker-options="pickerOptions"
          @change="dateStartChange" placeholder="选择日期">
        </el-date-picker>
      </div>
      <div class="query-item">
        <span>完成时间：</span>
        <el-date-picker v-model="query.endTime" type="date" clearable :picker-options="endDatePickerOptions"
          placeholder="选择日期">
        </el-date-picker>
      </div>
      <div class="break-item">
        <el-button type="primary" @click="getTableList()" :loading="table.loading">查询</el-button>
        <el-button type="primary" v-if="hasPermission('repair:add')" @click="showModal()">新增</el-button>
      </div>
    </template>
    <template slot="content">
      <el-table class="el-table--radius" :data="table.data" height="100%" border stripe ref="table" highlight-current-row
        style="width: 100%">
        <el-table-column label="序号" width="80">
          <template slot-scope="{$index}">
            <span>{{ (pager.current - 1) * pager.size + 1 + $index }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="所属车队" align="left" width="300" show-overflow-tooltip>
          <template slot-scope="scope">
            <span>{{ scope.row.company_name + '>>' + scope.row.dept_name }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="plate_no" label="车牌号"></el-table-column>
        <el-table-column prop="repair_time" label="送修时间" width="150"></el-table-column>
        <el-table-column label="修理类型">
          <template slot-scope="{row}">
            <span>{{ repairTypeValueLabelMap[row.repair_type] }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="repaired_time" label="修理完毕时间" width="150"></el-table-column>
        <el-table-column prop="repair_fee" label="修理总费用"></el-table-column>
        <el-table-column prop="self_pay" label="自费费用"></el-table-column>
        <el-table-column prop="location" label="操作" width="150">
          <template slot-scope="{row}">
            <el-button type="text" title="修改" v-if="hasPermission('repair:update')" @click="showModal(row)"><i
                class="pony-iconv2 pony-xiugai"></i></el-button>
            <el-button type="text" title="删除" v-if="hasPermission('repair:delete')" @click="deleteRow(row)"><i
                class="pony-iconv2 pony-shanchu"></i>
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </template>
    <template slot="footer">
      <el-pagination background small layout="prev, pager, next, total" :pager-count="5"
        :current-page.sync="pager.current" @current-change="getTableList" :page-size="pager.size" :total="pager.total">
      </el-pagination>
    </template>
    <PonyDialog v-model="modal.show" :title="modal.mode === 'add' ? modal.addModeTitle : modal.modifyModeTitle"
      :width="680" @close="closeModal" :loading="modal.loading">
      <el-form ref="form" :model="modal.data" label-width="100px" :rules="modal.rules" size="mini">
        <el-row>
          <el-col :span="12">
            <el-form-item label="车辆" prop="vehicle">
              <SelectTreeInput v-model="modal.data.vehicle" type="vehicle" placeholder="请选择车辆" title="请选择车辆"
                :condition="inputCondition">
              </SelectTreeInput>
            </el-form-item>
            <el-form-item label="送修里程" prop="repair_mile">
              <el-input v-model="modal.data.repair_mile"></el-input>
            </el-form-item>
            <el-form-item label="修理类型" prop="repair_type">
              <el-select v-model="modal.data.repair_type" style="width: 100%;">
                <el-option v-for="item in repairTypeList" :value="item.value" :label="item.label"
                  :key="item.value"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="修理总价" prop="repair_fee">
              <el-input v-model="modal.data.repair_fee"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="送修时间" prop="repair_time">
              <el-date-picker v-model="modal.data.repair_time" type="date" placeholder="选择日期"
                style="width: 100%;"></el-date-picker>
            </el-form-item>
            <el-form-item label="修理厂家" prop="repair_company">
              <el-input v-model="modal.data.repair_company"></el-input>
            </el-form-item>
            <el-form-item label="修完时间" prop="repaired_time">
              <el-date-picker v-model="modal.data.repaired_time" type="date" placeholder="选择日期"
                style="width: 100%;"></el-date-picker>
            </el-form-item>
            <el-form-item label="自费费用" prop="self_pay">
              <el-input v-model="modal.data.self_pay"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="故障内容" prop="fault_desc">
              <el-input type="textarea" v-model="modal.data.fault_desc"></el-input>
            </el-form-item>
            <el-form-item label="修理项目" prop="repair_content">
              <el-input type="textarea" v-model="modal.data.repair_content"></el-input>
            </el-form-item>
            <el-form-item label="备注" prop="remark">
              <el-input type="textarea" v-model="modal.data.remark"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template slot="footer">
        <el-button type="primary" @click="commitModal">{{ modal.mode ===
          'add' ? modal.addModeOkText : modal.modifyModeOkText }}
        </el-button>
        <el-button type="border" @click="closeModal">取消</el-button>
      </template>
    </PonyDialog>
  </Layout>
</template>

<script>
/**
 * @Author: yezy
 * @Email: <EMAIL>
 * @Date: 2020/1/9 10:38
 * @LastEditors: yezy
 * @LastEditTime: 2020/1/9 10:38
 * @Description:
 */
import SelectTreeInput from '@/components/common/SelectTreeInput'
export default {
  name: "carRepair",
  components: {
    SelectTreeInput,
  },
  data() {
    const modalData = {
      id: null,
      fault_desc: null,
      self_pay: null,
      remark: null,
      vehicle_id: null,
      repair_time: null,
      repair_company: null,
      repair_type: null,
      repair_content: null,
      repaired_time: null,
      repair_fee: null,
      repair_mile: null,
      vehicle: null,
    };
    return {
      repairTypeList: [],
      repairTypeValueLabelMap: {},
      query: {
        vehicleIds: [],
        type: null,
        startTime: null,
        endTime: null,
      },
      table: {
        data: [],
        loading: false,
      },
      pager: {
        current: 1,
        total: 0,
        size: 30,
      },
      modal: {
        show: false,
        mode: 'add',
        loading: false,
        addModeTitle: '新增保养信息',
        addModeOkText: '确认新增',
        modifyModeTitle: '修改保养信息',
        modifyModeOkText: '确认修改',
        data: JSON.parse(JSON.stringify(modalData)),
        defaultData: modalData,
        rules: {
          vehicle: [{ required: true, message: '请选择车辆', trigger: 'change' },],
          repair_type: [{ required: true, message: '请选择修理类型', trigger: 'change' },],
          repair_time: [{ required: true, message: '请选择送修时间', trigger: 'change' },],
          repaired_time: [{ required: true, message: '请选择修理完成时间', trigger: 'change' },],
        },
      },
      pickerOptions: {
        disabledDate: function (date) {
          return (date - moment().endOf('day').toDate()) > 0
        }
      },
    }
  },
  computed: {
    endDatePickerOptions: function () {
      return {
        disabledDate: (date) => {
          return (date - moment().endOf('day').toDate()) > 0 ||
            date - this.query.startTime < 0;
        }
      }
    },
  },
  methods: {
    dateStartChange(date) {
      if (this.query.endTime && (this.query.endTime - date < 0)) {
        this.query.endTime = date;
      }
    },
    async selectNodes(data, { checkedNodes }) {
      this.query.vehicleIds = checkedNodes.filter(item => item.type === 4).map(item => item.id);
    },
    inputCondition(node) {
      if (node.type !== 4) {
        return false;
      } else {
        return true;
      }
    },
    async getTableList(pageIndex = 1) {
      const params = {
        vehicle_id_list: this.query.vehicleIds.length ? this.query.vehicleIds : null,
        page: pageIndex,
        count: this.pager.size,
        sorting: -1,
        repair_type: this.query.type,
        repair_time_begin: this.query.startTime ? this.TimeFormat(this.query.startTime) : null,
        repair_time_end: this.query.endTime ? this.TimeFormat(this.query.endTime) : null,
      }
      this.table.loading = true;
      this.pager.current = pageIndex;
      try {
        const res = await this.$api.getVehicleRepairInfoV2(params);
        if (res.RS === 1) {
          this.table.data = res.VehicleRepairList;
          this.pager.total = res.Count;
        } else {
          this.table.data = [];
          this.pager.total = 0;
          throw new Error(res.Reason);
        }
      } catch (e) {
        this.$error(e);
      } finally {
        this.table.loading = false;
      }
    },
    async showModal(row) {
      if (!row) {
        this.modal.show = true;
        this.modal.mode = 'add';
      } else {
        this.$utils.assign(this.modal.data, row);
        this.modal.data.vehicle = { label: row.plate_no, value: row.vehicle_id };
        this.modal.show = true;
        this.modal.mode = 'modify';
      }
    },
    closeModal() {
      this.modal.data = JSON.parse(JSON.stringify(this.modal.defaultData))
      this.modal.show = false;
    },
    async commitModal() {
      if (!await this.$refs['form'].validate()) return;
      const params = JSON.parse(JSON.stringify(this.modal.data));
      params.vehicle_id = params.vehicle.value;
      delete params.vehicle;
      params.repair_time = this.TimeFormat(params.repair_time);
      params.repaired_time = this.TimeFormat(params.repaired_time);
      let res;
      this.modal.loading = true;
      try {
        if (this.modal.mode === 'add') {
          res = await this.$api.insertApair(params);
        } else {
          res = await this.$api.updateApair(params);
        }
        if (res.RS === 1) {
          this.$success(res.Reason)
          this.getTableList();
          this.closeModal();
        } else {
          throw new Error(`${this.modal.mode === 'add' ? this.modal.addModeTitle : this.modal.modifyModeTitle}失败`)
        }
      } catch (e) {
        this.$error(e)
      } finally {
        this.modal.loading = false;
      }
    },
    async deleteRow(row) {
      await this.$confirm('确定要删除此记录吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'error',
        center: true
      })
      let res = await this.$api.removeApairInfo({
        id: row.id,
      })
      if (res.RS === 1) {
        this.getTableList();
        this.$success('删除成功')
      } else {
        this.$error('删除失败')
      }
    },
  },
  async created() {
    this.repairTypeList = await this.$store.dispatch('dictionary/getFormatListByCode', 'repair_type');
    this.repairTypeValueLabelMap = await this.$store.dispatch('dictionary/getValueLabelMapByCode', 'repair_type');
  },
  mounted() {
    this.getTableList();
  }
}
</script>

<style scoped lang="scss"></style>

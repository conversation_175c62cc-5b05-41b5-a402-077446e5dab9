<template>
  <Layout :has-color="true" class="VehicleRunTrack" :contentLoading="table.loading">
    <template slot="aside">

      <ElementTree type="department" ref="department" :checkMode="false" @node-click="selectNodes">
      </ElementTree>
    </template>

    <template slot="query">
      <div class="query-item">
        <el-input size="mini" v-model="queryList.name" placeholder="请输入角色模板名称"></el-input>
      </div>
      <div class="query-item">
        <el-button type="primary" @click="getPageDataInfo(1)" :loading="table.loading">查询</el-button>
      </div>
      <div class="break-item"></div>
      <el-button type="primary" @click="handleModalState(0)">新增</el-button>
    </template>

    <template slot="footer">
      <el-pagination small background :current-page="queryList.page" :page-size="queryList.count"
        layout="prev, pager, next, total" :total="table.total" @current-change="getPageDataInfo">
      </el-pagination>
    </template>

    <template slot="content">
      <el-table ref="table" class="el-table--ellipsis el-table--radius" border stripe highlight-current-row size="mini"
        :data="table.list" height="100%" style="width: 100%">
        <el-table-column type="index" :index="(index) => index + 1 + pageStart" label="序号" width="80"></el-table-column>
        <el-table-column label="操作" min-width="120">
          <template slot-scope="{ row }">
            <el-button type="text" title="修改" size="mini" @click="handleModalState(row)">
              <i class="pony-iconv2 pony-xiugai"></i>
            </el-button>
            <el-button type="text" title="复制" size="mini" @click="handleModalState(row,'copy')">
              <i class="pony-iconv2 pony-fuzhi"></i>
            </el-button>
            <el-button type="text" title="删除" size="mini" @click="removeDataInfo(row)">
              <i class="pony-iconv2 pony-shanchu"></i>
            </el-button>
            <el-button type="text" title="查看绑定用户" size="mini" @click="searchBindUserInfo(row)">
              <i class="pony-iconv2 pony-chakanbangdingyonghu"></i>
            </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="dept_name" label="单位" show-overflow-tooltip min-width="300" header-align="center"
          align="left"></el-table-column>
        <el-table-column prop="name" label="模板名称" min-width="120"></el-table-column>
        <!-- <el-table-column prop="role_type" label="角色类型" min-width="150">
          <template slot-scope="scope">
            <span v-show="scope.row.role_type == 0">角色</span>
            <span v-show="scope.row.role_type == 1">模板</span>
            <span v-show="scope.row.role_type == 2">隐藏角色</span>
          </template>
        </el-table-column> -->
        <el-table-column prop="update_by" label="更新人" min-width="120"></el-table-column>

        <el-table-column prop="update_time" label="更新时间" min-width="140"></el-table-column>

        <el-table-column prop="remark" label="备注" min-width="150"></el-table-column>
        
      </el-table>
    </template>

    <PonyDialog width="600" :title="modal.state ? '修改模板' : '新增模板'" :contentMaxHeight="410" v-model="modal.show"
      @confirm="changeDataInfo">
      <el-form ref="form" :model="modal.data" :rules="modal.rules" label-width="80px">
        <el-form-item label="模板名称" prop="name">
          <el-input v-model="modal.data.name"></el-input>
        </el-form-item>
        <el-form-item label="首页">
          <el-select v-model="mainPageTypeRange" multiple collapse-tags>
            <el-option label="介绍型" :value="1" v-show="guideMainPageList.indexOf(1) != -1"></el-option>
            <el-option label="看板型" :value="2" v-show="guideMainPageList.indexOf(2) != -1"></el-option>
            <el-option label="高级调度" :value="4" v-show="guideMainPageList.indexOf(4) != -1"></el-option>
            <el-option label="实时监控" :value="8" v-show="guideMainPageList.indexOf(8) != -1"></el-option>
            <el-option label="看板型(简洁)" :value="16" v-show="guideMainPageList.indexOf(16) != -1"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="modal.data.remark" type="textarea"></el-input>
        </el-form-item>
      </el-form>
      <el-row :gutter="20">
        <el-col :span="12">
          <ZtreeMatics style="height: 260px" treeType="department" ref="modaldept" :checkMode="false">
          </ZtreeMatics>
        </el-col>
        <el-col :span="12">
          <ZtreeMatics ref="modalmenu" style="height: 260px" treeType="roleTree" suffix="menu"
            :extendSetting="extendSetting">
          </ZtreeMatics>
        </el-col>
      </el-row>
    </PonyDialog>

    <PonyDialog width="800" title="查看绑定用户" :contentMaxHeight="410" :show="userTable.show"
      @confirm="userTable.show = false" @close="userTable.show = false">
      <div class="dfb" style="height: 40px">
        <el-pagination small background :current-page="userQueryList.page" :page-size="userQueryList.count"
          layout="prev, pager, next, total" :total="userTable.total" @current-change="getPageBindDataInfo">
        </el-pagination>
      </div>
      <el-table class="el-table--ellipsis" border stripe highlight-current-row size="mini" :data="userTable.list"
        ref="userTable" height="250" style="width: 100%">
        <el-table-column type="index" :index="(index) => index + 1 + userPageStart" label="序号"
          width="80"></el-table-column>
        <el-table-column label="单位" header-align="center" align="left" min-width="250">
          <template slot-scope="scope">
            <span v-if="scope.row.company_name">{{ scope.row.company_name || "" }} >></span>
            <span>{{ scope.row.dept_name || "" }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="login_name" label="登录名" min-width="150"></el-table-column>
        <el-table-column prop="valid_date" label="到期时间" min-width="150"></el-table-column>
      </el-table>
    </PonyDialog>
  </Layout>
</template>

<script>
import PinyinMatch from "pinyin-match";
import { mapState, mapGetters } from "vuex";

export default {
  name: "roleTemplate",
  components: {},
  data() {
    return {
      queryList: {
        dept_id: "",
        name: "",
        page: 1,
        count: 30,
        sorting: "-1",
      },

      table: {
        loading: false,
        total: 0,
        list: [],
      },

      extendSetting: {
        check: {
          enable: true,
          chkStyle: "checkbox",
          chkboxType: {
            Y: "ps",
            N: "s",
          },
        },
        view: { showIcon: false },
      },

      userQueryList: {
        id: "",
        page: 1,
        count: 10,
        sorting: "-1",
      },

      userTable: {
        show: false,
        total: 0,
        list: [],
      },

      modal: {
        show: false,
        state: 0,
        data: {
          id: "",
          dept_id: "",
          name: "",
          remark: "",
          menu_list: [],
          mainPageType: 0,
        },
        rules: {
          name: [
            {
              required: true,
              message: "请输入模板名称",
              trigger: "blur",
            },
          ],
        },
      },
      mainPageTypeList: [
        {
          label: "介绍型",
          value: 1,
        },
        {
          label: "看板型",
          value: 2,
        },
        {
          label: "高级调度",
          value: 4,
        },
        {
          label: "实时监控",
          value: 8,
        },
        {
          label: "看板型(简洁)",
          value: 16,
        },
      ],
      mainPageTypeRange: [1],
      mainPageChooseRange: {
        1: [1],
        2: [2],
        3: [1, 2],
        4: [4],
        5: [1, 4],
        6: [2, 4],
        7: [1, 2, 4],
        8: [8],
        9: [1, 8],
        10: [2, 8],
        11: [1, 2, 8],
        12: [4, 8],
        13: [1, 4, 8],
        14: [2, 4, 8],
        15: [1, 2, 4, 8],
        16: [16],
        17: [1, 16],
        18: [2, 16],
        19: [1, 2, 16],
        20: [4, 16],
        21: [1, 4, 16],
        22: [2, 4, 16],
        23: [1, 2, 4, 16],
        24: [8, 16],
        25: [1, 8, 16],
        26: [2, 8, 16],
        27: [1, 2, 8, 16],
        28: [4, 8, 16],
        29: [1, 4, 8, 16],
        30: [2, 4, 8, 16],
        31: [1, 2, 4, 8, 16],
      },
      guideMainPageList: [],
    };
  },

  computed: {
    ...mapState("auth", ["userInfo"]),
    pageStart() {
      return (this.queryList.page - 1) * this.queryList.count;
    },
    userPageStart() {
      return (this.userQueryList.page - 1) * this.userQueryList.count;
    },
  },

  mounted() {
    this.guideMainPageList = this.userInfo.mainPageTypeEditRange
      ? this.mainPageChooseRange[this.userInfo.mainPageTypeEditRange]
      : [1, 2, 4, 8];

    this.getPageDataInfo();
  },

  methods: {
    /**
     *  查询绑定用户 --------------
     */
    clearUserUp() {
      this.userQueryList.page = 1;
      this.userTable.list = [];
      this.userTable.total = 0;
    },

    searchBindUserInfo(row) {
      this.userTable.show = true;
      this.userQueryList.id = row.id;
      this.getPageBindDataInfo(1);
    },

    async getPageBindDataInfo(page) {
      this.userQueryList.page = page;
      let result = await this.$api.getRoleUserMenuById(
        this.userQueryList
      );
      if (!result) {
        this.$error(result.Reason || "查询失败");
        this.clearUserUp();
        return;
      }
      if (!result.sysUserByRoleIdList.length) {
        this.$warning(result.Reason || "未查询到数据");
        this.clearUserUp();
        return;
      }
      this.userTable.list = result.sysUserByRoleIdList;
      this.userTable.total = result.Count;
      this.$nextTick(() => {
        this.$refs["userTable"].doLayout();
      });
    },

    /**
     * 增删改查------
     */
    clearModal() {
      this.modal.data = {
        id: "",
        dept_id: "",
        name: "",
        remark: "",
        menu_list: [],
      };
    },
    async handleModalState(obj,copy) {
      this.modal.show = true;
      this.modal.state = obj && !copy ? 1 : 0;
      await this.$nextTick();
      this.clearModal();
      if (!obj) return;
      await this.$refs["modaldept"].waitForInit;
      let select = this.$refs["modaldept"].ztreeObj.getNodeByParam(
        "id",
        obj.dept_id,
        null
      );
      if (select) {
        this.$refs["modaldept"].ztreeObj.selectNode(
          select,
          false,
          false
        );
      }

      await this.$refs["modalmenu"].waitForInit;
      this.$refs["modalmenu"].ztreeObj.checkAllNodes(false);
      let checkedRule = await this.$api.getRoleMenuById({
        roleid: obj.id,
      });
      if (!checkedRule || !checkedRule.length) return;
      checkedRule.forEach((item) => {
        let currentCheck = this.$refs[
          "modalmenu"
        ].ztreeObj.getNodeByParam("id", item, null);
        if (!currentCheck) return;
        this.$refs["modalmenu"].ztreeObj.checkNode(
          currentCheck,
          true,
          false,
          false
        );
      });

      this.modal.data = Object.assign(JSON.parse(JSON.stringify(obj)), {
        menu_list: checkedRule,
      });
      if(copy){
        this.modal.data.name = ''
      }
      if (this.modal.data.mainPageType == 0) {
        this.mainPageTypeRange = [1];
      } else {
        this.mainPageTypeRange =
          this.mainPageChooseRange[this.modal.data.mainPageType] || [1];
      }

    },

    changeDataInfo() {
      this.$refs["form"].validate(async (valid) => {
        if (!valid) return;

        let currentId = this.$refs["modaldept"].ztreeObj
          .getSelectedNodes()
          .map((node) => {
            return node.id;
          });
        if (!currentId || !currentId.length) {
          this.$warning("请选择要绑定的单位!");
          return;
        }
        this.modal.data.dept_id = currentId[0];

        this.modal.data.menu_list = this.$refs["modalmenu"].ztreeObj
          .getCheckedNodes(true)
          .map((node) => {
            return node.id;
          });
        if (
          !this.modal.data.menu_list ||
          !this.modal.data.menu_list.length
        ) {
          this.$warning("请选择菜单权限");
          return;
        }
        this.modal.data.mainPageType = this.mainPageTypeRange.reduce(
          (pre, cur) => {
            return pre + cur;
          }
        );
        let parmas = JSON.parse(JSON.stringify(this.modal.data));
        let result;
        if (this.modal.state) {
          result = await this.$api.modifySysRoleInfo(parmas);
        } else {
          delete parmas.id;
          result = await this.$api.addSysRoleInfo(parmas);
        }
        if (!result) {
          this.$error("操作出错");
          return;
        }
        if (result.RS != 1) {
          this.$warning(result.Reason || "操作失败");
          return;
        }
        this.$success(result.Reason || "操作成功");
        this.modal.show = false;
        this.getPageDataInfo();
      });
    },

    removeDataInfo(row) {
      this.$confirm("此操作将永久删除改模板, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        let result = await this.$api.deleteSysRoleInfo({ id: row.id });
        if (!result) {
          this.$error("删除出错");
          return;
        }
        if (result.RS != 1) {
          this.$error(result.Reason || "删除失败");
          return;
        }
        this.$success(result.Reason || "删除成功");
        this.getPageDataInfo();
      });
    },
    selectNodes(current) {
      this.queryList.dept_id = current.id;
      this.getPageDataInfo();
    },
    clearUp() {
      this.queryList.page = 1;
      this.table = {
        loading: false,
        total: 0,
        list: [],
      };
    },

    async getPageDataInfo(page = 1) {
      this.queryList.page = page;
      this.table.loading = true;
      let result = await this.$api.getSysRoleInfo(this.queryList);
      if (!result) {
        this.$error("查询出错");
        this.clearUp();
        return;
      }
      if (result.RS != 1) {
        this.$warning(result.Reason || "查询出错");
        this.clearUp();
        return;
      }
      if (!result.sysRoleList.length) {
        this.$warning("未查询到数据!");
        this.clearUp();
        return;
      }
      this.table.total = result.Count || 0;
      this.table.list = result.sysRoleList || [];
      this.table.loading = false;
      this.$nextTick(() => {
        this.$refs["table"].doLayout();
      });
    },
  },
};
</script>

<style lang="scss" scoped></style>

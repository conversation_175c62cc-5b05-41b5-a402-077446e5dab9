<template>
    <PonyDialog
        v-model="show"
        :title="mode === 'add' ? '新增分组' : '修改分组'"
        :loading="loading"
        :width="520"
        @confirm="submitConfirm"
        content-style="height:367px;padding:0"
    >
    <div class="tree">
        <div class="tree-warp">
            <el-input class="input" placeholder="请输入分组名称" v-model="data.groupName"></el-input>
            <div class="tree-element">
                <ElementTree type="vehicle" ref="tree" :checkMode="true" @check="selectNodes" node-key="id" :shezhiShow="false" @mounted="treeChecked"></ElementTree>
            </div>
        </div>
    </div>
    <div class="vehicle-list">
        <el-table 
                ref="table"
                class="el-table--ellipsis"
                border stripe highlight-current-row size="mini" 
                :data="data.vehicleIdList" 
                height="100%" style="width: 100%">
                <el-table-column type="index" width="50" :index="(index) => index + 1" label="序号" ></el-table-column>
                <el-table-column prop="name" label="车牌号" min-width="100" show-overflow-tooltip>
                    <template slot-scope="{row}">
                        <span>{{basicByVehicleId[row].plate || '无权限车辆'}}</span>
                    </template>
                </el-table-column>
            </el-table>
    </div>
    </PonyDialog>
</template>

<script>
import {mapState} from 'vuex';
export default {
    name: 'addVehicleGroup',
    data() {
        return {
            show:false,
            mode:'add',
            loading:false,
            rowData:null,
            data:{
                vehicleIdList:[],
                groupName:'',
                remark:''

            }
        };
    },
    computed:{
        ...mapState('vehicle', ['basicByVehicleId']),
    },
    mounted() {
    },
    methods: {
        selectNodes(data, {checkedNodes}) {
            this.data.vehicleIdList = checkedNodes.filter(item => item.type == 4).map(item=>item.id)
        },
        treeChecked($tree) {
            $tree.setCheckedKeys(this.data.vehicleIdList)
        },
        showModel(row){
            this.data.groupName = ''
            this.data.vehicleIdList = []
            this.show = true
            if(row){
                this.rowData = row
                this.data.groupName = this.rowData.groupName
                this.data.vehicleIdList = this.rowData.vehicleIdList
                this.data.remark = this.rowData.remark
                this.mode = 'modify'
            }else {
                this.mode = 'add'
            }

        },
        submitConfirm(){
            if(!this.data.groupName){
                this.$warning('请输入分组名称!')
                return
            }
            if(!this.data.vehicleIdList.length){
                this.$warning('请选择分组车辆!')
                return
            }
            let params
            if(this.mode == 'add'){
                params = Object.assign({
                    type:0,
                },this.data)
            }else {
                params = Object.assign({
                    type:1,
                    id:this.rowData.id,
                },this.data)
            }
            this.confirmoperate(params)

        },
        async confirmoperate(params){
            this.loading = true
            try{
                let result = await this.$api.operateUserVehicleGroup(params)
                if(!result || result.status != 200){
                    this.$error(result.message || '操作出错!')
                    return
                }
                this.$success('操作成功!')
                this.show = false
                this.$emit('change')
            }catch(e){
                console.log(e);
                this.$error('操作出错!')
            }finally{
                this.loading = false
            }
        }
    },
};
</script>

<style lang="scss" scoped>
.tree {
    float: left;
    width: 60%;
    height: 100%;
    padding: 10px;
    border-right: 1px solid var(--border-color);
    .tree-warp {
        height: 100%;
        border: 1px solid var(--border-color);
        border-radius: 5px;
        background-color: var(--background-color-light);

        .input {
            height: 30px;
            border-bottom: 1px solid var(--border-color);
            /deep/.el-input__inner {
                border: none;
                background-color: transparent;
            }
        }
        .tree-element {
            height: calc(100% - 30px);
        }
    }
}
.vehicle-list {
    float: right;
    width: 40%;
    height: 100%;
    padding: 10px;


}
</style>
<template>
    <Layout tag="div" class="vehicle-group">
        <template slot="query">
            <div class="query-item">
                <label>分组名称:</label>
                <el-input v-model="query.query_name" placeholder="请输入分组名称"></el-input>
            </div>
            <div class="query-item">
                <el-button type="primary" @click="showModel(null)">新增</el-button>
            </div>
        </template>
        <template slot="content">
            <el-table 
                ref="table"
                class="el-table--ellipsis"
                :loading="table.loading"
                border stripe highlight-current-row size="mini" 
                :data="formatViewList" 
                height="100%" style="width: 100%">
                <el-table-column type="index" width="50" :index="(index) => index + 1 + pageStart" label="序号" ></el-table-column>
                <el-table-column prop="groupName" label="分组名称" min-width="150"></el-table-column>
                <el-table-column prop="createTime" label="创建时间" min-width="150"></el-table-column>
                <el-table-column prop="updateTime" label="修改时间" min-width="150"></el-table-column>
                <el-table-column prop="vehicleNum" label="车辆数" min-width="80"></el-table-column>
                <el-table-column label="操作" min-width="100">
                    <template slot-scope="{row}">
                        <el-button icon="pony-iconv2 pony-zhuanhuan" type='text' title="同步" size="mini" @click="synchrony(row)"></el-button>
                        <el-button icon="pony-iconv2 pony-xiugai" @click="showModel(row)" type="text" title="修改" size="mini"></el-button>
                        <el-button icon="pony-iconv2 pony-shanchu" @click="removeGroup(row)" type="text" title="修改" size="mini"></el-button>
                    </template>
                </el-table-column>
            </el-table>
        </template>
        <AddVehicleGroup ref="addVehicleGroup" @change="getAllData()"></AddVehicleGroup>
        <synchronyGroup ref="synchronyGroup" @change="getAllData()"></synchronyGroup>
    </Layout>
</template>

<script>
import PinyinMatch from 'pinyin-match'

import AddVehicleGroup from "./component/AddVehicleGroup.vue";
import synchronyGroup from "./component/synchronyGroup.vue";

export default {
    name: 'vehicleGroupMgt',
    components:{
        AddVehicleGroup,synchronyGroup
    },
    data() {
        return {
            query:{
                query_name:''
            },
            table:{
                data:[],
                page:1,
                size:30,
                loading:false
            }
        };
    },
    computed:{
       
        pageStart() {
            return (this.table.page - 1) * this.table.size
        },
        formatViewList() {
            return this.query.query_name?this.table.data.filter(item => PinyinMatch.match(item.groupName, this.query.query_name)).slice(this.pageStart, this.pageStart + this.table.size):this.table.data.slice(this.pageStart, this.pageStart + this.table.size)
        }
    },
    mounted() {
        this.getAllData()
    },

    methods: {
        synchrony(row){
                this.$refs.synchronyGroup.showModal(row)
        },
        async getAllData(){
            this.table.loading = true
            try{
                let result = await this.$api.operateUserVehicleGroup({
                    type:2
                })
                if(!result || result.status != 200){
                    this.$error(result.massage || '查询出错!')
                    return
                }
                if(!result.data.length){
                    this.$warning('未查询到数据!')
                    return
                }
                this.table.data = result.data
                this.$nextTick(() => {
                    this.$refs['table'].doLayout()
                })
            }catch(e){
                console.log(e);
                this.$error('查询出错!')
            }finally{
                this.table.loading = false
            }
        },
        showModel(row){
            this.$refs['addVehicleGroup'].showModel(row)
        },
        
        async removeGroup(row){
            await this.$confirm("此操作将永久删除该分组, 是否继续?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "error"
            });
            let res = await this.$api.operateUserVehicleGroup({
                type: -2,
                id: row.id
            });
            if (res.status == 200) {
                this.$message({
                    type: "success",
                    showClose: true,
                    message: "删除成功！"
                });
                this.table.data = this.table.data.filter(item => item.id != row.id)
            } else {
                this.$message({
                    type: "error",
                    showClose: true,
                    message: "删除失败！"
                });
            }
        }
    },
};
</script>

<style lang="scss" scoped>
.vehicle-group {

}
</style>
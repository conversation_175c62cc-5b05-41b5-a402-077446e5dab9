<template>
    <Layout :has-color="true" :contentLoading="table.loading" class="switch-door">
        <template slot="aside" style="width:260px">
            <div class="query-top">
                <ElementTree ref="vehicleTree" :checkMode="true" type="vehicle" @check="selectNodes"></ElementTree>
            </div>
            <div class="query-bottom bg bg--light box-shadow">
                <div class="query-item">
                    <span>开始时间:</span>
                    <el-date-picker @change="dateStartChange" :clearable="false" :picker-options="startDatePickerOptions" v-model="queryList.startTime"  value-format="yyyy-MM-dd" placeholder="选择日期">
                    </el-date-picker>
                </div>
                <div class="query-item">
                    <span>结束时间:</span>
                    <el-date-picker :clearable="false" :picker-options="endDatePickerOptions" v-model="queryList.endTime" value-format="yyyy-MM-dd" placeholder="选择日期">
                    </el-date-picker>
                </div>
                <div class="query-item">
                    <el-button type="primary" style="width: 100%;" @click="search" :loading="table.loading">查询</el-button>
                </div>
            </div>
        </template>

        <template slot="query">
            <div class="query-item">
                <el-button size="mini" type="primary" @click="exportTable">导出</el-button>
            </div>
            <div class="break-item">

            </div>
            <div class="query-item">
                <!--  -->
                <el-pagination background small :current-page.sync="table.page" :page-size="table.size" layout="prev, pager, next, total" :total="pageLength">
                </el-pagination>
            </div>
        </template>

        <template slot="content">
            <el-table
             ref="breaktable"
             class="el-table--ellipsis el-table--radius" border stripe  highlight-current-row size="mini" :data="formatList" @sort-change="sortCurrentProp" height="100%" style="width: 100%">
                <el-table-column type="index" :index="(index) => index + 1 + pageStart" label="序号" width="50"></el-table-column>
                <el-table-column prop="dept" label="公司名称" min-width="230" show-overflow-tooltip></el-table-column>
                <el-table-column prop="plateNo" label="车牌号" min-width="110"></el-table-column>
                <el-table-column prop="driverName" label="驾驶员" min-width="90" show-overflow-tooltip></el-table-column>
                <el-table-column prop="beginTime" label="开始时间" min-width="130">
                </el-table-column>
                <el-table-column prop="endTime" label="结束时间" min-width="130">
                </el-table-column>
                <el-table-column prop="duration" label="报警时长(小时)" min-width="90">
                    <template slot-scope="{ row }">
                        <div>{{(row.duration/3600).toFixed(2)}}小时</div>
                    </template>
                </el-table-column>
                <el-table-column label="开始位置" min-width="150"
                prop="beginLocation" show-overflow-tooltip>
                </el-table-column>
                <el-table-column label="结束位置" min-width="150" prop="endLocation"show-overflow-tooltip="">
                </el-table-column>
            </el-table>
        </template>
    </Layout>
</template>

<script>
const ExportJsonExcel = require('js-export-excel')

export default {
    name: 'fatiguedrivie',
    data() {
        return {
            exportLoading: false,
            startDatePickerOptions: {
                disabledDate: function (date) {
                    return (moment().endOf('day').toDate() - date) < 0
                }
            },
            currentNodes: [],
            queryList: {
                startTime: moment().subtract(1, 'days').startOf('day').format('YYYY-MM-DD'),
                endTime: moment().subtract(1, 'days').endOf('day').format('YYYY-MM-DD'),
                vehicleIdList: [],
                
            },
            exportList: {},
            table: {
                loading: false,
                page: 1,
                size: 30,
                data: [],
            },
            openTime: '',
            srcList: [],
            list: []
        };
    },

    computed: {
        endDatePickerOptions: function () {
            return {
                disabledDate: (date) => {
                    return (date - moment().endOf('day').toDate()) > 0 ||
                        date - moment(this.queryList.startTime).valueOf() < 0;
                }
            }
        },
        pageStart() {
            return (this.table.page - 1) * this.table.size
        },
        formatList() {
            return this.table.data.filter(item=>item.duration>=this.openTime*60).slice(this.pageStart, this.pageStart + this.table.size)
        },
        pageLength(){
            return this.table.data.filter(item=>item.duration>=this.openTime*60).length
        },
    },

    mounted() {

    },

    methods: {
        // data:当前选中的节点信息，checkedNodes：目前已选的节点信息
        selectNodes(data, { checkedNodes }) {
            this.currentNodes = checkedNodes;
        },
        async search() {
            let idList = []
            let vehicleList = this.currentNodes.filter(item => item.type === 4)
            if (vehicleList.length == 0) return this.$warning('请选择至少一辆车！')
            vehicleList.forEach(item => {
                idList.push(item.id)
            })
            this.table.loading = true
            this.table.data = []
            this.table.page = 1

            let queryInfo = {
                vehicleIdList: idList,
                start: moment(this.queryList.startTime).format("YYYY-MM-DD"),
                end: moment(this.queryList.endTime).format("YYYY-MM-DD"),
            }
            this.exportList = queryInfo
            let res = await this.$api.FatiguedrivieQuery(queryInfo)
            if (!res || res.status != 200) {
                this.table.loading = false
                this.$error(res.message || '查询出错')
                return
            }
            if (!res.data.length) {
                this.table.loading = false
                this.$warning('未查询到数据')
                return
            }
            this.table.data = res.data
            this.table.loading = false
            this.$nextTick(() => {
                 this.$refs['breaktable'].doLayout()
            })
        },
        formatSecond(item) {
            return item.toFixed(2)
        },


        exportTable() {
            if (!this.table.data.length) {
                this.$warning('没有数据可以导出')
                return
            }
            let exportProp = ['dept', 'plateNo', 'driverName', 'beginTime', 'endTime', 'duration','beginLocation','endLocation']
           let result = this.table.data.map((item,index)=>{
             return[
               index+1,
               item.dept,
               item.plateNo,
               item.driverName,
               item.beginTime,
               item.endTime,
               (item.duration/3600).toFixed(2),
               item.beginLocation,
               item.endLocation,
             ]
           })
            let options = {
                fileName: "疲劳驾驶报表",
                datas: [
                    {
                        sheetData: result,
                        sheetHeader: ['序号', '公司名称', '车牌号', '驾驶员', '开始时间', '结束时间', '报警时长(小时)','开始位置','结束位置'],
                        columnWidths: ['3', '15', '8', '8', '20', '20', '8','20','20']
                    }
                ]
            }
            ExportJsonExcel(options).saveExcel();
        },

        sortCurrentProp(column) {
            if (!column.order || !column.prop) return
            if (column.order == 'ascending') {
                this.table.data.sort((a, b) => (a[column.prop] - b[column.prop]))
            } else {
                this.table.data.sort((a, b) => (b[column.prop] - a[column.prop]))
            }
        },

        dateStartChange(date) {
            if (moment(this.queryList.endTime).valueOf() - moment(date).valueOf() < 0) {
                this.queryList.endTime = date;
            }
        },
    }
}

</script>

<style lang='scss' scoped>
.switch-door {
    .query-top {
        height: calc(100% - 140px);
    }
    .query-bottom {
        margin-top: 5px;
        height: 125px;
        padding: 10px;

        .query-item {
            display: flex;
            height: 40px;
            line-height: 40px;
            justify-content: space-between;
            align-items: center;

            // >div {
            //     flex-grow: 1;
            // }

            > span {
                font-size: 12px;
                white-space: nowrap;
                margin-right: 5px;
            }
        }
    }
}
</style>

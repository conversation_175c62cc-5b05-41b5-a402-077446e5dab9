<template>
  <Layout tag="div" class="role-user-mgnt" :contentLoading="loading">
    <div slot="aside" class="bg bg--light box-shadow h100">
      <ElementTree type="department" ref="deptZtree" @node-click="selectRoleUserNode"></ElementTree>
    </div>
    <template slot="query" v-if="hasPermission('user:query')">
      <div class="query-item">
        <label style="margin-right: 5px">用户名</label>
        <el-input type="text" v-model="searchQuery.login_name" @keyup.enter.native="getUserList(1)"></el-input>
      </div>
      <div class="query-item">
        <label style="margin-right: 5px">用户数据类型</label>
        <el-select v-model="searchQuery.open_type" placeholder="请选择用户类型">
          <el-option label="全部" :value="-1"></el-option>
          <el-option label="系统用户" :value="0"></el-option>
          <el-option label="接口用户" :value="1"></el-option>
          <el-option label="上报用户" :value="2"></el-option>
        </el-select>
      </div>
      <div class="query-item">
        <label style="margin-right: 5px">用户类型</label>
        <el-select v-model="searchQuery.user_type" placeholder="请选择用户类型">
          <el-option label="系统用户" :value="1"></el-option>
          <el-option label="司机用户" :value="2"></el-option>
          <el-option label="车辆用户" :value="3"></el-option>
          <el-option label="钥匙柜管理员" :value="5"></el-option>
        </el-select>
      </div>
      <div class="query-item">
        <span style="margin-right: 5px">到期日期</span>
        <el-select placeholder="请选择到期日期" v-model="yearDueDate">
          <el-option :label="item" :value="index" v-for="(item, index) in dueDateList" :key="item"></el-option>
        </el-select>
      </div>
      <div class="query-item">
        <el-button type="primary" @click="getUserList()" :loading="loading"> 查询 </el-button>
        <el-button type="primary" @click="exportData()" :loading="exportLoading"> 导出 </el-button>
        <el-button size="mini" type="primary" @click="showTableSetting()"> 表格显示配置 </el-button>
      </div>
      <div class="break-item"></div>
      <el-button type="primary" v-if="hasPermission('user:add')" @click="showModal('add')"> 新增 </el-button>
      <el-button type="primary" @click="showGuideModal()"> 批量设置 </el-button>
    </template>
    <template slot="content">
      <el-table
        class="el-table--radius box-shadow"
        :data="formatList"
        height="100%"
        ref="table"
        border
        size="mini"
        stripe
        highlight-current-row
        style="width: 100%"
        @sort-change="sortChange"
        @selection-change="handleSelectionChange"
        :row-key="getRowsKey"
        @select-all="selectAll"
      >
        <span slot="empty">{{ table.noDataText }}</span>
        <el-table-column type="selection" width="55" :reserve-selection="true"></el-table-column>
        <el-table-column type="index" :index="(index) => index + 1 + pageStart" label="序号" width="50"></el-table-column>
        <el-table-column prop="location" label="操作" width="120">
          <template slot-scope="scope">
            <el-button
              v-if="hasPermission('user:update')"
              type="text"
              title="修改"
              size="mini"
              @click="showModal('modify', scope.row)"
            >
              <i class="pony-iconv2 pony-xiugai"></i>
            </el-button>
            <el-button
              type="text"
              size="mini"
              title="删除"
              style="margin-left: 0"
              @click="showModal('delete', scope.row)"
              :disabled="scope.row.user_type != 1"
            >
              <i class="pony-iconv2 pony-shanchu"></i>
            </el-button>
            <el-button type="text" size="mini" title="复制" style="margin-left: 0" @click="showModal('copy', scope.row)">
              <i class="pony-iconv2 pony-fuzhi"></i>
            </el-button>
            <el-dropdown @command="(cmd) => showModal(cmd, scope.row)">
              <el-button type="text" size="mini" title="操作">
                <i class="pony-iconv2 pony-xia"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="ip" v-if="hasPermission('rule:query')">
                  <el-button type="text" size="mini"> IP </el-button>
                </el-dropdown-item>
                <el-dropdown-item command="logo">
                  <el-button type="text" size="mini"> LOGO </el-button>
                </el-dropdown-item>
                <el-dropdown-item command="alarmset" v-if="hasPermission('rule:alarmset')">
                  <el-button type="text" size="mini"> 报警类型配置 </el-button>
                </el-dropdown-item>
                <el-dropdown-item command="alarmEventConfig" v-if="hasPermission('rule:alarmEventConfig')">
                  <el-button type="text" size="mini"> 报警事件类型配置 </el-button>
                </el-dropdown-item>
                <el-dropdown-item command="timeout">
                  <el-button type="text" size="mini"> 超时配置 </el-button>
                </el-dropdown-item>
                <el-dropdown-item command="interface" v-if="scope.row.open_type == 1">
                  <el-button type="text" size="mini"> 接口权限配置 </el-button>
                </el-dropdown-item>
                <el-dropdown-item command="passArea">
                  <el-button type="text" size="mini"> 区域配置 </el-button>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
        <template v-for="(item, index) in tableSettingList">
          <el-table-column
            :label="item.name"
            :min-width="item.size"
            :sortable="item.sort"
            :align="item.align"
            v-if="item.key == 'company_name'"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              <span>{{ (scope.row.company_name || "") + (scope.row.dept_name || "") }}</span>
            </template>
          </el-table-column>
          <el-table-column
            :label="item.name"
            :min-width="item.size"
            :sortable="item.sort"
            :align="item.align"
            v-else-if="item.key == 'name'"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              <span v-if="scope.row.name">{{ scope.row.name }}</span>
              <span v-if="!scope.row.name">无</span>
            </template>
          </el-table-column>
          <el-table-column
            :label="item.name"
            :min-width="item.size"
            :sortable="item.sort"
            :align="item.align"
            v-else-if="item.key == 'phone'"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              <span v-if="scope.row.phone">{{ scope.row.phone }}</span>
              <span v-if="!scope.row.phone">无</span>
            </template>
          </el-table-column>
          <el-table-column
            :label="item.name"
            :min-width="item.size"
            :sortable="item.sort"
            :align="item.align"
            v-else-if="item.key == 'sex'"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              <span v-if="scope.row.sex == 1">男</span>
              <span v-if="scope.row.sex == 2">女</span>
              <span v-if="!scope.row.sex">男</span>
            </template>
          </el-table-column>
          <el-table-column
            :label="item.name"
            :min-width="item.size"
            :sortable="item.sort"
            :align="item.align"
            v-else-if="item.key == 'age'"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              <span v-show="scope.row.age != 0">{{ scope.row.age }}</span>
              <span v-if="!scope.row.age">无</span>
            </template>
          </el-table-column>
          <el-table-column
            :label="item.name"
            :min-width="item.size"
            :sortable="item.sort"
            :align="item.align"
            v-else-if="item.key == 'valid_date'"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              <span v-text="scope.row.valid_date" v-if="scope.row.valid == true"></span>
              <span v-if="!scope.row.valid_date">无</span>
              <span v-if="scope.row.valid == false">{{ scope.row.valid_date }}<span class="valid">已过期</span></span>
            </template>
          </el-table-column>
          <el-table-column
            :label="item.name"
            :min-width="item.size"
            :sortable="item.sort"
            :align="item.align"
            v-else-if="item.key == 'main_page_type'"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              <span>{{ mainPageTypeList[scope.row.main_page_type] }}</span>
            </template>
          </el-table-column>
          <el-table-column
            :label="item.name"
            :min-width="item.size"
            :sortable="item.sort"
            :align="item.align"
            v-else-if="item.key == 'user_type'"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              <span v-if="scope.row.user_type == 1">系统用户</span>
              <span v-if="scope.row.user_type == 2">司机用户</span>
              <span v-if="scope.row.user_type == 3">车辆用户</span>
              <span v-if="scope.row.user_type == 5">钥匙柜管理员</span>

            </template>
          </el-table-column>
          <el-table-column
            :label="item.name"
            :min-width="item.size"
            :sortable="item.sort"
            :align="item.align"
            v-else-if="item.key == 'data_scope'"
          >
            <template slot-scope="scope">
              <span v-if="scope.row.data_scope == 1">全部车辆</span>
              <span v-if="scope.row.data_scope == 3">部分车辆</span>
            </template>
          </el-table-column>
          <el-table-column
            :label="item.name"
            :min-width="item.size"
            :sortable="item.sort"
            :align="item.align"
            :prop="item.key"
            v-else
            show-overflow-tooltip
          >
          </el-table-column>
        </template>
        <el-table-column label="优先级" prop="monitor_level" v-if="hasPermission('AlarmDetails:overload')">
          <template slot-scope="scope">
            {{
              scope.row.monitor_level == 1 ? "高" : scope.row.monitor_level == 2 ? "中" : scope.row.monitor_level == 3 ? "低" : ""
            }}
          </template>
        </el-table-column>
      </el-table>
    </template>
    <template slot="footer">
      <el-pagination
        small
        background
        layout="prev, pager, next,total"
        :current-page.sync="table.page"
        :page-size="table.size"
        :total="table.total"
      >
      </el-pagination>
    </template>
    <PonyDialog
      v-model="userModal.show"
      :title="userModal.mode === 'add' || userModal.mode === 'copy' ? userModal.addModeTitle : userModal.modifyModeTitle"
      :width="600 + (userModal.data.data_scope === 3 && userModal.data.user_type != 3 ? 300 : 0)"
    >
      <el-row
        type="flex"
        v-loading="userModal.loading"
        element-loading-text="正在加载"
        element-loading-spinner="el-icon-loading"
        style="height: 100%"
      >
        <el-col :span="userModal.data.data_scope === 3 && userModal.data.user_type != 3 ? 8 : 12">
          <el-form
            ref="userForm"
            label-width="98px"
            size="mini"
            :rules="userModal.rules"
            :model="userModal.data"
            style="height: 100%; padding-right: 5px; overflow: auto"
          >
            <el-form-item label="用户名" prop="login_name">
              <el-input type="text" v-model="userModal.data.login_name"></el-input>
            </el-form-item>
            <el-form-item label="归属部门" prop="dept_id">
              <SelectTreeInput
                v-model="userModal.data.group"
                type="department"
                placeholder="请选择所属"
                :condition="() => true"
                :withParent="true"
                ref="departmentInput"
                title="请选择所属"
              >
              </SelectTreeInput>
            </el-form-item>

            <el-form-item label="数据范围" prop="data_scope">
              <el-select
                v-model="userModal.data.data_scope"
                style="width: 100%"
                @change="scopeChange"
                :popper-append-to-body="false"
                :disabled="userModal.data.user_type != 1"
                v-if="userModal.data.user_type == 1"
              >
                <el-option :value="1" label="全部车辆"></el-option>
                <el-option
                  :label="`部分车辆${userModal.data.open_type === 2 ? '(用户数据必须为非上报用户)' : ''}`"
                  :value="3"
                  :disabled="userModal.data.open_type == 2"
                ></el-option>
              </el-select>
              <el-input v-model="userModal.data.reserve1" disabled v-else></el-input>
            </el-form-item>
            <!-- <el-form-item label="功能权限">
								 <el-radio-group v-model="userModal.data.userSelectData" @change="selectUserStyle">
										 <el-radio :label="1">采用角色模板</el-radio>
										 <el-radio :label="0">自定义权限</el-radio>
								 </el-radio-group>
						 </el-form-item>-->
            <el-form-item label="密码" prop="password">
              <el-input
                type="text"
                v-model="userModal.data.password"
                placeholder="密码不能为空"
                @change="userModal.passwordChanged = true"
              >
                <i
                  slot="suffix"
                  :class="['pony-iconv2', 'see-pwd', showPwd.status ? 'pony-biyan' : 'pony-chakan']"
                  @click="toShowPassword"
                  v-if="hasPermission('user:showPwd')"
                ></i>
              </el-input>
            </el-form-item>
            <el-form-item label="行业类型">
              <el-select
                style="width: 100%"
                v-model="userModal.data.data_industry_types"
                multiple
                :popper-append-to-body="false"
                :collapse-tags="userModal.data.data_industry_types.length > 2"
                placeholder="请选择行业类型(可多选)"
                trigger="hover"
              >
                <el-option
                  v-for="item in industryTypeList"
                  :key="item.value"
                  :label="item.name"
                  :value="item.value"
                  :disabled="item.disabled"
                >
                </el-option>
              </el-select>
            </el-form-item>

            <el-form-item v-if="userModal.data.user_type != 1" label="用户类型">
              <el-select style="width: 100%" v-model="userModal.data.user_type" placeholder="请选择用户类型" disabled>
                <el-option label="其他用户" :value="1"></el-option>
                <el-option label="司机用户" :value="2"></el-option>
                <el-option label="车辆用户" :value="3"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="到期日期">
              <el-date-picker
                style="width: 100%"
                v-model="userModal.data.valid_date"
                type="date"
                placeholder="选择日期"
                :picker-options="userModal.pickerOptions"
              >
              </el-date-picker>
            </el-form-item>
            <el-form-item label="管理同级账号">
              <el-checkbox v-model="userModal.data.account_manager"></el-checkbox>
            </el-form-item>
            <el-form-item label="用户级别" v-if="userModal.data.account_manager">
              <el-select v-model="userModal.data.account_manager_level" style="width: 100%" placeholder="请选择用户级别">
                <el-option label="1" :value="1"></el-option>
                <el-option label="2" :value="2"></el-option>
                <el-option label="3" :value="3"></el-option>
                <el-option label="4" :value="4"></el-option>
                <el-option label="5" :value="5"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="首页">
              <el-select v-model="userModal.data.main_page_type" style="width: 100%" placeholder="请选择首页类型">
                <el-option label="介绍型" :value="1" v-show="guideMainPageList.indexOf(1) != -1"></el-option>
                <el-option label="看板型" :value="2" v-show="guideMainPageList.indexOf(2) != -1"></el-option>
                <el-option label="高级调度" :value="4" v-show="guideMainPageList.indexOf(4) != -1"></el-option>
                <el-option label="实时监控" :value="8" v-show="guideMainPageList.indexOf(8) != -1"></el-option>
                <el-option label="看板型(简洁)" :value="16" v-show="guideMainPageList.indexOf(16) != -1"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="姓名">
              <el-input type="text" v-model="userModal.data.name"></el-input>
            </el-form-item>
            <el-form-item label="电话">
              <el-input type="text" v-model="userModal.data.phone"></el-input>
            </el-form-item>

            <el-form-item label="性别">
              <el-radio-group v-model="userModal.data.sex">
                <el-radio :label="1">男</el-radio>
                <el-radio :label="2">女</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="行政区域" prop="area_id">
              <SelectTreeInput
                v-model="userModal.data.areaGroup"
                type="area"
                placeholder="请选择所属"
                :condition="areaCondition"
                ref="areaInput"
                title="请选择所属"
              >
              </SelectTreeInput>
            </el-form-item>
            <el-form-item label="审批角色">
              <DictionarySelect
                style="width: 100%"
                v-model="userModal.data.approval_role"
                code="pass_approval_role"
                multiple
              ></DictionarySelect>
            </el-form-item>
            <!-- <el-form-item label="通行辖区">
								<DictionarySelect v-model="userModal.data.jurisdiction" code="pass_approval_jurisdiction" multiple></DictionarySelect>
						</el-form-item> -->
            <el-form-item label="归属辖区" prop="jurisdiction" class="selectInput">
              <!-- <SelectTreeInput
									checkStrictly
									:checkMode="true" checkModeText="个辖区"
									v-model="jurisdiction.selectInstance" type="zhongdui"
									placeholder="请选择所属" :condition="()=>true"
									title="请选择所属">
							</SelectTreeInput> -->
              <FenceTreeFilter
                :source="16"
                v-model="jurisdictionTree"
                type="xiaquMgt"
                ref="xiaqu"
                :extraKeys="['type', 'business_value']"
                :checkMode="true"
                :widthStyle="{ width: '181px !important' }"
              >
              </FenceTreeFilter>
            </el-form-item>
            <el-form-item label="归属限行区" prop="restricted_zone" class="selectInput">
              <FenceTreeFilter
                :source="8"
                v-model="xianxingquTree"
                type="xianxingqu"
                ref="xianxingqu"
                :extraKeys="['type', 'business_value']"
                :checkMode="true"
                :widthStyle="{ width: '181px !important' }"
              >
              </FenceTreeFilter>
            </el-form-item>
            <el-form-item label="用户优先级">
              <el-select style="width: 100%" v-model="userModal.data.monitor_level" placeholder="请选择优先级">
                <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"> </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="用户数据">
              <el-select style="width: 100%" v-model="userModal.data.open_type" placeholder="请选择用户数据类型">
                <el-option label="系统用户" :value="0"></el-option>
                <el-option label="接口用户" :value="1"></el-option>
                <el-option
                  :label="`上报用户${userModal.data.data_scope === 3 ? '(必须选择全部车辆)' : ''}`"
                  :value="2"
                  :disabled="userModal.data.data_scope !== 1"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="年龄">
              <el-input type="text" v-model="userModal.data.age"></el-input>
            </el-form-item>
            <el-form-item label="工号">
              <el-input type="text" v-model="userModal.data.user_no"></el-input>
            </el-form-item>
            <el-form-item label="职务">
              <el-input type="text" v-model="userModal.data.duty"></el-input>
            </el-form-item>
            <el-form-item label="科室">
              <el-input type="text" v-model="userModal.data.section_office"></el-input>
            </el-form-item>
          </el-form>
        </el-col>
        <el-col :span="userModal.data.data_scope === 3 ? 16 : 12">
          <div class="roleAreaWrap">
            <div style="min-width: 250px; width: 100%" v-show="userModal.data.userSelectData == 1">
              <div class="filterTop">
                <div class="nameFilter">
                  <span>名称搜索：</span>
                  <el-select
                    v-model="userModal.filterName"
                    class="filter-value"
                    remote
                    :remote-method="filterChangeName"
                    filterable
                    clearable
                    @clear="filterClear('filterName')"
                  >
                    <el-option
                      v-for="item in userModal.selectorListName"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"
                    ></el-option>
                  </el-select>
                </div>
                <div class="deptFilter">
                  <span>部门过滤：</span>
                  <el-select
                    v-model="userModal.filterDept"
                    class="filter-value"
                    :filter-method="filterChangeDept"
                    filterable
                    clearable
                    @clear="filterClear('filterDept')"
                  >
                    <el-option
                      v-for="item in selectorListDept"
                      :key="item.id"
                      :label="item.deptName"
                      :value="item.deptName"
                    ></el-option>
                  </el-select>
                </div>
              </div>

              <el-table
                ref="roleTable"
                class="el-table--radius"
                :data="userModal.userSelectDataList"
                height="88%"
                border
                style="width: 100%"
                highlight-current-row
                @row-click="handleCurrentChange"
                @selection-change="selsChange"
              >
                <el-table-column type="selection" width="55"> </el-table-column>
                <el-table-column prop="name" label="模板名称"> </el-table-column>
              </el-table>
            </div>
            <div
              :class="['roleArea', { 'has-error': userModal.treeError[1] === 2 }]"
              v-show="userModal.data.data_scope === 3 && userModal.data.user_type !== 3"
            >
              <div class="pony_ColorBlack">
                数据权限
                <i class="pony-iconv2 pony-shuaxin fr font-active" @click="setDataScopeTree()"></i>
              </div>
              <div
                class="roleTrueArea OnlyTree"
                v-loading="userModal.treeError[1] && !userModal.loading"
                :element-loading-text="userModal.treeError[1] === 2 ? '加载失败,请刷新' : '正在加载'"
                :element-loading-spinner="
                  userModal.treeError[1] === 2 ? 'pony-iconv2 pony-guanbiyuan font-error' : 'el-icon-loading'
                "
              >
                <ZtreeMatics
                  :extendSetting="dataScopeZTree.setting"
                  ref="dataScopeZTree"
                  :noRelevanceCheck="true"
                  :ztreeDblClickHandle="ztreeDblClickHandleEventMain"
                >
                </ZtreeMatics>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
      <template slot="footer">
        <el-button type="primary" @click="confirm">
          {{ userModal.mode === "add" || userModal.mode === "copy" ? userModal.addModeOkText : userModal.modifyModeOkText }}
        </el-button>
        <el-button @click="userModal.show = false">取消</el-button>
      </template>
    </PonyDialog>
    <PonyDialog v-model="ipModal.show" width="750" title="账号登录配置">
      <el-row v-loading="ipModal.loading" element-loading-text="正在加载" element-loading-spinner="el-icon-loading">
        <el-col :span="12" class="ip-config-box">
          <div class="input-group dbgroup">
            <span>IP:</span>
            <el-input placeholder="请输入新的可用IP" v-model="ipModal.data.ip"></el-input>
            <span class="input-group-addon onlyAddBtn" @click="addIpByUserId"><i class="pony-iconv2 pony-xinzeng"></i></span>
          </div>
          <p>
            <el-checkbox v-model="ipModal.data.IpBlackAddress">是否加入黑名单</el-checkbox>
          </p>
          <div class="config-table">
            <el-table height="100%" border highlight-current-row :data="ipModal.table.ipList" style="width: 100%">
              <el-table-column width="50" type="index" label="序号"></el-table-column>
              <el-table-column prop="ip" label="IP地址"> </el-table-column>
              <el-table-column prop="type_name" label="黑名单IP" width="80"> </el-table-column>
              <el-table-column width="70" label="操作">
                <template slot-scope="scope">
                  <span><i data-v-13e75463="" class="pony-iconv2 pony-shanchu" @click="deleteIpByUser(scope.row)"></i></span>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-col>
        <el-col :span="12" class="ip-config-box">
          <div class="input-group dbgroup">
            <span>地址:</span>
            <el-input placeholder="请输入城市地址" v-model="ipModal.data.city"></el-input>
            <span class="input-group-addon onlyAddBtn" @click="addCityByUserId"><i class="pony-iconv2 pony-xinzeng"></i></span>
          </div>
          <p class="cityTip">( 级别仅显示至城市 )</p>
          <div class="config-table">
            <el-table height="100%" border highlight-current-row :data="ipModal.table.cityList" style="width: 100%">
              <el-table-column width="50" type="index" label="序号"></el-table-column>
              <el-table-column label="城市地址">
                <template slot-scope="scope">
                  <span>{{ scope.row }}</span>
                </template>
              </el-table-column>
              <el-table-column width="80" label="操作">
                <template slot-scope="scope">
                  <span><i data-v-13e75463="" class="pony-iconv2 pony-shanchu" @click="deleteCityByUser(scope.row)"></i></span>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-col>
      </el-row>
      <template slot="footer">
        <el-button type="primary" @click="ipModal.show = false">确认</el-button>
      </template>
    </PonyDialog>
    <PonyDialog v-model="uploadModal.show" width="300" title="图片上传" @close="cancelUpload">
      <div v-loading="uploadModal.loading" element-loading-text="正在上传" element-loading-spinner="el-icon-loading">
        <p>注意：为了美观 请上传 大小(180 * 50) 的logo</p>
        <el-upload
          action="/ponysafety2/a/user/upLoadPhoto"
          name="img"
          :data="{ id: 0 }"
          :on-success="uploadSuccess"
          :on-error="uploadError"
          :before-upload="beforeUpload"
          :show-file-list="false"
        >
          <img
            :src="uploadModal.data.blob || uploadModal.data.url || uploadModal.data.defaultUrl"
            style="width: 180px; height: 50px; margin: 10px 50px"
          />
        </el-upload>
      </div>
      <template slot="footer">
        <el-button type="primary" @click="updateCompanyLogo">确认上传</el-button>
      </template>
    </PonyDialog>
    <PonyDialog v-model="timeoutModal.show" width="400" title="账户登录超时配置(未设置时默认30分钟,至少配置5分钟)">
      <el-form
        :inline="true"
        :model="timeoutModal.data"
        v-loading="timeoutModal.loading"
        element-loading-text="正在加载"
        element-loading-spinner="el-icon-loading"
      >
        <el-form-item label="超时时间">
          <el-input-number
            :disabled="timeoutModal.data.type"
            v-model="timeoutModal.data.times"
            style="width: 150px"
            :min="5"
          ></el-input-number>
        </el-form-item>
        <el-form-item label="其他">
          <el-checkbox v-model="timeoutModal.data.type">永不过期</el-checkbox>
        </el-form-item>
      </el-form>
      <template slot="footer">
        <el-button type="primary" @click="addOrUpSessionTime">确定</el-button>
        <el-button @click="deleteSessionTime">删除</el-button>
      </template>
    </PonyDialog>
    <PonyDialog v-model="interfaceModal.show" width="1000" @close="exitInterfaceOperate" :outSideClick="false">
      <span slot="title">
        开放接口权限配置
        {{ `（当前用户：${interfaceModal.data.title}）` }}
      </span>

      <div
        style="height: 400px"
        v-loading="interfaceModal.loading"
        element-loading-text="正在加载"
        element-loading-spinner="el-icon-loading"
      >
        <el-table
          :data="interfaceModal.data.interfaceList"
          height="100%"
          border
          ref="interfaceTable"
          highlight-current-row
          style="width: 100%"
        >
          <el-table-column type="index" label="序号"></el-table-column>
          <el-table-column label="接口类型" align="center">
            <template slot-scope="scope">
              <span v-if="scope.row.type == 0">基础数据接口</span>
              <span v-if="scope.row.type == 1">登陆登出接口</span>
            </template>
          </el-table-column>
          <el-table-column prop="name" label="接口名"></el-table-column>
          <el-table-column prop="permission" label="权限标识"></el-table-column>
          <el-table-column prop="url" label="接口地址" align="left"></el-table-column>
          <el-table-column prop="desc" label="接口描述"></el-table-column>
          <el-table-column label="启用/禁用">
            <template slot-scope="scope">
              <el-switch size="mini" v-model="scope.row.open" @change="handleInterfaceOpenChange(scope.row)" />
            </template>
          </el-table-column>
        </el-table>
      </div>
      <template slot="footer">
        <el-button @click="exitInterfaceOperate">关闭</el-button>
      </template>
      <!-- <span slot="headerCotent">{{ `用户：${interfaceModal.data.title}` }}</span> -->
    </PonyDialog>
    <PonyDialog
      v-model="alarmConfigModal.show"
      width="460"
      :loading="alarmConfigModal.loading"
      :has-mask="true"
      title="用户报警类型配置"
    >
      <el-button type="text" size="mini" slot="headerContent" @click="removeUserAlarmSet">恢复默认配置</el-button>
      <!-- <AlarmTabList v-model="alarmConfigModal.data.alarmList" @change="alarmConfigModal.change = true"></AlarmTabList> -->
      <AsideAlarm
        :filter="[]"
        :count="alarmCount"
        @changeAll="handleAlarmChangeModal"
        :checkedAll="false"
        :defaultCheckedList="defaultCheckedList"
      />

      <template slot="footer">
        <el-button type="primary" :disabled="!alarmConfigModal.change" @click="handleCloseAlarmConfigDialog">确认 </el-button>
      </template>
    </PonyDialog>
    <PonyDialog
      v-model="alarmEventConfigModal.show"
      :width="750"
      :loading="alarmEventConfigModal.loading"
      :offset-y="-120"
      :has-mask="true"
      title="用户报警事件类型配置"
    >
      <el-button type="text" size="mini" slot="headerContent" @click="removeUserAlarmEvent">恢复默认配置</el-button>
      <AlarmEventTabList v-model="alarmEventConfigModal.data.alarmEventList" @change="alarmEventConfigModal.change = true">
      </AlarmEventTabList>
      <template slot="footer">
        <el-button type="primary" :disabled="!alarmEventConfigModal.change" @click="handleCloseAlarmEventConfigDialog">
          确认
        </el-button>
      </template>
    </PonyDialog>
    <PonyDialog v-model="passAreaListModal.show" width="400" :loading="passAreaListModal.loading" title="用户区域配置">
      <div class="flex flex-align-center">
        <span style="width: 100px">区域配置：</span>
        <SelectTreeInput
          v-model="passAreaListModal.data.list"
          title="区域选择"
          placeholder="未设置"
          checkModeText="个区域"
          type="area"
          :checkMode="true"
          ref="passArea"
          class="flex-grow"
        ></SelectTreeInput>
      </div>
      <template slot="footer">
        <el-button type="primary" @click="handleCommitPassArea">确认</el-button>
      </template>
    </PonyDialog>
    <PonyDialog v-model="guideModal.show" width="1000" title="批量设置">
      <div class="tabsContent">
        <div class="aside">
          <div style="height: calc(100% - 48px); overflow-y: auto">
            <div
              v-for="(item, index) in filterList"
              :key="index"
              :class="['industry-item', { active: editTab === item.id }]"
              @click="handleClickIndustry(item)"
            >
              <span>{{ item.value }}</span>
            </div>
          </div>
        </div>
        <div class="tabs">
          <div v-show="currentTab == 'mainPage'" class="tabItem">
            <div class="title"><i style="background-color: #2880e2"></i><span>用户首页</span></div>
            <div style="padding: 10px">
              <span style="margin-right: 5px">首页类型</span>
              <el-select v-model="guideModal.data.main_page_type" style="width: 200px">
                <el-option label="介绍型" :value="1" v-show="guideMainPageList.indexOf(1) != -1"></el-option>
                <el-option label="看板型" :value="2" v-show="guideMainPageList.indexOf(2) != -1"></el-option>
                <el-option label="高级调度" :value="4" v-show="guideMainPageList.indexOf(4) != -1"></el-option>
                <el-option label="实时监控" :value="8" v-show="guideMainPageList.indexOf(8) != -1"></el-option>
                <el-option label="看板型(简洁)" :value="16" v-show="guideMainPageList.indexOf(16) != -1"></el-option>
              </el-select>
            </div>
          </div>
          <div class="tabItem" v-show="currentTab == 'area'">
            <div class="title"><i style="background-color: #2880e2"></i><span style="margin-right: 72%">区域配置</span></div>
            <div style="padding: 10px">
              <span style="width: 100px">区域配置：</span>
              <SelectTreeInput
                v-model="guideModal.pass_area"
                title="区域选择"
                placeholder="未设置"
                checkModeText="个区域"
                type="area"
                :checkMode="true"
                ref="passArea"
                style="width: 200px"
              ></SelectTreeInput>
            </div>
          </div>
          <div v-show="currentTab == 'alarmEvent'" class="tabItem">
            <div class="title">
              <i style="background-color: #2880e2"></i><span style="margin-right: 72%">报警事件类型配置</span>
              <el-button type="text" size="mini" slot="headerContent" @click="removeAlarmEvent">恢复默认配置</el-button>
            </div>
            <AlarmEventTabList
              v-model="guideModal.data.user_alarm_event_config.values"
              @change="guideModal.alarmEventChange = true"
            ></AlarmEventTabList>
          </div>
          <div v-show="currentTab == 'alarmType'" class="tabItem">
            <div class="title">
              <i style="background-color: #2880e2"></i><span style="margin-right: 75%">报警类型配置</span>
              <el-button type="text" size="mini" slot="headerContent" @click="removeAlarmSet">恢复默认配置</el-button>
            </div>
            <div style="width: 100%; height: 100%; overflow-y: auto">
              <!-- <AlarmTabList v-model="guideModal.data.user_alarm_config.alarm_code_list" @change="guideModal.alarmChange = true"></AlarmTabList> -->
              <AsideAlarm :filter="[]" :count="alarmCount" @changeAll="handleAlarmChange" :checkedAll="false" />
            </div>
          </div>
          <div v-show="currentTab == 'overTime'" class="tabItem">
            <div class="title"><i style="background-color: #2880e2"></i><span style="margin-right: 75%">超时配置</span></div>
            <div style="padding: 10px">
              <span style="width: 100px">超时配置：</span>
              <el-input-number
                v-model="guideModal.user_session_time"
                style="width: 150px"
                :min="5"
                :disabled="guideModal.type"
              ></el-input-number>
              <el-checkbox v-model="guideModal.type">永不过期</el-checkbox>
            </div>
          </div>
          <div v-show="currentTab == 'expiryDate'" class="tabItem">
            <div class="title"><i style="background-color: #2880e2"></i><span style="margin-right: 75%">到期日期配置</span></div>
            <div style="padding: 10px">
              <span style="width: 120px">到期日期配置：</span>

              <el-date-picker
                style="width: 200px"
                v-model="guideModal.data.valid_date"
                type="date"
                placeholder="选择日期"
                :picker-options="guideModal.pickerOptions"
              >
              </el-date-picker>
            </div>
          </div>
          <div v-show="currentTab == 'userPermisson'" class="tabItem">
            <div class="title"><i style="background-color: #2880e2"></i><span style="margin-right: 75%">数据权限配置</span></div>
            <div class="form_tree">
              <div class="form">
                <el-form label-width="80px" :model="guideModal.data">
                  <el-form-item label="归属部门" prop="dept_id">
                    <SelectTreeInput
                      v-model="guideModal.group"
                      type="department"
                      placeholder="请选择所属"
                      :condition="() => true"
                      :withParent="true"
                      ref="departmentInput"
                      title="请选择所属"
                    >
                    </SelectTreeInput>
                  </el-form-item>
                  <el-form-item label="数据范围" prop="data_scope">
                    <el-select
                      v-model="guideModal.data.data_scope"
                      style="width: 100%"
                      @change="scopeModifyChange"
                      :popper-append-to-body="false"
                    >
                      <el-option :value="1" label="全部车辆"></el-option>
                      <el-option label="部分车辆" :value="3"></el-option>
                    </el-select>
                  </el-form-item>
                </el-form>
              </div>
              <div class="tree">
                <div class="treeWrap" v-show="guideModal.data.data_scope === 3&&guideModal.data.dept_id">
                  <div class="colorBlack">
                    数据权限
                    <i class="pony-iconv2 pony-shuaxin fr font-active" @click="setDataScopeTree()"></i>
                  </div>
                  <div
                    class="roleTrueArea OnlyTree"
                    style="height: 390px;"
                    v-loading="userModal.treeError[1] && !userModal.loading"
                    :element-loading-text="userModal.treeError[1] === 2 ? '加载失败,请刷新' : '正在加载'"
                    :element-loading-spinner="
                      userModal.treeError[1] === 2 ? 'pony-iconv2 pony-guanbiyuan font-error' : 'el-icon-loading'
                    "
                  >
                    <ZtreeMatics
                      :extendSetting="dataScopeZTree.setting"
                      ref="dataScopeZTree"
                      :noRelevanceCheck="true"
                      :ztreeDblClickHandle="ztreeDblClickHandleEventMain"
                    >
                    </ZtreeMatics>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <template slot="footer">
        <el-button type="primary" @click="confirmGuideChange" :loading='confirmLoading'> 确认 </el-button>
        <el-button @click="guideModal.show = false">取消</el-button>
      </template>
    </PonyDialog>
    <TableShowConfigList
      ref="tableShowConfigList"
      v-model="tableSettingList"
      :filterableSearch="true"
      :list="allSettingList"
      :defaultSetting="defaultSettingList"
      :auto-width="640"
      :width="665"
      @change="settable"
      pageName="roleUserMgt"
      :isSave="true"
      @tableValue="getTableSetting"
    ></TableShowConfigList>
  </Layout>
</template>

<script>
/**
 * @Author: yezy
 * @Email: <EMAIL>
 * @Date: 2018-11-15 12:12:12
 * @LastEditors: yezy
 * @LastEditTime: 2020-04-02 11:40:58
 * @Description: 老页面了，不想重构
 */
import defaultLogo from "../../../static/img/company/admin_logo.png";
import SelectTreeInput from "../../components/common/SelectTreeInput";
import FenceTreeFilter from "@/view/business/components/FenceTreeFilter";
import AsideAlarm from "@/components/common/AsideAlarm";

import AlarmTabList from "./ThemeRuler/AlarmConfig/AlarmTabList";
import AlarmEventTabList from "./ThemeRuler/AlarmEventConfig/AlarmEventTabList";
import DictionarySelect from "@/components/common/DictionarySelect";
import { mapState, mapGetters } from "vuex";
import TableShowConfigList from "@/view/report/components/TableShowConfigList";
import { allSettingList, defaultSettingList } from "./utils/roleUserMgtTableList";

export default {
  name: "roleUserMgt",
  components: {
    DictionarySelect,
    AlarmTabList,
    AlarmEventTabList,
    SelectTreeInput,
    FenceTreeFilter,
    AsideAlarm,
    TableShowConfigList,
  },
  data() {
    const defaultData = {
      id: null,
      company_id: null,
      dept_id: null,
      area_id: null,
      name: null,
      login_name: null,
      password: null,
      user_no: null,
      sex: null,
      phone: null,
      user_type: 1,
      data_scope: null,
      age: null,
      valid_date: null,
      menu_date_list: [],
      data_scope_dept: [],
      data_scope_vehi: [],
      data_industry_types: [],
      open_type: 0,
      userSelectData: 1,

      approval_role: [],
      jurisdiction: [],
      restricted_zone: [],

      group: null,
      areaGroup: { label: "", value: "" },
      duty: null,
      section_office: null,
      monitor_level: 3,
      user_type: 1,
      reserve1: "",
      main_page_type: 1,
      account_manager: false,
      account_manager_level: null,
    };
    const guideData = {
      main_page_type: null,
      idList: [],
      valid_date: null,
      user_alarm_config: {
        industry_type: 0,
        alarm_code_list: [],
      },
      user_alarm_event_config: {
        type: 1,
        values: [],
      },
      pass_area_list: null,
      user_session_time: null,
      company_id: "",
      dept_id: null,
      data_scope: 1,
      data_scope_dept: [],
      data_scope_vehi: [],
      // role_type:'',
    };

    let validPassword = (rule, value, callback) => {
      // let reg= /^(?![0-9]+$)(?![a-zA-Z]+$)(?![a-z\d]+$)(?![A-Z\d]+$)[0-9A-Za-z\\W]{8,20}$/
      let reg = /^(?:(?=.*[A-Z])(?=.*[a-z])(?=.*[0-9])).{8,20}$/;
      let reg1 = /^[0-9A-Za-z\\W]{8,20}$/;
      if (!reg.test(value)) {
        callback(new Error("密码必须包含大小写,数字,至少8位"));
      } else if (!reg1.test(value)) {
        callback(new Error("密码只能包含字母,数字特殊字符"));
      } else {
        callback();
      }
    };

    return {
      loading: false,
      options: [
        {
          value: 3,
          label: "低",
        },
        {
          value: 2,
          label: "中",
        },
        {
          value: 1,
          label: "高",
        },
      ],
      //数据权限树
      dataScopeZTree: {
        obj: null,
        id: "dataScope",
        setting: {
          check: {
            enable: true,
            chkStyle: "checkbox",
            halfCheck: false,
            chkboxType: { Y: "", N: "" },
          },
          data: { simpleData: { enable: true } },
          view: { showIcon: true, dblClickExpand: false },
        },
      },
      //功能数据权限树
      roleMenuZTree: {
        obj: null,
        id: "roleMenuTree",
        setting: {
          check: {
            enable: true,
            chkStyle: "checkbox",
            chkboxType: { Y: "ps", N: "ps" },
            halfCheck: false,
          },
          data: { simpleData: { enable: true } },
          view: { showIcon: true },
        },
      },
      showPwd: {
        pwd: null, //存一下当前详情的未解码的密码
        status: 0,
      },
      defaultCheckedList: [],
      userModal: {
        show: false,
        mode: "add",
        filterName: "",
        filterDept: "0",
        filterDeptName: "",
        selectorListName: [],
        // selectorListDept:[],
        loading: false,
        treeError: [0, 0],
        addModeTitle: "新增用户",
        addModeOkText: "确认新增",
        modifyModeTitle: "修改用户",
        modifyModeOkText: "确认修改",
        passwordChanged: false,
        userSelectDataList: [],
        formatList: [],

        data: JSON.parse(JSON.stringify(defaultData)),
        defaultData,
        rules: {
          login_name: [
            {
              required: true,
              message: "请输入用户名",
              trigger: "blur",
            },
          ],
          dept_id: [
            {
              required: true,
              message: "请选择部门",
              trigger: "change",
            },
          ],
          group: [
            {
              required: true,
              message: "请选择所属部门",
              trigger: "change",
            },
          ],
          data_scope: [
            {
              required: true,
              message: "请选择数据范围",
              trigger: "change",
            },
          ],
          password: [
            {
              required: true,
              message: "请输入密码",
              trigger: "blur",
            },
            {
              min: 6,
              max: 20,
              message: "长度在 6 到 20 个字符",
              trigger: "blur",
            },
            // { validator: validPassword, trigger: 'blur' }
          ],
        },
        pickerOptions: {
          disabledDate: function (date) {
            const now = Date.now() - 24 * 3600 * 1000;
            // return (moment().subtract(1,'day').toDate() - now) > 0
            return date.getTime() < now;
          },
        },
      },
      ipModal: {
        show: false,
        loading: false,
        data: {
          id: "",
          ip: "",
          city: "",
          IpBlackAddress: false,
        },
        table: {
          cityList: [],
          ipList: [],
        },
      },
      uploadModal: {
        show: false,
        loading: false,
        data: {
          id: "",
          defaultUrl: defaultLogo,
          url: "",
          blob: "",
        },
      },
      timeoutModal: {
        show: false,
        loading: false,
        data: {
          id: "",
          times: "",
          type: false,
        },
      },
      alarmConfigModal: {
        show: false,
        loading: false,
        data: {
          id: "",
          title: "",
          alarmList: [],
        },
      },
      interfaceModal: {
        show: false,
        loading: false,
        change: false,
        data: {
          id: "",
          title: "",
          interfaceList: [],
          interfaceIdList: [],
        },
      },
      alarmEventConfigModal: {
        show: false,
        loading: false,
        change: false,
        data: {
          id: "",
          configId: null,
          title: "",
          alarmEventList: [],
        },
      },
      passAreaListModal: {
        show: false,
        loading: false,
        data: {
          id: null,
          list: [],
        },
      },
      industryTypeList: [],
      deptTreeData: [],
      searchQuery: {
        dept_id: null,
        login_name: null,
        add_name: null,
        sorting: 1,
        sort_order: "deptName",
        open_type: -1,
        user_type: 1,
      },
      table: {
        data: [],
        total: 0,
        page: 1,
        size: 30,
        noDataText: "",
      },
      sels: [], //选中的值显示

      jurisdiction: {
        selectInstance: null,
      },
      jurisdictionTree: null,
      xianxingquTree: null,
      // 过期时间
      yearDueDate: 0,
      dueDateList: ["全部", "已到期", "3天内到期", "10天内到期", "30天内到期"],
      selection: [],
      guideData,
      guideModal: {
        show: false,
        alarmChange: false,
        alarmEventChange: false,
        alarmMark: false,
        alarmEventMark: false,
        pass_area: [],
        type: false,
        user_session_time: null,
        group: null,
        data: JSON.parse(JSON.stringify(guideData)),
        pickerOptions: {
          disabledDate: function (date) {
            const now = Date.now() - 24 * 3600 * 1000;
            // return (moment().subtract(1,'day').toDate() - now) > 0
            return date.getTime() < now;
          },
        },
      },
      alarmCount: {},
      mainPageTypeList: {
        1: "介绍型",
        2: "看板型",
        4: "高级调度",
        8: "实时监控",
        16: "看板型(简洁)",
      },
      guideMainPageList: [],
      mainPageChooseRange: {
        1: [1],
        2: [2],
        3: [1, 2],
        4: [4],
        5: [1, 4],
        6: [2, 4],
        7: [1, 2, 4],
        8: [8],
        9: [1, 8],
        10: [2, 8],
        11: [1, 2, 8],
        12: [4, 8],
        13: [1, 4, 8],
        14: [2, 4, 8],
        15: [1, 2, 4, 8],
        16: [16],
        17: [1, 16],
        18: [2, 16],
        19: [1, 2, 16],
        20: [4, 16],
        21: [1, 4, 16],
        22: [2, 4, 16],
        23: [1, 2, 4, 16],
        24: [8, 16],
        25: [1, 8, 16],
        26: [2, 8, 16],
        27: [1, 2, 8, 16],
        28: [4, 8, 16],
        29: [1, 4, 8, 16],
        30: [2, 4, 8, 16],
        31: [1, 2, 4, 8, 16],
      },
      filterList: [
        {
          id: "mainPage",
          value: "用户首页",
        },
        {
          id: "alarmType",
          value: "报警类型配置",
        },
        {
          id: "alarmEvent",
          value: "报警事件类型配置",
        },
        {
          id: "overTime",
          value: "超时配置",
        },
        {
          id: "area",
          value: "区域配置",
        },
        {
          id: "expiryDate",
          value: "到期日期配置",
        },
        {
          id: "userPermisson",
          value: "数据权限配置",
        },
      ],
      editTab: "mainPage",
      currentTab: "mainPage",
      exportLoading: false,
      defaultSettingList,
      tableSettingList: [],
      allSettingList,
      confirmLoading:false
    };
  },
  watch: {
    "userModal.data.group": function (val) {
      if (!val) return;
      this.userModal.data.dept_id = val.value || null;
      this.userModal.data.company_id = val.parentNode ? val.parentNode.value : null;
      this.deptChange();
    },
    "guideModal.group": function (val) {
      if (!val) return;
      this.guideModal.data.dept_id = val.value || null;
      this.guideModal.data.company_id = val.parentNode ? val.parentNode.value : null;
      this.deptModifyChange();
    },
    jurisdictionTree: function (newVal) {
      if (!newVal) return;
      let result = newVal.map((item) => item.value);
      Object.assign(this.userModal.data, {
        jurisdiction: result,
      });
    },
    xianxingquTree: function (newVal) {
      if (!newVal) return;
      let result = newVal.map((item) => item.value);
      Object.assign(this.userModal.data, {
        restricted_zone: result,
      });
    },
    "userModal.filterName": function (val) {
      // console.log('val',val);
      let row = this.userModal.userSelectDataList.find((item) => item.id == val);
      if (row) {
        this.$refs.roleTable.toggleRowSelection(row);
      }
    },
    "userModal.filterDept": function (val) {
      this.userModal.filterDeptName = "";
      if (val) {
        this.userModal.userSelectDataList = this.userModal.formatList.filter((item) => {
          return item.deptName == val;
        });
      }
    },
    "userModal.data.account_manager_level": function (val) {
      if (this.$store.state.auth.userInfo.account_manager_level > val) {
        this.$warning("请选择小于当前用户级别的数字");
        return;
      }
    },
  },
  computed: {
    ...mapState("auth", ["userInfo"]),
    pageStart() {
      return (this.table.page - 1) * this.table.size;
    },
    formatList() {
      return this.table.data.slice(this.pageStart, this.pageStart + this.table.size);
    },
    selectorListDept() {
      return this.unique(JSON.parse(JSON.stringify(this.userModal.formatList))).filter((item) => {
        return item.deptName.match(this.userModal.filterDeptName);
      });
    },
  },
  methods: {
    ztreeDblClickHandleEventMain(event, treeId, treeNode) {
      let allCheck = !treeNode.children.length || (treeNode.children.length && !treeNode.children.some((item) => !item.checked));
      let checked = treeNode.checked && allCheck;
      this.$refs["dataScopeZTree"].ztreeObj.checkNode(treeNode, !checked, !checked, false);
      if (!checked) this.$refs["dataScopeZTree"].ztreeObj.expandNode(treeNode, true);
      if (treeNode.children.length) {
        treeNode.children.forEach((item) => {
          this.$refs["dataScopeZTree"].ztreeObj.checkNode(item, !checked, !checked, false);
        });
      }
    },
    showTableSetting() {
      this.$refs.tableShowConfigList.showModel();
    },
    settable(value) {
      this.tableSettingList = value;
      // console.log(this.tableSettingList)
      this.$nextTick(() => {
        this.$refs.table.doLayout();
      });
    },
    getTableSetting(val) {
      this.$nextTick(() => {
        if (val) {
          this.tableSettingList = val;
        } else {
          this.tableSettingList = this.defaultSettingList;
        }
      });
    },
    async exportData() {
      if (!this.table.data.length) return this.$warning("无数据导出");
      this.exportLoading = true;
      let excelBody = [];
      let sheetHeader = this.tableSettingList.map((item) => {
        let str = `${item.name}@${item.key}@10000@00000`;
        return str;
      });
      excelBody = JSON.parse(JSON.stringify(this.table.data));
      excelBody.forEach((item) => {
        item.company_name = (item.company_name || "") + (item.dept_name || "");
        item.sex = item.sex == 1 ? "男" : item.sex == 2 ? "女" : "男";
        item.main_page_type = this.mainPageTypeList[item.main_page_type];
        item.user_type = item.user_type == 1 ? "系统用户" : item.user_type == 2 ? "司机用户" : item.user_type == 5 ? "钥匙柜管理员" :"车辆用户";
        item.data_scope = item.data_scope == 1 ? "全部车辆" : "部分车辆";
        item.name = item.name ? item.name : "无";
        item.age = item.age ? item.age : "无";
        item.phone = item.phone ? item.phone : "无";
        item.age = item.age ? item.age : "无";
        item.valid_date =
          item.valid_date && item.valid ? item.valid_date : item.valid_date && !item.valid ? `${item.valid_date}(已过期)` : "无";
      });
      let params = {
        sheetName: "用户管理报表",
        title: "用户管理报表",
        headers: sheetHeader,
        dataList: excelBody,
      };
      let paramsList = [];
      paramsList.push(params);
      await this.$utils.jsExcelExport(JSON.stringify(paramsList), "用户管理报表.xlsx");
      this.exportLoading = false;
    },
    // 报警选择节点获取
    handleAlarmChange(alarmList) {
      this.guideModal.alarmChange = true;
      this.guideModal.data.user_alarm_config.alarm_code_list = alarmList;
    },
    handleAlarmChangeModal(alarmList) {
      this.alarmConfigModal.change = true;
      this.alarmConfigModal.data.alarmList = alarmList;
    },

    areaCondition(treeNode) {
      return true;
    },
    handleClickIndustry(item) {
      this.editTab = item.id;
      this.currentTab = item.id;
       switch(item.id){
        case 'mainPage':
         let obj =  Object.assign({},this.guideData,{
          main_page_type: this.guideModal.data.main_page_type
         })
         this.guideModal.data = obj
          break;
        case 'alarmEvent':
          let alarmEventObj = Object.assign({}, this.guideData, {
            user_alarm_event_config: {
              type: this.guideModal.data.user_alarm_event_config.type,
              values: this.guideModal.data.user_alarm_event_config.values
            }
          })
          this.guideModal.data = alarmEventObj
          break;
        case 'alarmType':
          let alarmTypeObj = Object.assign({}, this.guideData, {
            user_alarm_config: {
              industry_type: this.guideModal.data.user_alarm_config.industry_type,
              alarm_code_list: this.guideModal.data.user_alarm_config.alarm_code_list
            }
          })
          this.guideModal.data = alarmTypeObj
          break;
        case 'overTime':
          let overTimeObj = Object.assign({}, this.guideData, {
            user_session_time: this.guideModal.data.user_session_time
          })
          this.guideModal.data = overTimeObj
          // Also preserve the modal-level timeout settings
          this.guideModal.user_session_time = this.guideModal.user_session_time
          this.guideModal.type = this.guideModal.type
          break;
        case 'area':
          let areaObj = Object.assign({}, this.guideData, {
            pass_area_list: this.guideModal.data.pass_area_list
          })
          this.guideModal.data = areaObj
          // Also preserve the modal-level area settings
          this.guideModal.pass_area = this.guideModal.pass_area
          break;
        case 'expiryDate':
          let expiryDateObj = Object.assign({}, this.guideData, {
            valid_date: this.guideModal.data.valid_date
          })
          this.guideModal.data = expiryDateObj
          break;
        case 'userPermisson':
          let userPermissonObj = Object.assign({}, this.guideData, {
            data_scope: this.guideModal.data.data_scope,
            data_scope_dept: this.guideModal.data.data_scope_dept,
            data_scope_vehi: this.guideModal.data.data_scope_vehi,
            company_id: this.guideModal.data.company_id,
            dept_id: this.guideModal.data.dept_id
          })
          this.guideModal.data = userPermissonObj
          // Also preserve the modal-level group settings
          this.guideModal.group = this.guideModal.group
          break;
       }
    },
    selectAll(list) {},
    showGuideModal() {
      if (!this.selection.length) {
        this.$warning("请选择批量配置用户！");
        return;
      }
      this.guideModal.show = true;
      this.editTab = this.currentTab = "mainPage";
      this.guideModal.group = null
      this.guideModal.data = JSON.parse(JSON.stringify(this.guideData));
    },
    removeAlarmSet() {
      this.guideModal.alarmMark = true;
      this.guideModal.data.user_alarm_config.alarm_code_list = [];
      this.$success("报警类型配置已清空");
    },
    removeAlarmEvent() {
      this.guideModal.alarmEventMark = true;
      this.guideModal.data.user_alarm_event_config.values = [];
      this.$success("报警事件类型配置已清空");
    },
    async confirmGuideChange() {
      this.guideModal.data.idList = this.selection.map((item) => {
        return item.id;
      });
      if (this.guideModal.data.data_scope === 3) {
        this.guideModal.data.data_scope_vehi =this.dataScopeZTree.obj
          .getNodesByFilter((item) => {
            return item.checked == true && item.type == 4;
          }).length? this.dataScopeZTree.obj
          .getNodesByFilter((item) => {
            return item.checked == true && item.type == 4;
          })
          .map((item) => item.id):[];
        // 构建 data_scope_dept
        this.guideModal.data.data_scope_dept = this.dataScopeZTree.obj
          .getNodesByFilter((item) => {
            return item.checked == true && item.type !== 4;
          }).length?this.dataScopeZTree.obj
          .getNodesByFilter((item) => {
            return item.checked == true && item.type !== 4;
          }).map((item) => item.id):[]
      }
      if (!this.guideModal.alarmMark && this.guideModal.data.user_alarm_config.alarm_code_list.length == 0) {
        this.guideModal.data.user_alarm_config.alarm_code_list = null;
      }
      if (!this.guideModal.alarmEventMark && this.guideModal.data.user_alarm_event_config.values.length == 0) {
        this.guideModal.data.user_alarm_event_config.values = null;
      }
      if (this.guideModal.type) {
        this.guideModal.data.user_session_time = -2000;
      } else {
        this.guideModal.data.user_session_time = this.guideModal.user_session_time;
      }
      this.guideModal.data.pass_area_list = this.guideModal.pass_area.map((item) => item.value);
      this.guideModal.data.valid_date = moment(this.guideModal.data.valid_date).valueOf();
      this.confirmLoading = true
      let res = await this.$api.updateUserMainPages(this.guideModal.data);
      if (res.status !== 200) {
        this.$error("批量设置失败");
        this.confirmLoading = false
        return;
      } else {
        this.$success("批量设置成功");
        this.confirmLoading = false
        this.getUserList();
        this.selection = [];
        this.$refs.table.clearSelection();
        this.guideModal.show = false;
        this.guideModal.alarmChange = false;
        this.guideModal.alarmEventChange = false;
        this.guideModal.alarmMark = false;
        (this.guideModal.alarmEventMark = false), (this.guideModal.type = false);
        this.guideModal.user_session_time = null;
        this.guideModal.data = this.guideData;
      }
    },
    handleSelectionChange(list) {
      this.selection = list;
    },
    getRowsKey(row) {
      return row.id;
    },
    sortChange({ column, prop, order }) {
      let list = JSON.parse(JSON.stringify(this.table.data));
      const arr = list.filter((item) => item.valid_date);
      const emptyData = list.filter((item) => !item.valid_date);
      // 字符排序
      arr.sort((a, b) => {
        switch (order || "default") {
          case "default":
            break;
          case "ascending":
            if (a[prop] > b[prop]) return 1;
            if (a[prop] == b[prop]) return 0;
            if (a[prop] < b[prop]) return -1;
            break;
          case "descending":
            if (a[prop] > b[prop]) return -1;
            if (a[prop] == b[prop]) return 0;
            if (a[prop] < b[prop]) return 1;
        }
      });
      this.table.data = arr.concat(emptyData);
    },
    //对象数组去重
    unique(arr) {
      const res = new Map();
      arr.forEach((item) => {
        // console.log(!res.has(item.deptId) && res.set(item.deptId, 1));
      });
      return arr;
    },
    selsChange(sels) {
      this.sels = sels;
    },
    filterClear(type) {
      if (type == "filterName") {
        this.userModal.selectorListName = [];
      } else {
        this.userModal.userSelectDataList = this.userModal.formatList;
      }
    },
    filterChangeName(value) {
      if (value) {
        this.userModal.selectorListName = this.userModal.userSelectDataList.filter((item) => {
          return item.name.match(value);
        });
      } else {
        this.userModal.selectorListName = [];
      }
    },
    filterChangeDept(value) {
      this.userModal.filterDeptName = value;
    },
    handleCurrentChange(row, event, column) {
      this.$refs.roleTable.toggleRowSelection(row);
    },
    /*  async selectUserStyle() {
             if (this.userModal.data.userSelectData === 1 && this.userModal.data.dept_id) {
                     this.selectRoleByDeptId(this.userModal.data.dept_id[this.userModal.data.dept_id.length - 1]);
             } else if (this.userModal.data.userSelectData === 0) {
                     this.setRoleMenuTree();
             }
     }, */
    async selectRoleByCurrentUserId() {
      var isModify = this.userModal.mode == "modify" || this.userModal.mode == "copy";

      this.userModal.loading = true;
      if (isModify) this.$refs.roleTable.clearSelection();
      try {
        let res = await this.$api.selectRoleByDeptIdFun({
          user_id: this.$store.state.auth.userInfo.id,
        });
        if (res && res.length) {
          this.userModal.userSelectDataList = JSON.parse(JSON.stringify(res));
          this.userModal.formatList = JSON.parse(JSON.stringify(res));
          if (this.userModal.data.role_list && this.userModal.data.role_list.length && isModify) {
            this.$nextTick(() => {
              this.userModal.data.role_list.forEach((uuid) => {
                let row = this.userModal.userSelectDataList.find((item) => {
                  return item.id === uuid;
                });
                if (row) {
                  this.$refs.roleTable.toggleRowSelection(row, true);
                }
              });
            });
          }
        } else {
          this.$message({
            showClose: true,
            message: "查询模板失败",
            type: "warning",
          });
        }
      } catch (e) {
        this.$message({
          showClose: true,
          message: "系统错误",
          type: "warning",
        });
      } finally {
        this.$nextTick(() => {
          this.$refs.roleTable.doLayout();
        });
        this.userModal.loading = false;
      }
    },
    selectRoleUserNode: _.debounce(function (data, node, $node) {
      this.searchQuery.dept_id = data.id;
      this.getUserList();
    }, 500),
    async initData() {
      //初始化行业列表
      let industryTypeList = await this.$api.getUserIndustryBind({
        user_id: "-1",
      });
      this.industryTypeList = industryTypeList.data;

      this.initInterfaceData({ is_all: true });
    },
    async initInterfaceData(query) {
      let res = await this.$api.getInterfaceInfo(query);
      let data = res.data;
      this.interfaceModal.data.interfaceList = [];
      if (data && data.length) {
        data.forEach((item) => {
          item.open = false;
        });
      }
      this.interfaceModal.data.interfaceList = data ? data : [];
    },
    async getUserList() {
      this.table.data = [];
      this.table.noDataText = " ";
      this.loading = true;
      let params = Object.assign({}, this.searchQuery, {
        open_type: this.searchQuery.open_type === -1 ? null : this.searchQuery.open_type,
        filter_annual: this.yearDueDate,
      });
      try {
        let res = await this.$api.getAllUserByVue(params);
        this.table.noDataText = "暂无数据";
        if (res.RS === 1) {
          this.table.data = res.sysUsersList;
          this.table.total = res.Count;
        } else {
          this.table.data = [];
          this.table.total = 0;
        }
        this.$nextTick(() => {
          this.$refs["table"].doLayout();
          this.$refs["table"].clearSort();
        });
      } catch (e) {
        console.error(e);
        this.table.noDataText = "接口出错";
      } finally {
        this.loading = false;
      }
    },
    cleanUserModalData() {
      this.userModal.data = Object.assign({}, this.userModal.defaultData);
      this.userModal.passwordChanged = false;
      // this.jurisdiction.selectInstance = null
      // this.userModal.data.jurisdiction = null
      // this.userModal.data.restricted_zone = null
    },
    showModal(mode, data) {
      this.userModal.filterDept = "";
      this.userModal.filterName = "";

      switch (mode) {
        case "add":
          this.userModal.mode = mode;
          this.showAddUserModal(data);
          break;
        case "modify":
          this.userModal.mode = mode;
          this.showModifyUserModal(data);
          break;
        case "copy":
          this.userModal.mode = mode;
          this.showModifyUserModal(data);
          break;
        case "delete":
          this.deleteUser(data);
          break;
        case "ip":
          this.showIpConfigModal(data);
          break;
        case "logo":
          this.showLogoModal(data);
          break;
        case "alarmset":
          this.showAlarmConfig(data);
          break;
        case "alarmEventConfig":
          this.showAlarmEventConfig(data);
          break;
        case "timeout":
          this.showTimeoutModal(data);
          break;
        case "interface":
          this.showInterfaceModal(data);
          break;
        case "passArea":
          this.showPassAreaModal(data);
          break;
      }
    },
    async toShowPassword() {
      if (!this.showPwd.pwd) return;
      if (this.showPwd.status) {
        this.userModal.data.password = "********";
        this.showPwd.status = 0;
      } else {
        let result = await this.$api.pwdTransfor({
          pwd: this.showPwd.pwd,
        });
        if (!result || result.status != 200) {
          this.$warning(result.message || "密码解码失败");
        }
        this.userModal.data.password = result.data;
        this.showPwd.status = 1;
      }
    },
    async showModifyUserModal(data) {
      this.showPwd.status = 0;
      this.showPwd.pwd = this.userModal.mode == "copy" ? "" : data.password;
      this.cleanUserModalData();
      let { id, dept_id, jurisdiction, area_id } = data;
      this.$utils.assign(this.userModal.data, data, {
        password: "********",
        data_industry_types: await this.getIndustryBind(id),
      });
      if (this.userModal.mode == "copy") {
        this.userModal.data.login_name = "";
        this.userModal.data.password = "";
      }
      this.userModal.show = true;
      //待模态框出现后在渲染树 并勾选默认
      this.$nextTick(async () => {
        try {
          this.userModal.loading = true;
          this.userModal.treeError = [0, 0];
          //获得用户权限七七八八等东西
          await this.getDataScope(id);
          if (this.userModal.data.data_scope === 3) {
            await this.setDataScopeTree(dept_id);
          }
          //this.userModal.data.userSelectData = this.userModal.data.user_role_bind_type;
          this.userModal.data.userSelectData = 1;
          this.selectRoleByCurrentUserId();

          this.userModal.data.group = await this.$refs["departmentInput"].fillOtherProperty(dept_id);
          this.userModal.data.areaGroup = await this.$refs["areaInput"].fillOtherProperty(area_id);

          let jurisdiction = [];
          let restricted_zone = [];
          for (var i = 0; i < data.jurisdiction.length; i++) {
            if (!data.jurisdiction.length) return;
            jurisdiction = data.jurisdiction.map((item) => {
              return { value: item, label: "" };
            });
          }
          for (var i = 0; i < data.restricted_zone.length; i++) {
            if (!data.restricted_zone.length) return;
            restricted_zone = data.restricted_zone.map((item) => {
              return { value: item, label: "" };
            });
          }

          // let squadron_list = null
          // if(jurisdiction.length) {
          //     squadron_list = jurisdiction.map(item => {
          //         return { value: item, label: '' }
          //     })
          // }
          if (data.main_page_type == 0) {
            this.userModal.data.main_page_type = 1;
          }
          this.jurisdictionTree = jurisdiction;
          this.xianxingquTree = restricted_zone;

          this.userModal.loading = false;
        } catch (e) {
          console.log(e);
          this.$message({
            showClose: true,
            message: "获取数据失败，请重试",
            type: "error",
          });
          this.userModal.show = false;
        } finally {
          this.loading = false;
        }
      });
    },
    async showAddUserModal() {
      this.cleanUserModalData();
      Object.assign(this.userModal.data, {
        data_scope: 1,
        data_industry_types: [this.industryTypeList[0].value],
      });
      this.selectRoleByCurrentUserId();
      this.userModal.show = true;
      this.$nextTick(async () => {
        this.loading = false;
      });
    },
    async showIpConfigModal(data) {
      this.ipModal.show = true;
      this.ipModal.data.id = data.id;
      await this.setIpAndCitySingleList();
    },
    async showLogoModal(data) {
      this.uploadModal.show = true;
      this.uploadModal.data.id = data.dept_id;
      this.uploadModal.data.url = data.dept_logo;
    },
    async showTimeoutModal(data) {
      this.timeoutModal.show = true;
      this.timeoutModal.data.id = data.id;
      this.selectSessionTime(data.id);
    },
    async showAlarmConfig(data) {
      this.alarmConfigModal.show = true;
      this.alarmConfigModal.data.id = data.id;
      this.alarmConfigModal.loading = true;
      this.alarmConfigModal.change = false;
      let res = await this.$api.getSysAlarmTypes({
        user_id: data.id,
        industry_type: 0,
      });
      this.alarmConfigModal.data.alarmList = res.alarm_type_list.map((item) => item.code) || [];
      this.defaultCheckedList = this.alarmConfigModal.data.alarmList;
      this.alarmConfigModal.loading = false;
    },
    async showInterfaceModal(data) {
      this.interfaceModal.show = true;
      this.interfaceModal.data.id = data.id;
      this.interfaceModal.data.title = data.login_name;
      this.interfaceModal.loading = true;
      let res = await this.$api.getUserEnabledInterface({
        user_id: data.id,
      });
      this.interfaceModal.data.interfaceIdList = res.data ? res.data : [];
      this.interfaceModal.data.interfaceList?.forEach((item) => {
        if ($.inArray(item.id, this.interfaceModal.data.interfaceIdList) != -1) {
          item.open = true;
        }
      });
      this.interfaceModal.loading = false;
    },
    async showPassAreaModal(data) {
      const modal = this.passAreaListModal;
      try {
        modal.show = true;
        modal.loading = true;
        modal.data.id = data.id;
        let res = await this.$api.getUserPassAreaList({
          user_id: data.id,
        });
        if (res.status === 200) {
          modal.data.list = res.data.map((item) => ({ value: item }));
        } else {
          throw new Error("查询用户区域数据失败");
        }
      } catch (e) {
        this.$error(e);
        modal.show = false;
      } finally {
        modal.loading = false;
      }
    },
    async confirm() {
      if (!(await this.$refs["userForm"].validate())) return;
      if (this.userModal.treeError[0] === 2 || this.userModal.treeError[1] === 2) {
        this.$message.error("请确保表单上没有错误");
        return;
      }
      if (!this.userModal.data.account_manager_level && this.userModal.data.account_manager) {
        this.$warning("请选择用户级别");
        return;
      }
      if (this.userModal.treeError[0] === 1 || this.userModal.treeError[1] === 1) {
        this.$message.error("请等待列表加载完成");
        return;
      }
      //这个判断(因为父子节点关联取消,暂时去掉这个限制)
      // if (this.$store.state.auth.userInfo.data_scope != 3 && /*判断当前操作账号是否有全部车辆权限, 比如admin有全部车辆权限就要弹这个提示, test0815没有全部车辆权限不弹这个提示, 创建同级账号支持时的修改*/
      //     this.userModal.data.data_scope === 3 && this.dataScopeZTree.obj.getNodes()[0].check_Child_State === 2) {
      //     this.$message.warning('若用户拥有全部数据权限，数据范围应选择“全部车辆”');
      //     return;
      // }
      if (this.sels.length === 0) {
        this.$message.warning("请选择至少一个角色模板");
        return;
      }
      let params = {};
      let deptList = [];
      let vehicleList = [];
      let ids = this.sels.map((item) => item.id).join(); //获取所有选中行的id组成的字符串，以逗号分隔
      if (this.userModal.data.data_scope === 3) {
        //构建 data_scope_vehi
        // this.dataScopeZTree.obj.getNodesByFilter(item => {
        //     return item.checked === true && item.check_Child_State === 1 && item.type === 3
        // }).forEach(item => {
        //     vehicleList.push(...(this.dataScopeZTree.obj.getNodesByFilter((child) => {
        //         return child.checked === true;
        //     }, false, item).map(item => item.id)));
        // })
        vehicleList = this.dataScopeZTree.obj
          .getNodesByFilter((item) => {
            return item.checked == true && item.type == 4;
          })
          .map((item) => item.id);
        //构建 data_scope_dept
        deptList = this.dataScopeZTree.obj
          .getNodesByFilter((item) => {
            return item.checked == true && item.type !== 4;
          })
          .map((item) => item.id);
      }
      Object.assign(params, this.userModal.data, {
        dept_id: this.userModal.data.dept_id,
        data_scope_dept: deptList,
        data_scope_vehi: vehicleList,
        menu_date_list: this.roleMenuZTree.obj
          ? this.roleMenuZTree.obj.getNodesByFilter((item) => item.checked === true).map((item) => item.id)
          : null,
        password: this.userModal.passwordChanged ? this.userModal.data.password : null,
        type: this.userModal.data.userSelectData,
        role_type: ids.split(","),
        area_id: this.userModal.data.areaGroup?.value,
      });
      if (this.userModal.mode === "add" || this.userModal.mode === "copy") {
        await this.addUser(params, this.userModal.mode);
      } else {
        await this.updateUser(params);
      }
      this.userModal.show = false;
    },
    async updateUser(params) {
      this.userModal.loading = true;
      try {
        let res = await this.$api.updateUser(params);
        if (res.RS === 1) {
          this.$message({
            showClose: true,
            message: "修改成功",
            type: "success",
          });
          this.getUserList();
        } else {
          this.$message({
            showClose: true,
            message: "修改失败" + res.Reason,
            type: "warning",
          });
        }
      } catch (e) {
        this.$message({
          showClose: true,
          message: "系统错误",
          type: "warning",
        });
      } finally {
        this.userModal.loading = false;
      }
    },
    async addUser(params, type) {
      this.userModal.loading = true;
      try {
        let res = await this.$api.insertUser(params);
        if (res.RS === 1) {
          if (type == "copy" && res.data) {
            await this.copyAlarmConfig(params.id, res.data);
            await this.copyAlarmEventConfig(params.id, res.data);
            await this.copyPassareaConfig(params.id, res.data);
            await this.copyTimeoutConfig(params.id, res.data);
          }
          this.$message({
            showClose: true,
            message: "添加成功",
            type: "success",
          });
          this.getUserList();
        } else {
          this.$message({
            showClose: true,
            message: "添加失败" + res.Reason,
            type: "warning",
          });
        }
      } catch (e) {
        this.$message({
          showClose: true,
          message: "系统错误",
          type: "warning",
        });
      } finally {
        this.userModal.loading = false;
      }
    },
    async deleteUser(params) {
      await this.$confirm("此操作将永久删除此条信息, 是否继续?", "确认删除", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "error",
      });
      this.loading = true;
      try {
        let res = await this.$api.removeSysUserInfo({ id: params.id });
        if (res.RS === 1) {
          this.$message({
            showClose: true,
            message: "删除成功！",
            type: "success",
          });
          this.loading = false;
          this.getUserList();
        } else {
          this.loading = false;
          this.$message({
            showClose: true,
            message: res.Reason,
            type: "error",
          });
        }
      } catch (e) {
        this.loading = false;
        this.$message({
          showClose: true,
          message: "系统错误",
          type: "error",
        });
      }
    },
    async addIpByUserId() {
      let { id, ip, IpBlackAddress } = this.ipModal.data;
      let reg =
        /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/;
      if (!reg.test(ip)) {
        this.$warning("Ip输入不合法");
        return false;
      }
      try {
        this.ipModal.loading = true;
        let res = await this.$api.addIpByUserIdFun({
          id,
          ip,
          type: IpBlackAddress ? 1 : 0,
        });
        if (res.RS === 1) {
          this.$success(res.Reason);
          this.setIpAndCitySingleList();
          this.ipModal.data.ip = "";
        } else {
          this.$success(res.Reason);
        }
      } catch (e) {
        this.$error("系统错误");
      } finally {
        this.ipModal.loading = false;
      }
    },
    async addCityByUserId() {
      let { id, city } = this.ipModal.data;
      try {
        this.ipModal.loading = true;
        let res = await this.$api.addCityByUserIdFun({ id, city });
        if (res.RS === 1) {
          this.$success(res.Reason);
          await this.setIpAndCitySingleList();
          this.ipModal.data.city = "";
        } else {
          this.$warning(res.Reason);
        }
      } catch (e) {
        this.$error("系统错误");
      } finally {
        this.ipModal.loading = false;
      }
    },
    async setIpAndCitySingleList() {
      this.ipModal.loading = true;
      try {
        let res = await this.$api.selectIpAndCityList({
          id: this.ipModal.data.id,
        });
        if (res.RS === 1) {
          this.ipModal.table.ipList = res.ip;
          this.ipModal.table.cityList = res.city;
        } else {
          this.ipModal.table.ipList = [];
          this.ipModal.table.cityList = [];
        }
      } catch (e) {
        this.$error("系统错误");
      } finally {
        this.ipModal.loading = false;
      }
    },
    async deleteIpByUser(params) {
      await this.$confirm("确定删除此条信息?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "error",
      });
      this.loading = true;
      try {
        let res = await this.$api.removeIpByUserId({
          id: this.ipModal.data.id,
          ip: params.ip,
          type: params.type,
        });
        if (res.RS === 1) {
          this.$success(res.Reason);
          this.setIpAndCitySingleList();
        } else {
          this.$error(res.Reason);
        }
      } catch (e) {
        this.$error("系统错误");
      } finally {
        this.loading = false;
      }
    },
    async deleteCityByUser(params) {
      await this.$confirm("确定删除此条信息?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "error",
      });
      this.loading = true;
      try {
        let res = await this.$api.removeCityByUserId({
          id: this.ipModal.data.id,
          city: params,
        });
        if (res.RS === 1) {
          this.$success(res.Reason);
          this.setIpAndCitySingleList();
        } else {
          this.$error(res.Reason);
        }
      } catch (e) {
        this.$error("系统错误");
      } finally {
        this.loading = false;
      }
    },
    deptChange() {
      if (!this.userModal.data.dept_id) return;
      if (this.userModal.data.data_scope === 3) {
        this.setDataScopeTree(this.userModal.data.dept_id);
      }
    },
    deptModifyChange() {
      if (!this.guideModal.data.dept_id) return;
      if (this.guideModal.data.data_scope === 3) {
        this.setDataScopeTree(this.guideModal.data.dept_id);
      }
    },
    scopeModifyChange(value) {
      if (value === 3) {
        if (!this.guideModal.data.dept_id) return this.$warning("请先选择归属部门");
        this.setDataScopeTree(this.guideModal.data.dept_id);
      }
    },
    scopeChange(value) {
      if (value === 3) {
        this.setDataScopeTree(this.userModal.data.dept_id);
      }
    },
    async selectSessionTime(id) {
      this.timeoutModal.loading = true;
      let res = await this.$api.selsectSessionTimeFun({ id });
      if (res.RS === 1) {
        if (parseInt(res.time) == -2000) {
          this.timeoutModal.data.type = true;
          this.timeoutModal.data.times = -2000;
        } else {
          this.timeoutModal.data.type = false;
          this.timeoutModal.data.times = res.time;
        }
      } else {
        this.timeoutModal.data.type = false;
        this.timeoutModal.data.times = 30;
      }
      this.timeoutModal.loading = false;
    },
    async setDataScopeTree(deptId) {
      if (deptId === undefined) {
        deptId = this.guideModal.show ? this.guideModal.data.dept_id : this.userModal.data.dept_id;
      }
      try {
        this.$set(this.userModal.treeError, 1, 1);
        let $ztree = this.$refs["dataScopeZTree"];
        await $ztree.waitForInit;
        //数据权限部分
        let res = await this.$api.getTreeDataScope({
          userdeptid: deptId,
        });
        let dataScopeObj = await $ztree.init(res);
        this.dataScopeZTree.obj = dataScopeObj;
        //默认选中部门
        let depts = this.userModal.data.data_scope_dept;
        depts.forEach((item) => {
          let node = dataScopeObj.getNodeByParam("id", item);
          node && dataScopeObj.checkNode(node, true, true, false);
          if (node && node.getParentNode() && !node.getParentNode().open) {
            this.dataScopeZTree.obj.expandNode(node.getParentNode());
          }
        });
        //默认选中车辆
        let vehicles = this.userModal.data.data_scope_vehi;
        vehicles.forEach((item) => {
          let node = dataScopeObj.getNodeByParam("id", item);
          node && dataScopeObj.checkNode(node, true, true, false);
          if (node && node.getParentNode() && !node.getParentNode().open) {
            this.dataScopeZTree.obj.expandNode(node.getParentNode());
          }
        });
        this.$set(this.userModal.treeError, 1, 0);
      } catch (e) {
        console.error(e);
        this.$set(this.userModal.treeError, 1, 2);
      }
    },
    async getIndustryBind(userId) {
      let res = await this.$api.getUserIndustryBind({ user_id: userId });
      if (res.data != null) {
        return res.data.map((item) => item.value);
      } else {
        this.$message({
          showClose: true,
          message: "该用户未配置行业关联",
          type: "warning",
        });
        return [];
      }
    },
    async getDataScope(userId) {
      let res = await this.$api.getDataScopeAndMenuById({
        userid: userId,
      });
      Object.assign(this.userModal.data, res);
    },
    uploadSuccess(res, file) {
      this.uploadModal.loading = false;
      if (res.rs == 1) {
        let url = URL.createObjectURL(file.raw);
        this.uploadModal.data.blob = url;
        this.uploadModal.data.url = res.url;
      } else {
        this.$error("上传失败");
      }
    },
    uploadError() {
      this.$error("上传失败");
      this.uploadModal.loading = false;
    },
    beforeUpload(file) {
      this.uploadModal.loading = true;
    },
    cancelUpload() {
      this.uploadModal.data.blob = "";
      this.uploadModal.data.url = "";
      this.uploadModal.show = false;
    },
    async updateCompanyLogo() {
      let { id, url } = this.uploadModal.data;
      try {
        this.uploadModal.loading = true;
        let res = await this.$api.updateCompanyLogoFun({
          id: id,
          logo: url,
        });
        if (res.RS == 1) {
          this.$message({
            showClose: true,
            message: res.Reason,
            type: "success",
          });
          this.getUserList();
          this.uploadModal.data.blob = "";
          this.uploadModal.data.url = "";
          this.uploadModal.show = false;
        } else {
          this.$message({
            showClose: true,
            message: res.Reason,
            type: "warning",
          });
        }
      } catch (e) {
        this.$message({
          showClose: true,
          message: "设置logo失败",
          type: "error",
        });
      } finally {
        this.uploadModal.loading = false;
      }
    },
    async addOrUpSessionTime() {
      let { id, times, type } = this.timeoutModal.data;
      let params;
      if (type) {
        params = { id, time: -2000 };
      } else {
        params = { id, time: times };
      }
      this.timeoutModal.loading = true;
      try {
        let res = await this.$api.addOrUpSessionTimeFun(params);
        if (res.RS === 1) {
          this.$success(res.Reason);
        } else {
          this.$warning(res.Reason);
        }
        this.timeoutModal.show = false;
      } catch (e) {
        this.$error("系统错误");
      } finally {
        this.timeoutModal.loading = false;
      }
    },
    async deleteSessionTime() {
      this.timeoutModal.loading = true;
      try {
        let res = await this.$api.deleteSessionTimeFun({
          id: this.timeoutModal.data.id,
        });
        if (res.RS === 1) {
          this.$success(res.Reason);
          this.timeoutModal.show = false;
        } else {
          this.$warning(res.Reason);
        }
      } catch (e) {
        this.$error("系统错误");
      } finally {
        this.timeoutModal.loading = false;
      }
    },
    exitInterfaceOperate() {
      this.interfaceModal.show = false;
      this.interfaceModal.data.id = "";
      this.interfaceModal.data.interfaceIdList = [];
      this.interfaceModal.data.interfaceList.forEach((item) => {
        item.open = false;
      });
    },
    handleInterfaceOpenChange(data) {
      if (this.interfaceModal.data.id == null || this.interfaceModal.data.id == "") {
        this.$message({
          type: "warning",
          showClose: true,
          message: "无操作用户",
        });
      } else {
        let operateData = {
          user_id: this.interfaceModal.data.id,
          interface_id: data.id,
        };
        switch (data.open) {
          case true:
            this.$api
              .enableInterfaceSingle(operateData)
              .then((res) => {
                if (res.status == 200) {
                  this.$message({
                    type: "success",
                    showClose: true,
                    message: res.data,
                  });
                } else {
                  this.$message({
                    type: "warning",
                    showClose: true,
                    message: res.data,
                  });
                }
              })
              .catch((res) => {
                console.log("-----------调用接口,程序出错,返回结果如下：------------");
                console.log(res);
              });
            break;
          case false:
            disableInterfaceSingle(operateData)
              .then((res) => {
                if (res.status == 200) {
                  this.$message({
                    type: "error",
                    showClose: true,
                    message: res.data,
                  });
                } else {
                  this.$message({
                    type: "warning",
                    showClose: true,
                    message: res.data,
                  });
                }
              })
              .catch((res) => {
                console.log("-----------调用接口,程序出错,返回结果如下：------------");
                console.log(res);
              });
            break;
        }
      }
    },
    async removeUserAlarmSet() {
      let res = await this.$api.bindUserAlarm({
        industry_type: 0,
        user_id: this.alarmConfigModal.data.id,
        alarm_code_list: [],
      });
      if (res.status == 200) {
        this.$success("用户-报警类型清空成功");
        // this.showAlarmConfig({id:this.alarmConfigModal.data.id});

        this.alarmConfigModal.show = false;
        this.alarmConfigModal.data.id = "";
        this.alarmConfigModal.data.alarmList = [];
      } else {
        this.$warning(res.message + "，清空失败");
      }
    },
    async showAlarmEventConfig(data) {
      this.alarmEventConfigModal.show = true;
      this.alarmEventConfigModal.data.id = data.id;
      this.alarmEventConfigModal.loading = true;
      this.alarmEventConfigModal.data.configId = null;
      let res = await this.$api.queryAlarmEventRuleConfiguration({
        userId: data.id,
      });
      if (res.status === 200) {
        this.alarmEventConfigModal.data.configId = res.data.id;
        this.alarmEventConfigModal.data.alarmEventList = res.data.values;
        this.alarmConfigModal.change = false;
      } else {
        this.$message({
          type: "error",
          message: "查询报警事件绑定失败",
          showClose: false,
        });
      }
      this.alarmEventConfigModal.loading = false;
    },
    async removeUserAlarmEvent() {
      let id = this.alarmEventConfigModal.data.id;
      let res = await this.$api.queryAlarmEventRuleConfiguration({
        userId: id,
      });
      if (res.status === 200 && res.data.id) {
        let result = await this.$api.modifyAndDeleteAlarmEventRuleConfiguration({
          id: res.data.id,
          type: -1,
          userId: id,
        });
        if (result.status == 200) {
          this.$success("用户-报警事件类型绑定清空成功");
          // this.showAlarmEventConfig({id,})
          this.alarmEventConfigModal.show = false;
          this.alarmEventConfigModal.data.id = "";
          this.alarmEventConfigModal.data.configId = null;
          this.alarmEventConfigModal.data.alarmEventList = [];
        } else {
          this.$warning(res.message + "，清空失败");
        }
      } else {
        this.$success("用户-报警事件类型绑定清空成功");
        // this.showAlarmEventConfig({id,})
        this.alarmEventConfigModal.show = false;
        this.alarmEventConfigModal.data.id = "";
        this.alarmEventConfigModal.data.configId = null;
        this.alarmEventConfigModal.data.alarmEventList = [];
      }
    },
    async handleCloseAlarmConfigDialog() {
      this.alarmConfigModal.loading = true;
      let res = await this.$api.bindUserAlarm({
        industry_type: 0,
        user_id: this.alarmConfigModal.data.id,
        alarm_code_list: this.alarmConfigModal.data.alarmList,
      });
      this.alarmConfigModal.loading = false;
      if (res.status == 200) {
        this.$success("用户-报警类型绑定成功");
      } else {
        this.$warning(res.message + "，绑定失败");
      }
      this.alarmConfigModal.show = false;
      this.alarmConfigModal.data.id = "";
      this.alarmConfigModal.data.alarmList = [];
    },
    async copyPassareaConfig(id, newId) {
      let list = [];
      let res = await this.$api.getUserPassAreaList({
        user_id: id,
      });
      if (res.status === 200 && res.data.length) {
        list = res.data;
      }
      if (!list.length) return;
      await this.$api.setUserPassAreaList({
        user_id: newId,
        pass_area_list: list,
      });
    },
    async copyTimeoutConfig(id, newId) {
      let times;
      let res = await this.$api.selsectSessionTimeFun({ id });
      if (res.RS === 1) {
        if (parseInt(res.time) == -2000) {
          times = -2000;
        } else {
          times = res.time;
        }
      } else {
        times = 30;
      }
      await this.$api.addOrUpSessionTimeFun({
        id: newId,
        time: times,
      });
    },
    async copyAlarmConfig(id, newId) {
      let res = await this.$api.getSysAlarmTypes({
        user_id: id,
        industry_type: 0,
      });
      let alarmList = res.alarm_type_list.map((item) => item.code) || [];
      if (!alarmList.length) return;
      await this.$api.bindUserAlarm({
        industry_type: 0,
        user_id: newId,
        alarm_code_list: alarmList,
      });
    },
    async copyAlarmEventConfig(id, newId) {
      let alarmEventList = [];
      let res = await this.$api.queryAlarmEventRuleConfiguration({
        userId: id,
      });
      if (res.status === 200) {
        alarmEventList = res.data.values;
      }
      if (!alarmEventList.length) return;
      await this.$api.alarmEventRuleConfiguration({
        definedUserID: newId,
        values: alarmEventList,
        remark: "",
      });
    },
    async handleCloseAlarmEventConfigDialog() {
      this.alarmEventConfigModal.loading = true;
      let res;
      if (this.alarmEventConfigModal.data.configId) {
        //modify
        res = await this.$api.modifyAndDeleteAlarmEventRuleConfiguration({
          id: this.alarmEventConfigModal.data.configId,
          values: this.alarmEventConfigModal.data.alarmEventList,
          type: 1,
          userId: this.alarmEventConfigModal.data.id,
        });
      } else {
        //add
        res = await this.$api.alarmEventRuleConfiguration({
          definedUserID: this.alarmEventConfigModal.data.id,
          values: this.alarmEventConfigModal.data.alarmEventList,
          remark: "",
        });
      }
      this.alarmEventConfigModal.loading = false;
      if (res.status == 200) {
        this.$success("用户-报警事件类型绑定成功");
      } else {
        this.$warning(res.message + "，绑定失败");
      }
      this.alarmEventConfigModal.show = false;
      this.alarmEventConfigModal.data.id = "";
      this.alarmEventConfigModal.data.configId = null;
      this.alarmEventConfigModal.data.alarmEventList = [];
    },
    async handleCommitPassArea() {
      this.passAreaListModal.loading = true;
      try {
        let res = await this.$api.setUserPassAreaList({
          user_id: this.passAreaListModal.data.id,
          pass_area_list: this.passAreaListModal.data.list.map((item) => item.value),
        });
        if (res.status === 200) {
          this.$success("设置成功");
          this.passAreaListModal.show = false;
        } else {
          throw new Error("设置失败");
        }
      } catch (e) {
        this.$error(e);
      } finally {
        this.passAreaListModal.loading = false;
      }
    },
  },
  mounted() {
    this.initData();
    this.getUserList();
    this.guideMainPageList = this.userInfo.mainPageTypeEditRange
      ? this.mainPageChooseRange[this.userInfo.mainPageTypeEditRange]
      : [1, 2, 4, 8];
  },
};
</script>

<style scoped lang="scss">
.see-pwd {
  cursor: pointer;
}

.selectInput {
  /deep/ .select-tree-input {
    width: 202px !important;
  }
}

.dbgroup {
  display: flex;
  align-items: center;

  .el-input {
    width: calc(100% - 100px);
    flex-grow: 1;
    margin: 0 10px;
  }

  .input-group-addon {
    cursor: pointer;
  }
}

.role-user-mgnt {
  /deep/ label {
    font-weight: normal;
  }

  .valid {
    display: inline-block;
    // width: 65px;
    padding: 0 10px;
    height: 23px;
    border-width: 1px;
    border-style: solid;
    border-radius: 4px;
    color: #ff5359;
    background: rgba(255, 83, 89, 0.1);
    border-color: rgba(255, 83, 89, 0.2);
  }

  .ip-config-box {
    padding: 0 8px;
    height: 250px;

    > p {
      line-height: 30px;
      height: 30px;
      margin: 0;
    }

    .config-table {
      height: calc(100% - 80px);
      border: 1px solid #333;
    }

    .cityTip {
      color: #fd5b2e;
      font-size: 12px;
    }
  }

  .roleAreaWrap {
    width: 100%;
    height: 100%;
    display: flex;
    padding: 0 0 0 16px;
    justify-content: space-around;
    align-items: stretch;
    max-height: 730px;

    .filterTop {
      width: 100%;
      height: 11%;
      margin-bottom: 5px;

      .nameFilter {
        display: flex;
        margin-bottom: 5px;
      }

      .deptFilter {
        display: flex;
        margin-bottom: 5px;
      }

      span {
        display: inline-block;
        width: 80px;
      }

      .el-select {
        flex: 1;
      }
    }

    .roleArea {
      height: 100%;
      min-width: 250px;
      margin: 0 5px;
      width: 45%;
      border-radius: 4px;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      border: 1px solid var(--border-color-base);
      background: var(--background-color-light);

      &.has-error {
        border-color: #f56c6c;
      }

      > div:first-child {
        width: 100%;
        text-align: center;
        height: 30px;
        line-height: 30px;
        border-bottom: 1px solid var(--border-color-base);
        background: var(--background-color-lighter);

        i {
          line-height: 30px;
          height: 30px;
          width: 30px;
          display: inline-block;
          text-align: center;
        }
      }

      > div:last-child {
        height: calc(100% - 30px); // overflow-y: auto;
      }
    }
  }

  .tabsContent {
    display: flex;
    height: 450px;
    width: 100%;
    flex-direction: row;
    align-items: center;

    .aside {
      height: 100%;
      width: 16%;

      .input {
        padding: 0 6%;
        margin-top: 10px;
        margin-bottom: 10px;
      }

      background-color: var(--background-color-light);

      .industry-item {
        color: var(--color-text-regular);
        padding: 12px;
        cursor: pointer;
        transition: all 0.3s;

        &:hover {
          background-color: var(--color-primary-o-10);
        }

        &.active {
          // color: var(--color-bottom-operate);
          // background-color: var(--bacground-color-active);
          background: rgba(42, 128, 224, 0.2);
        }
      }
    }

    .tabs {
      margin-left: 1%;
      height: 100%;
      width: 80%;

      .tabItem {
        height: 97%;
        width: 100%;

        .title {
          i {
            display: inline-block;
            width: 2px;
            height: 20px;
            margin-right: 8px;
            vertical-align: -5px;
            border-radius: 3px;
          }
        }
        .form_tree {
          display: flex;
          .form {
            padding: 10px;
            width: 50%;
            height: 100%;
          }
          .tree {
            padding: 10px;
            width: 50%;
            height: 100%;
            .treeWrap{
              .colorBlack {
                width: 100%;
                text-align: center;
                height: 30px;
                line-height: 30px;
                border-bottom: 1px solid var(--border-color-base);
                background: var(--background-color-lighter);

                i {
                  line-height: 30px;
                  height: 30px;
                  width: 30px;
                  display: inline-block;
                  text-align: center;
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>

<template>
    <el-card :body-style="{ padding: '0px',height:'100%',display:'flex',flexDirection:'column' }" class="tree-manager" shadow="hover">
        <div class="query-wrap">
            {{title}}
            <div class="multiple" style="position:absolute;left:225px;">
              <el-button @click="multipleExport" type="primary" plain v-if="hasPermission('menu:multipleExport')">批量导出</el-button>
              <el-button @click="multipleDelete" type="primary" plain v-if="hasPermission('menu:multipleExport')">批量删除</el-button>
              <!-- <el-button @click="getTreeDataRefresh" type="primary" plain v-if="hasPermission('menu:multipleExport')">刷新</el-button> -->

            </div>
            <el-button class="pony-iconv2 pony-shuaxin" title="更新Header菜单" @click="refreshHeaderMenu"></el-button>
        </div>
        
        <div class="content-wrap " v-loading="this.form.state === 'saving'" ref="content">
           
            <div class="tree">
                <div class="filter-value">
                    <div class="icon" style="left:21%">
                            <i class="pony-iconv2 pony-sousuo icon"></i>
                    </div>
                    <el-select v-model="searchKey" remote class="select select-key" style="width:20%;float:left"
                       placeholder="请输入菜单"
                    >
                        <el-option label="名称" value="name"></el-option>
                        <el-option label="权限" value="permission"></el-option>

                    </el-select>
                    <el-select v-model="searchValue" remote class="select select-value" style="width:80%"
                       :remote-method="filterChange" filterable clearable
                       @clear="filterClear"
                       placeholder="请输入菜单"
                    >
                        <el-option v-for="(item,index) in selectorList" :key="item.id"
                                  :label="item[searchKey]" :value="item.id"></el-option>
                    </el-select>

                </div>
                <el-tree
                        v-loading="menuTableTree.loading "
                        :data="menuTableTree.data"
                        node-key="id" highlight-current
                        ref="tree" :expand-on-click-node="false"
                        show-checkbox
                        @check="handleCheckChange"
                        accordion @node-click="nodeClick">
                    <div class="custom-tree-node" slot-scope="{ node, data }">
                        <div>
                            <el-tag size="mini" :type="tree.levelTypeMap[node.level - 1]">
                                {{data.menu_type ===1?'按钮':'导航'}}
                            </el-tag>
                            <span :class="{'new-node':!node.data.id}">{{ getName(node.data.name)}}</span>
                        </div>
                        <div>
                          <el-button
                                    v-if="data.menu_type !== 1 && hasPermission('menu:multipleExport')"
                                    type="text"
                                    size="mini"
                                    class="pony-iconv2 pony-daoru"
                                    @click="multipleImport(node.data)"
                            >
                            </el-button>
                            <el-button
                                    v-if="data.menu_type !== 1"
                                    type="text"
                                    size="mini"
                                    class="pony-iconv2 pony-jia"
                                    @click="appendNode(node)"
                            >
                            </el-button>
                            <el-button
                                    type="text"
                                    size="mini"
                                    class="pony-iconv2 pony-jian"
                                    @click="deleteNode(node)"
                            >
                            </el-button>
                        </div>
                    </div>
                </el-tree>
                <div style="text-align: right;padding-right: 10px;">
                  <el-button
                          v-if="hasPermission('menu:multipleExport')"
                          type="text"
                          size="mini"
                          class="pony-iconv2 pony-daoru"
                          @click="multipleImport(null)"
                  >
                  </el-button>
                    <el-button
                            type="text"
                            size="mini"
                            class="pony-iconv2 pony-jia"
                            @click="appendNode(null)"
                    ></el-button>
                </div>
            </div>
            <div class="tree-node-info">
                <template v-if="currentNode">
                    <el-form label-width="80px" :model="form.data" :rules="form.rules" size="mini" ref="form"
                             style="max-width: 600px;"
                             :disabled="form.mode === 'saving'"
                    >
                        <el-form-item label="菜单名称" prop="name">
                            <el-input v-for="lang in langList" :key="lang"
                                      v-model="form.data.name[lang]">
                                <template slot="prepend">
                                    <span style="display: inline-block;width: 55px;text-align: center">{{lang}}</span>
                                </template>
                            </el-input>
                        </el-form-item>
                        <el-form-item label="菜单链接">
                            <el-input v-model="form.data.href"></el-input>
                        </el-form-item>
                        <el-form-item label="菜单级别" :required="true">
                            <el-select v-model="form.data.menu_level">
                                <el-option :label="item.label" :value="item.value" :key="item.value" v-for="item in menuLevel"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="菜单图标">
                            <el-input v-model="form.data.icon"></el-input>
                        </el-form-item>
                        <el-form-item label="菜单类型" :required="true">
                            <el-select v-model="form.data.menu_type">
                                <el-option :label="item.label" :value="item.value" :key="item.value" v-for="item in menuType"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="菜单排序" prop="sort">
                            <el-input v-model="form.data.sort"></el-input>
                        </el-form-item>
                        <el-form-item label="是否可见">
                            <el-checkbox v-model="form.data.is_show">可见</el-checkbox>
                        </el-form-item>
                        <el-form-item label="权限标识" prop="permission">
                            <el-input v-model="form.data.permission"></el-input>
                        </el-form-item>
                        <el-form-item label="菜单备注">
                            <el-input type="textarea" v-model="form.data.remark"></el-input>
                        </el-form-item>
                    </el-form>
                    <div style="max-width: 600px;text-align: right">
                        <el-button size="mini" :type="form.state == 'needSave'?'primary':''" :plain="form.state !== 'needSave'" :disabled="form.state !== 'needSave'"
                                   :loading="form.state === 'saving'" @click="save"
                        >保存
                        </el-button>
                        <el-button size="mini" plain :disabled="form.state !== 'needSave'" @click="resetData">取消</el-button>

                        <el-button size="mini" class="fl" type="primary" :plain="!routerHelp" @click="routerHelp = !routerHelp">菜单提示</el-button>
                        <el-button size="mini" class="fl" type="primary" :plain="!iconHelp" @click="iconHelp = !iconHelp">图标库</el-button>
                        <el-button size="mini" class="fl" type="primary" plain @click="showBindModel(form.data.id)">查看绑定模版</el-button>

                    </div>
                </template>
                <span v-else>请选择一个树节点</span>
            </div>
        </div>

        <RouterHint v-model="routerHelp" @node-change="onMenuNodeChange"></RouterHint>
        <IconHint v-model="iconHelp" @icon-change="onIconChange" :high-light="form.data.icon"></IconHint>
        <MultipleImport ref="multipleImport" @refresh="refreshData"></MultipleImport>
        <BindModel ref="bindModel"></BindModel>
    </el-card>
</template>

<script>
    /**
     * @Author: yezy
     * @Email: <EMAIL>
     * @Date: 2019-04-24 10:45:27
     * @LastEditors: yezy
     * @LastEditTime: 2019-06-20 15:25:48
     * @Description: 菜单管理
     */
    import {mapState, mapActions} from 'vuex'
    import RouterHint from './components/RouterHint';
    import IconHint from './components/IconHint';
    import BindModel from './components/BindModel';

    import pinyinMatch from 'pinyin-match'
    import MultipleImport from './components/MultipleImport'

    const files = require.context('@/assets/lang/', false, /^.\/[^_].*\.js$/)
    let modules = {}
    files.keys().forEach(key => {
        if (key === './index.js') return
        modules[key.replace(/(\.\/|\.js)/g, '')] = files(key).default
    })
    //将zh-CN提到最前
    modules = {
        'zh-CN': null,
        ...modules,
    }
    export default {
        name: "menuTreeMgt",
        components: {
            RouterHint,
            IconHint,
            MultipleImport,
            BindModel
        },
        data() {
            return {
                title: '菜单管理',
                tree: {
                    type: 'menuTable',
                    levelLabel: ['第一级', '第二级', '第三级', '第四级', '第五级'],
                    levelTypeMap: ['', 'success', 'info', 'warning', 'danger'],
                },
                menuLevel: [
                    {value: 0, label: '系统级'},
                    {value: 1, label: '代理商级别'},
                    {value: 2, label: '公司级别'},
                    {value: 3, label: '车队级别'},
                ],
                menuType: [
                    {value: 0, label: '导航'},
                    {value: 1, label: '按钮'},
                ],
                currentNode: null,
                form: {
                    data: {
                        id: null,
                        name: {},
                        href: '',
                        menu_type: 0,
                        menu_level: 0,
                        parent_id: null,
                        parent_ids: null,
                        icon: 'default',
                        sort: '',
                        is_show: true,
                        permission: '',
                        remark: '',
                    },
                    rules: {
                        sort: [{required: true, message: '请输入菜单排序', trigger: 'change'}, {pattern: /\d+/, message: '请输入整数', trigger: 'change'}],
                        permission: [{required: true, message: '请输入权限标识', trigger: 'change'}],
                    },
                    state: 'default',//default needSave saving
                },

                langModules: modules,
                routerHelp: false,
                iconHelp: false,
                searchValue:'',//搜索功能
                searchKey:'name',
                selectorList:[],
                menuList:[],
                selectTreeList:[],
                selectTreeIdList:[],
                menuTableTree:{
                  loading:false,
                  data:[]
                }
            }
        },
        computed: {
            ...mapState('ztreeData', ['treeData']),
            // menuTableTree: function () {
            //   console.log('数据更新了');
            //     return JSON.parse(JSON.stringify(this.treeData.menuTable));
            // },
            currentLang: function () {
                return this.$store.state.main.locale;
            },
            langList: function () {
                return [...new Set([this.currentLang, ...Object.keys(this.langModules)])]
            },
            
        },
        watch: {
            'form.data': {
                handler: function (value) {
                    this.form.state = 'needSave';
                },
                deep: true,
            },
            'searchValue': async function (value){
                    if(!value) return;
                    const $tree = this.$refs['tree'];
                let node = $tree.getNode(value);
                $tree.setCurrentKey(value);
                node.expand(null, true);
                node.setChecked(true, true);
                this.nodeClick(node.data,node)
                //手动聚焦到节点
                await this.$utils.sleep(600) //要等el-tree展开动画完了以后scrollto才能生效
                const $node = document.querySelector('div[role=treeitem].el-tree-node.is-current');
                const wrapRect = this.$refs['content'].getBoundingClientRect(),
                      nodeRect = $node.getBoundingClientRect();
                this.$refs['content'].scrollTo({
                    top: (nodeRect.top + this.$refs['content'].scrollTop) - wrapRect.top,
                });
            }
        },
        methods: {
          async getTreeDataMenu(){
            this.menuTableTree.loading = true
            let res = await this.getStateTreeData('menuTable')
            this.menuTableTree.loading = false
            if (!res) {
								this.$error('查询菜单树出错!')
						} else {
							this.menuTableTree.data = JSON.parse(JSON.stringify(res))
						}

          },
          async getTreeDataRefresh(){
            let res = await this.$store.dispatch('ztreeData/refreshTreeData', 'menuTable')
            if (!res) {
								// this.$error('查询菜单树出错!')
						} else {
							this.menuTableTree.data = JSON.parse(JSON.stringify(res))
						}
            this.getListData(true)
          },
          refreshData(data){
            this.getListData(true)
            data.forEach((item,index)=>{
              // if(index){  
                this.$refs['tree'].append({
                    id: item.id,
                    name: item.name,
                    href: item.link,
                    menu_type: item.type,
                    menu_level: item.level,
                    parent_id: item.parentId,// 0 是最上级菜单的id；
                    parent_ids: item.parentIds,
                    icon: item.icon,
                    sort: item.sort,
                    is_show: item.show,
                    permission: item.permission,
                    remark: item.remark,
                }, item.parentId);
              // }
            })
          },
          async multipleExport(){
            if(!this.selectTreeIdList.length){
              this.$warning('没有选中的对象！')
              return
            }
            await this.$utils.excelExport(
                "/ponysafety2/a/permission/excel/export",
                JSON.stringify(this.selectTreeIdList),
                "菜单结构" + ".xlsx"
            );
          },
          multipleImport(data){
            this.$refs['multipleImport'].showModal({
              id:data ? data.id : '0',
              name:data ? this.getName(data.name) : '菜单',
              parent_ids:data ? data.parent_ids : '0,0'
            });
          },
          multipleDelete(){
            if(!this.selectTreeIdList.length){
              this.$warning('没有选中的对象！')
              return
            }
             this.$confirm('是否删除选中的菜单？', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            }).then(async () => {
              let res = await this.$utils.excelExport(
                "/ponysafety2/a/permission/batch/delete",
                JSON.stringify(this.selectTreeIdList),
                "菜单结构" + ".xlsx",
                false,
                false
            );
            let resJob = JSON.parse(res);
            if(!resJob || resJob.status != 200){
              this.$error(resJob ? resJob.message : '删除失败')
            }else {
              this.$success('删除成功')
              this.selectTreeList.forEach(item=>{
                let parentList = item.parent_ids.split(',') 
                if(parentList.length >2){
                  // let node = this.$refs.tree.getNode(item)
                  // console.log(node,'node');
                  this.$refs.tree.setChecked(item,false)
                  this.$refs.tree.remove(item)
                }
              })
              this.getListData(true)
              // this.$store.dispatch('ztreeData/refreshTreeData', 'menuTable')

            }
            })
              
        
          },
          showBindModel(id){
            this.$refs.bindModel.showModel(id)
          },
          handleCheckChange(data, {checkedNodes}) {
            this.selectTreeIdList = checkedNodes.map(item => {
                return item.id
            })
            this.selectTreeList = checkedNodes.map(item => {
                return item
            })
          },
            filterClear(){
                this.selectorList = []
            }, 

            filterChange(value){
                 if (value) {
                    this.selectorList = this.menuList.filter(data => {
                        return pinyinMatch.match(data[this.searchKey], value);
                    })
                } else {
                    this.selectorList = [];
                }

            },
            //获取用户菜单列表
            async getListData(refresh = false){
              if(refresh){
                 this.$store.dispatch('ztreeData/refreshTreeData', 'menuTable')
              }
              // this.$store.dispatch('ztreeData/refreshTreeData', 'menuTable')
              // return
                 let res = await this.$api.getPermission()
                    this.menuList = res.filter(item=>{
                        if(item.name){
                            item.name = this.getName(item.name)
                             return item
                        }
                       
                    })
            },
            ...mapActions('ztreeData', {
                getStateTreeData: 'getTreeData',
            }),

            isJSON(val) {
                if (typeof val !== 'string') return false;
                try {
                    JSON.parse(val);
                    return true;
                } catch (e) {
                    return false;
                }
            },
            getName(val) {
                try {
                    val = JSON.parse(val);
                    return val[this.currentLang];
                } catch (e) {
                    return val;
                }
            },
            nodeClick(data, node) {
              // console.log(data,'data');
                Object.assign(this.form.data, {
                    id: data.id,
                    name: this.isJSON(data.name) ?
                        Object.assign({}, this.buildDefaultNode(), JSON.parse(data.name)) :
                        this.buildDefaultNode(data.name),
                    href: data.href,
                    menu_type: data.menu_type,
                    menu_level: data.menu_level,
                    parent_id: data.parent_id,
                    parent_ids: data.parent_ids,
                    icon: data.icon,
                    sort: data.sort,
                    is_show: Boolean(data.is_show),
                    permission: data.permission,
                    remark: data.remark,
                })
                this.currentNode = node;
                this.$nextTick(() => {
                    this.form.state = 'default';
                })
            },
            resetData() {
                const data = this.currentNode.data;
                Object.assign(this.form.data, {
                    id: data.id,
                    name: this.isJSON(data.name) ?
                        Object.assign({}, this.buildDefaultNode(), JSON.parse(data.name)) :
                        this.buildDefaultNode(data.name),
                    href: data.href,
                    menu_type: data.menu_type,
                    menu_level: data.menu_level,
                    parent_id: data.parent_id,
                    parent_ids: data.parent_ids,
                    icon: data.icon,
                    sort: data.sort,
                    is_show: Boolean(data.is_show),
                    permission: data.permission,
                    remark: data.remark,
                })
                this.$nextTick(() => {
                    this.form.state = 'default';
                })
            },
            async save() {
                if (!await this.$refs['form'].validate()) return;
                this.form.state = 'saving';
                const data = this.form.data;
                let params = {
                    id: data.id,
                    href: data.href,
                    icon: data.icon,
                    is_show: data.is_show ? 1 : 0,
                    menu_level: data.menu_level,
                    menu_type: data.menu_type,
                    name: JSON.stringify(data.name),
                    parent_id: data.parent_id,
                    parent_ids: data.parent_ids,
                    permission: data.permission,
                    remark: data.remark,
                    sort: data.sort,
                    version: 3,
                }
                this.saveNode(params)
            },
            async saveNode(params) {
                if (params.id) {
                    // do modify
                    let res = await this.$api.updateMenuTable(params);
                    if (res) {
                        this.$success('修改菜单成功')
                        Object.assign(this.currentNode.data, this.form.data, params)
                        this.form.state = 'default';
                    } else {
                        this.$error('修改菜单失败')
                        this.form.state = 'needSave';
                    }
                } else {
                    // do add
                    delete params.id;
                    let res = await this.$api.addMenuTable(params);
                    if (res.status === 200) {
                        this.$success('新增菜单成功')
                        this.$refs['tree'].remove(this.currentNode.data);
                        this.form.data.id = res.data;
                        this.$utils.assign(this.currentNode.data,this.form.data, params)
                        this.$refs['tree'].append(this.currentNode.data, this.currentNode.data.parent_id);
                        this.$nextTick(() => {
                            this.form.state = 'default';
                        })
                    } else {
                        this.$error('新增菜单失败')
                        this.form.state = 'needSave';
                    }
                }
                this.getListData(true)
            },
            async deleteNode(node) {
                await this.$confirm('确认删除此节点?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                })
                node.loading = true;
                if (node.data.id) {
                    let res = await this.$api.removeMenuTable({id: node.data.id})
                    if (!res) {
                        this.$error('删除失败')
                        node.loading = false;
                        return;
                    }
                }
                this.$refs['tree'].remove(node);
                this.$success('删除成功')
                node.loading = false;
                this.getListData(true)
                this.currentNode = null;
            },
            appendNode(node) {
                this.$refs['tree'].append({
                    id: null,
                    name: JSON.stringify(this.buildDefaultNode('<<--新增节点-->>')),
                    href: '',
                    menu_type: 0,
                    menu_level: 0,
                    parent_id: node ? node.data.id : 0,// 0 是最上级菜单的id；
                    parent_ids: node ? (node.data.parent_ids + ',' + node.data.id) : '0,0',
                    icon: 'default',
                    sort: '',
                    is_show: true,
                    permission: '',
                    remark: '',
                }, node);
                node.expanded = true;
                // this.nodeClick(node.data, node);
            },
            buildDefaultNode(str = '') {
                let temp = {};
                Object.keys(this.langModules).forEach(item => {
                    temp[item] = str;
                })
                return temp;
            },
            onMenuNodeChange(data) {
                const parentNode = this.currentNode.parent;
                if (!parentNode) return;
                Object.assign(this.form.data, {
                    name: this.buildDefaultNode(
                        (data._type - 1) === 0 ?
                            data.meta.title
                            : data.label
                    ),
                    href: `/home/<USER>
                    menu_type: data._type - 1,
                    permission: (data._type - 1) === 0 ? (parentNode.data.permission + ':' + data.path) : data.symbol,
                })
            },
            onIconChange(name) {
                this.form.data.icon = name;
            },

            refreshHeaderMenu() {
                this.$root.$emit('menuUpdate');
            }
        },
        async created() {
            // this.getStateTreeData(this.tree.type)
            this.getTreeDataMenu()
            this.getListData()
        }
    }
</script>

<style scoped lang="scss" customTheme>
    @import "../DepartmentTreeMgt";
    .select-key {
        width: 20%;
        float: left;
        /deep/ .el-input__inner {
            border-radius: 4px 0 0 4px;
        }
    }
    .select-value {
        width: 80%;
        /deep/ .el-input__inner {
            border-radius: 0 4px 4px 0;
            border-left:none;
        }
    }
</style>

<template>
		<Layout class="terminal-type-mgt" :hasColor="true" :contentLoading="table.loading">
				<template slot="query">
						<div class="query-item">
								<span>终端类型名称：</span>
								<el-input v-model="query.name"></el-input>
						</div>
						<div class="query-item">
								<span>终端分类：</span>
								<el-select v-model="query.usage" @change="query.protocol = null">
										<el-option label="全部" :value="null"></el-option>
										<el-option label="车载终端" :value="0"></el-option>
										<el-option label="场地终端" :value="1"></el-option>
										<el-option label="人员终端" :value="2"></el-option>
								</el-select>
						</div>
						<div class="query-item">
								<span>协议类型：</span>
								<el-select v-model="query.protocol">
										<el-option label="全部" :value="null"></el-option>
										<el-option :label="item.label" :value="item.value" v-for="item in protocolTypeList"
										           :key="item.value"></el-option>
								</el-select>
						</div>
						<div class="break-item">
								<el-button type="primary" @click="getTableList()">查询</el-button>
								<el-button type="primary" v-if="hasPermission('terminaltype:add')" @click="showModal()">新增</el-button>
						</div>
						<div class="query-item">
								<el-pagination background small layout="prev, pager, next, total" :pager-count="5"
								               :current-page.sync="pager.current" @current-change="getTableList" :page-size="pager.size"
								               :total="pager.total">
								</el-pagination>
						</div>
				</template>
				<template slot="content">
						<el-table :data="table.data" border stripe highlight-current-row height="100%" ref="table"
						          class="el-table--radius">
								<el-table-column label="序号" width="60">
										<template slot-scope="{$index}">
												<span>{{ (pager.current - 1) * pager.size + 1 + $index }}</span>
										</template>
								</el-table-column>
								<el-table-column prop="name" label="终端类型名称">
								</el-table-column>
								<el-table-column label="状态">
										<template slot-scope="{row}">
												<span v-if="row.status == -1">禁用/暂停</span>
												<span v-if="row.status == 0">正常</span>
												<span v-if="row.status == -2">已删除</span>
										</template>
								</el-table-column>
								<el-table-column prop="product_name" label="厂商终端名称"></el-table-column>
								<el-table-column prop="product_code" label="厂商终端编码"></el-table-column>
								<el-table-column label="厂商终端分类">
										<template slot-scope="{row}">
												<span v-if="row.classify == 0">车载终端</span>
												<span v-if="row.classify == 1">场地终端</span>
												<span v-if="row.classify == 2">人员终端</span>
										</template>
								</el-table-column>
								<el-table-column label="协议类型">
										<template slot-scope="{row}">
												<span>{{ protocolTypeValueLabelMap[row.protocol_type] }}</span>
										</template>
								</el-table-column>
								<el-table-column prop="location" width="100" label="操作">
										<template slot-scope="{row}">
												<el-button type="text" title="修改" v-if="hasPermission('terminaltype:update')" size="mini"
												           @click="showModal(row)">
														<i class="pony-iconv2 pony-xiugai"></i>
												</el-button>
												<el-button type="text" title="删除" v-if="hasPermission('terminaltype:delete')" size="mini"
												           @click="deleteTerminal(row)">
														<i class="pony-iconv2 pony-shanchu"></i>
												</el-button>
										</template>
								</el-table-column>
						</el-table>
				</template>
				<PonyDialog v-model="modal.show" :title="modal.mode === 'add'?modal.addModeTitle:modal.modifyModeTitle"
				            class="car-modal" :loading="modal.loading" @close="modal.show =false" :width="400">
						<el-form :model="modal.data" :rules="modal.rules" label-width="140px" size="mini" ref="form">
								<el-form-item prop="name" label="终端类型名称：">
										<el-input v-model="modal.data.name"></el-input>
								</el-form-item>
								<el-form-item prop="product_name" label="厂商名称：">
										<el-input v-model="modal.data.product_name"></el-input>
								</el-form-item>
								<el-form-item prop="product_code" label="厂商编码：">
										<el-input v-model="modal.data.product_code"></el-input>
								</el-form-item>
								<el-form-item prop="model" label="终端型号：">
										<el-input v-model="modal.data.model"></el-input>
								</el-form-item>
								<!-- <el-form-item prop="terminal_useage" label="终端用途：">
										<el-select v-model="modal.data.terminal_useage" @change="modal.data.protocol_type = null">
												<el-option :label="item.label" :value="parseInt(item.value)" v-for="item in usageTypeList"
																	 :key="item.value"></el-option>
										</el-select>
								</el-form-item> -->
								<el-form-item prop="protocol_type" label="协议类型：">
										<el-select v-model="modal.data.protocol_type" placeholder="请选择协议类型">
												<el-option :label="item.label" :value="parseInt(item.value)" v-for="item in protocolTypeList"
												           :key="item.value"></el-option>
										</el-select>
								</el-form-item>
								<!-- <el-form-item prop="is_video_support" label="是否支持视频：">
										<el-select v-model="modal.data.is_video_support">
												<el-option label="支持" :value="1"></el-option>
												<el-option label="不支持" :value="0"></el-option>
										</el-select>
								</el-form-item>
								<el-form-item prop="is_ftp_support" label="是否支持FTP：">
										<el-select v-model="modal.data.is_ftp_support">
												<el-option label="支持" :value="1"></el-option>
												<el-option label="不支持" :value="0"></el-option>
										</el-select>
								</el-form-item> -->
								<el-form-item prop="classify" label="终端分类：">
										<el-select v-model="modal.data.classify">
												<el-option label="车载终端" :value="0"></el-option>
												<el-option label="场地终端" :value="1"></el-option>
												<el-option label="人员终端" :value="2"></el-option>
										</el-select>
								</el-form-item>
								<el-form-item prop="terminal_business" label="支持业务：">
										<el-select v-model="modal.data.terminal_business" multiple collapse-tags placeholder="请选择业务（可多选）">
												<el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
												</el-option>
										</el-select>
								</el-form-item>

								<el-form-item prop="icon" label="图标：">
										<div class="popup-item icon">
												<div class="iconBox" @click.stop="showIcon" ref="iconBox">
														<div class="iconImg">
																<i class="play_icon pony-iconv2" :class="modal.data.icon" style="color: #00c190; font-size: 30px"></i>
														</div>
														<div class="closeIcon" @click.stop="closeIcon" v-show="isShowIconSelect">
																<i class="play_icon pony-iconv2 pony-guanbi"></i>
														</div>
												</div>
												<div class="iconSelect" v-show="isShowIconSelect">
														<el-collapse v-model="activeNames">
																<el-collapse-item v-for="(item,index) in iconList" :key="index" :title="item.name">
																		<span v-for="item1 in item.icon" @click="selectIcon(item1.icon)" style="cursor: pointer;margin-right: 5px">
																				<i class="play_icon pony-iconv2" :class="item1.icon" style="color: #00c190; font-size: 30px"></i>
																		</span>
																</el-collapse-item>
														</el-collapse>
												</div>
										</div>
								</el-form-item>

						</el-form>
						<template slot="footer">
								<el-button type="primary" @click="commit">{{
												modal.mode ===
												'add' ? modal.addModeOkText : modal.modifyModeOkText
										}}
								</el-button>
								<el-button type="border" @click="modal.show=false">取消</el-button>
						</template>
				</PonyDialog>
		</Layout>
</template>

<script>
/**
 * @Author: yezy
 * @Email: <EMAIL>
 * @Date: 2020/1/6 9:28
 * @LastEditors: yezy
 * @LastEditTime: 2020/1/6 9:28
 * @Description:
 */

export default {
		name: "terminalTypeMgt",
		data() {
				const modalData = {
						id: '',
						name: '',
						product_name: '',
						product_code: '',
						model: '',
						terminal_useage: null,
						protocol_type: null,
						// is_video_support: 1,
						// is_ftp_support: 1,
						classify: 0,
						terminal_business: [],
						icon: ''
				};
				return {
						query: {
								name: '',
								usage: null,
								protocol: null,
						},
						// usageTypeList: [],
						protocolTypeList: [],
						options: [],
						table: {
								data: [],
								loading: false,
						},
						pager: {
								current: 0,
								size: 30,
								total: 0,
						},
						modal: {
								show: false,
								mode: 'add',
								loading: false,
								addModeTitle: '新增终端类型',
								addModeOkText: '确认新增',
								modifyModeTitle: '修改终端类型',
								modifyModeOkText: '确认修改',
								data: JSON.parse(JSON.stringify(modalData)),
								defaultData: modalData,
								rules: {
										name: [{required: true, message: '请输入终端类型名', trigger: 'blur'},],
										product_name: [{required: true, message: '请输入厂商终端名称', trigger: 'blur'},],
										product_code: [{required: true, message: '请输入厂商终端编码', trigger: 'blur'},],
										terminal_useage: [{required: true, message: '请选择终端用途', trigger: 'change'},],
										protocol_type: [{required: true, message: '请选择协议类型', trigger: 'change'},],
										// is_video_support: [{required: true, message: '', trigger: 'change'},],
										// is_ftp_support: [{required: true, message: '', trigger: 'change'},],
										classify: [{required: true, message: '请选择终端分类', trigger: 'change'},],
										terminal_business: [{required: true, message: '请选择支持业务', trigger: 'change'},],
										icon: [{required: true, message: '请选择图标', trigger: 'change'},],
								},
						},
						// 是否显示Icon
						isShowIconSelect: false,
						// 折叠框
						activeNames: [],
						iconList: [
								{
										name: '终端',
										icon: [
												{
														label: '终端类型1',
														icon: 'pony-zhongduanleixing1',
												},
												{
														label: '终端类型2',
														icon: 'pony-zhongduanleixing2',
												},
												{
														label: '终端类型3',
														icon: 'pony-zhongduanleixing3',
												},
												{
														label: '终端类型4',
														icon: 'pony-zhongduanleixing4',
												},
										]
								}
						]
				}
		},
		computed: {
				// protocolTypeTempList: function () {
				//     const usage = this.query.usage;
				//     if (usage === null) {
				//         return this.protocolTypeList;
				//     } else {
				//         return this.protocolTypeList.filter(item => {
				//             return item.value[0] === usage;
				//         });
				//     }
				// },
				// modalProtocolTypeTempList: function () {
				//     const usage = this.modal.data.terminal_useage;
				//     if (usage !== null) {
				//         return this.protocolTypeList.filter(item => {
				//             return item.value[0] === usage.toString();
				//         });
				//     }
				// },
				protocolTypeValueLabelMap: function () {
						let temp = {};
						this.protocolTypeList.forEach(item => {
								temp[item.value] = item.label;
						})
						return temp;
				}
		},
		methods: {
				// 显示icon框
				showIcon() {
						this.isShowIconSelect = true
				},
				// 关闭icon框
				closeIcon() {
						this.isShowIconSelect = false
				},
				// 选择icon
				selectIcon(icon) {
						this.iconList.forEach(item => {
								item.icon.forEach(a => {
										if (a.icon === icon)
												this.modal.data.icon = a.icon
								})
						})
						this.isShowIconSelect = false
						this.$refs.iconBox.style.borderColor = '#63ba47'
				},
				async getTableList(pageIndex = 1) {
						if (!this.hasPermission('terminaltype:query')) return;
						const params = {
								name: this.query.name,
								classify: this.query.usage,
								protocol_type: this.query.protocol,
								page: pageIndex,
								count: this.pager.size,
								sorting: -1,
						}
						this.pager.current = pageIndex;
						try {
								this.table.loading = true;
								let res = await this.$api.getAllTerminalTypeByVue(params);
								if (res.RS === 1) {
										this.table.data = res.SysTerminalTypeList;
										// console.log(res.SysTerminalTypeList);
										this.pager.total = res.Count;

								} else {
										this.table.data = [];
										this.pager.total = 0;
										throw new Error(res.Reason);
								}
						} catch (e) {
								this.$error(e)
						} finally {
								this.$nextTick(() => {
										this.$refs.table.doLayout()
								});
								this.table.loading = false;
						}
				},
				showModal(rowData) {
						this.modal.data = JSON.parse(JSON.stringify(this.modal.defaultData));
						if (rowData) {
								this.modal.mode = 'modify';
								this.$utils.assign(this.modal.data, rowData)
						} else {
								this.modal.mode = 'add';
						}
						this.modal.show = true;
				},
				async deleteTerminal(rowData) {
						await this.$confirm('此操作不可恢复，确认删除终端?', '提示', {
								confirmButtonText: '确定',
								cancelButtonText: '取消',
								type: 'error'
						})
						let res = await this.$api.removeTerminalType({
								id: rowData.id,
						})
						if (res.RS === 1) {
								this.$success(res.Reason);
								this.getTableList();
						} else {
								this.$error(res.Reason);
						}
				},
				async commit() {
						if (!this.$refs['form'].validate()) {
								if (!this.modal.data.icon) {
										this.$refs.iconBox.style.borderColor = '#ff5359'
								}
								return
						}
						if (!this.modal.data.icon) {
								return this.$refs.iconBox.style.borderColor = '#ff5359'
						}
						try {
								this.modal.loading = true;
								let res;
								if (this.modal.mode === 'add') {
										res = await this.$api.insertTerminalType(this.modal.data);
								} else {
										res = await this.$api.updateTerminalType(this.modal.data);
								}
								if (res.RS === 1) {
										this.$success(res.Reason);
										this.getTableList();
										this.modal.show = false;
								} else {
										throw new Error(res.Reason);
								}
						} catch (e) {
								this.$error(e);
						} finally {
								this.modal.loading = false
						}
				}
		},
		mounted() {
				// 获取options数据
				this.$api.getSysDictByCode({code: 'terminal_business'}).then(res => {
						this.options = res.map(item => ({
								label: item.name,
								value: item.value,
						}))
				})
				// this.$api.getSysDictByCode({code: 'terminal_uesage'}).then(res => {
				//     this.usageTypeList = res.map(item => ({
				//         label: item.name,
				//         value: item.value,
				//     }))
				// })
				this.$api.getSysDictByCode({code: 'protocol_type'}).then(res => {
						this.protocolTypeList = res.map(item => ({
								label: item.name,
								value: item.value,
						}))
				})
				this.getTableList();
		}
}
</script>

<style scoped lang="scss">

.el-collapse {
		border: 0;
}

/deep/ .el-collapse-item__header {
		width: 235px;
		height: 35px;
		padding: 0 10px;
		border: 0;
}

/deep/ .el-collapse-item__content {
		padding: 0;
}

.iconBox {
		display: flex;
		align-items: center;
		justify-content: space-between;
		width: 235px;
		height: 28px;
		border: 1px solid var(--border-color-light);
		background-color: var(--background-color-base);
		background-image: none;
		border-radius: 4px;
		box-sizing: border-box;
		font-size: inherit;
		line-height: 40px;
		outline: none;
		padding: 0 15px;
		transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);

		.closeIcon {
				display: flex;
				align-items: center;
				justify-content: center;
				width: 20px;
				height: 20px;
				opacity: 0.5;
				border-radius: 50%;
		}

		.iconImg {
				height: 100%;
				width: 30px;
				display: flex;
				align-items: center;

				img {
						height: 100%;
						width: 100%;
				}
		}
}
</style>

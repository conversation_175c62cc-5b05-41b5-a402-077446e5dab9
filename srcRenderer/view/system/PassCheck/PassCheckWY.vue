<template>
    <Layout :has-color="true" :contentLoading="tool.loading" class="passCheckJH">

        <template slot="aside">
            <div class="query-top">
                <!-- <ZtreeMatics treeType="vehicle" ref="vehicleTree" 
                    :checkMode="true" style="padding: 10px;"
                    >
                </ZtreeMatics> -->
                <ElementTree type="vehicle" ref="vehicleTree" :checkMode="true"
                @check="selectNodes" node-key="id"
                @mounted="getBasicTreeDataInfo" >
                </ElementTree>
            </div>
            <div class="query-bottom bg bg--tab">
                <div class="query-item">
                    <span>选择辖区</span>
                    <el-select style="width: 100%" v-model="queryList.jurisdiction" placeholder="请选择">
                        <el-option v-for="(item, index) in jurisdictionList" :key="index" :label="item.name" :value="item.jurisdiction"></el-option>
                    </el-select>
                </div>
                <!-- <div class="query-item">
                    <span>是否有效</span>
                    <el-select style="width: 100%" v-model="queryList.isItEffective" placeholder="请选择">
                        <el-option label="全部" :value="6"></el-option>
                        <el-option label="无效" :value="7"></el-option>
                        <el-option label="有效" :value="8"></el-option>
                        <el-option label="未生效" :value="9"></el-option>
                    </el-select>
                </div> -->
                <div class="query-item">
                    <el-button size="mini" type="primary" style="width: 100%;"
                        @click="searchCurrentDetail(false)">查询
                    </el-button>
                </div>
            </div>
        </template>

        <template slot="query">
            <div class="query-item">
                <span>审批状态：</span>
                <el-select @change="filterPasscheckState" v-model="tool.currentState" placeholder="请选择">
                    <el-option label="全部" :value="3"></el-option>
                    <el-option label="已注销" :value="-1"></el-option>
                    <el-option label="审批通过" :value="1"></el-option>
                    <el-option label="等待审批" :value="2"></el-option>
                    <el-option label="被驳回" :value="0"></el-option>
                    <el-option :label="statusDown?'一级审批':'待中队审批'" :value="5"></el-option>
                    <el-option :label="statusDown?'二级审批':'待大队审批'" :value="6"></el-option>
                </el-select>
            </div>
            
            <div class="query-item">
                <el-button size="mini" type="primary" @click="openVehicleChooseModal(null)" v-if="pagePerssion.initiateApplication == 0">通行证申请</el-button>
                <el-button size="mini" type="primary" @click="handlListDelete('more')" v-if="pagePerssion.deleteButton == 0">批量撤回</el-button>

                <el-button size="mini" type="primary" @click="handlListStateChange(false)" v-if="pagePerssion.bulkRejection == 0">批量驳回</el-button>
                <el-button size="mini" type="primary" @click="handlListStateChange(true)" v-if="pagePerssion.batchApproval == 0">批量通过</el-button>
                <el-button size="mini" type="primary" @click="exportDataInfo">导出</el-button>
                <el-button size="mini" type="text" @click="preface.show = true">审批须知<i style="vertical-align:-1px" class="pony-iconv2 pony-bangzhu"></i></el-button>
            </div>
            <div class="break-item"></div>
            <div class="query-item">
                <el-pagination background small
                    layout="prev, pager, next, total"
                    :current-page.sync="table.page"
                    :page-size="table.size"
                    :total="filterStateList.length">
                </el-pagination>
            </div>
        </template>

        <template slot="content">
            <el-table
                ref="blacktable" slot="content"
                class="el-table--ellipsis el-table--radius"
                border stripe highlight-current-row size="mini"
                :data="formatList"
                height="100%" style="width: 100%">
                <!-- <template slot-scope="{row}">
                        
                </template> -->
                <el-table-column type="selection" width="55" :selectable="checkSelectable"></el-table-column>
                <el-table-column type="index" label="序号" width="70">
                    <template slot-scope="scope">
                        <div class="fl">
                            <span class="circle circle--unactive" v-if="scope.row.approval_proposal == 2" ></span>
                            <span class="circle circle--danger" v-else-if="scope.row.approval_proposal == 0" ></span>
                            <span class="circle circle--success"  v-else-if="scope.row.approval_proposal == 1" ></span>
                            <span v-else></span>
                        </div>
                        {{ scope.$index + 1 + pageStart}}
                    </template>
                </el-table-column>
                <el-table-column label="操作"  min-width="140">
                    <template slot-scope="{row}">
                        <el-button type="text" title="详情" v-if="pagePerssion.detailsButton == 0" size="mini" @click="openVehicleCheckModal(row, false)">
                            <i class="pony-iconv2 pony-xiangqing"></i>
                        </el-button>
                        <el-button type="text" title="修改" size="mini"
                            :disabled="row.status != 0"
                            v-if="pagePerssion.modifyButton == 0"
                            @click="openVehicleChooseModal(row)">
                            <i class="pony-iconv2 pony-xiugai"></i>
                        </el-button>
                        <el-button type="text" title="审批" 
                            :disabled="!row.approve_or_not" 
                            v-if="pagePerssion.approvalButton == 0" size="mini" @click="openVehicleCheckModal(row, true)">
                            <i class="pony-iconv2 pony-shenpi"></i>
                        </el-button>
                        <el-button type="text" title="注销" size="mini"
                            :disabled="row.status != 1" 
                            v-if="!pagePerssion.logoutButton"
                            @click="handlListStateEffective(row)">
                            <i class="pony-iconv2 pony-shanchu"></i>
                        </el-button>

                        <el-button type="text" title="撤回" size="mini"
                            v-if="pagePerssion.deleteButton == 0"
                            :disabled="!(row.delete_or_not === 0)" 
                            @click="handlListDelete(row)">
                            <i class="pony-iconv2 pony-shanchu"></i>
                        </el-button>
                    </template>
                </el-table-column>
                <el-table-column prop="company_name" show-overflow-tooltip label="单位" min-width="150"></el-table-column>
                <el-table-column prop="plate_no" label="车牌号" min-width="100"></el-table-column>
                <!-- 安全码 -->
                <el-table-column prop="recent_rating" min-width="80"
                    :render-header="customHeader">
                    <template slot-scope="{row}">
                        <el-tag style="cursor: pointer;" @click="showScoreDetail(row)" :type="row | formatWarningColor">{{ `${row.recent_rating} / ${row.all_days}` }}</el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="需停运天数"  props="de_days" min-width="80">
                    <template slot-scope="{row}" >
                        <el-tag :type="row | formatWarningColor">{{ row.de_days }}</el-tag>
                    </template>
                </el-table-column>
               
                <el-table-column prop="alarm_state" label="审批状态" min-width="80">
                    <template slot-scope="{row}">
                        {{ tool.stateDesc[row.status].text }}
                    </template>
                </el-table-column>
                <el-table-column prop="start_day" label="有效期" min-width="180">
                    <template slot-scope="{row}">
                        {{ row.start_day }} ~ {{ row.end_day }}
                    </template>
                </el-table-column>
                <el-table-column prop="application_time" label="申请时间" min-width="150"></el-table-column>
                <el-table-column prop="jurisdiction" label="通行辖区" min-width="100"></el-table-column>
                <el-table-column prop="pass_number" label="通行证申请编号" min-width="150"></el-table-column>
                <!-- <el-table-column prop="annual_review_time" label="下次年审时间" min-width="150"></el-table-column> -->
            </el-table>
        </template>

        <ApprovalJHModalNew ref="jhmodaldetail" @change="searchCurrentDetail(true)"></ApprovalJHModalNew>

        <ApprovalJHCheck ref="jhmodalcheck" @change="sumbitCheckAdvice" :statusMessage="statusMessage"></ApprovalJHCheck>


        <!-- 偷鸡操作 -->
        <PonyDialog width="580" 
            title="通行证审批须知"
            v-model="preface.show" :hasFooter="false">
            <ul style="line-height: 30px; letter-spacing: 1px; padding: 0 10px 0 15px">
                <li v-for="(item, index) in preface.data" :key="index">
                    {{ item }}
                </li>
                <li class="dfc" style="margin-top: 10px">
                    <el-button size="mini" type="primary" @click="preface.show = false">我已知晓</el-button>
                </li>
            </ul>
        </PonyDialog>

        <PassCheckSafeNumber v-model="dictionartModal.show" :title="title"></PassCheckSafeNumber>
        <JHModalScoreNew  ref="jhmodalscore"></JHModalScoreNew>

        <PonyDialog width="480" 
            title="通知"
            v-model="noticeModal.show" :hasFooter="false">
            <ul style="line-height: 30px; letter-spacing: 1px; padding: 0 10px 0 15px">
                <li >
                    金华市渣土车电子通行证申请系统试运行阶段，允许红码车辆申请通行证。
                </li>
                <li >
                    试运行期：2020年7月27日至2020年8月27日
                </li>
                <li class="dfc" style="margin-top: 10px">
                    <el-button size="mini" type="primary" @click="noticeModal.show = false">确认</el-button>
                </li>
            </ul>
        </PonyDialog>

        <JHReject ref="jhreject" :statusMessage="statusMessage"></JHReject>
        <!-- 申请通行证统一须知 -->
        <PonyDialog width="480" 
            title="工程运输车辆责任人承诺书"
            v-model="agreeKnow.show" :hasFooter="false">
            
            <div class="content">
                <p style="text-indent:2em">为配合公安机头交警部门门做好大中型运输车辆的交通安全行驶管理，保隊大群众出行安全，进一步明确职击、落实责任、促进运输车辆源头管理工作深入开展，确保文明行车、安全出行，本人承诺自觉做到以下几点：</p>
                <p style="text-indent:2em">1、工严格督促驾驶员遵守 《道路交通安全法》 区相关交通法规。严密实施运输车辆 “一人一档”、“一车一档”监管制度，并面对面开展交通安全宣传数育。同时，要求驾驶员必须持证驾驶，增强安全行车意识，坚决杜绝“三超一疲劳”，酒后驾驶，自觉遵守道路交通安全法。</p>
                <p style="text-indent:2em">2督促驾驶员做到所驾驶的车辆保持号牌清晰、完整，禁止车辆在到道路上双排行驶，严禁超速行驶；车辆如需要暂时停放在道路上的必须做好防护措施，不得长时间停放，禁止车辆运载的物体有洒落现象。</p>
                <p style="text-indent:2em">3、遇斑马线必须提早减速并停止让行，与转弯路口必须慢行通过，不得随意变更通行车道。</p>
                <p style="text-indent:2em">4、早晚高峰时间段不得驶入禁止货运车辆驶入路段（包含已审批发放通行证的车），高峰时间段为。上午7点30分至9点00分，下午17点30分至18点00分，学校高峰岗时间为：上午7点00分至8点00分，下午15点00分至17点30分。</p>
                <p style="text-indent:2em">5、对运输车辆驾驶人交通违法和记分情况进行清查，对交通违法未处理、交通违法记分达12分以上的，及时通报所属运输单位及交通管理部门、督促接受处理。</p>
            </div>
            <div class="agree" style="text-align:center">
                <el-checkbox label="同意" v-model="agreeKnow.isAgree"></el-checkbox>
            </div>
            <div class="dfc" style="margin-top: 10px">
                <el-button size="mini" type="primary" @click="agreeConfirm()">确认</el-button>
            </div>
        </PonyDialog>
    </Layout>
</template>

<script>
import ApprovalJHModalNew from './components/ApprovalJHModalNew'
import ApprovalJHCheck from './components/ApprovalJHCheck'
import JHModalScoreNew from './components/JHModalScoreNew'
import PassCheckSafeNumber from './components/PassCheckSafeNumber'
import JHReject from './components/JHReject'
import { mapState } from 'vuex'
const ExportJsonExcel = require('js-export-excel')
export default {
    name: 'passCheckWY',
    components: { ApprovalJHModalNew, ApprovalJHCheck, JHModalScoreNew, PassCheckSafeNumber, JHReject },
    data () {
        return {
            //同意须知
            agreeKnow:{
                show:false,
                isAgree:false
            },
             // 车辆状态查询信息]
            off:false,
            waitForInit:null,
            title:"",
            statusMessage:"",
            tool: {
                loading: false,

                stateDesc: {
                    0: {
                        color: 'danger',
                        text: '被驳回'
                    },
                    2: {
                        color: 'warning',
                        text: '待审批'
                    },
                    1: {
                        color: 'success',
                        text: '审批通过'
                    },
                    4: {
                        color: 'warning',
                        text: '待我审批'
                    },
                    5: {
                        color: 'warning',
                        text: '待中队审批'
                    },
                    6: {
                        color: 'warning',
                        text: '待大队审批'
                    },
                    '-1': {
                        color: 'info',
                        text: '已注销'
                    }
                },

                currentState: 6,
            },

            jurisdictionList: [],
            
            queryList: {
                vehicle_ids: [],
                status: 3,
                isItEffective: 6,
                jurisdiction: 0,
            },

            table: {
                data: [],
                size: 30,
                page: 1,
            },
            
            pagePerssion: {
                approvalButton: -1,
                batchApproval: -1,
                bulkRejection: -1,
                deleteButton: 0,
                detailsButton: 0,
                initiateApplication: 0,
                modifyButton: 0,
                logoutButton: 0
            },

            preface: {
                show: false,
                data: [
                    '* 如发生信息错填，漏填的情况，通行证申请将会被驳回；',
                    '* 如出现车载设备数据异常，通行证申请将会被驳回；',
                    '* 如出现人为恶意填写，通行证申请有可能会被暂停；',
                    '* 年审时间到期、有违章未处理、未绑定驾驶员车辆不允许申请通行证。',
                    '1、请确保所申请通行证的车辆、驾驶员登记信息的真实性、有效性和完整性，证件均在有效期内，违章已处理；',
                    '2、请确保申请所需的相关证明材料的真实性、有效性和完整性；',
                    '3、请确保所申请的驾驶员已接受安全培训，充分了解市区驾驶安全须知，且安全码达到要求；',
                    '4、请确保所申请车辆安装的设备技术条件满足相关要求，数据传输正常；',
                    '5、车辆首次申请通行证期限为 30 天，再次申请时如果是绿码，可申请 60 天；再次申请时还是绿码，可以申请 90 天，最长申请期为 90 天；',
                    '6、高峰期限行时间段已默认，如需临时调整，根据各大队要求填写。'
                ]
            },
            
            dictionartModal: {
                show: false,
            },

            noticeModal: {
                show: false,
            },

            filterStateList: [],
            treeObj:null,
            _promiseResolve:null,
        };
    },

    filters: {
        formatWarningColor(row) {
            if(row.all_days){
                let n=Math.ceil(row.recent_rating/row.all_days*10)/10
                // console.log(n);
                if(n >= 0 && n <= 0.1){
                    return 'success'
                }else if(n > 0.1 && n <= 0.2){
                    return 'warning'
                }else if(n > 0.2){
                    return 'danger'
                }
             
            }else {
                return 'success'
            }
        }

    },

    computed: {
        ...mapState('main', ['company','changeNameList']),
        statusDown(){
            if(this.company=='yx'){
                return true
            }else {
                return false
            }
        },
        pageStart() {
            return (this.table.page - 1) * this.table.size
        },
        formatList() {
            return this.filterStateList.slice(this.pageStart, this.pageStart + this.table.size)
        }
    },
     created(){
        this.waitForInit = new Promise((resolve => {
            this._promiseResolve = resolve;
            resolve()
        }))

    },
    async activated(){
        if(Object.keys(this.$route.query).length){
            this.tool.currentState = 2
            await this.$refs['vehicleTree'].waitForInit
            this.searchCurrentDetail(true,true)
        }
    },
    async mounted() {

        await this.getCustomPression()   //获取操作权限
        await this.getJurisdictionList() //获取选择辖区
        // console.log(this.$route);
        if(this.$route.query.parmas){
            this.table.data=JSON.parse(this.$route.query.parmas)
            setTimeout(()=>{
                // let nodes=this.$refs['vehicleTree'].ztreeObj.getNodesByFilter(node=>node.type==4)
                // let index=nodes.findIndex(item=>item.id==this.table.data[0].vehicle_id)
                // if(index!=-1){
                //     this.$refs['vehicleTree'].ztreeObj.selectNode(nodes[index])
                //     this.$refs['vehicleTree'].ztreeObj.checkNode(nodes[index])
                // }
                this.treeObj.setChecked(this.table.data[0].vehicle_id,true,false)
            },2000)
        }
        if(this.$route.query.status){
            this.tool.currentState = 2
            await this.$refs['vehicleTree'].waitForInit
            this.searchCurrentDetail(true,true)
        }
        
        
    },

    methods: {
        getBasicTreeDataInfo(obj) {
            this.treeObj = obj
            this._promiseResolve()
        },
        selectNodes(current, { checkedNodes }) {
        this.queryList.vehicle_ids = checkedNodes
        .filter((item) => item.type == 4)
        .map((item) => item.id);
        },
        // 删除单条或批量删除
        handlListDelete(row){
            let ids=[]
            this.$confirm('操作不可逆,确认执行', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async() => {
                this.tool.loading=true
                if(row=='more'){
                    if(!this.$refs.blacktable.selection.length) {
                        this.$warning('请选择处理对象')
                        return
                    }  
                    // 选中实例id列表
                    this.tool.loading=true
                    this.$refs.blacktable.selection.forEach(item=>{
                        ids.push(item.proc_inst_id)
                    })
                }else {
                    ids.push(row.proc_inst_id)
                }
                let result = await this.$api.removePassApplyBack({
                    process_inst_ids: ids,
                    process_inst_key: 'pass_approval_jh'
                })
                this.tool.loading=false

                if(!result || result.status != 200) {
                    this.$warning(result.message || '撤销失败')
                    return
                }
                
                this.$success('撤销成功')
                this.searchCurrentDetail(true)
                
            }).catch(() => {
                this.tool.loading=false
            })


        },
        filterPasscheckState() {
            if(this.tool.currentState == 3) {
                this.filterStateList = this.table.data
                return
            }
            if(this.tool.currentState == 4) {
                this.filterStateList = this.table.data.filter(item => item.status == 2 && item.approve_or_not == 1)
                return
            }
          if(this.tool.currentState == 5) {
            this.filterStateList = this.table.data.filter(item => item.status == 2 && item.approval_proposal == 2)
            return
          }
          if(this.tool.currentState == 6) {
            this.filterStateList = this.table.data.filter(item => item.status == 2 && item.approval_proposal !=-1 && item.approval_proposal != 2)
            return
          }
            this.filterStateList = this.table.data.filter(item => item.status == this.tool.currentState)
        },

        exportDataInfo() {
            if(!this.filterStateList.length) {
                this.$warning('没有数据可以导出')
                return
            }

            let excelBody = []
            this.filterStateList.forEach((item, index) => {
                let array = []
                array.push(
                    index + 1, item.company_name, item.plate_no, `${ item.recent_rating } / 30`, item.de_days,
                    this.tool.stateDesc[item.status].text, `${ item.start_day } ~ ${ item.end_day }`,
                    item.application_time, item.jurisdiction, item.pass_number, item.annual_vaild_date
                )
                excelBody.push(array)
            })

            let options = {
                fileName: '通行证审批详情',
                datas: [
                    {
                        sheetData: excelBody,
                        sheetHeader:[ '序号', '单位',  '车牌号', '安全码', '需停运天数', '审批状态', '有效期', '申请时间', '通行辖区', '通行证申请编号', '下次年审时间' ],
                        columnWidths: ['3','15','6','6','6', '8', '12', '8', '8', '8', '8'],
                    }
                ]
            }
            ExportJsonExcel(options).saveExcel();
        },

        checkSelectable(row) {
          
            if(this.pagePerssion.deleteButton==0){
                return row.delete_or_not==0
            }else if(this.pagePerssion.approvalButton==0){
                return row.approve_or_not==1
                
            }
           
            
        },

        showScoreDetail(row) {
            this.$refs['jhmodalscore'].initVehicleScore(row)
        },

        async getCustomPression() {
            let result = await this.$api.buttonManage()

            if(!result || result.status != 200) {
                this.$warning('获取操作权限失败, 请刷新重试')
                return
            }
            Object.assign(this.pagePerssion, result.data)
            // console.log(this.pagePerssion);
        },

        async getJurisdictionList() {
            let result = await this.$api.queryJurisdiction()
            if(!result || result.status != 200) {
                this.$warning('获取字典出错，请刷新重试')
                return
            }
            this.jurisdictionList = result.data || ''
        },

        async handlListStateChange(type) {
            
            if(!this.$refs.blacktable.selection.length) {
                this.$warning('请选择处理对象')
                return
            }
            // 选中实例id列表
            this.tool.loading=true
            let proInstIds=[]
            this.$refs.blacktable.selection.forEach(item=>{
                proInstIds.push(item.proc_inst_id)
            })

            let resultMessage = await this.$api.getMoreStatusV3({
                proInstIds,
                type:2
            })
            this.tool.loading=false

            this.statusMessage=resultMessage.data
           
            let result = await this.$refs['jhreject'].showModal(type)
            if(!result) return
            let ins_id_and_keys = this.$refs.blacktable.selection.map(item => {
                return {
                    ins_id: item.proc_inst_id,
                    ins_key: 'key'
                }
            })
            let parmas = {
                ins_id_and_keys: ins_id_and_keys,
                judge: type,
                remark: result
            }
            this.sumbitCheckAdvice(parmas)
        },

        handlListStateEffective(row) {
            this.$confirm('操作不可逆,确认执行', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async() => {
                let result = await this.$api.removePassApproval({
                    process_inst_ids: [row.proc_inst_id],
                    process_inst_key: 'pass_approval_jh'
                })
                if(!result || result.code != 200) {
                    this.$warning(result.message || '注销失败')
                    return
                }
                this.$success('注销成功')
                this.searchCurrentDetail(true)
            }).catch(() => {
                console.log('出错了!!!')
            })
        },

        async sumbitCheckAdvice(parmas) {
            let result = await this.$api.updatePassApprovalJH(parmas)
            if(!result || result.status != 200) {
                this.$error(result.message || '操作出错');
                return
            }
            this.$success('处理成功')
            this.searchCurrentDetail(true)
        },

        async openVehicleCheckModal(row, type) {
           
             let proInstIds=[]
             proInstIds.push(row.proc_inst_id)
            let resultMessage = await this.$api.getMoreStatusV3({
                proInstIds,
                type:1
            })
            this.statusMessage=resultMessage.data

            this.$refs['jhmodalcheck'].initApprovalModal(row, type)
        },

        async openVehicleChooseModal(value) {
            if(value){
               this.$refs['jhmodaldetail'].initApprovalModal(value)
               return
            }
            this.agreeKnow.show = true
        },

        agreeConfirm(){
            this.agreeKnow.show = false
            if(!this.agreeKnow.isAgree)return
            this.agreeKnow.isAgree = false
            this.$refs['jhmodaldetail'].initApprovalModal(null)
        },

        async searchCurrentDetail(type,routeType = false) {
            // this.queryList.vehicle_ids = this.$refs['vehicleTree'].ztreeObj.getCheckedNodes(true).filter(item => item.type == 4).map(current => current.id)
            if(!routeType){
                if(!this.queryList.vehicle_ids.length) {
                    if(!type) {
                        this.$warning('请先选择查询对象!')
                    }
                    return
                }
            }
            
            this.table.data = []
            this.table.page = 1
            this.tool.loading = true
            let result = await this.$api.queryPassTaskByListNew(JSON.parse(JSON.stringify(this.queryList)))
            // console.log(result);
            if(!result || result.status != 200) {
                this.$error(result.message || '查询出错');
                this.tool.loading = false
                return
            }
            if(!result.data || !result.data.length) {
                this.$warning('未查询到数据');
                this.tool.loading = false
                return
            }
            this.table.data = result.data
            this.filterPasscheckState()
            this.tool.loading = false
        },

        customHeader(h, {column}){
            let _this = this
            return h('span', [
                h('span', {
                    attrs: {
                        title: '点击查看详情',
                        style: 'outline: none'
                    }
                }, '安全码'),
                h('i', {
                    class: 'pony-iconv2 pony-bangzhu',
                    style: {
                        cursor: 'pointer',
                        'vertical-align':'-1px'
                        
                    },
                    on: {
                        click: function(e) {
                            
                           _this.dictionartModal.show = true
                           _this.title=_this.changeNameList[_this.company] ? window.PONY.companyOptions[_this.changeNameList[_this.company]].net.title : window.PONY.companyOptions[_this.company].net.title
                       
                        }
                    }
                })
            ])
        },
    },
   
}

</script>

<style lang='scss' scoped>
.passCheckJH {
    .query-top {
        height: calc(100% - 105px);
    }   
    .query-bottom {
        margin-top: 5px;
        padding: 10px;

        .query-item {
            display: flex;
            height: 40px;
            line-height: 40px;
            justify-content: space-between;
            align-items: center;

            > span {
                font-size: 12px;
                white-space: nowrap;
                margin-right: 10px;
            }
        }
    }

    .circle {
        display: inline-block;
        height: 8px;
        width: 8px;
        border-radius: 4px;

        &--danger {
            background-color: var(--color-danger)
        }
        &--unactive {
            background-color: var(--color-text-disabled)
        }
        &--success {
            background-color: var(--color-success)
        }
    }
    .content {
        padding: 8px;
        sup {
            font-size: 12px;
            margin: 0 4px 0 2px;
            font-weight: normal;
            font-style: italic;
        }
        h3 {
            font-size: 15px;
            margin: 8px;
        }
        .dividerLine {
            margin-top: 30px;
            height: 1px;
            width: 60%;
        }
        .notice {
            margin-top: 5px;
            p{
                font-size: 12px;
                color: var(--color-danger);
                font-style: italic;
            }
        }
        
    }
    
    
}
</style>

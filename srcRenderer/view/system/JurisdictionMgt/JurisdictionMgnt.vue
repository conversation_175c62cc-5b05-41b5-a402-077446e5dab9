<template>
    <Layout :has-color="true" :contentLoading="contentLoading"
        class="jurisdictionMgnt">
        
        <template slot="aside">
            <div class="query-top ">
                <ElementTree type="area" ref="tree" 
                    :always-new="true" 
                    :check-mode="true"
                    @check="selectNodes">
                </ElementTree>
            </div>
            <div class="query-bottom bg bg--light box-shadow">
                <el-button size="mini" type="primary" 
                    style="width: 100%;"
                    @click="searchList">查询
                </el-button>
            </div>
        </template>

        <template slot="query">
            <div class="query-item">
                <el-button type="primary" size="mini" @click="changeJurisd(null)">新增</el-button>
                <el-button size="mini" type="primary" @click="showTableSetting()">
                    表格显示配置
                </el-button>
            </div>
            <div class="break-item"></div>
            <div class="query-item">
                <el-pagination small background
                    :current-page.sync="table.page"
                    :page-size="table.size"
                    :total="table.data.length"
                    layout="prev, pager, next, total">
                </el-pagination>
            </div>
        </template>

        <template slot="content">
            <el-table  
                border :data="formatList" 
                size="mini" stripe class="el-table--radius"
                height="100%" style="width: 100%" ref="table">

                <el-table-column type="index" :index="(index) => index + 1 + pageStart" label="序号" width="100"></el-table-column>
                <el-table-column label="操作" min-width="50">
                    <template slot-scope="{row}">
                        <el-button type="text" size="mini" @click="changeJurisd(row)" title="修改">
                            <i class="pony-iconv2 pony-xiugai"></i>
                        </el-button>
                        <el-button type="text" size="mini" @click="removeJurisd(row)" title="删除">
                            <i class="pony-iconv2 pony-shanchu"></i>
                        </el-button>
                    </template>
                </el-table-column>
                <template  v-for="(item,index) in tableSettingList" >
                    <el-table-column :label="item.name" :min-width="item.size" :sortable="item.sort" :align="item.align" v-if="item.key=='level'">
                    <template slot-scope="{row}">
                        <span>{{row.level == 4? '区、县级别' : '街道级别'}}</span>
                    </template>
                </el-table-column>

                <el-table-column :label="item.name" :min-width="item.size" :sortable="item.sort" :align="item.align" v-else-if="item.key=='fensePoint'">
                    <template slot-scope="{row}">
                        <span>{{row.fensePoint == null?'无':'有'}}</span>
                    </template>
                </el-table-column>
                
                <el-table-column :label="item.name" :min-width="item.size" :sortable="item.sort" :align="item.align" v-else-if="item.key=='users'">
                    <template slot-scope="{row}">
                        <span>{{row.users ? row.users.length : 0}}</span>
                    </template>
                </el-table-column>
                <el-table-column :label="item.name" :min-width="item.size" :sortable="item.sort" :align="item.align" v-else-if="item.key=='depts'">
                    <template slot-scope="{row}">
                        <span>{{row.users ? row.depts.length : 0}}</span>
                    </template>
                </el-table-column>
                <el-table-column :label="item.name" :min-width="item.size" :sortable="item.sort" :align="item.align" v-else-if="item.key=='vehicles'">
                    <template slot-scope="{row}">
                        <span>{{row.users ? row.vehicles.length : 0}}</span>
                    </template>
                </el-table-column>

                    <el-table-column  :label="item.name" :min-width="item.size" :sortable="item.sort" :align="item.align" :prop="item.key" v-else >
                    </el-table-column>
                </template>
            </el-table>
        </template>
        <TableShowConfigList ref="tableShowConfigList" v-model="tableSettingList" :filterableSearch="true" :list="allSettingList" :defaultSetting="defaultSettingList"  @change="settable"
        pageName="jurisdictionMgt"
        :isSave="true"
        @tableValue="getTableSetting"
        ></TableShowConfigList>
        <JurisdModal ref="jurisdModal" @change="commitJurisdChange"></JurisdModal>

    </Layout>
</template>

<script>
    import TableShowConfigList from "@/view/report/components/TableShowConfigList";
import {allSettingList,defaultSettingList} from "./modal/restrictedZoneMgtTable"
import JurisdModal from './modal/JurisdModal'
export default {
    name: 'jurisdictionMgt',
    components: { JurisdModal,TableShowConfigList },
    data () {
        const defaultData = {
             name:"",
             superior:null,
             fence:""
        };
        return {
            contentLoading: false,


            table: {
                page: 1,
                size: 30,
                data: []
            },


            defaultData,

            currentNodes: [],

            modal: {
                data: JSON.parse(JSON.stringify(defaultData)),
                model: false,
                rules: {
                    name: [{ required: true, message: '请输入名称',trigger: 'blur' }],
                    superior: [{ required: true, message: '请选择', trigger: 'change' }],
                    fensePoint: [{ required: false, trigger: 'change' }],
                }
            },
            allSettingList,
            defaultSettingList,
            tableSettingList:[]
        };
    },

    computed: {
        pageStart() {
            return (this.table.page - 1) * this.table.size
        },

        formatList() {
            return this.table.data.slice(this.pageStart, this.pageStart + this.table.size)
        },
    },

    mounted() {

    },

    methods: {
        showTableSetting(){
            this.$refs.tableShowConfigList.showModel()
        },
        settable(value){
            this.tableSettingList=value;
            // console.log(this.tableSettingList)
            this.$nextTick(()=>{
                this.$refs.table.doLayout()
            })
        },
        getTableSetting(val){
            this.$nextTick(()=>{
                if(val){
                this.tableSettingList = val
            }else{
                this.tableSettingList = this.defaultSettingList
            }
            })
            
        },
        selectNodes(current, {checkedNodes}) {
            this.currentNodes = checkedNodes;
        },

        async changeJurisd(data) {
            this.$refs['jurisdModal'].showModal(data)
        },

        async commitJurisdChange(type, parmas) {
            try {
                let result
                this.contentLoading = true
                if(type) {
                    result = await this.$api.updateJurisdiction(parmas)
                } else {
                    result = await this.$api.newJurisdiction(parmas)
                }
                if(!result || result.status != 200) {
                    this.$warning(result.message || '操作失败')
                    this.contentLoading = false
                    return
                }
                await this.searchList(false)
                this.$success('操作成功')
                await this.$nextTick()
                this.contentLoading = false
            } catch (error) {
                this.$error('执行出错')
            } finally {
                this.contentLoading = false
            }
        },


        async searchList(msg = true) {
            if(!this.currentNodes.length) {
                msg && this.$warning('请选择区域')
                return;
            }
            this.contentLoading = true
            let params = this.currentNodes.map(item => item.id)
            let result = await this.$api.getJurisdiction({ ids: params, areaTypes:[1] })
            if(!result || result.status != 200) {
                this.contentLoading = false
                this.$error(result.message || "'查询出错，请重试")
                return
            }
            if(!result.data.length) {
                this.contentLoading = false
                // 没有数据结果补充刷新表格
                this.table.data = result.data ? result.data : [];
                this.$warning('未查询到数据信息')
                return
            }
            this.table.data = result.data
            await this.$nextTick()
            this.contentLoading = false
        },  


        removeJurisd(data) {
            this.$confirm('此操作将永久删除该记录, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then( async() => {
                this.contentLoading = true
                let result = await this.$api.updateJurisdiction({
                    status: -2,
                    id: data.id
                })
                if(!result || result.status != 200) {
                    this.$warning(result.message || '删除失败')
                    this.contentLoading = false
                    return
                }
                this.table.data = this.table.data.filter(item => item.id != result.id)
                await this.$nextTick()
                this.contentLoading = false
                this.searchList(false)
            })
        },

        addJurisdirction() {

        }
    }
}

</script>

<style lang='scss' scoped>
.jurisdictionMgnt {

    .query-top {
        height: calc(100% - 55px);
    }

   .query-bottom {
        margin-top: 5px;
        padding: 10px;

        .query-item {
            display: flex;
            height: 40px;
            line-height: 40px;
            justify-content: space-between;
            align-items: center;

            > div {
                flex-grow: 1;
            }

            > span {
                font-size: 12px;
                white-space: nowrap;
                margin-right: 5px;
            }
        }
    }

}
</style>
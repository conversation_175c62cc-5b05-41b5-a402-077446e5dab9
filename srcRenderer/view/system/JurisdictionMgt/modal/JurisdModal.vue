<template>
    <PonyDialog v-model="modal.show" 
        :title="computedModalTitle" class="fense-modal" :width="800"
        :loading="modal.loading"
        @confirm="handleJurisdChange">

        <div class="header">
            <el-form inline :model="modal.data" :rules="modal.rules" ref="form" label-width="100px">
                <el-form-item label="归属上级：" prop="parentId">
                    <SelectTreeInput v-model="selectSuperior" 
                        ref="areaInput"
                        type="xiaquMgt" 
                        :condition="treeCondition"
                        placeholder="请选择"
                        title="请选择">
                    </SelectTreeInput>
                </el-form-item>
                <el-form-item label="名称：" prop="name">
                    <el-input v-model="modal.data.name"></el-input>
                </el-form-item>
                <el-form-item >
                    <el-button size="mini" type="primary" 
                    style="width: 100%;"
                    @click="autoLine">{{auto==false?'自动画围栏':'关闭自动画围栏'}}</el-button>
                </el-form-item>
            </el-form>
        </div>

        <div class="main">
            <div class="auto-line" v-show="auto">
                <autoAreaRange ref="autoAreaRange" @change="toRegionalRange"></autoAreaRange>
            </div>
            <DrawMap ref="drawmap" :autoLine="autoline"></DrawMap>
        </div>

    </PonyDialog>
</template>

<script>
// import SimpleMap from '@/view/monitor/leaflet/leafletMap/MaticsMapV2'
import autoAreaRange from './autoAreaRange'
import DrawMap from '@/view/monitor/components/DrawMap'
import SelectTreeInput from '@/components/common/SelectTreeInput';
export default {
    name: '',
    components: { 
        // SimpleMap,
        DrawMap,
        SelectTreeInput,
        autoAreaRange
    },
    data () {
        const defaultData = {
            id: '',
            parentId: null,
            level: '',          //  1
            name: "",
            shape: null,
            styleData: -1,      //  1
            radius: -1,
            areaId: '',     
            deptId: '',         //  1
            areaType: 1,
            status: '',
            remark: '',         //  1
            userId: '',         //    1
            fensePoint: [],
            
        };

        return {
            defaultData,

            selectSuperior: null,

            modal: {
                loading: false,
                show: false,
                data: JSON.parse(JSON.stringify(defaultData)),
                model: false,
                rules: {
                    name: [{ required: true, message: '请输入名称',trigger: 'blur' }],
                    parentId: [{ required: true, message: '请选择', trigger: 'change' }],
                }
            },
            auto:false,
            _extandLayer:null,
            autoPointList:[],
            autoline:false,
        };
    },

    computed: {
        computedModalTitle() {
            return this.modal.model?'修改辖区':'新增辖区'
        }
    },

    watch: {
        selectSuperior(newVal) {
            if (!newVal) return;
            Object.assign(this.modal.data, {
                parentId: newVal.value,
            })
        }  
    },

    mounted() {
           
    },

    methods: {
        async autoLine(){
            this.auto = !this.auto
             this.toRegionalRange({})
        },
        async  toRegionalRange(parmas){
            let _map = this.$refs['drawmap'].$refs['simpleMap']._map
            let result = await this.$refs['autoAreaRange'].showModal(parmas)
            if(!result) return
            this.autoPointList = result.pointList
            if(this._extandLayer) {
                    this._extandLayer.clearLayers()
                    this._extandLayer = null
                }
                this._extandLayer = result.result.addTo(_map)
                
                _map.fitBounds(this._extandLayer.getBounds())
                this._extandLayer.on('dblclick',(e)=>{
                    if(this._extandLayer) {
                    this._extandLayer.clearLayers()
                    this._extandLayer = null
                }
                })
        },
        handleJurisdChange() {
            this.$refs['form'].validate( async(res) => {
                if(!res) return
                let params = {
                    parentId:this.modal.data.parentId,
                    name:this.modal.data.name,
                    auto : this.auto
                }
                let fensePoint = []
                if(this.auto){
                    params.shape = 2,
                    params.radius = 0,
                    fensePoint = this.autoPointList

                }else{
                    let fensepoint = await this.$refs['drawmap'].submitCurrentLayer()
                    if(!fensepoint) return
                     params.shape= fensepoint.area_shape
                     fensePoint = fensepoint.fence_list
                     params.radius= fensepoint.range!=undefined?fensepoint.range : 0
                }
                
                params.fensePoint = fensePoint.map(item => {
                    return {
                        lat: item[0],
                        lng: item[1]
                    }
                })
                this.$emit('change', this.modal.model, Object.assign(this.modal.data, params))
                await this.$nextTick()
                this.auto = false
                this.modal.show = false
            })
        },



        async showModal(data) {
            this.modal.loading = true
            this.modal.data = JSON.parse(JSON.stringify(this.defaultData))
            await this.$nextTick()
            this.modal.show = true
            this.modal.model = Boolean(data)
            await this.$nextTick()
            this.$refs["drawmap"]?.clearMap();

            if(this.modal.model) {
                Object.assign(this.modal.data, data)

                let { fensePoint, shape, radius } = this.modal.data

                if(fensePoint && fensePoint.length) {
                    fensePoint = fensePoint.map(item => [item.lat, item.lng])
                    this.$refs['drawmap'].drawCurrentLayer(shape, {
                        radius: radius,
                        options: {},
                        latlng: fensePoint
                    })
                } 
                this.autoline = data.auto
                this.selectSuperior = await this.$refs['areaInput'].fillOtherProperty(data.parentId)
            } else {
                this.selectSuperior = null
            }   

            await this.$nextTick()
            this.modal.loading = false
        },





        treeCondition(treeNode) {
            if (treeNode.type < 3 ) {
                this.$warning('请选择到市级以下！')
                return false;
            }
            return true;
        },
    }
}

</script>

<style lang='scss' scoped>
.header {
    display: flex;
    align-items: center;
}
.main {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 380px;
    width: 100%;
    .auto-line{
    position: absolute;
    z-index: 999;
    width: 200px;
    height: 82%;
    left: 1px;
    border-radius: 5px;
    overflow: hidden;

    }
}
</style>
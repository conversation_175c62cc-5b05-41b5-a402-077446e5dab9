<template>
  <Layout :has-color="true" :contentLoading="table.loading" class="LockStatusQuery">
    <template slot="aside">
      <div class="query-top">
        <ElementTree type="vehicle" :expiredDisabled="false" ref="tree" :checkMode="true" @check="selectNodes">
        </ElementTree>
      </div>
      <div class="query-bottom bg bg--light box-shadow">
        <div class="query-item">
          <el-button type="primary" style="width: 100%" @click="queryTimelist()" :loading="table.loading">查询</el-button>
        </div>
      </div>
    </template>
    <template slot="query">
      <div class="query-item">
        <el-button size="mini" type="primary" @click="openDealModal()"
          v-if="hasPermission('carExpirationTime:change')">批量设置
        </el-button>
      </div>
      <div class="query-item">
        <el-button size="mini" type="primary" @click="exportDataInfo()" :loading="exportLoading">导出
        </el-button>
      </div>
      <div class="query-item">
        <span style="margin-right:10px">
          到期状态:
        </span>
        <el-select v-model="query.statusSelect" placeholder="车辆到期状态" filterable style="width: 120px; margin-right: 10px">
          <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </div>
      <div class="break-item"></div>
      <div>
        <el-pagination background small :pager-count="5" :current-page.sync="pager.current"
          layout="prev, pager, next, total" :page-size="pager.size" :total="pager.total">
        </el-pagination>
      </div>
    </template>

    <template slot="content">
      <el-table ref="breaktable" slot="content" class="el-table--ellipsis el-table--radius" border stripe
        highlight-current-row size="mini" :data="formatList" @sort-change="sortCurrentProp" height="100%"
        style="width: 100%" @selection-change="selectLine" :row-key="getRowsKey" @select-all="selectAll">
        <el-table-column type="selection" width="45" :reserve-selection="true"> </el-table-column>
        <el-table-column type="index" :index="(index) => index + 1 + pageStart" label="序号" width="70">
        </el-table-column>
        <el-table-column label="操作" width="70">
          <template slot-scope="{ row }">
            <el-button type="text" title="设置" size="mini" v-if="hasPermission('carExpirationTime:change')"
              @click="showDetail(row)">
              <i class="pony-iconv2 pony-xiugai"></i>
            </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="dept" label="部门" min-width="120" show-overflow-tooltip>
        </el-table-column>
        <el-table-column prop="plateNo" label="车牌号" min-width="80" show-overflow-tooltip>
        </el-table-column>
        <el-table-column prop="validStateView" label="到期状态" min-width="80" show-overflow-tooltip></el-table-column>
        <el-table-column prop="validDateView" label="到期时间" min-width="120" show-overflow-tooltip sortable="custom">
        </el-table-column>
      </el-table>
    </template>
    <pony-dialog v-model="dealModal.show" width="400" @confirm="batchAll" :loading="dealModal.loading">
      <span slot="title">{{ dealModal.title }}</span>
      <div style="width:100%;height:100px;">
        <span>到期时间：</span>
        <el-date-picker type="date" v-model="dealModal.validDate" style="width: 230px;margin-top:10px">
        </el-date-picker>
        <el-button size="mini" type="primary" @click="clearTime">清除</el-button>
      </div>
    </pony-dialog>
  </Layout>
</template>
<script>
import moment from "moment";
const ExportJsonExcel = require("js-export-excel");
export default {
  name: "carExpirationTime",
  components: {},
  data() {
    return {
      table: {
        loading: false,
        data: [],
      },
      query: {
        deptId: [],
        statusSelect: 0
      },
      pager: {
        current: 1,
        size: 30,
        total: 0,
      },
      options: [
        {
          value: 0,
          label: "全部",
        },
        {
          value: 1,
          label: "已到期",
        },
        {
          value: 2,
          label: "3天内到期",
        },
        {
          value: 3,
          label: "10天内到期",
        },
        {
          value: 4,
          label: "30天内到期",
        },
        {
          value: 5,
          label: "3个月内到期",
        },
        {
          value: 6,
          label: "未到期",
        },
      ],
      value: "",
      //   选中的数据
      selection: [],
      dealModal: {
        loading: false,
        show: false,
        title: "",
        vehicleId: "",
        validDate: "",
      },
      search: {
        dateBegin: moment()
          .startOf("day")
          .format("YYYY-MM-DD"),
        dateEnd: moment()
          .endOf("day")
          .format("YYYY-MM-DD"),
        expTime: moment().format("YYYY-MM-DD"),
      },
      currentNodes: [],
      exportLoading: false,
      selectAllStatus: false,
    };
  },

  computed: {
    pageStart() {
      return (this.pager.current - 1) * this.pager.size;
    },
    formatList() {
      return this.table.data.slice(this.pageStart, this.pageStart + this.pager.size)
    }
  },
  methods: {
    selectAll() {
      if (this.selectAllStatus) {
        this.$refs.breaktable.clearSelection()
        this.selectAllStatus = false
      } else {
        this.$refs.breaktable.clearSelection()
        this.table.data.forEach(item => {
          this.$refs.breaktable.toggleRowSelection(item)
        })
        this.selectAllStatus = true
      }

    },
    // toggleAllSelection()
    clearTime() {
      this.dealModal.validDate = ""
    },
    getRowsKey(row) {
      return row.vehicleId
    },
    //   初始查询
    // treeMounted($tree) {
    //     let rootNodes = $tree.data.map($tree.getNode);
    //     this.currentNodes = rootNodes[0].data.id;
    //     this.queryfacelist();
    // },

    // 查询
    async queryTimelist(noneQuery = true) {
      if (noneQuery && !this.query.deptId.length) {
        this.$warning("请选择部门!");
        return;
      }
      this.table.data = [];
      this.pager.total = 0;
      let params = {
        vehicleIdList: this.query.deptId,
        validState: this.query.statusSelect
      };
      this.table.loading = true;
      let res = await this.$api.getcarExpirationTime(params);
      if (!res || res.status !== 200) {
        this.$error(res.message || "请选择部门");
        this.table.loading = false;
        return;
      }
      if (!res.data || !res.data.length) {
        this.$warning("未查询到数据");
        this.table.loading = false;
        return;
      }
      this.table.data = res.data;
      this.pager.total = res.data.length;
      this.table.loading = false;
      this.$nextTick(() => {
        this.$refs["breaktable"].doLayout();
      });
    },
    //导出表格
    async exportDataInfo() {
      if (this.table.data.length == 0) {
        this.$warning("没有数据可以导出");
        return;
      }
      this.exportLoading = true
      let resData = !this.selection.length ? this.table.data : this.selection;
      let result = resData.map((item, index) => {
        return [
          index + 1,
          item.dept,
          item.plateNo,
          item.validStateView,
          item.validDateView,
        ];
      });
      let options = {
        fileName: `车辆服务到期记录表`,
        datas: [
          {
            sheetName: "车辆服务到期记录表",
            sheetData: result,
            sheetHeader: ["序号", "部门", "车牌号", "到期状态", "到期时间"],
            columnWidths: ["3", "20", "8", "8", "10"],
          },
        ],
      };
      ExportJsonExcel(options).saveExcel();
      this.exportLoading = false;
    },
    // 设置到期时间
    async showDetail(row) {
      this.dealModal.vehicleId = row.vehicleId;
      this.dealModal.validDate =
        row.validDateView == "-" ? null : row.validDateView;
      this.dealModal.show = true;
      this.dealModal.title = row.plateNo;
    },
    // 批量设置
    openDealModal() {
      if (this.selection.length === 0) {
        this.$warning("请勾选表格数据项");
        return;
      }
      this.dealModal.show = true;
      this.dealModal.title = "批量设置";
      this.dealModal.vehicleId = this.selection.map((item) => {
        return item.vehicleId;
      });
      this.dealModal.validDate = "";
    },
    // 设置
    async batchAll() {
      this.dealModal.loading = true;

      let result = await this.$api.changecarExpirationTime({
        vehicleIdList:
          this.dealModal.vehicleId instanceof Array
            ? this.dealModal.vehicleId
            : [this.dealModal.vehicleId],
        validDate:
          this.dealModal.validDate == "-" ? null : this.dealModal.validDate,
      });
      if (result.status === 200) {
        this.dealModal.loading = false;
        this.dealModal.show = false;
        this.$refs["breaktable"].clearSelection();
        this.selection = [];
        this.queryTimelist();
      } else {
        this.$error(result.message);
      }
    },
    // 表格可选择
    selectLine(selection) {
      this.selection = selection;
    },
    // 多选树
    selectNodes(current, { checkedNodes }) {
      let currentNodes = checkedNodes
        .filter((item) => item.type == 4)
        .map((item) => item.id);
      this.query.deptId = currentNodes;
    },
    //    表单排序
    sortCurrentProp({ column, prop, order }) {
      // 字符串排序
      this.table.data.sort((a, b) => {
        switch (order || "default") {
          case "default":
            return;
          case "ascending":
            if (a[prop] > b[prop]) return 1;
            if (a[prop] == b[prop]) return 0;
            if (a[prop] < b[prop]) return -1;
          case "descending":
            if (a[prop] > b[prop]) return -1;
            if (a[prop] == b[prop]) return 0;
            if (a[prop] < b[prop]) return 1;
        }
      });
      // 纯数字排序
      // this.table.data.sort((a, b) => {
      //     switch (order || 'default') {
      //         case 'default':
      //             return
      //         case 'ascending':
      //             return a[prop] - b[prop];
      //         case 'descending':
      //             return b[prop] - a[prop];
      //     }
      // })
    },
  },
  activated() {
    if (this.$route.query) {
      if (this.$route.query.statusSelect) {
        this.query.statusSelect = Number(this.$route.query.statusSelect)
        this.queryTimelist(false)
      }
     
    }
  }
};
</script>
<style lang="scss" scoped>
.LockStatusQuery {
  .query-top {
    // height: 100%;
    height: calc(100% - 65px);
  }

  .query-bottom {
    margin-top: 5px;
    // height: 125px;
    padding: 10px;

    .query-item {
      display: flex;
      height: 40px;
      line-height: 40px;
      justify-content: space-between;
      align-items: center;

      >span {
        font-size: 12px;
        white-space: nowrap;
        margin-right: 5px;
      }
    }
  }
}
</style>

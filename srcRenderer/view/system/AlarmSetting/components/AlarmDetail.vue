<template>
    <Layout tag="div" class="alarm-mgnt"
            :contentLoading="table.loading">

        <template slot="query">
            <div class="query-item">
                报警分类：
                <el-select v-model="queryList.show_type">
                    <el-option :value="-1" :label="$ct('filter.all')"></el-option>
                    <el-option :value="2" :label="$ct('filter.adas')"></el-option>
                    <el-option :value="1" :label="$ct('filter.dms')"></el-option>
                    <el-option :value="0" :label="$ct('filter.gps')"></el-option>
                    <el-option :value="4" :label="$ct('filter.vadas')"></el-option>
<!--                    <el-option :value="5" :label="$ct('filter.vdss')"></el-option>-->
                    <el-option :value="10" :label="$ct('filter.tbox_driver')"></el-option>
                    <el-option :value="9" :label="$ct('filter.tbox_fault')"></el-option>
                    <el-option :value="3" :label="$ct('filter.bussiness')"></el-option>
                    <el-option :value="7" :label="$ct('filter.cold')"></el-option>
                    <el-option :value="6" :label="$ct('filter.business')"></el-option>
                    <el-option :value="11" label="LOCK 电子锁"></el-option>
                    <el-option :value="12" label="CONTAINER 货箱识别"></el-option>
                    <el-option :value="13" label="Renewable 新能源"></el-option>
                    <el-option :value="14" label="AEB 制动"></el-option>



                </el-select>
            </div>
            <div class="query-item">
                是否关注：
                <el-select v-model="queryList.set_type" style="width: 100px">
                    <el-option :value="0" label="全部"></el-option>
                    <el-option :value="1" label="已关注"></el-option>
                    <el-option :value="2" label="未关注"></el-option>
                </el-select>
            </div>
            <div class="query-item">
                <el-input v-model="queryList.query_name" placeholder="请输入报警名称"></el-input>
            </div>
            <div class="break-item"></div>
            <div class="query-item" style="margin: 0">
                <!--                <el-button type="primary" @click="clearSettingRule">{{ $ct('unsetBtn') }}</el-button>-->
                <el-button type="primary" @click="checkChangeOrNot">{{ $ct('setBtn') }}</el-button>
            </div>
        </template>

        <template slot="content">
            <el-table
              ref="filterTable"
              class="el-table--ellipsis"
              border stripe highlight-current-row size="mini"
              :data="alarmViewList"
              height="100%" style="width: 100%">
                <el-table-column type="index" width="50"
                                 :label="$ct('common.index')"></el-table-column>
                <el-table-column type="selection" width="55" align="center"></el-table-column>
                <el-table-column prop="warn_desc" align="left" header-align="center"
                                 :label="$ct('alarmTable.alarmName')" min-width="130"></el-table-column>
                <el-table-column align="left" header-align="center" :label="$ct('alarmTable.alarmRemark')"
                                 min-width="300" show-overflow-tooltip>
                    <template slot-scope="scope">
                        {{ scope.row.detail ? scope.row.detail.introduce : ''}}
                    </template>
                </el-table-column>
                <el-table-column align="center" :label="$ct('alarmTable.alarmIcon')" min-width="60">
                    <template slot-scope="scope">
                        <span :class="scope.row.detail ? scope.row.detail.className : ''"></span>
                    </template>
                </el-table-column>
                <el-table-column prop="typedesc" align="center" :label="$ct('query.type')"
                                 min-width="130"></el-table-column>

                <el-table-column align="center" :label="$ct('common.warnIngnoe')" min-width="60">
                    <template slot-scope="scope">
                        <el-switch v-model="scope.row.warn_ignore"
                                   @change="change(scope.row, 'warn_ignore')"></el-switch>
                    </template>
                </el-table-column>
                <el-table-column align="center" :label="$ct('common.warnLight')" min-width="60">
                    <template slot-scope="scope">
                        <el-switch v-model="scope.row.warn_bling" :disabled="!scope.row.warn_ignore" @change="change(scope.row, 'warn_bling')"></el-switch>
                    </template>
                </el-table-column>
                <el-table-column align="center" label="是否提示声音" min-width="80">
                    <template slot-scope="scope">
                        <el-switch v-model="scope.row.warn_sound_on" :disabled="!scope.row.warn_ignore"
                                   @change="change(scope.row, 'warn_sound_on')"></el-switch>
                    </template>
                </el-table-column>
                <el-table-column align="center" prop="warn_sound" min-width="100" :label="$ct('common.warnSound')">
                    <template slot-scope="scope">
                        {{ scope.row.warn_sound ? scope.row.warn_sound == 'human.mp3' ? '人声': scope.row.warn_sound : $ct('common.default') }}
                    </template>
                </el-table-column>
                <el-table-column align="center" prop="warn_repeat" :label="$ct('common.warnRepart')"
                                 min-width="60"></el-table-column>
                <el-table-column align="center" prop="warn_interval" :label="$ct('common.warnInter')"
                                 min-width="60"></el-table-column>
                <!-- v-if="rootUser"  -->
                <el-table-column align="center" :label="$ct('common.warnClass')" width="150" min-width="70"
                                 v-if="rootUser">
                    <template slot-scope="scope">
                        {{ scope.row.detail ? scope.row.detail.className : '' }}
                    </template>
                </el-table-column>
                <!-- v-if="rootUser" -->
                <el-table-column prop="warn_code" align="center" :label="$ct('alarmTable.alarmType')" min-width="60"
                                 v-if="rootUser"></el-table-column>
                <!-- v-if="rootUser" -->
                <el-table-column :label="$ct('alarmTable.operate')" min-width="60" align="center" v-if="rootUser">
                    <template slot-scope="scope">
                        <i class="cur pony-iconv2 pony-xiugai" @click="modifyAlarmDetail(scope.row)"></i>
                    </template>
                </el-table-column>
            </el-table>
        </template>

        <!--修改报警弹窗-->
        <PonyDialog width="550" :title="$ct('alarmModal.title')"
                    :contentMaxHeight="410"
                    v-model="table.show"
                    @confirm="modifyAlarm">
            <el-form ref="form" :model="currentAlarm" size="mini" label-width="85px">
                <el-form-item :label="$ct('alarmModal.name')">
                    <el-row type="flex" justify="end">
                        <el-col :span="12">
                            <el-input v-model="currentAlarm.name"></el-input>
                        </el-col>
                        <el-col :span="12" style="padding-left: 20px;">
                            {{ TimeFormat(currentAlarm.createTime) }}
                        </el-col>
                    </el-row>
                </el-form-item>
                <el-form-item :label="$ct('alarmModal.alarmIcon')">
                    <el-input v-model="currentAlarm.className"></el-input>
                </el-form-item>
                <el-form-item :label="$ct('alarmModal.alarmType')">
                    <el-radio-group v-model="currentAlarm.type">
                        <el-radio :label="0">GPS</el-radio>
                        <el-radio :label="1">DMS</el-radio>
                        <el-radio :label="2">ADAS</el-radio>
                        <el-radio :label="4">VADAS</el-radio>
<!--                        <el-radio :label="5">VDSS</el-radio>-->
                        <el-radio :label="7">COLD CHAIN</el-radio>
                        <el-radio :label="9">TBOX-FAULT</el-radio>
                        <el-radio :label="10">TBOX-DRIVER</el-radio>
                        <el-radio :label="3">TRUCK</el-radio>

                        <el-radio :label="8">BSD</el-radio>
                        <el-radio :label="11">LOCK 电子锁</el-radio>
                        <el-radio :label="12">CONTAINER 货箱识别</el-radio>
                        <el-radio :label="13">Renewable 新能源</el-radio>
                        <el-radio :label="14">AEB 制动</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item :label="$ct('alarmModal.alarmSort')">
                    <el-input v-model="currentAlarm.sort"></el-input>
                </el-form-item>
                <el-form-item :label="$ct('alarmModal.remark')">
                    <el-input v-model="currentAlarm.remark"></el-input>
                </el-form-item>
                <el-form-item :label="$ct('alarmModal.alarmInter')">
                    <el-input type="textarea" v-model="currentAlarm.introduce"></el-input>
                </el-form-item>
            </el-form>
        </PonyDialog>

        <!--批量设置弹窗-->
        <PonyDialog width="350" :title="$ct('common.title')"
                    :contentMaxHeight="410"
                    v-model="table.setting"
                    @confirm="modifyAlarmRule">
            <el-form ref="form" :model="currentRule" size="mini" label-width="100px">
                <el-form-item :label="$ct('common.warnIngnoe')">
                    <el-switch v-model="currentRule.warn_ignore"></el-switch>
                </el-form-item>
                <el-form-item :label="$ct('common.warnLight')">
                    <el-switch :disabled="!currentRule.warn_ignore" v-model="currentRule.warn_bling"></el-switch>
                </el-form-item>
                <el-form-item label="是否提示声音">
                    <el-switch :disabled="!currentRule.warn_ignore" v-model="currentRule.warn_sound_on"></el-switch>
                </el-form-item>
                <el-form-item :label="$ct('common.warnSound')">
                    <el-select v-model="currentRule.warn_sound"
                               placeholder="请选择" style="width: calc(100% - 40px)"
                               :disabled="!currentRule.warn_sound_on">
                        <el-option
                          v-for="(item, index) in currentSoundList"
                          :key="index"
                          :label="item.name"
                          :value="item.key">
                        </el-option>
                    </el-select>
                    <el-button type="primary" icon="pony-iconv2 pony-dianhua" title="试听"
                               circle @click="listenAlarmSounds">
                    </el-button>
                </el-form-item>
                <el-form-item :label="$ct('common.warnRepart')">
                    <el-input-number v-model="currentRule.warn_repeat" :disabled="!currentRule.warn_sound_on"
                                     :min="1" :max="5" style="width: 100%">
                    </el-input-number>
                </el-form-item>
                <el-form-item :label="$ct('common.warnInter')">
                    <el-input-number v-model="currentRule.warn_interval" :disabled="!currentRule.warn_sound_on"
                                     :min="1" :max="8" style="width: 100%">
                    </el-input-number>
                </el-form-item>
            </el-form>

            <audio style="display: none"
                   ref="alarmReadio"
                   :src="currentSound">
            </audio>
        </PonyDialog>
    </Layout>
</template>

<script>
import PinyinMatch from 'pinyin-match'
import {mapState} from 'vuex'
import {alarmSoundSetting} from '../util/customSetting'

export default {
    name: 'alarmSetting',
    components: {},
    data() {
        return {
            queryList: {
                show_type: -1,
                set_type: 0,
                query_name: '',
            },
            currentSoundList: alarmSoundSetting,
            table: {
                loading: false,
                setting: false,
                show: false,
                list: [],
            },
            currentAlarm: {},
            currentRule: {
                operate_type: 1,                        // 0 新增  1 修改  2移除
                user_id: '-1',
                warn_ignore: true,                      // 是否忽略
                warn_popup: false,                      // 是否弹框
                warn_bling: false,                      // 是否闪光
                warn_sound_on: false,                    // 是否提示声音
                warn_sound: 'default.wav',              // 提示声音
                warn_repeat: 2,                         // 提示音重复次数
                warn_interval: 1,                       // 提示音间隔
            },
            alarmTypeDesc: {
                0: 'GPS 北斗定位系统',
                1: 'DMS 驾驶员检测系统',
                2: 'ADAS 防撞预警系统',
                3: '渣土报警',
                4: 'VADAS 车身姿态检测系统',
                // 5: 'VDSS 事件',
                6: 'BUSSINESS 业务',
                7: '冷链报警',
                8: 'BSD 报警',
                9: 'TBOX-FAULT 车辆故障诊断系统',
                10: 'TBOX-DRIVER 驾驶行为检测系统',
                11:'LOCK 电子锁',
                12:'CONTAINER 货箱识别',
                13:'Renewable 新能源',
                14:'AEB 制动'
            }
        }
    },

    computed: {
        ...mapState('auth', ['userInfo']),
        currentSound() {
            return `./static/radio/${this.currentRule.warn_sound}`
        },
        rootUser() {
            return this.userInfo.login_name == 'admin'
        },
        //  报警类型过滤
        alarmTypeList() {
            return this.queryList.show_type == -1 ? this.table.list :
              this.table.list.filter(alarm => alarm.type == this.queryList.show_type)
        },
        //  过滤配置
        setTypeList() {
            return !this.queryList.set_type ? this.alarmTypeList :
              this.queryList.set_type == 1 ? this.alarmTypeList.filter(item => item.warn_ignore) :
                this.alarmTypeList.filter(item => !item.warn_ignore)
        },
        //  过滤模糊查询
        alarmViewList() {
            if (!this.queryList.query_name) return this.setTypeList
            return this.setTypeList.filter(item => PinyinMatch.match(item['warn_desc'], this.queryList.query_name))
        }
    },
    watch: {
        'currentRule.warn_ignore': function (val) {
            this.currentRule.warn_bling = val
            this.currentRule.warn_sound_on = val
        }
    },
    methods: {
        async change(item, type) {
            let batch_operate_obj = []
            batch_operate_obj.push({
                warn_code_list: [item.warn_code],
                warn_type: 0
            })
            if (type === 'warn_ignore') {
                if (!item.warn_ignore) {
                    item.warn_bling = false
                    item.warn_sound_on = false
                }
            }
            let data = {
                batch_operate_obj: batch_operate_obj,
                operate_type: 1,
                user_id: '-1',
                warn_popup: item.warn_popup,
                warn_sound_on: item.warn_sound_on,
                warn_sound: item.warn_sound,
                warn_repeat: item.warn_repeat,
                warn_interval: item.warn_interval,
                warn_ignore: item.warn_ignore,
                warn_bling: item.warn_bling
            }
            let setResult = await this.$api.operateUserWarnConfigv2(data)
            if (!setResult || setResult.status != 200) {
                this.$error(setResult.message || this.$ct('message.queryError'))
                return
            }
            this.$success('配置成功')
            this.$store.commit('switch/changeBoxRuleState')
        },
        // 试听
        listenAlarmSounds() {
            this.$refs.alarmReadio.play()
        },
        // 批量设置
        checkChangeOrNot() {
            let checkedList = this.$refs.filterTable.selection
            if (!checkedList.length) {
                this.$warning(this.$ct('message.noQuery'))
                return
            }
            this.table.setting = true
        },
        // 批量设置提交
        async modifyAlarmRule() {
            let checkedList = this.$refs.filterTable.selection
            let checkedCode = checkedList.map(item => +item.warn_code)
            let parmas = JSON.parse(JSON.stringify(this.currentRule))
            parmas.batch_operate_obj = [
                {
                    warn_type: 0,
                    warn_code_list: checkedCode
                }
            ]
            let setResult = await this.$api.operateUserWarnConfigv2(parmas)
            if (!setResult && setResult.status != 200) {
                this.$error(setResult.message || this.$ct('message.queryError'))
                return
            }
            checkedList.forEach(item => {
                item.id = (new Date()).getTime()
                Object.assign(item, this.currentRule)
            })
            this.table.setting = false
            this.$refs.filterTable.clearSelection()
            this.$success('配置成功')
            this.$store.commit('switch/changeBoxRuleState')
        },
        // async clearSettingRule() {
        //     let checkedList = this.$refs.filterTable.selection
        //     let checkedCode = checkedList.map(item => +item.warn_code)
        //     if(!checkedCode.length) {
        //         this.$warning(this.$ct('message.noQuery'))
        //         return
        //     }
        //     let parmas = {
        //         operate_type: 2,
        //         user_id: '-1',
        //         batch_operate_obj: [
        //             {
        //                 warn_type: 0,
        //                 warn_code_list: checkedCode
        //             }
        //         ]
        //     }
        //     let setResult = await this.$api.operateUserWarnConfigv2(parmas)
        //     if (!setResult && setResult.status != 200) {
        //         this.$error(setResult.message || this.$ct('message.queryError'))
        //         return
        //     }
        //     let option = {
        //         id: null, warn_ignore: false,  warn_popup: false,
        //         warn_sound: null, warn_repeat: 0, warn_interval: 0,
        //     }
        //     checkedList.forEach(item => {
        //         Object.assign(item, option)
        //     })
        //     this.$refs.filterTable.clearSelection()
        //     this.$success('取消配置成功')
        //     this.$store.commit('switch/changeBoxRuleState')
        // },
        // 打开修改报警弹窗
        modifyAlarmDetail(row) {
            this.currentAlarm = JSON.parse(JSON.stringify(row.detail))
            this.table.show = true
        },
        // 修改报警提交
        async modifyAlarm() {
            if (!this.currentAlarm.id) return
            let result = JSON.parse(JSON.stringify(this.currentAlarm))
            let changeResult = await this.$api.updateSysAlarmType(result)
            if (!changeResult || changeResult.RS != 1) {
                this.$error(changeResult.Reason || this.$ct('message.queryError'))
                return
            }
            this.$success(changeResult.Reason)
            this.$nextTick(() => {
                this.table.show = false
                let current = this.table.list.find(alarm => alarm.warn_code == this.currentAlarm.code)
                Object.assign(current.detail, result)
            })
        },
        // 获取数据
        async setAlarmDetailList(list) {
            let result = await this.$api.getSysAlarmTypes({alarm_type: 5})
            let alarmObj = {}
            result.alarm_type_list.forEach(item => {
                alarmObj[item.code] = item
            })
            list.forEach(item => {
                if (alarmObj[item.warn_code]) {
                    item.detail = alarmObj[item.warn_code]
                    item.type = alarmObj[item.warn_code].type
                    item.typedesc = this.alarmTypeDesc[item.type]
                }
            })
            this.table.list = list.filter(item => item.type != 5)
            this.$nextTick(() => {
                this.$refs['filterTable'].doLayout()
            })
        }
    }
}

</script>

<style lang='scss' scoped>
.el-radio-group {
    .el-radio {
        line-height: 25px;
    }
}
</style>

<template>
    <Layout tag="div" class="alarm-mgnt"
        :contentLoading="table.loading">

        <template slot="query">
            <el-select class="query-item" v-model="queryList.set_type">
                <el-option :value="0" :label="$ct('setting.all')"></el-option>
                <el-option :value="1" :label="$ct('setting.yesSet')"></el-option>
                <el-option :value="2" :label="$ct('setting.noSet')"></el-option>
            </el-select>
            <div class="query-item">
                <el-input v-model="queryList.query_name" :placeholder="$ct('query.message')"></el-input>
            </div>
            <div class="break-item"></div>
            <div class="query-item" style="margin: 0">
                <el-button type="primary" @click="clearSettingRule">{{ $ct('unsetBtn') }}</el-button>
                <el-button type="primary" @click="checkChangeOrNot">{{ $ct('setBtn') }}</el-button>
            </div>
        </template>

        <template slot="content">
            <el-table 
                ref="filterTable"
                class="el-table--ellipsis"
                border stripe highlight-current-row size="mini" 
                :data="formatViewList" 
                height="100%" style="width: 100%">
                <el-table-column type="index" width="50" :index="(index) => index + 1 + pageStart" :label="$ct('common.index')" ></el-table-column>
                <el-table-column type="selection" width="55" align="center"></el-table-column>

                <el-table-column prop="warn_desc" align="left" header-align="center" :label="$ct('common.bussType')" width="250"></el-table-column>
               
                <el-table-column align="center" :label="$ct('common.warnPopup')">
                    <template slot-scope="{row}">
                        <el-switch v-model="row.warn_popup" @change="setConfig($event,row,'warn_popup')"></el-switch>
                    </template>
                </el-table-column>
                <el-table-column align="center" :label="$ct('common.soundWarn')">
                    <template slot-scope="{row}">
                        <el-switch v-model="row.warn_ignore" @change="setConfig($event,row,'warn_ignore')"></el-switch>
                    </template>
                </el-table-column>

                 <el-table-column align="center" prop="warn_sound" width="180" :label="$ct('common.warnSound')">
                    <template slot-scope="scope">
                        {{ scope.row.warn_sound ? scope.row.warn_sound == 'human.mp3' ? '人声': scope.row.warn_sound : $ct('common.default') }}
                    </template>
                </el-table-column>
                <el-table-column align="center" prop="warn_repeat" :label="$ct('common.warnRepart')">
                    <template slot-scope="{row}">
                        <el-input-number v-model="row.warn_repeat" :min="1" :max="10" @change="setConfig($event,row,'warn_repeat')"></el-input-number>
                    </template>
                </el-table-column>
                <el-table-column align="center" prop="warn_interval" :label="$ct('common.warnInter')">
                    <template slot-scope="{row}">
                        <el-input-number v-model="row.warn_interval" :min="1" :max="10" @change="setConfig($event,row,'warn_interval')"></el-input-number>
                    </template>
                </el-table-column>
            </el-table>
        </template>

        <template slot="footer">
            <el-pagination background small
                slot="page"
                :current-page.sync="table.page"
                :page-size="table.size"
                layout="prev, pager, next, total"
                :total="alarmViewList.length">
            </el-pagination>
        </template>
        <PonyDialog width="350" :title="$ct('common.title')" 
            :contentMaxHeight="410"
            v-model="table.setting"
            @confirm="modifyAlarmRule">
            <el-form ref="form" :model="currentRule" size="mini" label-width="90px">
                <el-form-item :label="$ct('common.warnPopup')">
                    <el-switch v-model="currentRule.warn_popup"></el-switch>
                </el-form-item>
                <el-form-item :label="$ct('common.soundWarn')">
                    <el-switch v-model="currentRule.warn_ignore"></el-switch>
                </el-form-item>
                <el-form-item :label="$ct('common.warnSound')">
                    <el-select v-model="currentRule.warn_sound" 
                        placeholder="请选择" style="width: calc(100% - 40px)">
                        <el-option
                            v-for="(item, index) in currentSoundList"
                            :key="index"
                            :label="item.name"
                            :value="item.key">
                        </el-option>
                    </el-select>
                    <el-button type="primary" icon="pony-iconv2 pony-dianhua" title="试听" 
                        circle @click="listenAlarmSounds">
                    </el-button>
                </el-form-item>
                <el-form-item :label="$ct('common.warnRepart')">
                    <el-input-number v-model="currentRule.warn_repeat" 
                        :min="1" :max="5" style="width: 100%">
                    </el-input-number>
                </el-form-item>
                <el-form-item :label="$ct('common.warnInter')">
                    <el-input-number v-model="currentRule.warn_interval" 
                        :min="1" :max="8" style="width: 100%">
                    </el-input-number>
                </el-form-item>
            </el-form>

            <audio style="display: none"
                ref="alarmReadio" 
                :src="currentSound">
            </audio>
        </PonyDialog>

    </Layout>
</template>

<script>
import PinyinMatch from 'pinyin-match'
import { alarmSoundSetting } from '../util/customSetting'

export default {
    name: 'alarmSetting',
    components: {  },
    data () {
        return {
            table: {
                loading: false,
                setting: false,
                show: false,
                list: [
                    // { 
                    //     id: "1b9ce6bb-62e7-4901-b9a3-7b3cb22f58be",
                    //     warn_desc: "开锁前打卡提醒",
                    //     warn_popup: true,  //是否弹框
                    //     warn_ignore: true,   //声音提示
                    //     warn_sound: "default.wav",
                    //     warn_interval: 1,
                    //     warn_repeat: 3,
                    //     warn_code: "before_unlock_approval",
                    //     warn_type: 2,
                    // },
                ],
                defaultList:[],
                page: 1,
                size: 30,
            },
            currentRule: {
                operate_type: 1,                        // 0 新增  1 修改  2移除
                user_id: '-1',
                warn_ignore: false,                      // 是否声音提示
                warn_popup: false,                      // 是否弹框
                warn_bling: false,                      // 是否闪光
                warn_sound: 'default.wav',              // 提示声音
                warn_repeat: 2,                         // 提示音重复次数
                warn_interval: 1,                       // 提示音间隔
            },
            currentSoundList: alarmSoundSetting,

            queryList: {
                set_type: 0,
                query_name: '',
            }
        };
    },
    
    beforeCreate(){
        this.$options._i18Name = 'alarmSetting'
    },

    computed: {
        currentSound() {
            return `./static/radio/${ this.currentRule.warn_sound }`
        },
        //  过滤配置
        setTypeList() {
            return !this.queryList.set_type ? this.table.list :
                this.queryList.set_type == 1? this.table.list.filter(item => item.id) :
                    this.table.list.filter(item => !item.id)
        },
        //  过滤模糊查询
        alarmViewList() {
            return this.queryList.query_name? 
                this.setTypeList.filter(item => PinyinMatch.match(item.warn_desc, this.queryList.query_name)):this.setTypeList
        },
        pageStart() {
            return (this.table.page - 1) * this.table.size
        },
        formatViewList() {
            return this.alarmViewList.slice(this.pageStart, this.pageStart + this.table.size)
        }
    },

    mounted() {

    },

    methods: {
        listenAlarmSounds() {
            this.$refs.alarmReadio.play()
        },
        async setConfig(event,row,type){
            let {warn_ignore,warn_popup,warn_repeat,warn_interval} = row
            let params = {
                operate_type: 1,                        // 0 新增  1 修改  2移除
                user_id: '-1',
                warn_ignore,                      // 是否声音提示
                warn_popup,                      // 是否弹框
                warn_bling: false,                      // 是否闪光
                warn_sound: 'default.wav',              // 提示声音
                warn_repeat,                         // 提示音重复次数
                warn_interval, 
                batch_operate_obj:[
                    {
                        warn_type: 2,
                        warn_code_list: [row.warn_code]
                    }
                ]
                
            }
            this.table.loading = true
            let setResult = await this.$api.operateUserWarnConfigv2(params)
            if (!setResult && setResult.status != 200) {
                this.$error(setResult.message || this.$ct('message.queryError'))
                this.table.loading = false
                let lastState = this.table.defaultList.find(item=>item.id == row.id)
                Object.assign(row,lastState)
                return
            }
            let lastState = this.table.defaultList.find(item=>item.id == row.id)
            row.id = (new Date()).getTime()
            Object.assign(lastState,row)
            // Object.assign(row, this.currentRule)
            this.table.loading = false
            this.$success('配置成功')
            this.$store.commit('switch/changeBoxRuleState')
        },
        async modifyAlarmRule() {
            let checkedList = this.$refs.filterTable.selection
            let checkedCode = checkedList.map(item => +item.warn_code)
            let parmas = JSON.parse(JSON.stringify(this.currentRule))
            parmas.batch_operate_obj = [
                {
                    warn_type: 2,
                    warn_code_list: checkedCode
                }
            ]
            this.table.loading = true
            let setResult = await this.$api.operateUserWarnConfigv2(parmas)
            if (!setResult && setResult.status != 200) {
                this.$error(setResult.message || this.$ct('message.queryError'))
                this.table.loading = false

                return
            }
            checkedList.forEach(item => {
                item.id = (new Date()).getTime()
                Object.assign(item, this.currentRule)
            })
            this.table.loading = false
            this.table.setting = false
            this.$refs.filterTable.clearSelection()
            this.$success('配置成功')
            this.$store.commit('switch/changeBoxRuleState')
        },
        async setAlarmDetailList(list) {
            this.table.list = list
            this.table.defaultList = list
            
            this.$nextTick(() => {
                this.$refs['filterTable'].doLayout()
            })
        },
         async clearSettingRule() {
            let checkedList = this.$refs.filterTable.selection
            let checkedCode = checkedList.map(item => +item.warn_code)
            if(!checkedCode.length) {
                this.$warning(this.$ct('message.noQuery'))
                return
            }
            let parmas = {
                operate_type: 2,                    
                user_id: '-1',
                batch_operate_obj: [
                    {
                        warn_type: 2,
                        warn_code_list: checkedCode
                    }
                ]
            }
            let setResult = await this.$api.operateUserWarnConfigv2(parmas)
            if (!setResult && setResult.status != 200) {
                this.$error(setResult.message || this.$ct('message.queryError'))
                return
            }
            let option = { 
                id: null, warn_ignore: false,  warn_popup: false,                      
                warn_sound: null, warn_repeat: 0, warn_interval: 0,                       
            }
            checkedList.forEach(item => {
                Object.assign(item, option)
            })
            this.$refs.filterTable.clearSelection()
            this.$success('取消配置成功')
            this.$store.commit('switch/changeBoxRuleState')
        },
         checkChangeOrNot() {
            let checkedList = this.$refs.filterTable.selection
            if(!checkedList.length) {
                this.$warning(this.$ct('message.noQuery'))
                return
            }
            this.table.setting = true
        },
    }
}

</script>

<style lang='scss' scoped>

</style>
<template>
    <Layout tag="div" class="alarm-mgnt"
        :contentLoading="table.loading">
       
        <template slot="query">
            <el-select class="query-item" v-model="queryList.set_type">
                <el-option :value="0" :label="$ct('setting.all')"></el-option>
                <el-option :value="1" :label="$ct('setting.yesSet')"></el-option>
                <el-option :value="2" :label="$ct('setting.noSet')"></el-option>
            </el-select>
            <div class="query-item">
                <el-input v-model="queryList.query_name" :placeholder="$ct('query.message')"></el-input>
            </div>
            <div class="break-item"></div>
            <div class="query-item" style="margin: 0">
                <!-- <el-button type="primary" @click="clearSettingRule">{{ $ct('unsetBtn') }}</el-button> -->
                <el-button type="primary" @click="checkChangeOrNot">{{ $ct('setBtn') }}</el-button>
            </div>
        </template>

        <template slot="content">
            <el-table 
                ref="filterTable"
                class="el-table--ellipsis"
                border stripe highlight-current-row size="mini" 
                :data="formatViewList" 
                height="100%" style="width: 100%">
                <el-table-column type="index" width="50" :index="(index) => index + 1 + pageStart" :label="$ct('common.index')" ></el-table-column>
                <el-table-column type="selection" width="55" align="center"></el-table-column>
                <el-table-column prop="warn_desc" align="left" header-align="center" :label="$ct('eventTable.name')" width="250"></el-table-column>
                <el-table-column align="center" :label="$ct('eventTable.eventIcon')" width="100">
                    <template slot-scope="scope">
                        <span :class="scope.row.detail.remark"></span>
                    </template>
                </el-table-column>
                <el-table-column align="center" :label="$ct('common.warnIngnoe')">
                      <template slot-scope="scope">
                        <el-switch v-model="scope.row.warn_ignore"
                                   @change="change(scope.row, 'warn_ignore')"></el-switch>
                    </template>
                </el-table-column>
                <el-table-column align="center" :label="$ct('common.warnLight')">
                     <template slot-scope="scope">
                        <el-switch v-model="scope.row.warn_bling" :disabled="!scope.row.warn_ignore" @change="change(scope.row, 'warn_bling')"></el-switch>
                    </template>
                </el-table-column>
                <el-table-column align="center" :label="$ct('common.warnPopup')">
                   
                    <template slot-scope="scope">
                        <el-switch v-model="scope.row.warn_popup" :disabled="!scope.row.warn_ignore"
                                   @change="change(scope.row, 'warn_popup')"></el-switch>
                    </template>
                </el-table-column>
                 <el-table-column align="center" label="是否提示声音" min-width="80">
                    <template slot-scope="scope">
                        <el-switch v-model="scope.row.warn_sound_on" :disabled="!scope.row.warn_ignore"
                                   @change="change(scope.row, 'warn_sound_on')"></el-switch>
                    </template>
                </el-table-column>
                  <el-table-column align="center" prop="warn_sound" width="180" :label="$ct('common.warnSound')">
                    <template slot-scope="scope">
                        {{ scope.row.warn_sound ? scope.row.warn_sound == 'human.mp3' ? '人声': scope.row.warn_sound : $ct('common.default') }}
                    </template>
                </el-table-column>
                <el-table-column align="center" prop="warn_repeat" :label="$ct('common.warnRepart')"></el-table-column>
                <el-table-column align="center" prop="warn_interval" :label="$ct('common.warnInter')"></el-table-column>
                <el-table-column align="center" :label="$ct('common.warnClass')" width="150" v-if="rootUser">
                    <template slot-scope="scope">
                        {{ scope.row.detail.remark }}
                    </template>
                </el-table-column>
                <el-table-column prop="warn_code" align="center" :label="$ct('eventTable.eventType')" v-if="rootUser"></el-table-column>

            </el-table>
        </template>

        <template slot="footer">
            <el-pagination background small
                :current-page.sync="table.page"
                :page-size="table.size"
                layout="prev, pager, next, total"
                :total="alarmViewList.length">
            </el-pagination>
        </template>

        <PonyDialog width="350" :title="$ct('common.title')"

            :contentMaxHeight="410"
            v-model="table.setting"
            @confirm="modifyAlarmRule">
            <el-form ref="form" :model="currentRule" size="mini" label-width="100px">
                <el-form-item :label="$ct('common.warnIngnoe')">
                    <el-switch v-model="currentRule.warn_ignore"></el-switch>
                </el-form-item>
                <el-form-item :label="$ct('common.warnPopup')">
                    <el-switch v-model="currentRule.warn_popup" :disabled="!currentRule.warn_ignore"></el-switch>
                </el-form-item>
                <el-form-item :label="$ct('common.warnLight')">
                    <el-switch v-model="currentRule.warn_bling" :disabled="!currentRule.warn_ignore"></el-switch>
                </el-form-item>
                   <el-form-item label="是否提示声音">
                    <el-switch :disabled="!currentRule.warn_ignore" v-model="currentRule.warn_sound_on"></el-switch>
                </el-form-item>
                <el-form-item :label="$ct('common.warnSound')">
                    <el-select v-model="currentRule.warn_sound" 
                        placeholder="选择" style="width: calc(100% - 40px)">
                        <el-option
                            v-for="(item, index) in currentSoundList"
                            :key="index"
                            :label="item.name"
                            :value="item.key">
                        </el-option>
                    </el-select>
                    <el-button type="primary" icon="pony-iconv2 pony-dianhua" title="试听" 
                        circle @click="listenAlarmSounds">
                    </el-button>
                </el-form-item>
                <el-form-item :label="$ct('common.warnRepart')">
                    <el-input-number v-model="currentRule.warn_repeat" 
                        :min="1" :max="5" style="width: 100%">
                    </el-input-number>
                </el-form-item>
                <el-form-item :label="$ct('common.warnInter')">
                    <el-input-number v-model="currentRule.warn_interval" 
                        :min="1" :max="8" style="width: 100%">
                    </el-input-number>
                </el-form-item>
            </el-form>
            
            <audio style="display: none"
                ref="alarmReadio" 
                :src="currentSound">
            </audio>
        </PonyDialog>

    </Layout>
</template>

<script>
import PinyinMatch from 'pinyin-match'
import { mapState } from 'vuex'
import { getAlarmMapIcon } from '../../../monitor/util/monitorUtil'
import { alarmSoundSetting } from '../util/customSetting'
export default {
    name: 'alarmSetting',
    components: { },
    data () {
        return {
            queryList: {
                set_type: 0,
                query_name: '',
            },

            currentSoundList: alarmSoundSetting,

            table: {
                loading: false,
                setting: false,
                list: [],
                page: 1,
                size: 30,
            },

            currentAlarm: {},

            currentRule: {
                operate_type: 1,                        // 0 新增  1 修改  2移除
                user_id: '-1',
                warn_ignore: true,                      // 是否忽略
                warn_popup: false,                      // 是否弹框
                warn_bling: false,                      // 是否闪光
                warn_sound: 'default.wav',              // 提示声音
                warn_repeat: 2,                         // 提示音重复次数
                warn_interval: 1,                       // 提示音间隔
                warn_sound_on:false                    //是否提示声音
            }
        };
    },

    computed: {
        ...mapState('auth', ['userInfo']),

        currentSound() {
            return `./static/radio/${ this.currentRule.warn_sound }`
        },

        rootUser() {
            return this.userInfo.login_name == 'admin'
        },
        //  过滤配置
        setTypeList() {
            return !this.queryList.set_type ? this.table.list :
                this.queryList.set_type == 1? this.table.list.filter(item => item.warn_ignore)  :
                    this.table.list.filter(item => !item.warn_ignore) 
        },
        //  过滤模糊查询
        alarmViewList() {
            return this.queryList.query_name? 
                this.setTypeList.filter(item => PinyinMatch.match(item.warn_desc, this.queryList.query_name)):this.setTypeList
                
        },
        pageStart() {
            return (this.table.page - 1) * this.table.size
        },
        formatViewList() {
            return this.alarmViewList.slice(this.pageStart, this.pageStart + this.table.size)
        }
    },

    mounted() {
        
    },
    watch: {
        'currentRule.warn_ignore': function (val) {
            this.currentRule.warn_bling = val
            this.currentRule.warn_sound_on = val
            this.currentRule.warn_popup = val
        }
    },

    methods: {
        //切换按钮
        async change(item, type) {
            let batch_operate_obj = []
            batch_operate_obj.push({
                warn_code_list: [item.warn_code],
                warn_type: 1
            })
            if (type === 'warn_ignore') {
                if (!item.warn_ignore) {
                    item.warn_bling = false
                    item.warn_sound_on = false
                    item.warn_popup = false;
                }
            }
            let data = {
                batch_operate_obj: batch_operate_obj,
                operate_type: 1,
                user_id: '-1',
                warn_popup: item.warn_popup,
                warn_sound_on: item.warn_sound_on,
                warn_sound: item.warn_sound,
                warn_repeat: item.warn_repeat,
                warn_interval: item.warn_interval,
                warn_ignore: item.warn_ignore,
                warn_bling: item.warn_bling
            }
            let setResult = await this.$api.operateUserWarnConfigv2(data)
            if (!setResult || setResult.status != 200) {
                this.$error(setResult.message || this.$ct('message.queryError'))
                return
            }
            this.$success('配置成功')
            this.$store.commit('switch/changeBoxRuleState')
        },
        listenAlarmSounds() {
            this.$refs.alarmReadio.play()
        },

        checkChangeOrNot() {
            let checkedList = this.$refs.filterTable.selection
            if(!checkedList.length) {
                this.$warning(this.$ct('message.noQuery'))
                return
            }
            this.table.setting = true
        },
        
        async modifyAlarmRule() {
            let checkedList = this.$refs.filterTable.selection
            let checkedCode = checkedList.map(item => +item.warn_code)
            if(!checkedCode.length) {
                this.$warning(this.$ct('message.noQuery'))
                return
            }
            let parmas = JSON.parse(JSON.stringify(this.currentRule))
            parmas.batch_operate_obj = [
                {
                    warn_type: 1,
                    warn_code_list: checkedCode
                }
            ]
            let setResult = await this.$api.operateUserWarnConfigv2(parmas)
            if (!setResult && setResult.status != 200) {
                this.$error(setResult.message || this.$ct('message.queryError'))
                return
            }
            checkedList.forEach(item => {
                item.id = (new Date()).getTime()
                Object.assign(item, this.currentRule)
            })
            this.table.setting = false
            this.$refs.filterTable.clearSelection()
            this.$success('配置成功')
            this.$store.commit('switch/changeBoxRuleState')
        },

        async clearSettingRule() {
            let checkedList = this.$refs.filterTable.selection
            let checkedCode = checkedList.map(item => +item.warn_code)
            if(!checkedCode.length) {
                this.$warning(this.$ct('message.noQuery'))
                return
            }
            let parmas = {
                operate_type: 2,                    
                user_id: '-1',
                batch_operate_obj: [
                    {
                        warn_type: 1,
                        warn_code_list: checkedCode
                    }
                ]
            }
            let setResult = await this.$api.operateUserWarnConfigv2(parmas)
            if (!setResult && setResult.status != 200) {
                this.$error(setResult.message || this.$ct('message.queryError'))
                return
            }
            let option = { 
                id: null, warn_ignore: false,  warn_popup: false,                      
                warn_sound: null, warn_repeat: 0, warn_interval: 0,                       
            }
            checkedList.forEach(item => {
                Object.assign(item, option)
            })
            this.$refs.filterTable.clearSelection()
            this.$success('取消配置成功')
            this.$store.commit('switch/changeBoxRuleState')
        },

        async setAlarmDetailList(list) {
            let result = await this.$api.getSysDictByCode({ code: 'danger_event_type' })
            let eventObj = {}
            result.forEach(item => {
                eventObj[item.value] = item
            })
            list.forEach(item => {
                item.detail = eventObj[item.warn_code]
            })
            this.table.list = list
        }
    }
}

</script>

<style lang='scss' scoped>

</style>
<template>
  <el-card
    :body-style="{
      padding: '0px',
      height: '100%',
      display: 'flex',
      flexDirection: 'column',
    }"
    class="tree-manager"
    shadow="hover"
  >
    <div class="query-wrap">
      {{ title }}
    </div>
    <div class="content-wrap " v-loading="this.form.state === 'saving'">
      <div class="tree">
        <el-tree
          v-loading="deptTableTree.loading"
          :data="deptTableTree.data"
          node-key="id"
          highlight-current
          ref="tree"
          :expand-on-click-node="false"
          accordion
          @node-click="nodeClick"
        >
          <div class="custom-tree-node" slot-scope="{ node, data }">
            <div>
              <el-tag size="mini" :type="tree.levelTypeMap[data.type]">
                {{ tree.levelLabel[data.type] }}
              </el-tag>
              <span :class="{ 'new-node': !node.data.id }">{{
                node.data.name
              }}</span>
            </div>
            <div>
              <el-button
                type="text"
                size="mini"
                class="pony-iconv2 pony-jia"
                v-if="data.type < 3"
                @click="appendNode(node, data)"
              >
              </el-button>
              <el-button
                type="text"
                size="mini"
                class="pony-iconv2 pony-jian"
                @click="deleteNode(node)"
              >
              </el-button>
            </div>
          </div>
        </el-tree>
      </div>
      <div class="tree-node-info">
        <template v-if="currentNode">
          <el-form
            label-width="90px"
            :model="form.data"
            :rules="form.rules"
            size="mini"
            ref="form"
            :disabled="form.mode === 'saving'"
          >
            <el-row>
              <el-col :span="24" :lg="{ span: 11 }">
                <el-form-item label="机构名称" prop="name">
                  <el-input v-model="form.data.name"></el-input>
                </el-form-item>
                <el-form-item label="所属区域">
                  <SelectTreeInput
                    v-model="form.data.areaGroup"
                    prefixCls="chinArea"
                    :condition="nodeCondition2"
                    type="area"
                    placeholder="请选择所属区域"
                    title="请选择所属区域"
                  >
                  </SelectTreeInput>
                </el-form-item>
                <el-form-item label="离线时长">
                  <el-input v-model="form.data.offline_during_time"></el-input>
                </el-form-item>
                <el-form-item label="别名重复">
                  <el-checkbox v-model="form.data.vehicle_alias_repeat"
                    >可见</el-checkbox
                  >
                </el-form-item>
                <el-form-item label="所属乡镇">
                  <el-input v-model="form.data.town"></el-input>
                </el-form-item>
                <!-- <el-form-item label="通行辖区">
                                    <SelectTreeInput v-model="form.data.jurisdictionGroup" :type="jurisdictionModal.typeKey"
                                                    :extraKeys="defaultJurisdictionModal.extraKeys"
                                                    placeholder="请选择通行辖区" :condition="()=>true" 
                                                    :checkMode ="true" checkModeText="个通行辖区" :withParent="true" ref="jurisdictionInput"
                                                    title="请选择通行辖区">
                                    </SelectTreeInput>
                                </el-form-item> -->
                <el-form-item prop="areaGroup" label="归属辖区:">
                  <FenceTreeFilter
                    :source="16"
                    v-model="jurisdictionTree"
                    type="xiaquMgt"
                    ref="xiaqu"
                    :checkMode="true"
                  ></FenceTreeFilter>
                </el-form-item>
                <el-form-item label="归属限行区:">
                  <FenceTreeFilter
                    :source="8"
                    v-model="xianxingquTree"
                    type="xianxingqu"
                    ref="xianxingqu"
                    :checkMode="true"
                  ></FenceTreeFilter>
                </el-form-item>
                <el-form-item label="车辆上限">
                  <el-input v-model="form.data.vehicle_limit"></el-input>
                </el-form-item>
                <el-form-item label="核发机关">
                  <el-input v-model="form.data.trans_license_issue"></el-input>
                </el-form-item>
                <el-form-item label="备注">
                  <el-input
                    type="textarea"
                    v-model="form.data.remark"
                  ></el-input>
                </el-form-item>
                <el-form-item label="企业类型">
                  <DictionarySelect code="dept_sign" v-model="form.data.dept_sign" style="display:block;">
                  </DictionarySelect>
                </el-form-item>
              </el-col>
              <el-col :span="24" :lg="{ span: 11, offset: 2 }">
                <!--<el-form-item label="负责人">-->
                <el-form-item label="法定代表人">
                  <el-input v-model="form.data.master"></el-input>
                </el-form-item>
                <el-form-item label="联系电话">
                  <el-input v-model="form.data.phone"></el-input>
                </el-form-item>
                <el-form-item label="联系地址">
                  <el-input v-model="form.data.address"></el-input>
                </el-form-item>
                <el-form-item label="联系邮箱">
                  <el-input v-model="form.data.email"></el-input>
                </el-form-item>
                <el-form-item label="邮政编码">
                  <el-input v-model="form.data.postal_code"></el-input>
                </el-form-item>
                <el-form-item label="部门传真">
                  <el-input v-model="form.data.fax"></el-input>
                </el-form-item>
                <el-form-item label="联系人">
                  <el-input v-model="form.data.contact_name"></el-input>
                </el-form-item>
                <el-form-item label="联系方式">
                  <el-input v-model="form.data.contact_tel"></el-input>
                </el-form-item>
                <!--
                <el-form-item label="经营证号">
                  <el-input v-model="form.data.license_no"></el-input>
                </el-form-item> -->
                <el-form-item label="经营范围">
                  <el-input v-model="form.data.license_scope"></el-input>
                </el-form-item>
                <el-form-item label="运输许可证">
                  <el-input v-model="form.data.trans_license_no"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
          <div style="text-align: right">
            <el-button
              size="mini"
              :disabled="form.state !== 'needSave'"
              :loading="form.state === 'saving'"
              @click="save"
              >保存
            </el-button>
            <el-button
              size="mini"
              :disabled="form.state !== 'needSave'"
              @click="nodeClick(currentNode.data, currentNode)"
              >取消</el-button
            >
          </div>
        </template>
        <span v-else>请选择一个树节点</span>
      </div>
    </div>
  </el-card>
</template>

<script>
/**
 * @Author: yezy
 * @Email: <EMAIL>
 * @Date: 2019-04-25 10:50:08
 * @LastEditors: yezy
 * @LastEditTime: 2019-04-25 10:50:12
 * @Description: 部门管理
 */
import { mapState, mapActions } from "vuex";
import FenceTreeFilter from "@/view/business/components/FenceTreeFilter";
import SelectTreeInput from "../../components/common/SelectTreeInput";
import DictionarySelect from "@/components/common/DictionarySelect";

export default {
  name: "departmentTreeMgt",
  components: {
    DictionarySelect,
    SelectTreeInput,
    FenceTreeFilter,
  },
  data() {
    return {
      title: "部门管理",
      tree: {
        type: "departmentTable",
        levelLabel: ["平台", "系统", "企业", "部门", "第五级"],
        levelTypeMap: ["", "success", "info", "warning", "danger"],
      },
      menuLevel: [
        { value: 0, label: "平台级" },
        { value: 1, label: "系统级别" },
        { value: 2, label: "公司级别" },
        { value: 3, label: "车队级别" },
      ],
      menuType: [
        { value: 0, label: "导航" },
        { value: 1, label: "按钮" },
      ],
      currentNode: null,
      form: {
        data: {
          id: null,
          name: "",
          remark: "",
          parent_id: null,
          areaGroup: { label: "", value: "" },
          vehicle_alias_repeat: false,
          offline_during_time: 10,
          type: 0, //层级
          master: "",
          email: "",
          postal_code: "",
          fax: "",
          phone: "",
          license_no: "",
          license_scope: "",
          address: "",
          jurisdiction: [],
          jurisdictionGroup: [],
          trans_license_no: "",
          trans_license_issue: "",
          contact_name: "",
          contact_tel: "",

          town: "", //接口应传[],但现在没有字典，暂时使用字符串替代
          vehicle_limit: null,
          alias:'',
          dept_sign: 0,
        },
        rules: {
          name: [
            { required: true, message: "请输入菜单名称", trigger: "change" },
          ],
        },
        state: "default", //default needSave saving
      },
      defaultJurisdictionModal: {
        typeKey: "approval:APPROVAL_JURISDICTION_TOP",
        extraKeys: ["type", "business_value"],
      },
      jurisdictionTree: null,
      xianxingquTree: null,
    };
  },
  computed: {
    ...mapState("ztreeData", ["treeData"]),
    deptTableTree: function() {
      return JSON.parse(JSON.stringify(this.treeData.departmentTable));
    },
    jurisdictionModal: function() {
      let newArea = this.form.data.areaGroup;
      if (newArea == null || newArea.value === "") {
        this.defaultJurisdictionModal.typeKey =
          "approval:APPROVAL_JURISDICTION_TOP";
      } else {
        this.defaultJurisdictionModal.typeKey =
          "approval:APPROVAL_JURISDICTION_TOP:" + newArea.value;
      }
      return this.defaultJurisdictionModal;
    },
  },
  watch: {
    "form.data": {
      handler: function(value) {
        this.form.state = "needSave";
      },
      deep: true,
    },
    jurisdictionTree: function(newVal) {
      if (!newVal) return;
      Object.assign(this.form.data, {
        jurisdiction: newVal.map((item) => item.value),
      });
    },

    xianxingquTree: function(newVal) {
      if (!newVal) return;
      Object.assign(this.form.data, {
        restricted_zone: newVal.map((item) => item.value),
      });
    },
  },
  methods: {
    ...mapActions("ztreeData", {
      getStateTreeData: "getTreeData",
    }),
    nodeClick(data, node) {
      console.log("DepartmentTreeMgt", data);
      this.$utils.assign(this.form.data, {
        ...data,

        areaGroup: { label: data.area_name, value: data.area_id },
        jurisdictionGroup: data.jurisdiction.map((item) => {
          return { value: item };
        }),
        vehicle_alias_repeat: data.vehicle_alias_repeat === 1,
        town: data.town[0],
      });
      let jurisdiction = [];
      let restricted_zone = [];
      for (var i = 0; i < data.jurisdiction.length; i++) {
        if (!data.jurisdiction.length) return;
        jurisdiction = data.jurisdiction.map((item) => {
          return { value: item, label: "" };
        });
      }
      for (var i = 0; i < data.restricted_zone.length; i++) {
        if (!data.restricted_zone.length) return;
        restricted_zone = data.restricted_zone.map((item) => {
          return { value: item, label: "" };
        });
      }
      this.$nextTick(() => {
        this.jurisdictionTree = jurisdiction;
        this.xianxingquTree = restricted_zone;
      });

      console.log("this.form.data", this.form.data);
      this.currentNode = node;
      this.$nextTick(() => {
        this.form.state = "default";
      });
    },
    async save() {
      if (!(await this.$refs["form"].validate())) return;
      this.form.state = "saving";
      const data = this.form.data;
      let params = {
        ...data,

        area_id: data.areaGroup.value,
        area_name: data.areaGroup.label,
        vehicle_alias_repeat: data.vehicle_alias_repeat ? "2" : "1",
        town: [data.town],
        // jurisdiction: data.jurisdictionGroup/*.filter(item => item.type === 2)*/.map(item => item.value)
      };
      this.saveNode(params);
    },
    async saveNode(params) {
      if (params.id) {
        // do modify
        let res = await this.$api.updateDepartment(params);
        if (res.rs === 1) {
          this.$success("修改部门成功");
          Object.assign(this.currentNode.data, this.form.data, params);
          this.form.state = "default";
        } else {
          this.$error("修改部门失败");
          this.form.state = "needSave";
        }
      } else {
        // do add
        delete params.id;
        let res = await this.$api.insertDepartment(params);
        if (res.rs === 1) {
          this.$success("新增部门成功");
          Object.assign(this.form.data, { id: res.id });
          Object.assign(this.currentNode.data, this.form.data, params);
          this.$nextTick(() => {
            this.form.state = "default";
          });
        } else {
          this.$error("新增部门失败");
          this.form.state = "needSave";
        }
      }
    },
    async deleteNode(node) {
      await this.$confirm("确认删除此节点?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      });
      node.loading = true;
      if (node.data.id) {
        let res = await this.$api.deleteDepartment({ id: node.data.id });
        if (res.rs !== 1) {
          this.$error(res.result);
          node.loading = false;
          return;
        }
      }
      this.$refs["tree"].remove(node);
      this.$success("删除成功");
      node.loading = false;
      this.currentNode = null;
    },
    appendNode(node, data) {
      this.$refs["tree"].append(
        {
          id: null,
          name: "<<--新增节点-->>",
          remark: "",
          parent_name: "",
          parent_id: node.data.id,
          area_name: "",
          area_id: "",
          vehicle_alias_repeat: false,
          offline_during_time: 10,
          type: data.type + 1,
          master: "",
          email: "",
          postal_code: "",
          fax: "",
          phone: "",
          jurisdiction: [],
          restricted_zone: [],
          license_no: "",
          license_scope: "",
          address: "",
          town: [],
          trans_license_no: "",
          trans_license_issue: "",
          contact_name: "",
          contact_tel: "",
          alias:'',
          dept_sign:0
        },
        node
      );
      node.expanded = true;
      // this.nodeClick(node.data, node);
    },
    nodeCondition2(treeNode) {
      if (treeNode.type < 3) {
        this.$message({
          type: "warning",
          showClose: true,
          message: "至少选择到市级别！",
        });
        return false;
      }
      return true;
    },
    showSelected() {
      // data.jurisdictionGroup.map(item => item.value)
      console.log(
        "selected",
        this.form.data.jurisdictionGroup
          .filter((item) => item.type === 2)
          .map((item) => item.value)
      );
    },
  },
  async created() {
    this.getStateTreeData(this.tree.type);
  },
};
</script>

<style scoped lang="scss">
@import "DepartmentTreeMgt";
.tree-manager {
  /deep/ .select-tree-input {
    width: 100% !important;
  }
}
</style>

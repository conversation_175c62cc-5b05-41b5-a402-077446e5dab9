<template>
  <Layout :has-color="true" :contentLoading="table.loading">
    <template slot="aside">
      <!-- <ZtreeMatics style="padding: 10px;" 
                treeType="vehicle" ref="vehicleTree"
                :ztreeCheckHandle="ztreeCheckHandle">
            </ZtreeMatics> -->
      <ElementTree type="vehicle" ref="vehicleTree" :checkMode="true" @check="selectNodes" node-key="id">
      </ElementTree>
    </template>

    <template slot="query">
      <div class="query-item">
        <span style="margin-right:5px">任务名字:</span>

        <el-input v-model="queryList.name" placeholder="请输入任务名字"></el-input>
      </div>
      <div class="query-item">
        <span style="margin-right:5px">任务状态:</span>
        <el-select size="mini" v-model="queryList.state">
          <el-option :value="-1" label="全部"></el-option>
          <el-option :value="0" label="待执行"></el-option>
          <el-option :value="1" label="进行中"></el-option>
          <el-option :value="2" label="已完成"></el-option>
        </el-select>
      </div>
      <div class="query-item">
        <span style="margin-right:5px">过期状态:</span>
        <el-select size="mini" v-model="queryList.stateDate">
          <el-option :value="-1" label="全部"></el-option>
          <el-option :value="0" label="已过期"></el-option>
          <el-option :value="1" label="未过期"></el-option>
        </el-select>
      </div>
      <el-button style="margin-left: 10px;" size="mini" type="primary" @click="getPageDataInfo">刷新</el-button>
      <el-button type="primary" @click="confirmModalObj(null, 0)">新增</el-button>
      <div class="break-item"></div>
      <el-button size="mini" type="primary" @click="exportRadioCast" :loading="exportLoading">导出</el-button>
    </template>

    <template slot="footer">
      <el-pagination background small :pager-count="5" layout="prev, pager, next, total" :current-page.sync="table.page"
        :page-size="table.size" :total="table.list.length">
      </el-pagination>
    </template>

    <template slot="content">
      <el-table class="el-table--ellipsis" highlight-current-row stripe border size="mini" :data="formatTableList"
        height="100%" style="width: 100%;" ref="table">
        <el-table-column align="center" type="index" :index="(index) => index + 1 + pageStart"
          label="序号"></el-table-column>
          <el-table-column align="center" label="操作" min-width="100">
          <template slot-scope="scope">
            <!-- v-if="scope.row.task_process != 2"  -->
            <el-button type="text" size="mini" @click="confirmModalObj(scope.row, 1)">
              <i class="pony-iconv2 pony-xiugai"></i>
            </el-button>
            <el-button type="text" size="mini" @click="removeRadioTimer(scope.row)">
              <i class="pony-iconv2 pony-shanchu"></i>
            </el-button>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="task_name" label="任务名字" min-width="200" show-overflow-tooltip></el-table-column>
        <!-- <el-table-column align="center" label="任务类型">
          <template slot-scope="scope">
            {{ scope.row.task_type == 201 ? '定时任务' : '循环任务' }}
          </template>
        </el-table-column> -->
        <el-table-column align="center" label="下发一次">
          <template slot-scope="scope">
            {{ scope.row.trigger_type == 0 ? '是' : '' }}
          </template>
        </el-table-column>
        
        <el-table-column align="center" prop="start_date" label="执行日期" min-width="240" show-overflow-tooltip>
          <template slot-scope="scope">
            <!-- {{
              scope.row.task_type == 201 ? `${scope.row.start_date}` :
              `${scope.row.start_date} 至 ${scope.row.end_date}`
            }} -->
            {{`${scope.row.start_date} 至 ${scope.row.end_date}`}}
            <el-tag type="primary" v-show="!scope.row.date_valid">已过期</el-tag>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="start_time" label="执行时间" min-width="150" show-overflow-tooltip>
          <template slot-scope="scope">
            <!-- {{
              scope.row.task_type == 201 ? `${scope.row.start_time}` :
              `${scope.row.start_time} 至 ${scope.row.end_time}`
            }} -->
            {{`${scope.row.start_time} 至 ${scope.row.end_time}`}}
          </template>
        </el-table-column>
        <el-table-column align="center" prop="task_last_exectime" label="最近执行时间" min-width="140" show-overflow-tooltip></el-table-column>
        <el-table-column align="center" label="任务状态" min-width="120">
          <template slot="header">
            最近任务状态
          <el-popover placement="bottom" width="150" trigger="click">
                    <p> 
                      最近执行时间的执行任务状态
                    </p>
                    <i class="pony-iconv2 pony-bangzhu" slot="reference" style="font-size:18px;vertical-align: -1px;"></i>
                </el-popover>
          </template>
          <template slot-scope="scope">
            {{ table.stateDesc[scope.row.task_process] }}
          </template>
        </el-table-column>
        <el-table-column align="center" prop="vehicle_number" label="成功率" show-overflow-tooltip>
          <template slot-scope="scope">
            <span style="color:var(--color-primary);cursor: pointer;" @click="showDetail(scope.row)">
              {{ (scope.row.task_process == 0 ? 0 : (scope.row.vehicle_terminal_list.length - scope.row.vehicle_terminal_list_remain.length)) + '/' + scope.row.vehicle_terminal_list.length }}
            </span>
          </template>
        </el-table-column>
        
        
        <el-table-column align="center" prop="tts_msg" label="任务内容" min-width="400" show-overflow-tooltip></el-table-column>
        
        <el-table-column align="center" prop="create_by" label="创建者" min-width="200"></el-table-column>
        
      </el-table>
    </template>
    <PonyDialog title="未发成功列表" width="480" v-model="modal.showDetail" contentStyle="height: 350px;" :hasFooter="false"
    @close="modal.showDetail = false"
      >
      <el-table class="el-table--ellipsis" highlight-current-row stripe border size="mini" :data="modal.detailData" 
        height="100%" style="width: 100%;overflow: auto;">
        <el-table-column align="center" type="index" :index="(index) => index + 1"
          label="序号"></el-table-column>
        <el-table-column align="center" prop="terminal_no" label="终端号" min-width="140" show-overflow-tooltip></el-table-column>
        <el-table-column align="center" prop="plate_no" label="车牌号" min-width="120" show-overflow-tooltip></el-table-column>
      </el-table></PonyDialog>
    <PonyDialog :title="`${modal.state ? '修改' : '新增'}消息任务`" width="780" v-model="modal.show" contentStyle="height: 350px;"
      @confirm="handleModalCommit">
      <el-row class="el-row-flow">
        <el-col :span="9">
          <ZtreeMatics treeType="vehicle" :checkMode="true" ref="tree"></ZtreeMatics>
        </el-col>
        <el-col :span="15">
          <el-form ref="form" :model="modal.data" :rules="modal.rules" label-width="80px">
            <el-form-item label="任务名字" prop="task_name">
              <el-input v-model="modal.data.task_name"></el-input>
            </el-form-item>

            <!-- <el-form-item label="任务类型">
              <el-select size="mini" style="width: 100%" v-model="modal.data.task_type">
                <el-option :value="201" label="定时任务"></el-option>
                <el-option :value="301" label="循环任务"></el-option>
              </el-select>
            </el-form-item> -->
          
            <el-form-item class="free_margin" label="开始日期" required>
              <el-col :span="11">
                <el-form-item prop="start_date">
                  <el-date-picker type="date" placeholder="选择日期" style="width: 100%;" v-model="modal.data.start_date"
                  :picker-options="pickerOptionsStart"
                    value-format="yyyy-MM-dd">
                  </el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="2" style="text-align: center;">至</el-col>
              <el-col :span="11">
                <el-form-item prop="end_date">
                  <el-date-picker type="date" placeholder="选择日期" style="width: 100%;" v-model="modal.data.end_date"
                  :picker-options="pickerOptionsEnd"
                    value-format="yyyy-MM-dd">
                  </el-date-picker>
                </el-form-item>
              </el-col>
            </el-form-item>

            <el-form-item class="free_margin" label="开始时间" required>
              <el-col :span="11">
                <el-form-item prop="start_time">
                  <el-time-picker placeholder="选择时间" style="width: 100%;" v-model="modal.data.start_time"
                  :picker-options="startTimePickerOption(modal.data)"
                    value-format="HH:mm:ss">
                  </el-time-picker>
                </el-form-item>
              </el-col>
              <el-col :span="2" style="text-align: center;">至</el-col>
              <el-col :span="11">
                <el-form-item prop="end_time">
                  <el-time-picker placeholder="选择时间" style="width: 100%;" v-model="modal.data.end_time"
                  :picker-options="endTimePickerOption(modal.data)"

                    value-format="HH:mm:ss">
                  </el-time-picker>
                </el-form-item>
              </el-col>
            </el-form-item>
            <el-form-item label="下发一次">
            <el-checkbox v-model="modal.data.trigger_type" style="margin-right: 5px"></el-checkbox>
            <el-popover
              placement="top"
              width="150"
              trigger="hover">
              <p>勾选后只下发一次, 不勾选则所选时间段内每天下发一次</p>
              <i class="pony-iconv2 pony-bangzhu" slot="reference"></i>
            </el-popover>
              <!-- <el-select size="mini" style="width: 100%" v-model="modal.data.task_type">
                <el-option :value="201" label="定时任务"></el-option>
                <el-option :value="301" label="循环任务"></el-option>
              </el-select> -->
            </el-form-item>
            <el-form-item label="执行周期">
              <el-checkbox-group v-model="modal.data.valid_weekday">
                <el-checkbox :label="1">周一</el-checkbox>
                <el-checkbox :label="2">周二</el-checkbox>
                <el-checkbox :label="3">周三</el-checkbox>
                <el-checkbox :label="4">周四</el-checkbox>
                <el-checkbox :label="5">周五</el-checkbox>
                <el-checkbox :label="6">周六</el-checkbox>
                <el-checkbox :label="7">周日</el-checkbox>
              </el-checkbox-group>
            </el-form-item>

            <el-form-item label="任务内容" prop="tts_msg">
              <el-input type="textarea" v-model="modal.data.tts_msg"></el-input>
            </el-form-item>

          </el-form>
        </el-col>
      </el-row>
    </PonyDialog>

  </Layout>
</template>

<script>
import PinyinMatch from 'pinyin-match'
const ExportJsonExcel = require('js-export-excel')
export default {
  name: 'radioBroadCast',
  components: {},
  data() {
    const modalData = {
      task_id: null,
      task_name: '',
      task_process: 0,            // 2 无效 1 进行中  0 未开始 >>> 2 无效, 0有效 >>> 0: '待执行',1:'进行中',2: '已完成'
      // task_type: 201,
      trigger_type:false,
      start_date: moment().startOf('day').format('YYYY-MM-DD'),
      start_time: moment().add(10, 'minute').format('HH:mm:ss'),
      end_date: moment().endOf('day').format('YYYY-MM-DD'),
      end_time: moment().add(20, 'minute').format('HH:mm:ss'),
      valid_weekday: [1, 2, 3, 4, 5, 6, 7],
      tts_msg: '',
      vehicle_terminal_list: []
    }
    return {
      table: {
        loading: false,
        list: [],
        page: 1,
        size: 30,
        stateDesc: {
          0: '待执行',
          1:'进行中',
          2: '已完成'
        }
      },

      queryList: {
        name: '',
        state: -1,
        stateDate:-1,
        vehicle_list: []
      },

      modal: {
        show: false,
        state: 0,
        showDetail:false,
        data: JSON.parse(JSON.stringify(modalData)),
        modalData,
        detailData:[],
        rules: {
          task_name: [
            { required: true, message: '请输入任务名字', trigger: 'blur' },
          ],
          tts_msg: [
            { required: true, message: '请输入任务内容', trigger: 'blur' },
          ],
          start_date: [
            { type: 'string', required: true, message: '请选择日期', trigger: 'change' }
          ],
          start_time: [
            { type: 'string', required: true, message: '请选择时间', trigger: 'change' }
          ],
          end_date: [
            { type: 'string', required: true, message: '请选择日期', trigger: 'change' }
          ],
          end_time: [
            { type: 'string', required: true, message: '请选择时间', trigger: 'change' }
          ]
        }
      },
      exportLoading: false
    };
  },

  computed: {
    pickerOptionsStart() {
        return {
            disabledDate: (date) => {
                return this.modal.data.end_date ? (moment(date).valueOf() - moment(this.modal.data.end_date).endOf('day').valueOf()) > 0:false
            }
        }
    },
    pickerOptionsEnd() {
            return {
                disabledDate: (date) => {
                    return this.modal.data.start_date?(moment(this.modal.data.start_date).valueOf() - moment(date).startOf('day').valueOf()) > 0:false
                }
            }
        },
    pageStart() {
      return (this.table.page - 1) * this.table.size
    },
    vehicleFilter() {
      return this.queryList.vehicle_list.length ? this.table.list.filter(item =>
        item.vehicle_terminal_list.some(vehicle =>
          this.queryList.vehicle_list.includes(vehicle.vehicle_id.toString())
        )
      ) : this.table.list
    },
    filterStateList() {
      if(this.queryList.state == -1){
        if(this.queryList.stateDate == -1){
          return this.vehicleFilter
        }else {
          return this.vehicleFilter.filter(item => this.queryList.stateDate ? item.date_valid :  !item.date_valid)
        }
      }else {
        if(this.queryList.stateDate == -1){
          return this.vehicleFilter.filter(item => item.task_process == this.queryList.state)
        }else {
          return this.vehicleFilter.filter(item => item.task_process == this.queryList.state).filter(item => this.queryList.stateDate ? item.date_valid :  !item.date_valid)
        }
      }
    },
    filterList() {
      return this.queryList.name ? this.filterStateList.filter(item => PinyinMatch.match(item.task_name, this.queryList.name)) : this.filterStateList
    },
    formatTableList() {
      return this.filterList.slice(this.pageStart, this.pageStart + this.table.size)
    },
  },

  mounted() {
    this.getPageDataInfo()
  },

  methods: {
    startTimePickerOption(item) {
            if(item.end_time) {
                return { selectableRange: `00:00:00 - ${ item.end_time }` }
            } else {
                return { selectableRange: `00:00:00 - 23:59:59` }
            }
        },
    endTimePickerOption(item) {
            if(item.start_time) {
              let time = moment(item.start_time,'HH:mm:ss').add(5, 'minutes').format('HH:mm:ss')
                return { selectableRange: `${ time } - 23:59:59` }
            } else {
                return { selectableRange: `00:00:00 - 23:59:59` }
            }
        },
    selectNodes(current, { checkedNodes }) {
      this.queryList.vehicle_list = checkedNodes
        .filter((item) => item.type == 4)
        .map((item) => item.id);
    },
    showDetail(row){
      this.modal.detailData = row.vehicle_terminal_list_remain
      this.modal.showDetail = true
    },
    cleanUp() {
      this.table.page = 1
      this.table.list = []
      this.table.loading = false
    },

    async getPageDataInfo() {
      this.table.loading = true
      let result = await this.$api.queryVoiceBroadCast()
      if (!result) {
        this.$error('查询出错')
        this.cleanUp()
        return
      }
      if (result.status != 200) {
        this.$warning(result.message || '查询失败')
        this.cleanUp()
        return
      }
      if (!result.data.length) {
        this.$warning(result.message || '未查询到数据')
        this.cleanUp()
        return
      }
      this.table.list = result.data
      this.$nextTick(() => {
        this.$refs['table'].doLayout()
        this.table.loading = false

      })
    },

    async confirmModalObj(obj, type) {
      this.modal.state = type
      this.modal.show = true
      this.modal.data = JSON.parse(JSON.stringify(this.modal.modalData))
      this.modal.data.start_time = moment().add(10, 'minute').format('HH:mm:ss'),
      this.modal.data.end_time = moment().add(20, 'minute').format('HH:mm:ss'),
      await this.$nextTick()
      if (obj) {
        let parmas = JSON.parse(JSON.stringify(obj))
        parmas.trigger_type = parmas.trigger_type == 0

        if (parmas.valid_weekday) {
          let weekList = parmas.valid_weekday.split('')
          let list = []
          for (let i = 1; i < 8; i++) {
            if (+weekList[i - 1]) {
              list.push(i)
            }
          }
          parmas.valid_weekday = list
        }
        this.modal.data = parmas
      }
      this.checkRadioBindVehicle()

    },

    async checkRadioBindVehicle() {
      await this.$refs['tree'].waitForInit
      this.$refs['tree'].ztreeObj.checkAllNodes(false)
      let ruleList = this.modal.data.vehicle_terminal_list
      if (!ruleList.length) return
      ruleList.forEach((item, index) => {
        let node = this.$refs['tree'].ztreeObj.getNodeByParam('id', item.vehicle_id);
        this.$refs['tree'].ztreeObj.checkNode(node, true, true, true)
        if (index == ruleList.length - 1) {
          this.$refs['tree'].ztreeObj.expandNode(node.getParentNode())
          this.$refs['tree'].ztreeObj.selectNode(node.getParentNode())
        }
      })
    },

    removeRadioTimer(row) {
      this.$confirm('此操作将永久删除该任务, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        let pramas = {
          operate_type: -2,
          task_id: row.task_id
        }
        let result = await this.$api.changeVoiceBroadcastV2(pramas)
        if (!result || result.status != 200) {
          this.$message({ showClose: true, message: result.message || '删除失败', type: 'error' })
          return
        }
        let index = this.table.list.findIndex(item => item.task_id == row.task_id)
        if (index != -1) {
          this.table.list.splice(index, 1)
        }
        this.$message({ showClose: true, message: '删除成功', type: 'success' })
      })
    },

    async handleModalCommit() {
      let checkNode = this.$refs['tree'].ztreeObj.getCheckedNodes(true).filter(node => node.type == 4).map(vehicle => {
        return {
          vehicle_id: vehicle.id,
          terminal_no: vehicle.terminalNo
        }
      })
      if (!checkNode.length) {
        this.$message({ showClose: true, message: "请选择任务车辆!", type: 'warning' })
        return
      }

      this.$refs.form.validate(async (res) => {
        if (!res) return
        this.modal.data.vehicle_terminal_list = checkNode
        let pramas = JSON.parse(JSON.stringify(this.modal.data))
        let valid_weekday = ''
        for (let i = 1; i < 8; i++) {
          let value = pramas.valid_weekday.includes(i) ? '1' : '0'
          valid_weekday += value
        }
        pramas.valid_weekday = valid_weekday
        pramas.trigger_type = this.modal.data.trigger_type ? 0 : 1
        if (this.modal.state) {
          let result = await this.$api.changeVoiceBroadcastV2(pramas)
          if (!result || result.status != 200) {
            this.$error(result.message || '查询出错')
            return
          }
          this.$success('修改语音任务成功')
        } else {
          let result = await this.$api.voiceBroadCastV2(pramas)
          if (!result || result.status != 200) {
            this.$error(result.message || '查询出错')
            return
          }
          this.$success('新增语音任务成功')
        }
        this.modal.show = false
        this.getPageDataInfo()
      })
    },

    exportRadioCast() {
      if (!this.table.list.length) return
      let excelBody = []
      this.exportLoading = true
      this.table.list.forEach((item, index) => {
        let array = []
        array.push(
          index + 1,
          item.task_name,
          item.trigger_type == 0 ? '是' : '',
          this.table.stateDesc[item.task_process],
          `${item.start_date} 至 ${item.end_date} ${!item.date_valid ? '(已过期)':''}`,
          `${item.start_time} 至 ${item.end_time}`,
          item.task_last_exectime,
          item.tts_msg,
          (item.vehicle_terminal_list.length - item.vehicle_terminal_list_remain.length) + '/' + item.vehicle_terminal_list.length
        )
        excelBody.push(array)
      })
      let options = {
        fileName: '定时语音广播任务列表',
        datas: [
          {
            sheetData: excelBody,
            sheetHeader: ['序号', '任务名字', '下发一次', '任务状态', '执行日期', '执行时间','最近执行时间', '任务内容', '成功率'],
            columnWidths: ['3', '10', '8', '8', '10', '10', '30', '10']
          }
        ]
      }
      ExportJsonExcel(options).saveExcel();
      this.exportLoading = false
    }
  }
}

</script>

<style lang='scss' scoped>
.free_margin {
  .el-form-item {
    margin: 0;
  }
}

.el-form {
  height: 340px;
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  align-content: space-between;

  .el-form-item {
    width: 100%;
  }
}
</style>

<template>
  <PonyDialog v-model="show" title="批量导入" class="multiple-import" :loading="loading" :hasFooter="false" :width="1350"
    contentStyle="padding:0;height:550px">
    <Layout :content-loading="table.loading">
      <template slot="query">
        <div class="query-item">
          <el-upload style="float:left;" action="/ponysafety2/a/driver/readExcelDriverToTable"
            accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
            :show-file-list="false" :before-upload="() => { table.loading = true }" :on-success="handleSuccess"
            :on-error="handleError">
            <el-button type="primary" size="mini">
              <i class="pony-iconv2 pony-sousuo"></i>选择文件
            </el-button>
          </el-upload>
          <el-button type="primary" size="mini" style="margin-left: 10px" @click="confirmImport" :loading="table.loading">
            <i class="pony-iconv2 pony-pdf"></i>确认导入
          </el-button>
        </div>
        <div class="break-item">
          <span>共{{ table.data.length }}条数据</span>
        </div>
        <div class="query-item">
          <el-button type="primary" size="mini" @click="exportExample" :loading="exportExampleLoading">
            <i class="pony-iconv2 pony-excel"></i>导出范本
          </el-button>
          <el-button type="primary" size="mini" @click="table.data = []">
            清空
          </el-button>
          <el-button type="primary" size="mini" @click="exportTableList" :loading="exportLoading">
            <i class="pony-iconv2 pony-excel"></i>导出
          </el-button>
        </div>
      </template>
      <template slot="content">
        <el-table :empty-text="table.data.length ? '暂无数据' : ''" :data="table.data" height="100%" border ref="carTable"
          highlight-current-row style="width: 100%">
          <el-table-column type="index" label="序号" width="50">
          </el-table-column>
          <el-table-column prop="message" min-width="200" label="异常原因" show-overflow-tooltip>
            <template slot-scope="{row}">
              <span style="color:red">{{ row.message }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="companyName" min-width="100" label="归属企业" show-overflow-tooltip></el-table-column>
          <el-table-column prop="deptName" min-width="100" label="归属部门" show-overflow-tooltip></el-table-column>
          <el-table-column prop="name" min-width="80" label="姓名" show-overflow-tooltip></el-table-column>
          <el-table-column prop="sexView" min-width="60" label="性别" show-overflow-tooltip></el-table-column>
          <el-table-column prop="idcardNo" min-width="110" label="身份证号码" show-overflow-tooltip></el-table-column>

          <el-table-column prop="phone" min-width="90" label="手机号码" show-overflow-tooltip></el-table-column>
          <el-table-column prop="staffNo" min-width="90" label="工号" show-overflow-tooltip></el-table-column>
          <el-table-column prop="region" min-width="100" label="所属区域" show-overflow-tooltip></el-table-column>
          <el-table-column prop="nativePlace" min-width="100" label="籍贯" show-overflow-tooltip></el-table-column>
          <el-table-column prop="validDateView" min-width="100" label="驾驶证有效期" show-overflow-tooltip></el-table-column>
          <el-table-column prop="plateNo" min-width="100" label="绑定车辆" show-overflow-tooltip></el-table-column>
          <el-table-column prop="plateNoAllow" min-width="100" label="准驾车辆" show-overflow-tooltip></el-table-column>
          <el-table-column prop="salaryBasic" min-width="60" label="底薪" show-overflow-tooltip></el-table-column>
          <el-table-column prop="perfectAttendanceDays" min-width="80" label="满勤天数" show-overflow-tooltip></el-table-column>
          <el-table-column prop="jobStatus" min-width="100" label="入/离职状态" show-overflow-tooltip></el-table-column>
          <el-table-column prop="jobTime" min-width="100" label="入/离职时间" show-overflow-tooltip></el-table-column>
          <el-table-column prop="remark" min-width="100" label="备注" show-overflow-tooltip></el-table-column>
          <!-- 状态与操作 -->

          <el-table-column label="操作">
            <template slot-scope="scope">
              <el-button type="text" size="mini" class="pony-iconv2 pony-shanchu"
                @click="removeRow(scope.$index)"></el-button>
            </template>
          </el-table-column>
        </el-table>
      </template>
    </Layout>
  </PonyDialog>
</template>

<script>

export default {
  name: "multipleImport",
  data() {
    return {
      loading: false,
      show: false,
      table: {
        loading: false,
        data: [],
      },
      exportLoading: false,
      exportExampleLoading: false
    }
  },
  methods: {
    showModal() {
      this.show = true;
      this.table.data = [];
    },
    handleSuccess(res) {
      this.table.loading = false;
      this.$message({ type: 'success', showClose: true, message: this.$ct('expression.1', [res.length]) });
      this.table.data = res;
    },
    handleError(err) {
      this.$message({ type: 'error', showClose: true, message: this.$ct('label.parseFailure') })
      this.table.loading = false;
    },
    removeRow(index) {
      this.table.data.splice(index, 1);
    },
    async confirmImport() {
      if (this.table.data.length === 0) {
        this.$message({ type: 'info', showClose: true, message: this.$ct('messageInfo.0') })
        return;
      }
      this.table.loading = true;
      let res = await this.$api.multipledriver(this.table.data);

      this.$message({ type: 'success', showClose: true, message: res.Reason });
      this.table.data = res.DriverToTables;
      this.$emit('refresh')
      this.table.loading = false;
    },
    async exportExample() {
      this.exportExampleLoading = true
      this.$utils.excelExport("/ponysafety2/a/driver/exportSysDriverInfoDemo", JSON.stringify({}),
        '驾驶员范本' + ".xlsx");
      this.exportExampleLoading = false
    },
    async exportTableList() {
      if (this.table.data.length === 0) {
        this.$message({ type: 'info', showClose: true, message: this.$ct('messageInfo.2') })
        return
      }
      let fileName = "驾驶员信息报表"
      let sheetName = [
        "归属企业@companyName@8000@000000",
        "归属部门@deptName@8000@000000",
        "驾驶员姓名@name@8000@000000",
        "性别@sexView@12000@000000",
        "身份证号码@idcardNo@8000@000000",
        "手机号码@phone@8000@000000",
        "工号@staffNo@8000@000000",
        "所属区域@region@8000@000000",
        "籍贯@nativePlace@12000@000000",
        "驾驶证有效期@validDateView@12000@000000",
        "绑定车辆@plateNo@10000@000000",
        "准驾车辆@plateNoAllow@5000@000000",
        "底薪@salaryBasic@5000@000000",
        "满勤天数@perfectAttendanceDays@5000@000000",
        "入/离职状态@jobStatus@5000@000000",
        "入/离职时间@jobTime@5000@000000",
        "备注@remark@5000@000000"
      ]
      let params = {}
      let paramsList = [];
      let resData = this.table.data;
      params = {
        sheetName: "驾驶员信息",
        title: "驾驶员信息",
        headers: sheetName,
        dataList: resData,
      }
      paramsList.push(params)
      this.exportLoading = true;
      await this.$utils.jsExcelExport(JSON.stringify(paramsList), fileName + '.xlsx')
      this.exportLoading = false;
    }
  }
}
</script>

<style scoped lang="scss">
.multiple-import {
  .pony-iconv2 {
    font-size: 12px;
  }
}
</style>

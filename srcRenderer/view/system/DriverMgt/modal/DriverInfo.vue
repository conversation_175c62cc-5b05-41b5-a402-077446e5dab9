<template>
  <PonyDialog
    v-model="show"
    :title="mode === 'add' ? addModeTitle : modifyModeTitle"
    class="car-modal"
    :loading="loading"
    :width="850"
    content-style="padding:0;max-height:650px"
    :is-fullscreen="true"
  >
    <el-form
      :model="data"
      :rules="rules"
      label-width="100px"
      size="mini"
      ref="form"
    >
      <el-tabs
        v-model="activeTab"
        type="border-card"
        style="height:630px;border:none;box-shadow:none"
      >
        <el-tab-pane
          name="basic"
          :label="$ct('label.driverDetails')"
          style="padding:10px;overflow-y: auto;"
        >
        <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item prop="phone" :label="$ct('label.phone') + ':'">
                <el-input
                  v-model="data.phone"
                  :placeholder="$ct('label.phonePlz')"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="deptGroup" :label="$ct('label.group') + ':'">
                <SelectTreeInput
                  v-model="data.deptGroup"
                  ref="departmentInput"
                  type="department"
                  :placeholder="$ct('label.deptChoosePlease')"
                  :title="$ct('label.deptChoosePlease')"
                  :withParent="true"
                  :condition="deptCondition"
                ></SelectTreeInput>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="name" :label="$ct('label.name') + ':'">
                <el-input
                  v-model="data.name"
                  :disabled="
                    mode === 'modify' && !hasPermission('driver:editName')
                  "
                ></el-input>
              </el-form-item>
            </el-col>
        </el-row>
        <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item
                prop="audit_status"
                :label="$ct('label.checkStatus') + ':'"
              >
                <el-select
                  :placeholder="$ct('label.choosePlease')"
                  v-model="data.audit_status"
                >
                  <el-option
                    v-for="item in auditStatusList"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                prop="work_start_date"
                :label="$ct('label.workStartDate') + ':'"
              >
                <el-date-picker
                  v-model="data.work_start_date"
                  value-format="yyyy-MM-dd"
                  type="date"
                  style="width: 100%;"
                  :placeholder="$ct('label.selectDatePlz')"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                prop="work_end_date"
                :label="$ct('label.workEndDate') + ':'"
                v-if="data.audit_status == 2"
              >
                <el-date-picker
                  v-model="data.work_end_date"
                  value-format="yyyy-MM-dd"
                  type="date"
                  style="width: 100%;"
                  :placeholder="$ct('label.selectDatePlz')"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
        </el-row>
        <el-divider style="margin:0 0 24px 0"></el-divider>
        <el-row :gutter="20">
          <el-col :span="16">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item
                prop="idcard_no"
                :label="$ct('label.identifyNo') + ':'"
              >
                <el-input
                  v-model="data.idcard_no"
                  @blur="getOtherMsg"
                  :disabled="
                    !!data.idcard_no &&
                      mode === 'modify' &&
                      !hasPermission('driver:editId')
                  "
                  style="width: 200px;"
                ></el-input>
              </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item prop="age" :label="$ct('label.age') + ':'">
                <el-input v-model="data.age"></el-input>
              </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item
                prop="driver_birth"
                :label="$ct('label.birthDate') + ':'"
              >
                <el-date-picker
                  v-model="data.driver_birth"
                  type="date"
                  style="width: 100%;"
                  :placeholder="$ct('label.selectDatePlz')"
                >
                </el-date-picker>
              </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item prop="sex" :label="$ct('label.sex') + ':'">
                <el-radio v-model="data.sex" :label="1">{{
                  $ct("label.male")
                }}</el-radio>
                <el-radio v-model="data.sex" :label="2">{{
                  $ct("label.female")
                }}</el-radio>
              </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item
                  prop="idcard_photo_list"
                  :label="$ct('label.identifyPic') + ':'"
                >
                  <ImageUploaderGroup
                    :max="2"
                    v-model="data.idcard_photo_list"
                    v-bind="uploadCondition"
                    :uploadKey="`driver_id_card:${data.name}`"
                  >
                  </ImageUploaderGroup>
                </el-form-item>
              </el-col>
            </el-row>
          </el-col>
          <el-col :span="8">
            <el-form-item
                prop="face_image_url"
                :label="$ct('label.identifyPhoto') + ':'"
              >
                <ImageUploader
                  v-model="data.face_image_url"
                  v-bind="uploadCondition"
                  :uploadKey="`driver_face:${data.name}`"
                  style="height: 200px;width: 143px;"
                >
                </ImageUploader>
              </el-form-item>
          </el-col>

        </el-row>
        <el-divider></el-divider>
        <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item prop="staff_no" :label="$ct('label.workNo') + ':'">
                <el-input v-model="data.staff_no"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="region" label="所属地域：">
                <el-input v-model="data.region"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="native_place" label="籍贯：">
                <el-input v-model="data.native_place"></el-input>
              </el-form-item>
            </el-col>
        </el-row>
        <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item
                prop="black_white"
                :label="$ct('label.white&blackList') + ':'"
              >
                <el-select
                  :placeholder="$ct('label.choosePlease')"
                  v-model="data.black_white"
                >
                  <el-option
                    :label="$ct('label.unset')"
                    :value="null"
                  ></el-option>
                  <el-option
                    :label="$ct('label.blackList')"
                    :value="2"
                  ></el-option>
                  <el-option
                    :label="$ct('label.whiteList')"
                    :value="1"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="16">
              <el-form-item prop="remark" :label="$ct('label.remark') + ':'">
                <el-input v-model="data.remark" />
              </el-form-item>
            </el-col>
        </el-row>
        </el-tab-pane>
        <el-tab-pane
          name="license"
          :label="$ct('label.workLicense')"
          style="padding:10px;"
        >
          <el-row>
            <el-col :span="12">
              <el-form-item
                prop="license_number"
                :label="$ct('label.workLicense') + ':'"
              >
                <el-input v-model="data.license_number"></el-input>
              </el-form-item>
              <el-form-item
                prop="license_number"
                :label="$ct('label.awardDate') + ':'"
              >
                <el-date-picker
                  v-model="data.issuing_date"
                  type="date"
                  style="width: 100%;"
                  :placeholder="$ct('label.selectDatePlz')"
                >
                </el-date-picker>
              </el-form-item>
              <el-form-item
                prop="license_number"
                :label="$ct('label.validateStart') + ':'"
              >
                <el-date-picker
                  v-model="data.validity_start_date"
                  type="date"
                  style="width: 100%;"
                  :placeholder="$ct('label.selectDatePlz')"
                >
                </el-date-picker>
              </el-form-item>
              <el-form-item
                prop="license_number"
                :label="$ct('label.validateEnd') + ':'"
              >
                <el-date-picker
                  v-model="data.validity_end_date"
                  type="date"
                  style="width: 100%;"
                  :placeholder="$ct('label.selectDatePlz')"
                >
                </el-date-picker>
              </el-form-item>
              <el-form-item
                prop="license_number"
                :label="$ct('label.licenseStatus') + ':'"
              >
                <el-select v-model="data.licence_status">
                  <el-option :label="$ct('label.valid')" :value="0"></el-option>
                  <el-option
                    :label="$ct('label.invalid')"
                    :value="-1"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item
                prop="license_number"
                :label="$ct('label.licenseOrganization') + ':'"
              >
                <el-input v-model="data.issuing_authority"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item
                label-width="140px"
                prop="document_left_page"
                :label="$ct('label.licensePicLeft') + ':'"
              >
                <ImageUploader
                  v-model="data.document_left_page"
                  v-bind="uploadCondition"
                  :uploadKey="`driver_papework:${data.id}`"
                  style="height: 150px;width: 240px;"
                ></ImageUploader>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label-width="140px"
                prop="document_right_page"
                :label="$ct('label.licensePicRight') + ':'"
              >
                <ImageUploader
                  v-model="data.document_right_page"
                  v-bind="uploadCondition"
                  :uploadKey="`driver_papework:${data.id}`"
                  style="height: 150px;width: 240px;"
                ></ImageUploader>
              </el-form-item>
            </el-col>
          </el-row>
        </el-tab-pane>
        <el-tab-pane
          name="driverLicense"
          :label="$ct('label.driverLicense')"
          style="padding:10px;height: 100%;overflow: auto"
        >
          <el-row style="margin-left: 10px;">
            <el-col :span="12">
              <el-form-item
                prop="driver_license_no"
                :label="$ct('label.driverLicenseNo') + ':'"
              >
                <el-input v-model="data.driver_license_no"></el-input>
              </el-form-item>
              <el-form-item
                prop="issue_date"
                :label="$ct('label.driverLicenseAwardDate') + ':'"
              >
                <el-date-picker
                  v-model="data.issue_date"
                  style="width: 100%;"
                  type="date"
                  :placeholder="$ct('label.selectDatePlz')"
                >
                </el-date-picker>
              </el-form-item>
              <el-form-item
                prop="drive_class"
                :label="$ct('label.allowedCarType') + ':'"
              >
                <el-input v-model="data.drive_class"></el-input>
              </el-form-item>
              <el-form-item
                prop="valid_date"
                :label="$ct('label.validateEnd') + ':'"
              >
                <el-date-picker
                  v-model="data.valid_date"
                  style="width: 100%;"
                  type="date"
                  :placeholder="$ct('label.selectDatePlz')"
                >
                </el-date-picker>
              </el-form-item>
              <el-form-item
                prop="org_name"
                :label="$ct('label.licenseFrom') + ':'"
              >
                <el-input v-model="data.org_name"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item
                prop="driver_license_photo_list"
                :label="$ct('label.driverLicensePics') + ':'"
              >
                <ImageUploaderGroup
                  :max="4"
                  v-model="data.driver_license_photo_list"
                  v-bind="uploadCondition"
                  :uploadKey="`driver_license:${data.name}`"
                >
                </ImageUploaderGroup>
              </el-form-item>
            </el-col>
          </el-row>
        </el-tab-pane>
        <el-tab-pane
          name="extra"
          :label="$ct('label.extraInfo')"
          style="padding:10px;"
        >
        <el-divider content-position="center">成本关联</el-divider>
          <el-row>
            <el-col :span="8">
              <!-- <el-form-item
                prop="career"
                :label="$ct('label.profession') + ':'"
              >
                <el-input v-model="data.career"></el-input>
              </el-form-item> -->
              <el-form-item prop="salary_basic" label="底薪：">
                <el-input v-model="data.salary_basic"></el-input>
              </el-form-item>
              
            </el-col>
            <el-col :span="8">
              <el-form-item prop="perfect_attendance_days" label="满勤天数：">
                <el-input v-model="data.perfect_attendance_days"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        <el-divider content-position="center">其他</el-divider>

          <el-row>
            <el-col :span="16">
                <el-form-item prop="address" :label="$ct('label.address') + ':'">
                <el-input v-model="data.address"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-tab-pane>
        <el-tab-pane
          name="device"
          label="设备管理"
          style="padding:10px;overflow: auto;"
        >
        <el-divider content-position="left">
            <span>设备管理</span>
            <i class="pony-iconv2 pony-xinzeng" style="color: var(--color-primary)"
                @click="addDevice()"></i>
        </el-divider>
          <el-row style="margin-left: 10px;" v-for="(item,index) in data.terminal_bind_list" :key="index">
            <el-col :span="8">
              <el-form-item
                :prop="`terminal_bind_list.${index}.terminal_desc`"
                label="设备名称: "
                style="margin-bottom: 10px"
                :rules="allRules.terminal_desc"

              >
                <el-input v-model="item.terminal_desc"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
              :prop="`terminal_bind_list.${index}.terminal_type_id`"
                style="margin-bottom: 10px"
                label="设备类型: "
                :rules="allRules.terminal_type_id"

              >
                <el-select
                  placeholder="请选择设备类型"
                  v-model="item.terminal_type_id"
                >
                  <el-option
                    v-for="item in deviceTypeList"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
              :prop="`terminal_bind_list.${index}.terminal_code`"
                :rules="allRules.terminal_code"

                style="margin-bottom: 10px"
                label="设备编号: "
              >
                <el-input v-model="item.terminal_code"></el-input>
              </el-form-item>
            </el-col>
            <el-button type="text" @click="deleteDevice(index)">删除</el-button>
        </el-row>
        </el-tab-pane>
      </el-tabs>
    </el-form>
    <template slot="footer">
      <el-button type="primary" @click="commit"
        >{{ mode === "add" ? addModeOkText : modifyModeOkText }}
      </el-button>
      <el-button type="border" @click="show = false">{{
        $ct("label.cancel")
      }}</el-button>
    </template>
  </PonyDialog>
</template>

<script>
/**
 * @Author: yezy
 * @Email: <EMAIL>
 * @Date: 2019/12/25 13:43
 * @LastEditors: yezy
 * @LastEditTime: 2019/12/25 13:43
 * @Description:
 */
import SelectTreeInput from "../../../../components/common/SelectTreeInput";
import ImageUploader from "@/components/common/ImageUploader";
import ImageUploaderGroup from "@/components/common/ImageUploaderGroup";

export default {
  name: "driverInfo",
  components: {
    ImageUploaderGroup,
    SelectTreeInput,
    ImageUploader,
  },
  data() {
    const defaultData = {
      id: null,
      staff_no: "",
      phone: "",
      remark: "",
      dept_id: "",
      company_id: "",
      name: "",
      sex: "",
      career: "",
      idcard_no: "",
      age: "",
      address: "",
      contact: "",
      driver_license_no: "",
      drive_class: "",
      issue_date: "",
      valid_date: "",
      driver_license_photo: "",
      audit_refuse: "",
      audit_status: 1,
      // photo: '',
      org_name: "",
      region: "",
      native_place: "",
      licence: "",
      blob: null,
      face_image_url: "",
      license_number: "",
      document_left_page: "",
      document_right_page: "",
      validity_start_date: null,
      validity_end_date: null,
      issuing_date: null,
      licence_status: 0,
      issuing_authority: null,
      black_white: null,
      driver_birth: "",
      idcard_photo_list: [],
      driver_license_photo_list: [],
      work_start_date: null,
      work_end_date: null,
      deptGroup: null,
      terminal_bind_list:[],
      salary_basic:'',
      perfect_attendance_days:''
    };
    const numberRule= (rule, value, callback) => {
      let index = rule.field.split(".")[1];
      let type = rule.field.split(".")[2];
      if(this.data.terminal_bind_list[index][type]){
          callback();
      }else {
          callback(new Error("请输入设备信息！"));
      }
    };
    return {
      show: false,
      mode: "add",
      loading: false,
      addModeTitle: this.$ct("label.addDriver"),
      addModeOkText: this.$ct("label.addConfirm"),
      modifyModeTitle: this.$ct("label.modifyDriver"),
      modifyModeOkText: this.$ct("label.modifyConfirm"),
      data: JSON.parse(JSON.stringify(defaultData)),
      defaultData,
      auditStatusList: [
        { id: -1, name: this.$ct("label.checkUnpass") },
        { id: 0, name: this.$ct("label.uncheck") },
        { id: 1, name: this.$ct("label.checkSuccess") },
        { id: 2, name: this.$ct("label.checkResign") },
      ],
      activeTab: "basic",
      deviceTypeList:[],
      allRules:{
        terminal_type_id:[{ required: true,validator: numberRule, trigger: "change" }],
        terminal_desc:[{ required: true,validator: numberRule, trigger: "blur" }],
        terminal_code:[{ required: true,validator: numberRule, trigger: "blur" }],
      }
      
    };
  },
  computed: {
    rules: function() {
      return {
        phone: [
          {
            required: true,
            message: this.$ct("messageInfo.0"),
            trigger: "blur",
          },
        ],
        deptGroup: [
          {
            required: true,
            message: this.$ct("messageInfo.1"),
            trigger: "change",
          },
        ],
        name: [
          {
            required: true,
            message: this.$ct("label.namePlz"),
            trigger: "blur",
          },
        ],
        idcard_no: this.data.face_image_url
          ? [
              {
                required: true,
                message: this.$ct("messageInfo.2"),
                trigger: "change",
              },
            ]
          : undefined,
      };
    },
    uploadCondition: function() {
      return {
        disabled: !this.data.name,
        whyDisable: this.$ct("messageInfo.3"),
      };
    },
  },
  watch: {
    "data.deptGroup": function(val) {
      if (!val) return;
      this.data.dept_id = val.value;
      this.data.company_id = val.parentNode.value;
    },
  },
  methods: {
    getOtherMsg(){
      let val = this.data.idcard_no
      if(!val){
        this.data.age = ''
        this.data.driver_birth = ''
        this.data.sex = ''
        return;
      }
      let regIdNo = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
        if (!regIdNo.test(val)&&val!=='') {
            this.data.age = ''
            this.data.driver_birth = ''
            this.data.sex = ''
            return;
        }
        let birthday = val.substring(6, 14);
        let year = birthday.substring(0, 4);
        let month = birthday.substring(4, 6);
        let day = birthday.substring(6, 8)
        let currentYear = new Date().getFullYear();
        this.data.age = currentYear - parseInt(year);
        this.data.driver_birth = moment(`${year}-${month}-${day}`).format("YYYY-MM-DD")
        let genderCode = parseInt(val.charAt(16));
        this.data.sex = genderCode % 2 === 0 ? 2 : 1;
    },
    addDevice(){
      if(!this.data.terminal_bind_list){
        this.data.terminal_bind_list = [
          {
        terminal_id:'',
        terminal_desc:'',
        terminal_type_id:'',
        terminal_code:''
      }
        ]

      }else {
        this.data.terminal_bind_list.push({
        terminal_id:'',
        terminal_desc:'',
        terminal_type_id:'',
        terminal_code:''
      })
      }
      
    },
    deleteDevice(index){
      if(!this.data.terminal_bind_list.length)return
      this.data.terminal_bind_list.splice(index,1)
    },
    deptCondition(treeNode) {
      if (treeNode.type !== 3) {
        this.$message({
          type: "info",
          message: this.$ct("messageInfo.4"),
          showClose: true,
        });
        return false;
      } else {
        return true;
      }
    },
    async commit() {
       var regIdNo = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
            if (!regIdNo.test(this.data.idcard_no)&&this.data.idcard_no!=='') {
                this.$warning("请输入正确的身份证号")
                return;
            }
      if (!(await this.$refs["form"].validate())) return;
      if (this.mode === "add") {
        await this.addDriver();
      } else {
        await this.updateDriver();
      }
      this.show = false;
      this.$emit("afterCommit");
    },
    async addDriver() {
      this.loading = true;
      try {
        const data = this.aimDateFormat(this.data);
        let params = {
          name: data.name,
          sex: parseInt(data.sex) - 1,
          image_url: data.image_url,
          birth: data.driver_birth,
          license_date: data.issue_date,
          data_driver_add: data,
        };
        let res = await this.$api.addSysDriverInfo(params);
        if (res.base_resp_add.RS === 1) {
          //司机信息添加成功
          switch (res.code) {
            case 1001: //添加Face信息成功
              this.$success(this.$ct("label.addSuccess"));
              break;
            case 1002:
            case 0: //添加Face信息失败
              this.$success(this.$ct("messageInfo.5"));
              break;
            default:
              break;
          }
        } else {
          this.$error(res.base_resp_add.Reason);
        }
      } catch (e) {
        this.$error(e);
      } finally {
        this.loading = false;
      }
    },
    async updateDriver() {
      this.loading = true;
      try {
        const data = this.aimDateFormat(this.data);
        let params = {
          uni_id: data.uni_id,
          name: data.name,
          sex: parseInt(data.sex) - 1,
          image_url: data.image_url,
          birth: data.driver_birth,
          license_date: data.issue_date,
          data_driver_update: data,
        };
        let res = await this.$api.modifySysDriverInfo(params);
        if (res.base_resp_update.RS === 1) {
          //司机信息修改成功
          switch (res.code) {
            case 1001: //修改Face信息成功
              this.$success(this.$ct("label.modifySuccess"));
              break;
            case 1002:
            case 0: //修改Face信息失败
              this.$success(this.$ct("messageInfo.6"));
              break;
            default:
              break;
          }
        } else {
          throw new Error(res.base_resp_update.Reason);
        }
      } catch (e) {
        this.$error(e);
      } finally {
        this.loading = false;
      }
    },
    async showModal(rowData) {
      if (rowData) {
        this.mode = "modify";
        this.data = JSON.parse(JSON.stringify(this.defaultData));
        this.$utils.assign(this.data, rowData);
        this.$nextTick(async () => {
          this.data.deptGroup = await this.$refs[
            "departmentInput"
          ].fillOtherProperty(rowData.dept_id);
        });
      } else {
        this.data = JSON.parse(JSON.stringify(this.defaultData));
        this.mode = "add";
      }
      this.show = true;
    },

    // 一些日期格式处理[2020年6月4日 19:45:48 | yuqf]
    aimDateFormat(data) {
      if (data.driver_birth != null && data.driver_birth != "") {
        data.driver_birth = moment(data.driver_birth).format("YYYY-MM-DD");
      }

      if (data.issue_date != null && data.issue_date != "") {
        data.issue_date = moment(data.issue_date).format("YYYY-MM-DD");
      }
      if (data.valid_date != null && data.valid_date != "") {
        data.valid_date = moment(data.valid_date).format("YYYY-MM-DD");
      }

      if (data.issuing_date != null && data.issuing_date != "") {
        data.issuing_date = moment(data.issuing_date).format("YYYY-MM-DD");
      }
      if (data.validity_end_date != null && data.validity_end_date != "") {
        data.validity_end_date = moment(data.validity_end_date).format(
          "YYYY-MM-DD"
        );
      }
      if (data.validity_start_date != null && data.validity_start_date != "") {
        data.validity_start_date = moment(data.validity_start_date).format(
          "YYYY-MM-DD"
        );
      }

      return data;
    },
  },
  async mounted() {
      this.deviceTypeList = await this.$api.getTerminalType({
        classify:2
      })

  },
};
</script>

<style scoped lang="scss"></style>

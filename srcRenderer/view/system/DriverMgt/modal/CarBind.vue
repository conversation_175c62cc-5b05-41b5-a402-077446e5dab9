<template>
    <PonyDialog class="car-bind" v-model="show"
                :loading="loading" :title="$ct('label.carBind')"
                :width="300"
    >
        <div style="height: 400px;">
            <div class="form-item">
                <span>{{$ct('label.currentBinded')}}</span>
                <span style="flex-grow: 1">{{plateNo || $ct('label.unbound')}}</span>
                <el-button type="border" v-if="plateNo" @click="unbindDriver">{{$ct('label.unbind')}}</el-button>
            </div>
            <div class="form-item">
                <span style="width: 100%;">{{$ct('label.changeBinding')}}</span>
                <ElementTree type="vehicle" ref="tree" style="height: 340px;overflow: auto" @node-click="selectVehicle"></ElementTree>
            </div>
        </div>
        <template slot="footer">
            <el-button type="primary" v-if="vehicleSelect" @click="updateDriverBind">{{$ct('label.confirm')}}</el-button>
            <el-button type="border" @click="show=false">{{$ct('label.cancel')}}</el-button>
        </template>
    </PonyDialog>
</template>

<script>
    /**
     * @Author: yezy
     * @Email: <EMAIL>
     * @Date: 2019/12/26 10:00
     * @LastEditors: yezy
     * @LastEditTime: 2019/12/26 10:00
     * @Description:
     */
    export default {
        name: "carBind",
        data() {
            return {
                show: false,
                loading: false,
                plateNo: '',
                vehicleId: '',
                vehicleSelect: '',
                driverId: '',
            }
        },
        methods: {
            async showModal(row) {
                this.plateNo = '';
                this.vehicleId = '';
                this.vehicleSelect = '';
                this.driverId = row.id;
                if (row.vehicle_id) {
                    this.plateNo = row.plate_no;
                    this.vehicleId = row.vehicle_id;
                }
                this.show = true;
            },
            selectVehicle(data,node,$node) {
                if (data.type === 4) {
                    this.vehicleSelect = data.id;
                }
            },
            async updateDriverBind() {
                this.loading = true;
                try {
                    let res = await this.$api.addBindDriverVehicle({
                        vehicleId: this.vehicleSelect,
                        driverId: this.driverId,
                    });
                    if (res.RS === 1) {
                        this.$success(this.$ct('messageInfo.0'));
                        this.show = false;
                        this.$emit('afterCommit');
                    } else {
                        throw new Error(this.$ct('messageInfo.1'))
                    }
                } catch (e) {
                    this.$error(e)
                } finally {
                    this.loading = false;
                }
            },
            async unbindDriver() {
                this.loading = true;
                try {
                    let res = await this.$api.vehivelUnbindToDriver({
                        vehicleId: this.vehicleId,
                        driverId: this.driverId,
                    });
                    if (res.RS === 1) {
                        this.$success(this.$ct('messageInfo.2'));
                        this.$emit('afterCommit');
                        this.plateNo = '';
                        this.vehicleId = '';
                    } else {
                        throw new Error(this.$ct('messageInfo.3'))
                    }
                } catch (e) {
                    this.$error(e)
                } finally {
                    this.loading = false;
                }
            }
        }
    }
</script>

<style scoped lang="scss">
    .car-bind {
        .form-item {
            display: flex;
            height: 30px;
            line-height: 30px;
            flex-wrap: wrap;
            align-items: center;
        }
    }
</style>
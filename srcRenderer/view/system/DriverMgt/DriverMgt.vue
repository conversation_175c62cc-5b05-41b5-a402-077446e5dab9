<template>
  <Layout class="driver-mgt" :hasColor="true" :contentLoading="table.loading">
    <template slot="aside">
      <ElementTree type="department" ref="tree" :checkMode="true" @check="selectNodes"></ElementTree>
    </template>
    <template slot="query">
      <div class="query-item">
        <el-select :placeholder="$ct('label.choosePlease')" v-model="query.type" style="width: 120px">
          <el-option :label="$ct('label.phone')" :value="0"></el-option>
          <el-option :label="$ct('label.identityNo')" :value="1"></el-option>
          <el-option :label="$ct('label.name')" :value="2"></el-option>
        </el-select>
      </div>
      <div class="query-item">
        <el-input v-model="query.value" style="width: 150px" :placeHolder="$ct('label.keyPlease')"></el-input>
      </div>
      <div class="break-item">
        <el-button size="mini" type="primary" @click="getTableList()" :loading="table.loading">
          {{ $ct("label.search") }}
        </el-button>
        <el-button size="mini" type="primary" @click="exportDataInfo" :loading="exportLoading">导出</el-button>
      </div>
      <div class="query-item">
        <el-button size="mini" type="primary" @click="operateTableSetting">表格显示设置</el-button>

        <!-- <el-button size="mini" type="primary" @click="exportDataInfo(1)">批量修改</el-button> -->
        <el-button size="mini" type="primary" @click="multipleDelete()">
          批量删除
        </el-button>
        <el-button size="mini" type="primary" @click="multipleImport">批量导入</el-button>
        <el-button size="mini" type="primary" @click="$refs['driverInfo'].showModal()">{{ $ct("label.add") }}
        </el-button>
      </div>
    </template>
    <template slot="content">
      <el-table class="el-table--radius " stripe border :empty-text="table.data.length ? $ct('label.noData') : ''"
        :data="table.data" height="100%" ref="table" style="width: 100%" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55">
        </el-table-column>
        <el-table-column type="index" :label="$ct('label.index')" width="50">
          <template slot-scope="{ $index }">
            <span>{{ (pager.current - 1) * pager.size + 1 + $index }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="location" :label="$ct('label.operate')" width="120">
          <template slot-scope="{ row }">
            <el-button type="text" :title="$ct('label.modify')" size="mini" @click="$refs['driverInfo'].showModal(row)">
              <i class="pony-iconv2 pony-xiugai"></i>
            </el-button>
            <el-button type="text" :title="$ct('label.del')" size="mini" v-if="hasPermission('driver:del')"
              @click="deleteDriver(row)">
              <i class="pony-iconv2 pony-shanchu"></i>
            </el-button>
            <el-button type="text" :title="$ct('label.bind')" size="mini" @click="$refs['carBind'].showModal(row)">
              <i class="pony-iconv2 pony-bangdingsiji"></i>
            </el-button>
            <el-button type="text" size="mini" @click="vehicleList(row)">
              <i class="pony-iconv2 pony-xiangqing" title="车辆绑定记录"></i>
            </el-button>
          </template>
        </el-table-column>
        <el-table-column v-for="(item, index) in tableSettingList" :key="index" :min-width="item.size + 'px'"
          header-align="center" :align="item.align" :prop="item.key" :label="item.name" show-overflow-tooltip>
          <template slot-scope="{row}">
            <span v-if="item.key == 'alarm_type' && !row.level">{{ row.alarm_type }}</span>
            <el-tag v-if="item.key == 'alarm_type' && row.level"
              :type="row.level == 1 ? 'default' : row.level == 2 ? 'warning' : 'danger'">
              {{ row.alarm_type }}
            </el-tag>
            <span v-if="item.key == 'company_name'">{{ row.company_name }} >>
              {{ row.dept_name }}</span>
            <span v-if="item.key == 'sex'">{{ row.sex == 1 ? $ct("label.male") : $ct("label.female") }}</span>
            <span v-if="item.key == 'audit_status' && row.audit_status == -1">{{
              $ct("label.checkUnpass")
            }}</span>
            <span v-if="item.key == 'audit_status' && row.audit_status == 0">
              {{ $ct("label.uncheck") }}</span>
            <span v-if="item.key == 'audit_status' && row.audit_status == 1">{{
              $ct("label.checkSuccess")
            }}</span>
            <span v-if="item.key == 'audit_status' && row.audit_status == 2">{{
              $ct("label.checkResign")
            }}</span>
            <span v-if="item.key == 'audit_status' && row.audit_status == 3">{{
              $ct("label.completed")
            }}</span>
            <span v-if="item.key == 'audit_status' && row.audit_status == 4">{{
              $ct("label.deleted")
            }}</span>
            <span v-if="item.key == 'plate_no'" class="bindedCar" @click="jump(row)">{{
              row.plate_no
            }}</span>
            <span v-if="item.key == 'black_white'">{{
              row.black_white
              ? row.black_white === 1
                ? $ct("label.whiteList")
                : $ct("label.blackList")
              : $ct("label.unset")
            }}</span>
            <span v-if="!item.type">{{ row[item.key] }}</span>

          </template>

        </el-table-column>
        <!-- <el-table-column
          :label="$ct('label.group')"
          width="250"
          show-overflow-tooltip
          align="left"
        >
          <template slot-scope="scope">
            <span>{{
              scope.row.company_name + ">>" + scope.row.dept_name
            }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="phone"
          :label="$ct('label.phone')"
          width="100"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          prop="name"
          :label="$ct('label.name')"
          width="150"
        ></el-table-column>
        <el-table-column :label="$ct('label.sex')" width="80">
          <template slot-scope="scope">
            <span v-if="scope.row.sex == 1">{{ $ct("label.male") }}</span>
            <span v-if="scope.row.sex == 2">{{ $ct("label.female") }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="staff_no"
          :label="$ct('label.workNo')"
          width="100"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column prop="age" :label="$ct('label.age')"></el-table-column>
        <el-table-column prop="region" label="所属地域"></el-table-column>
        <el-table-column prop="native_place" label=" 籍贯"></el-table-column>
        <el-table-column
          prop="valid_date"
          :label="$ct('label.driverLicenseValidate')"
          width="150"
        ></el-table-column>
        <el-table-column :label="$ct('label.checkStatus')">
          <template slot-scope="scope">
            <span v-if="scope.row.audit_status == -1">{{
              $ct("label.checkUnpass")
            }}</span>
            <span v-if="scope.row.audit_status == 0">
              {{ $ct("label.uncheck") }}</span
            >
            <span v-if="scope.row.audit_status == 1">{{
              $ct("label.checkSuccess")
            }}</span>
            <span v-if="scope.row.audit_status == 3">{{
              $ct("label.completed")
            }}</span>
            <span v-if="scope.row.audit_status == 4">{{
              $ct("label.deleted")
            }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="plate_no" :label="$ct('label.bindedCar')">
          <template slot-scope="scope">
            <span class="bindedCar" @click="jump(scope.row)">{{
              scope.row.plate_no
            }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="$ct('label.white&blackList')">
          <template slot-scope="scope">
            {{
              scope.row.black_white
                ? scope.row.black_white === 1
                  ? $ct("label.whiteList")
                  : $ct("label.blackList")
                : $ct("label.unset")
            }}
          </template>
        </el-table-column> -->

      </el-table>
    </template>
    <template slot="footer">
      <el-pagination background small layout="prev, pager, next, total" :pager-count="5"
        :current-page.sync="pager.current" @current-change="getTableList" :page-size="pager.size" :total="pager.total">
      </el-pagination>
    </template>
    <MultipleImport ref="multipleImport" @refresh="getTableList()"></MultipleImport>
    <DriverInfo ref="driverInfo" @afterCommit="getTableList()"></DriverInfo>
    <CarBind ref="carBind" @afterCommit="getTableList()"></CarBind>
    <!-- 表格显示配置弹窗 -->
    <TableShowConfigList ref="tableShowConfigList" v-model="tableSettingList" :list="allSettingList"
      @tableValue="getSetValue" pageName="driverMgt" :isSave="true" :defaultSetting="defaultSettingList"
      @change="settable">
    </TableShowConfigList>
    <VehicleList ref="vehicleList"></VehicleList>
  </Layout>
</template>

<script>
/**
 * @Author: yezy
 * @Email: <EMAIL>
 * @Date: 2019/12/25 9:50
 * @LastEditors: yezy
 * @LastEditTime: 2019/12/25 9:50
 * @Description:
 */
import DriverInfo from "./modal/DriverInfo";
import MultipleImport from './modal/MultipleImport'
import CarBind from "./modal/CarBind";
import TableShowConfigList from "@/view/report/components/TableShowConfigList";
import { allSettingList } from './driverTable.js'
import VehicleList from "./modal/VehicleList";

export default {
  name: "driverMgt",
  components: {
    DriverInfo,
    CarBind,
    MultipleImport,
    TableShowConfigList,
    VehicleList
  },
  data() {
    return {
      selection: [],
      query: {
        type: 2,
        value: null,
        deptId: [],
      },
      pager: {
        current: 1,
        total: 0,
        size: 30,
      },
      table: {
        loading: false,
        data: [],
      },
      allSettingList,
      tableSettingList: [],
      defaultSettingList: allSettingList,
      exportLoading: false,

    };
  },
  methods: {
    settable() {
      this.$nextTick(() => {
        this.$refs.table.doLayout()
      })
    },
    // 点击设置表格按钮
    operateTableSetting() {
      this.$refs.tableShowConfigList.showModel()
    },

    getSetValue(val) {
      if (!val) {
        this.tableSettingList = this.defaultSettingList
      } else {
        this.tableSettingList = val
      }
    },
    async multipleDelete() {
      if (this.selection.length == 0) {
        this.$warning("未选择批量删除的驾驶员")
        return
      }
      await this.$confirm(this.$ct("messageInfo.0"), this.$ct("label.tip"), {
        confirmButtonText: this.$ct("label.confirm"),
        cancelButtonText: this.$ct("label.cancel"),
        type: "error",
      });
      let params = {
        // uni_id: row.uni_id,
        ids: this.selection.map(item => item.id),
      };
      let res = await this.$api.deleteSysDriverInfo(params);
      if (res.code === 0 && res.base_resp_delete.RS === 1) {
        this.$success(this.$ct("label.delSuccess"));
        await this.getTableList();
      } else {
        this.$error(res.base_resp_delete.Reason);
      }
    },
    multipleImport() {
      this.$refs['multipleImport'].showModal();
    },
    vehicleList(row){
      this.$refs['vehicleList'].showModal(row);
    },
    jump(data) {
      this.$router.push({
        name: "carMgt",
        query: {
          plate_no: data.plate_no,
        },
      });
    },
    handleSelectionChange(list) {
      this.selection = list
    },
    // async checkAllVehicle($tree) {
    //     let rootNodes = $tree.data.map($tree.getNode);
    //     rootNodes.forEach(node => {
    //         node.setChecked(true, true)
    //     })
    //     this.query.deptId = await $tree.getCheckedNodes(true).filter(item => item.type >= 3).map(item => item.id);
    //     this.getTableList();
    // },
    selectNodes(current, { checkedNodes }) {
      let currentNodes = checkedNodes
        .filter((item) => item.type >= 3)
        .map((item) => item.id);
      this.query.deptId = currentNodes;
      this.getTableList();
    },
    async getTableList() {
      this.table.loading = true;
      try {
        let params = {
          dept_id_list: this.query.deptId,
          phone: this.query.type === 0 ? this.query.value : "",
          idcard_no: this.query.type === 1 ? this.query.value : "",
          name: this.query.type === 2 ? this.query.value : "",
          page: this.pager.current,
          count: this.pager.size,
          sorting: -1,
        };
        let res = await this.$api.getSysDriverPageV2(params);
        this.table.data = [];
        this.pager.total = 0;
        if (res.RS === 1) {
          this.table.data = res.DriverList;
          this.pager.total = res.Count;
        }
        this.$nextTick(() => {
          this.$refs["table"].doLayout();
        });
      } catch (e) {
        this.$error(e);
      } finally {
        this.table.loading = false;
      }
    },
    async deleteDriver(row) {
      await this.$confirm(this.$ct("messageInfo.0"), this.$ct("label.tip"), {
        confirmButtonText: this.$ct("label.confirm"),
        cancelButtonText: this.$ct("label.cancel"),
        type: "error",
      });
      let params = {
        uni_id: row.uni_id,
        id: row.id,
      };
      let res = await this.$api.deleteSysDriverInfo(params);
      if (res.code === 0 && res.base_resp_delete.RS === 1) {
        this.$success(this.$ct("label.delSuccess"));
        await this.getTableList();
      } else {
        this.$error(res.base_resp_delete.Reason);
      }
    },
    async exportDataInfo(modify) {
      var toModify = (modify != null && modify == 1);
      if (this.table.data.length == 0) {
        this.$warning("没有数据可以导出");
        return;
      } else {
        this.exportLoading = true;
        let params = {
          dept_id_list: this.query.deptId,
          phone: this.query.type === 0 ? this.query.value : "",
          idcard_no: this.query.type === 1 ? this.query.value : "",
          name: this.query.type === 2 ? this.query.value : "",
          page: this.pager.current,
          count: this.pager.size,
          sorting: -1,
          headers: toModify
            // 导出支持修改后重新导入
            ? ["company_name","name","sex","idcard_no","phone","staff_no","region","native_place","valid_date","plate_no","salary_basic","perfect_attendance_days","audit_status","remark"]
            // 导出和前端页面列对应（或支持自定义列导出）
            : this.tableSettingList.map(item => item.key)
        };
        await this.$utils.excelExport(
          "/ponysafety2/a/driver/table/export",
          JSON.stringify(params),
          "司机管理" + ".xlsx"
        );
        this.exportLoading = false;
      }
    },
  },
  activated() {
    if (this.$route.query.name) {
      this.query.value = this.$route.query.name;
      this.$nextTick(async () => {
        await this.getTableList();
        await this.$router.push("/home/<USER>");
      });

    }
  },
  mounted() {
    if (this.$route.query.name) {
      this.query.value = this.$route.query.name;
      this.getTableList();
    }
    this.getTableList();
  },
};
</script>

<style scoped lang="scss">
.bindedCar:hover {
  color: #2880e2;
  cursor: pointer;
}
</style>

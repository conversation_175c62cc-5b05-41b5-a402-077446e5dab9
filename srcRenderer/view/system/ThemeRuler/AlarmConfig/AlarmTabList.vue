<template>
    <div class="alarm-tab-list">
        <el-tabs v-model="tabName" v-loading="loading"  >
            <el-tab-pane :label="`${item.label} 报警配置`" :name="item.key" :key="item.key" v-for="(item,index) in alarmTypeList"
                         style="border-top: 1px solid #ecdecd">
                <el-checkbox v-model="item.checkAll"
                             @change="checkAll(index)">全选
                </el-checkbox>
                <div style="margin: 10px 0;"></div>
                <el-checkbox-group v-model="item.checked" @change="checkCheckAll(item)">
                    <el-checkbox v-for="alarmItem in item.list" :label="alarmItem.code" :key="alarmItem.id">{{alarmItem.name}}</el-checkbox>
                </el-checkbox-group>
            </el-tab-pane>
        </el-tabs>
    </div>

</template>

<script>
    export default {
        name: "alarm-tab-list",
        model: {
            prop: 'value',
            event: 'change'
        },
        props: {
            value: Array,
        },
        data() {
            return {
                tabName: 'gps',
                loading: false,
                alarmTypeList: [
                    {label: 'GPS', value: 0, key: 'gps', list: [], checkAll: false, checked: [],},
                    {label: 'DMS', value: 1, key: 'dms', list: [], checkAll: false, checked: [],},
                    {label: 'ADAS', value: 2, key: 'adas', list: [], checkAll: false, checked: [],},
                    {label: 'VADS', value: 4, key: 'vads', list: [], checkAll: false, checked: [],},
                    {label: 'VDSS', value: 5, key: 'vdss', list: [], checkAll: false, checked: [],},
                    {label: '渣土', value: 3, key: 'buss', list: [], checkAll: false, checked: [],},
                    {label: '冷链', value: 7, key: 'cold', list: [], checkAll: false, checked: [],},
                    {label: 'BSD', value: 8, key: 'bds', list: [], checkAll: false, checked: [],},
                    {label: 'TBOX-FAULT', value: 9, key: 'tbox_fault', list: [], checkAll: false, checked: [],},
                    {label: 'TBOX-DRIVER', value: 10, key: 'tbox_driver', list: [], checkAll: false, checked: [],},
                    {label: 'BUSSINESS 业务', value: 6, key: 'bussiness', list: [], checkAll: false, checked: [],},
                    {label: '电子锁', value: 11, key: 'elock', list: [], checkAll: false, checked: [],},
                    {label: 'CONTAINER 货箱识别', value: 12, key: 'container', list: [], checkAll: false, checked: [],},
                    {label: 'Renewable 新能源', value: 13, key: 'newEnergy', list: [], checkAll: false, checked: [],},
                    {label: 'AEB 制动', value: 14, key: 'aeb', list: [], checkAll: false, checked: [],},

                ],

            }
        },
        watch: {
            value: 'refreshChecked'
        },
        methods: {
            completedValue() {
                let res = this.alarmTypeList.map(item => item.checked).flat().map(item => item);
                this.$emit('change', res);
            },
            checkAll(index) {
                let alarmItem = this.alarmTypeList[index];

                if (alarmItem.checkAll) {
                    alarmItem.checked = alarmItem.list.map(item => item.code);
                } else {
                    alarmItem.checked = [];
                }
                this.completedValue();
            },
            checkCheckAll(item) {
                item.checkAll = item.list.length === item.checked.length;
                this.completedValue();
            },
            refreshChecked() {
                this.alarmTypeList.forEach((item, index) => {
                    item.checked = item.list.filter(item => {
                        return ~this.value.indexOf(item.code);
                    }).map(item => item.code)
                    item.checkAll = item.list.length === item.checked.length;
                })
            }
        },
        async mounted() {
            this.loading = true;
            let res = await Promise.all(this.alarmTypeList.map(item => {
                return this.$api.getSysAlarmTypes({alarm_type: item.value, type: 2})
            }))
		        console.log(res,'res')
            this.alarmTypeList.forEach((item, index) => {
                item.list = res[index].alarm_type_list;
            })
            this.refreshChecked()
            this.loading = false;
        }
    }
</script>

<style scoped lang="scss">
    .alarm-tab-list{
        /deep/ .el-tab-pane{
            padding: 7px;
            .el-checkbox {
                width: 222px;
                margin-right: 0;
                .el-checkbox__input {
                    width: 14px;
                    vertical-align: 3px;
                }
                .el-checkbox__label {
                    width: calc(100% - 14px);
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }
            }
        }
    }
</style>

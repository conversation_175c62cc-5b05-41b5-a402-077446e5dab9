<template>
    <Layout class="layout--theme-1 ">
        <template slot="aside">
            <ElementTree type="department" @node-click="chooseNode" :shezhiShow="false"
                         @mounted="treeMounted"></ElementTree>
        </template>
        <template slot="content">
            <el-tabs v-model="activeName" @tab-click="getData">
                <el-tab-pane label="超速报警" name="overspeedAlarm">
                    <OverspeedAlarm ref="overspeedAlarm" @del="del"></OverspeedAlarm>
                </el-tab-pane>
                <el-tab-pane label="路口超速" name="crossingSpeeding">
                    <CrossingSpeeding ref="crossingSpeeding" @del="del"></CrossingSpeeding>
                </el-tab-pane>
                <el-tab-pane label="转弯超速" name="turningOverspeed">
                    <TurningOverspeed ref="turningOverspeed" @del="del"></TurningOverspeed>
                </el-tab-pane>
                <el-tab-pane label="连续超速" name="continuousOverspeed">
                    <ContinuousOverspeed ref="continuousOverspeed" @del="del"></ContinuousOverspeed>
                </el-tab-pane>
                <el-tab-pane label="作业超时驾驶" name="workOvertimeDriving">
                    <WorkOvertimeDriving ref="workOvertimeDriving" @del="del"></WorkOvertimeDriving>
                </el-tab-pane>
                <el-tab-pane label="限速圈围栏限速" name="speedLimitFence">
                    <SpeedLimitFence ref="speedLimitFence" @del="del"></SpeedLimitFence>
                </el-tab-pane>
                <el-tab-pane label="高风险围栏限速" name="highRiskFence">
                    <HighRiskFence ref="highRiskFence" @del="del"></HighRiskFence>
                </el-tab-pane>
            </el-tabs>
        </template>
    </Layout>
</template>
<script>
import OverspeedAlarm from "./components/OverspeedAlarm";
import CrossingSpeeding from "./components/CrossingSpeeding";
import ContinuousOverspeed from "./components/ContinuousOverspeed";
import TurningOverspeed from "./components/TurningOverspeed";
import WorkOvertimeDriving from "./components/WorkOvertimeDriving";
import SpeedLimitFence from "./components/SpeedLimitFence";
import HighRiskFence from "./components/HighRiskFence";
export default {
    name: "over-speed",
    data() {
        return {
            type: null,
            activeName: 'overspeedAlarm',
            currentId: ''
        };
    },
    components: {
        OverspeedAlarm,
        CrossingSpeeding,
        ContinuousOverspeed,
        TurningOverspeed,
        WorkOvertimeDriving,
        SpeedLimitFence,
        HighRiskFence
    },
    methods: {
        treeMounted($tree) {
            this.$refs.overspeedAlarm.getList()
            this.$refs.crossingSpeeding.getList()
            this.$refs.continuousOverspeed.getList()
            this.$refs.turningOverspeed.getList()
            this.$refs.workOvertimeDriving.getList()
            this.$refs.highRiskFence.getList()

    
        },
        chooseNode: _.debounce(function (data, node, $node) {
            this.currentId = data.id
            this.type = data.type
            this.getData()
        }, 222),
        getData() {
            if (!this.currentId) {
                return
            }
            switch (this.activeName) {
                case 'overspeedAlarm':
                    this.$refs.overspeedAlarm.getList(this.currentId)
                    break
                case 'crossingSpeeding':
                    this.$refs.crossingSpeeding.getList(this.currentId)
                    break
                case 'continuousOverspeed':
                    this.$refs.continuousOverspeed.getList(this.currentId)
                    break
                case 'turningOverspeed':
                    this.$refs.turningOverspeed.getList(this.currentId)
                    break
                case 'workOvertimeDriving':
                    this.$refs.workOvertimeDriving.getList(this.currentId)
                    break
                case 'speedLimitFence':
                    this.$refs.speedLimitFence.getSysConfigSpeed(this.currentId, this.type)
                    break
                case 'highRiskFence':
                    this.$refs.highRiskFence.getList(this.currentId)
                    break
            }
        },
        //新增,修改速度配置
        async del(id,fn) {
            await this.$confirm("确认要删除此记录吗?", "确认删除", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "error",
            });
            let res = await this.$api.delTTS({
                id: id,
            });
            if (res.RS == 1) {
                this.$success("删除成功")
                fn(this.currentId)
            } else {
                this.$error("删除失败" + res.message)
            }
        },
    },
};
</script>


<style lang="scss" scoped>
.ruler-config {
    .rules-unit {
        padding-left: 10px;
    }
    
    .el-input {
        max-width: 300px;
    }
    
    .industry-item {
        color: var(--color-text-regular);
        padding: 15px;
        cursor: pointer;
        transition: all .3s;
        
        &:hover {
            background-color: var(--color-primary-o-10);
        }
        
        &.active {
            color: var(--color-text-primary);
            background-color: var(--color-primary);
        }
    }
    
    .title {
        height: 32px;
        line-height: 32px;
        margin: 4px 0;
        font-size: 14px;
        color: var(--color-primary);
        
        &:before {
            content: ' ';
            border-left: 2px solid var(--color-primary);
            height: 24px;
            margin-right: 10px;
        }
    }
    
    .query-item {
        display: flex;
        height: 40px;
        line-height: 40px;
        // .label{
        //     width: 100px;
        //     text-align: right;
        // }
        > span {
            flex-shrink: 0;
            margin-right: 10px;
        }
    }
    
    .btn {
        margin-top: 10px;
        margin-bottom: 35px;
    }
}

.page {
    line-height: 20px !important;
}
</style>

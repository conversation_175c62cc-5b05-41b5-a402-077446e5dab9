<template>
    <Layout tag="div" class="VehicleRunTrack" :contentLoading="table.loading" :has-color="true">
        <template slot="query">
            <div class="query-item">
                <el-button type="primary" @click="add">新增</el-button>
            </div>
            <div class="break-item"></div>
            <div class="query-item page">
                <el-pagination small background :current-page.sync="table.page" :page-size="table.size"
                               layout="prev, pager, next, total" :total="table.list.length">
                </el-pagination>
            </div>
        </template>
        <el-table ref="table" class="el-table--ellipsis el-table--radius" slot="content" border stripe
                  highlight-current-row size="mini" :data="formatList" height="100%">
            <el-table-column type="index" :index="(index) => index + 1 + pageStart" label="序号"
                             min-width="50"></el-table-column>
            <el-table-column prop="dept_name" label="部门" show-overflow-tooltip></el-table-column>
            <el-table-column prop="config_value_s1" label="普通道路超速值(km/h)"></el-table-column>
            <el-table-column prop="config_value_s2" label="高速道路超速值(km/h)"></el-table-column>
            <el-table-column prop="config_value_duration" label="持续时长(s)"></el-table-column>

            <el-table-column prop="update_by" label="更新人"></el-table-column>
            <el-table-column prop="update_time" label="更新时间"></el-table-column>
            <el-table-column label="操作" min-width="70">
                <template slot-scope="{ row }">
                    <el-button type="text" title="修改" size="mini">
                        <i class="pony-iconv2 pony-xiugai" @click="edit(row)"></i>
                    </el-button>
                    <el-button type="text" title="删除" size="mini" @click="del(row)">
                        <i class="pony-iconv2 pony-shanchu"></i>
                    </el-button>
                </template>
            </el-table-column>
        </el-table>
        <AddSpeedConfig ref="addSpeedConfig" @refresh="getList"></AddSpeedConfig>
    </Layout>
</template>

<script>
import AddSpeedConfig from "./../../modal/addSpeedConfig";

export default {
    name: "OverspeedAlarm",
    components: {
        AddSpeedConfig
    },
    data() {
        return {
            table: {
                loading: false,
                list: [],
                page: 1,
                size: 30,
                total: 0
            },
            currentId: null
        }
    },
    computed: {
        pageStart() {
            return (this.table.page - 1) * this.table.size
        },
        formatList() {
            return this.table.list.slice(this.pageStart, this.pageStart + this.table.size)
        },
    },
    methods: {
        add() {
            this.$refs.addSpeedConfig.showModal()
        },
        edit(row) {
            this.$refs.addSpeedConfig.showModal(row)
        },
        async getList(id) {
            if (id) {
                this.currentId = id
            }
            this.table.list = []
            this.table.loading = true
            const res = await this.$api.getEventRulerConfig({
                version: 'SGSOverSpeed',
                obj_id: this.currentId
            })
            this.table.loading = false
            if (res.data.length === 0) {
                return this.$warning('暂无数据！')
            }
            if (res.status === 200) {
                this.table.list = res.data
            }
        },
        del(rowData) {
            this.$emit('del', rowData.id,() => this.getList())
        }
    }
}
</script>

<style scoped lang="scss">

</style>

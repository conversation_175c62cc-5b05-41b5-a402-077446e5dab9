<template>
        <div class="intensity">
        <div class="left">
    <Layout :has-color="true">
        <template slot="query">
            <div class="query-item">
                <div class="title"><i style="background-color:#2880E2"></i><span>中外运短倒工作时长配置</span></div>
            </div>
        </template>
        <template slot="content">
            <el-tabs type="border-card" v-model="radio">
                <el-tab-pane label="白班时间" name="109">
                    <el-row style="height:100%;width:50%;padding: 20px;">
                        <el-col :span="8" style="height: 100%;">
                            <ZtreeMatics treeType="vehicle" ref="vehicleTree" :checkMode="true" :ztreeCheckHandle="ztreeCheckHandle" :ztreeClickHandle="ztreeClickHandle">
                            </ZtreeMatics>
                        </el-col>
                        <el-col :span="12">
                            <el-form v-model="form109" label-width="110px" style="margin-top:50px;">
                                <el-form-item label="白班">
                                    <el-form-item label="开始时间">
                                        <el-time-picker v-model="form109.day.start" placeholder="请选择开始时间" clearable value-format="HH:mm:ss" style="width:100%">

                                        </el-time-picker>
                                    </el-form-item>
                                    <el-form-item label="结束时间">
                                        <el-time-picker v-model="form109.day.end" placeholder="请选择结束时间" clearable value-format="HH:mm:ss" style="width:100%">

                                        </el-time-picker>
                                    </el-form-item>
                                </el-form-item>
                                <el-form-item label="夜班">
                                    <el-form-item label="开始时间">
                                        <el-time-picker v-model="form109.night.start" placeholder="请选择开始时间" clearable value-format="HH:mm:ss" style="width:100%">

                                        </el-time-picker>
                                    </el-form-item>
                                    <el-form-item label="结束时间">
                                        <el-time-picker v-model="form109.night.end" placeholder="请选择结束时间" clearable value-format="HH:mm:ss" style="width:100%">

                                        </el-time-picker>
                                    </el-form-item>
                                </el-form-item>
                                <el-form-item>
                                    <el-form-item label="加班时长(分钟)">
                                    <el-input v-model="form109.delay" placeholder="请填写加班时长">

                                    </el-input>
                                </el-form-item>
                                </el-form-item>
                                <el-form-item size="large">
                                    <el-button type="primary" @click="chooseVehicle(109)">保存配置</el-button>
                                </el-form-item>
                            </el-form>
                        </el-col>

                    </el-row>
                </el-tab-pane>
                <el-tab-pane label="长白班" name="110">
                    <el-row style="height:100%;width:50%;padding: 20px;">
                        <el-col :span="8" style="height: 100%;">
                            <ZtreeMatics treeType="vehicle" ref="vehicleLongTree" :checkMode="true" :ztreeCheckHandle="ztreeLongCheckHandle" :ztreeClickHandle="ztreeLongClickHandle">
                            </ZtreeMatics>
                        </el-col>
                        <el-col :span="12">
                            <el-form v-model="form110" label-width="140px" style="margin-top:50px;">
                                <el-form-item label="开始时间">
                                    <el-time-picker v-model="form110.start" placeholder="请选择开始时间" clearable value-format="HH:mm:ss" style="width:100%">

                                    </el-time-picker>
                                </el-form-item>
                                <el-form-item label="结束时间">
                                    <el-time-picker v-model="form110.end" placeholder="请选择结束时间" clearable value-format="HH:mm:ss" style="width:100%">

                                    </el-time-picker>
                                </el-form-item>

                                <el-form-item label="加班时长(分钟)">
                                    <el-input v-model="form110.delay" placeholder="请填写加班时长">

                                    </el-input>
                                </el-form-item>
                                <el-form-item label="计算停止时间(次日)">
                                    <el-time-picker v-model="form110.stop" placeholder="请选择计算停止时间" clearable
                                    value-format="HH:mm:ss" style="width:100%" ></el-time-picker>
                                </el-form-item>
                                <el-form-item size="large">
                                    <el-button type="primary" @click="chooseVehicle(110)">保存配置</el-button>
                                </el-form-item>
                            </el-form>
                        </el-col>
                    </el-row>
                </el-tab-pane>
            </el-tabs>
        </template>
    </Layout>
    </div>
    <div class="right">
            <el-card shadow="never" class="card">
                <div class="title"><i style="background-color:#2880E2"></i><span>短倒停留时长配置</span></div>
                <div ref="chart1" id="chart1">
                    <div class="input">
                        <el-input v-model="data.configValue" style="width: 200px; padding:10px 0;">
                            <template slot="append">分钟</template>
                        </el-input>
                    </div>
                    <el-button type="primary" @click="updateSysConfigTime()">确定</el-button>
                </div>
            </el-card>
        </div>
        </div>
</template>
<script>
export default {
    name: 'shortStopTime',
    data() {
        return {
            radio: '109',
            ids109: [],
            ids110: [],
            vehicle109: [],
            vehicle110: [],
            config109Obj: {
                id: 1,
                obj_id: '',
                obj_type: '',
                config_type: '',
                config_value: '',
            },
            config110Obj: {
                id: 1,
                obj_id: '',
                obj_type: '',
                config_type: '',
                config_value: '',
            },
            form109: {
                day: {
                    start: '',
                    end: '',
                },

                night: {
                    start: '',
                    end: '',
                },
                delay: '',

            },
            form110: {
                start: '',
                end: '',
                delay: '',
                stop:''
            },
            data: {
                configValue: '',
                id:''
            },
        }
    },
    mounted() {
        this.initData()
        this.getSysConfigTime()
    },
    watch: {
        'radio'(val){
            this.initData()
        }
    },
    computed: {

    },
    methods: {
        async getSysConfigTime() {
            let res = await this.$api.getShortHaulConfig()
            if (!res || res.status != 200) {
                this.$error('查询出错!')
                return
            }
            if (res.data) {
                this.data = res.data
            }
        },
        async updateSysConfigTime() {
                let res = await this.$api.updateShortHaulConfig({
                    configValue: this.data.configValue,
                    id: this.data.id
                })
                if (!res || res.status != 200) {
                    this.$error('配置出错!')
                    return
                }
                this.$success('配置成功')

        },
        async initData() {
            let res = await this.$api.getEventRulerConfig({
                version: "SinotransWorktimeV2"
            })
            if (res.status == 200) {
                this.config109Obj = res.data.time109
                this.config110Obj = res.data.time110
                let day109 = JSON.parse(res.data.time109.config_value)
                let day110 = JSON.parse(res.data.time110.config_value)
                this.form109 = day109
                this.form110.start = day110.day.start
                this.form110.end = day110.day.end
                this.form110.delay = day110.delay
                this.form110.stop = day110.stop
            }
            await this.$refs['vehicleTree'].waitForInit
            await this.$refs['vehicleLongTree'].waitForInit

            res.data.ids110.forEach(item => {
                let current = this.$refs['vehicleTree'].ztreeObj.getNodesByParam('id', item, null)
                this.$refs['vehicleTree'].ztreeObj.setChkDisabled(current[0], true)
                this.$refs['vehicleTree'].ztreeObj.updateNode(current[0])
            })
            res.data.ids110.forEach(item => {
                let current = this.$refs['vehicleLongTree'].ztreeObj.getNodesByParam('id', item, null)
                this.$refs['vehicleLongTree'].ztreeObj.checkNode(current[0], true, true, true)
                this.$refs['vehicleLongTree'].ztreeObj.updateNode(current[0])
            })
            res.data.ids109.forEach(item => {
                let current = this.$refs['vehicleLongTree'].ztreeObj.getNodesByParam('id', item, null)

                this.$refs['vehicleLongTree'].ztreeObj.setChkDisabled(current[0], true)
                this.$refs['vehicleLongTree'].ztreeObj.updateNode(current[0])
            })
            res.data.ids109.forEach(item => {
                let current = this.$refs['vehicleTree'].ztreeObj.getNodesByParam('id', item, null)
                this.$refs['vehicleTree'].ztreeObj.checkNode(current[0], true, true, true)
                this.$refs['vehicleTree'].ztreeObj.updateNode(current[0])
            })
        },
        async chooseVehicle(type) {
            let params = {
                operateType: 11,
                type: type,
                ids: []
            }
            if (type == 109) {
                this.config109Obj.config_value = JSON.stringify(this.form109)
                params.ids = this.vehicle109
            } else {
                this.config110Obj.config_value = JSON.stringify({
                    day: { start: this.form110.start, end: this.form110.end },
                    delay: this.form110.delay,
                    stop:this.form110.stop
                })
                params.ids = this.vehicle110

            }
            let [configRes, vehicleRes] = await Promise.all([this.$api.insertSysConfig(type == 109 ? this.config109Obj : this.config110Obj), this.$api.deleteLongBillWay(params)])
            if (configRes.RS == 1 && vehicleRes.status == 200) {
                this.$success('设置成功')
            } else {
                this.$error('设置失败')
            }

        },
        ztreeCheckHandle(event, treeId, treeNode) {
            if (!treeNode) return
            this.vehicle109 = this.$refs['vehicleTree'].ztreeObj.getCheckedNodes().filter(item => item.type === 4).map(current => {
                return current.id
            });

        },
        ztreeClickHandle(event, treeId, treeNode) {
            if (!treeNode) return
            if (treeNode.checked) {
                this.$refs['vehicleTree'].ztreeObj.checkNode(treeNode, false, true, true)
            } else {
                this.$refs['vehicleTree'].ztreeObj.checkNode(treeNode, true, true, true)
            }

        },
        ztreeLongCheckHandle(event, treeId, treeNode) {
            if (!treeNode) return
            this.vehicle110 = this.$refs['vehicleLongTree'].ztreeObj.getCheckedNodes().filter(item => item.type === 4).map(current => {
                return current.id
            });
        },
        ztreeLongClickHandle(event, treeId, treeNode) {
            if (!treeNode) return
            if (treeNode.checked) {
                this.$refs['vehicleLongTree'].ztreeObj.checkNode(treeNode, false, true, true)
            } else {
                this.$refs['vehicleLongTree'].ztreeObj.checkNode(treeNode, true, true, true)
            }

        }

    }
}
</script>
<style lang="scss" scoped>
 .intensity {
        display: flex;
        height: 100%;

        .left {
            width: 85%;
            height: 100%;
            .query-top {
                height: 100%;
            }
        }

        .right {
            width: 15%;
            height: 100%;
            padding-top: 44px;
            padding-right: 10px;
            display: flex;
            flex-direction: column;

            .card {
                margin-bottom: 10px;
                flex: 1;
                background-color: var(--background-color-light);

                .title {
                    i {
                        display: inline-block;
                        width: 2px;
                        height: 20px;
                        margin-right: 8px;
                        vertical-align: -5px;
                        border-radius: 3px;
                    }
                }
            }

            .bottom {
                margin-bottom: 45px;
            }

            #chart1 {
                width: 100%;
                height: 300px;
            }

            #chart2 {
                width: 100%;
                height: 300px;
            }
        }
    }
</style>
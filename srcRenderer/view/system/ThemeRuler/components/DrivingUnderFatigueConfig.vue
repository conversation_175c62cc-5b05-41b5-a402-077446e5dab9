<template>
    <Layout tag="div" class="VehicleRunTrack" :contentLoading="table.loading" :has-color="true">
        <template slot="aside">
            <ElementTree type="department" @node-click="nodeClick" ref="vehicleTree" node-key="id"
                         @mounted="treeMounted" :shezhiShow="false"></ElementTree>
        </template>
        <template slot="query">
            <div class="query-item">
                <el-button type="primary" @click="add">新增</el-button>
            </div>
            <div class="break-item"></div>
            <div class="query-item">
                <el-pagination small background :current-page.sync="table.page" :page-size="table.size"
                               layout="prev, pager, next, total" :total="table.list.length">
                </el-pagination>
            </div>
        </template>
        <el-table ref="table" class="el-table--ellipsis el-table--radius" slot="content" border stripe
                  highlight-current-row size="mini" :data="formatList" height="100%">
            <el-table-column type="index" :index="(index) => index + 1 + pageStart" label="序号"
                             min-width="50"></el-table-column>
            <el-table-column prop="dept_name" label="部门"></el-table-column>
            <el-table-column prop="config_value_tired" label="超时驾驶时长(小时)"></el-table-column>
            <el-table-column prop="config_value_interval" label="连续停驶时长(分钟)"></el-table-column>
            <el-table-column label="停车判断">
                <el-table-column prop="config_value_acc" label="速度/ACC">
                    <template slot-scope="{row}">
                        {{ row.config_value_acc ? 'ACC' : '' }}
                        {{ row.config_value_speed && row.config_value_acc ? ',' : '' }}
                        {{ row.config_value_speed ? '速度' : '' }}
                    </template>
                </el-table-column>
                <el-table-column prop="config_value_speedValue" label="速度"></el-table-column>
            </el-table-column>
            <el-table-column prop="update_time" label="更新时间"></el-table-column>
            <el-table-column prop="update_by" label="更新人"></el-table-column>
            <el-table-column prop="remark" label="备注"></el-table-column>
            <el-table-column label="操作" min-width="70">
                <template slot-scope="{ row }">
                    <el-button
                     type="text"
                     title="修改"
                     size="mini"
                     @click="modify(row)"
                    >
                        <i class="pony-iconv2 pony-xiugai"></i>
                    </el-button>
                    <el-button
                     type="text"
                     title="删除"
                     size="mini"
                     @click="del(row)"
                    >
                        <i class="pony-iconv2 pony-shanchu"></i>
                    </el-button>
                </template>
            </el-table-column>
        </el-table>
        <AddDriverUnderFatigue ref="addDriverUnderFatigue" @update="search"></AddDriverUnderFatigue>
    </Layout>
</template>

<script>
import AddDriverUnderFatigue from "./modal/addDriverUnderFatigue";

export default {
    name: 'timeReport',
    components: {
        AddDriverUnderFatigue
    },
    data() {
        return {
            table: {
                loading: false,
                list: [],
                page: 1,
                size: 30,
                total: 0
            },
            currentNodes: null
        }
    },
    computed: {
        pageStart() {
            return (this.table.page - 1) * this.table.size
        },
        formatList() {
            return this.table.list.slice(this.pageStart, this.pageStart + this.table.size)
        },
    },
    methods: {
        treeMounted($tree) {
            this.search()
        },
        // 单选树节点选择
        nodeClick(data, node) {
            this.currentNodes = data.id;
            this.search()
        },
        async search() {
            this.table.list = []
            this.table.loading = true
            let res = await this.$api.getEventRulerConfig({
                version: "SGSTiredDrive",
                obj_id: this.currentNodes
            });
            this.table.loading = false
            if (res.status !== 200) {
                return this.$error('查询失败！')
            }
            if (res.data.length === 0) {
                return this.$warning('暂无数据!')
            }
            this.table.list = res.data
            this.$nextTick(() => {
                this.$refs.table.doLayout()
            })
        },
        add() {
            this.$refs.addDriverUnderFatigue.showModal()
        },
        modify(rowData) {
            this.$refs.addDriverUnderFatigue.showModal(rowData);
        },
        async del(rowData) {
            await this.$confirm("确认要删除此记录吗?", "确认删除", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "error",
            });
            let res = await this.$api.delTTS({
                id: rowData.id,
            });
            if (res.RS == 1) {
                this.$success("删除成功")
                await this.search()
            } else {
                this.$error("删除失败" + res.message)
            }
        }
    }
}
</script>
<style lang='scss' scoped>
.speedSearch {
    margin-left: 20px;
}

.VehicleRunTrack {
    .query-top {
        height: 100%;
    }
    
    .query-bottom {
        margin-top: 5px;
        padding: 10px;
        
        .query-item {
            display: flex;
            height: 40px;
            line-height: 40px;
            justify-content: space-between;
            align-items: center;
            
            > span {
                font-size: 12px;
                white-space: nowrap;
                margin-right: 5px;
            }
        }
    }
}
</style>

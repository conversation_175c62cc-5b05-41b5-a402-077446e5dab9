<template>
    <PonyDialog
     v-model="show"
     class="dealer-site-info"
     :has-mask="false"
     :insert-body="true"
     :title="mode === 'add' ? addModeTitle : modifyModeTitle"
     :ok-text="mode === 'add' ? addModeOkText : modifyModeOkText"
     :width="600"
     content-style="min-height:150px;overflow:auto;padding:15px"
     @confirm="add"
     @close="clear"
    >
        <el-row :gutter="20" style="padding:5px 10px;">
            <el-col :span="11">
                <ElementTree
                 type="department"
                 ref="departmentTree"
                 @node-click="nodeClick"
                 node-key="id"
                 style="height:300px"
                 :extraKeys="['type']"
                >
                </ElementTree>
            </el-col>
            <el-col :span="13">
                <el-form
                 :model="form"
                 label-width="140px"
                 size="mini"
                 ref="form"
                 label-position="top"
                 style="padding-top:10px"
                >
                    <el-form-item  label="路口直行超速值:" style="width:100%">
                        <el-input v-model.number="form.straight" placeholder="请输入路口直行超速值">
                            <template slot="append">km/h</template>
                        </el-input>
                    </el-form-item>
                    <el-form-item  label="左转弯超速值:" style="width:100%">
                        <el-input v-model.number="form.left" placeholder="请输入左转弯超速值">
                            <template slot="append">km/h</template>
                        </el-input>
                    </el-form-item>
                    <el-form-item label="右转弯超速值:" style="width:100%">
                        <el-input v-model.number="form.right" placeholder="请输入右转弯超速值">
                            <template slot="append">km/h</template>
                        </el-input>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
    </PonyDialog>
</template>

<script>


export default {
    name: "addSpeedConfig",
    data() {
        const checkNum = (rule, value, callback) => {
            if (!Number.isInteger(value)) {
                callback(new Error('请输入数字值'));
            } else {
                if (value < 0) {
                    callback(new Error('请输入大于0的数字'));
                } else {
                    callback();
                }
            }
        };
        return {
            show: false,
            mode: "add",
            loading: false,
            activeTab: "basic",
            addModeTitle: "新增路口超速",
            addModeOkText: "确认新增",
            modifyModeTitle: "修改路口超速",
            modifyModeOkText: "确认修改",
            form: {
                straight: null,
                left: null,
                right: null,

            },
            deptSelectValue: null,
            deptSelectType: null,
            id: '',
           
        };
    },
    methods: {
        async add() {
            if (!this.deptSelectValue) {
                return this.$warning('请选择部门！')
            }
            this.loading = true;
            try {
                this.$refs["form"].validate(async (valid) => {
                    if (!valid) return;
                    const res = await this.$api.getSpeedSetting({
                        operate_type: 1,
                        target_id: this.deptSelectValue,
                        target_type: this.deptSelectType,
                        config_type: 'crossTurnSpeed',
                        config_value_obj: this.form
                    })
                    if (res.status == 200) {
                        this.$success(this.mode === 'modify' ? "修改成功" : '新增成功');
                        this.$emit("refresh");
                        this.show = false;
                    } else {
                        this.$error(this.mode === 'modify' ? "修改失败" : '新增失败' + res.message);
                        throw new Error(this.mode === 'modify' ? "修改失败" : '新增失败' + res.message);
                    }
                });
            } catch (e) {
                this.$error(e);
            } finally {
                this.loading = false;
            }
        },
        async showModal(rowData) {
            this.show = true;
            if (rowData) {
                this.mode = "modify";
                this.form = JSON.parse(rowData.config_value)
                this.deptSelectType = rowData.obj_type
                this.deptSelectValue = rowData.obj_id
                this.id = rowData.id
                await this.$nextTick();
                await this.$refs['departmentTree'].waitForInit
                const $tree = this.$refs['departmentTree'].$refs['tree']
                $tree.setCurrentKey(this.deptSelectValue)
                const node = $tree.getNode(this.deptSelectValue)
                node.expand(null, true);
            } else {
                this.mode = "add";
                this.clear()
            }
        },
        // 单选树节点选择
        nodeClick(data, node) {
            this.deptSelectValue = data.id;
            this.deptSelectType = data.type
        },
        clear() {
            this.form = {
                straight: null,
                left: null,
                right: null,
            }
            this.deptSelectValue = null;
            this.deptSelectType = null;
            this.id = null
        }
    },
};
</script>

<style scoped lang="scss">
.dealer-site-info {
    .detail .el-form-item--mini.el-form-item {
        height: 28px;
    }
    
    /deep/ .el-tab-pane {
        padding: 10px;
    }
    
    /deep/ .select-tree-input {
        width: 100% !important;
    }
    
    /deep/ .el-divider {
        margin: 12px 0 24px;
        float: left;
        
        .el-divider__text {
            padding: 0 10px;
            background-color: var(--background-color-light);
        }
        
        .el-button--mini.el-button--text {
            vertical-align: -1px;
        }
    }
    
    /deep/ .el-form-item {
        width: 50%;
        float: left;
        
        .el-input {
            width: 100%;
        }
    }
}
</style>

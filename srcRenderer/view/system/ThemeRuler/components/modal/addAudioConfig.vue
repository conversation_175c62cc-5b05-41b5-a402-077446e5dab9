<template>
    <PonyDialog
     v-model="show"
     class="dealer-site-info"
     :has-mask="false"
     :insert-body="true"
     :title="mode === 'add' ? addModeTitle : modifyModeTitle"
     :ok-text="mode === 'add' ? addModeOkText : modifyModeOkText"
     :width="700"
     :loading="loading"
     content-style="min-height:150px;overflow:auto;"
     @confirm="commit"
    >
        <el-row style="height: 350px">
            <el-col :span="12">
                <ElementTree
                 type="vehicle"
                 ref="vehicleTree"
                 @node-click="nodeClick"
                 node-key="id"
                 style="height:350px"
                >
                </ElementTree>
            </el-col>
            <el-col :span="12">
                <el-form ref="form" :model="data" label-width="95px" :rules="rules" size="mini" height="300" class="form">
                    <el-form-item label="音频编码:" prop="audioCodec">
                        <el-select v-model="data.audioCodec" placeholder="音频编码" style="width: 100%">
                        <el-option
                            v-for="item in optionsCode"
                            :key="item"
                            :label="item"
                            :value="item">
                        </el-option>
                    </el-select>
                    </el-form-item>
                    
            <el-form-item label="音频声道数:" prop="audioChannel">
                <el-input v-model="data.audioChannel"></el-input>
            </el-form-item>
            <el-form-item label="音频采样率:" prop="sampleRate">
                <el-select v-model="data.sampleRate" placeholder="音频采样率" style="width: 100%">
                        <el-option
                            v-for="item in optionsRate"
                            :key="item"
                            :label="item"
                            :value="item">
                        </el-option>
                    </el-select>
            </el-form-item>
        </el-form>
            </el-col>
        </el-row>
    </PonyDialog>
</template>
<script>
export default {
    name: "addCycle",
    components: {
    },
    props:{
        showNotice:{
            type:Boolean,
            default:false
        }
    },
    data() {
        return {
            show: false,
            mode: "add",
            loading: false,
            addModeTitle: "新增模板",
            addModeOkText: "确认新增",
            modifyModeTitle: "修改模板",
            modifyModeOkText: "确认修改",
            optionsCode:['NO','G711U','G711A','ADPCMA','G726_16','G726_24','G726_32','G726_40','G726_16_LE','G726_24_LE','G726_32_LE','G726_40_LE'],
            optionsRate:['8000','22050','44100','48000'],
            data: {
                audioCodec:'NO',
                audioChannel:'1',
                sampleRate:'8000',
                
            },
            rules: {
                // sms_name: [
                //     { required: true, message: '请输入模板名称', trigger: 'blur' }
                // ],
                // sms_content: [
                //     { required: true, message: '请输入模板内容', trigger: 'blur' },
                // ],
                //  sms_sign: [
                //     { required: true, message: '请输入短信签名', trigger: 'blur' },
                // ],
                //  sms_ak: [
                //     { required: true, message: '请输入访问秘钥AK', trigger: 'blur' },
                // ],
                //  sms_sk: [
                //     { required: true, message: '请输入访问秘钥SK', trigger: 'blur' },
                // ],
                //  sms_code: [
                //     { required: true, message: '请输入短信CODE', trigger: 'blur' },
                // ],
            },
            deptSelectValue: null,
            deptSelectType: null,

            id: null,
        }
    },
    mounted() {
    },
    methods: {
        
        async add(id) {
            const res = await this.$api.insertSysConfig({
                id: id,
                config_type: 104,
                obj_id: this.deptSelectValue,
                obj_type: this.deptSelectType,
                config_value: JSON.stringify(this.data),
            })
            if (res.RS !== 1) {
                return this.$error(res.Reason || '提交失败！')
            }
            this.$success(`${this.mode === 'add' ? '新增' : '修改'}成功！`)
            this.show = false
            this.$emit('refresh')
        },
        commit() {
            if (!this.deptSelectValue) {
                return this.$warning('请选择配置对象！')
            }
            this.$refs.form.validate((valid) => {
                if (valid) {
                    if (this.mode === "add") {
                        this.add(null);
                    } else {
                        this.add(this.id);
                    }
                }
            })
        },
        async showModal(rowData) {
            this.clearData()
            this.show = true;
            this.params = null;
            if (rowData) {
                this.mode = "modify";
                this.data.audioCodec = rowData.config_value_codec
                this.data.audioChannel = rowData.config_value_channel
                this.data.sampleRate = rowData.config_value_rate
                this.deptSelectType = rowData.obj_type
                
                this.deptSelectValue = rowData.obj_id
                this.id = rowData.id
                await this.$nextTick();
                await this.$refs['vehicleTree'].waitForInit
                const $tree = this.$refs['vehicleTree'].$refs['tree']
                $tree.setCurrentKey(rowData.obj_id)
                const node = $tree.getNode(rowData.obj_id)
                node.expand(null, true);
            } else {
                this.mode = "add";
            }
        },
        // 单选树节点选择
        nodeClick(data, node) {
            this.deptSelectValue = data.id;
            this.deptSelectType = data.type
        },
        clearData() {
            this.data = {
                audioCodec:'NO',
                audioChannel:'1',
                sampleRate:'8000',
            },
            this.deptSelectValue = null
            this.deptSelectType = null
            this.id = null
        }
    }
}
</script>

<style scoped lang="scss">
</style>

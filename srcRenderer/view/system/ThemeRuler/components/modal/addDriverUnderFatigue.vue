<template>
    <PonyDialog v-model="show" class="dealer-site-info" :has-mask="true"
                :title="mode === 'add' ? addModeTitle : modifyModeTitle"
                :ok-text="mode === 'add' ? addModeOkText : modifyModeOkText" :width="500" @confirm="commit"
                @close="close">
        <div class="w100 h100">
            <div class="form-group">
                <div class="title">配置部门</div>
                <div class="item" style="width:200px">
                    <SelectTreeInput v-model="deptSelectValue" ref="deptInput" type="department"
                                     :condition="treeCondition" placeholder="请选择部门" title="请选择部门"
                                     :extraKeys="['type']"></SelectTreeInput>
                </div>
                <div class="title">超时驾驶判定</div>
                <div class="item">
                    <div class="top">
                        超时驾驶时长
                        <el-input-number v-model="form.tired" :min="0"></el-input-number>
                        小时
                    </div>
                    <div class="title">
                        连续驾驶时间大于此设定值将视为超时驾驶
                    </div>
                </div>
                <div class="item">
                    <div class="top">
                        连续停驶时长
                        <el-input-number v-model="form.interval" :min="0"></el-input-number>
                        分钟
                    </div>
                    <div class="title">
                        连续停车时间小于此设定值将视为连续驾驶
                    </div>
                </div>
                <div class="item">
                    <el-checkbox v-model="form.acc" disabled>以ACC信号界定车辆停驶</el-checkbox>
                    <el-checkbox v-model="form.speed">以速度值界定车辆停驶</el-checkbox>
                    <div class="speed">
                        停驶速度界定值
                        <el-input-number v-model="form.speedValue" :min="0" :disabled="!form.speed"></el-input-number>
                        Km/h
                    </div>
                </div>
                <div class="item">
                    <div>备注</div>
                    <el-input v-model="remark" type="textarea" style="margin-top: 10px"></el-input>
                </div>
                <div class="title">判定规则</div>
                <div class="hint">
                    <p> 1、规则修改后，超时驾驶统计数据在次日0:00生效，之后的超时驾驶数据将按照新规则进行统计。</p>
                    <p> 2、历史超时驾驶数据（规则修改日之前的数据）不会随规则变动。</p>
                    <p>3、可能存在规则修改前后的统计逻辑不一致，请谨慎修改。</p>
                </div>
            </div>
        </div>
    </PonyDialog>
</template>

<script>
import SelectTreeInput from "@/components/common/SelectTreeInput";

export default {
    name: "addStopRuler",
    components: {
        SelectTreeInput
    },
    data() {
        return {
            show: false,
            mode: "add",
            activeTab: "basic",
            addModeTitle: "新增超时驾驶报表规则配置",
            addModeOkText: "确认新增",
            modifyModeTitle: "修改超时驾驶报表规则配置",
            modifyModeOkText: "确认修改",
            deptSelectValue: null,
            checkList: true,
            remark: null,
            id: null,
            form: {
                obj_id: null,
                obj_type: null,
                tired: null,
                interval: null,
                acc: true,
                speed: false,
                speedValue: null
            }
        };
    },
    watch: {
        'deptSelectValue': function (newVal) {
            if (!newVal) return;
            Object.assign(this.form, {
                obj_id: newVal.value,
                obj_type: newVal.type
            })
        },
    },
    methods: {
        treeCondition(treeNode) {
            return true
        },
        commit() {
            if (this.form.obj_id == null || this.form.obj_id === "") {
                return this.$warning('请选择配置部门!')
            }
            if (!this.form.tired) {
                return this.$warning('请输入超时驾驶时长！')
            }
            if (!this.form.interval) {
                return this.$warning('请输入连续驾驶时长！')
            }
            if (this.form.speed && !this.form.speedValue) {
                return this.$warning('请输入驾驶速度界定值！')
            }
            this.add()
        },
        async add() {
            let params = {
                config_type: 99,
                obj_id: this.form.obj_id,
                obj_type: this.form.obj_type,
                config_value: JSON.stringify({
                    tired: this.form.tired,
                    interval: this.form.interval,
                    acc: this.form.acc,
                    speed: this.form.speed,
                    speedValue: this.form.speedValue
                }),
                remark: this.remark
            }
            if (this.mode === 'modify') {
                Object.assign(params,{id: this.id})
            }
            const res = await this.$api.insertSysConfig(params)
            if (res.RS !== 1) {
                return this.$warning(res.Reason)
            } else {
                this.$success(this.mode === 'modify' ? '修改成功！' : '新增成功！')
                await this.close()
                this.$emit('update')
            }
        },
        close() {
            this.form = {
                obj_id: null,
                obj_type: null,
                tired: null,
                interval: null,
                acc: true,
                speed: false,
                speedValue: null
            }
            this.show = false
            this.id = null
            this.remark = ''
        },
        async showModal(rowData) {
            this.show = true;
            if (rowData) {
                this.mode = "modify";
                this.form = JSON.parse(rowData.config_value)
                this.form.obj_id = rowData.obj_id
                this.form.obj_type = rowData.obj_type
                this.id = rowData.id
                this.remark = rowData.remark
                await this.$nextTick();
                this.deptSelectValue = await this.$refs['deptInput'].fillOtherProperty(rowData.obj_id);
                
            } else {
                this.mode = "add";
                this.deptSelectValue = null;
            }
        },
    }
};
</script>

<style scoped lang="scss">
.dealer-site-info {
    overflow: hidden;
    
    .form-group {
        > .title {
            height: 32px;
            line-height: 32px;
            margin: 4px 0;
            font-size: 14px;
            color: var(--color-primary);
            
            &:before {
                content: " ";
                border-left: 2px solid var(--color-primary);
                height: 24px;
                margin-right: 10px;
            }
        }
        
        .item {
            margin-bottom: 10px;
            display: flex;
            flex-direction: column;
            
            .title {
                color: var(--color-danger);
            }
            
            .speed {
                margin-left: 25px;
            }
        }
    }
}
</style>

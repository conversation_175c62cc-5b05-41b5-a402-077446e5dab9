<template>
    <PonyDialog
     v-model="show"
     class="dealer-site-info"
     :has-mask="false"
     :insert-body="true"
     :title="mode === 'add' ? addModeTitle : modifyModeTitle"
     :ok-text="mode === 'add' ? addModeOkText : modifyModeOkText"
     :width="850"
     :loading="loading"
     content-style="min-height:150px;overflow:auto;"
     @confirm="commit"
    >
        <el-row style="height: 500px">
            <el-col :span="10">
                <ElementTree
                 type="department"
                 ref="departmentTree"
                 @node-click="nodeClick"
                 node-key="id"
                 style="height:500px"
                >
                </ElementTree>
            </el-col>
            <el-col :span="14">
                <el-form
                 :model="form"
                 :rules="rules"
                 label-width="90px"
                 size="mini"
                 ref="form"
                 label-position="top"
                 style="padding:20px"
                >
                    <el-form-item prop="alarms" label="报警配置:" style="width:100%">
                        <div style="max-height: 300px;overflow: auto;">
                            <div v-for="(alarm, index) in form.alarms" :key="index">
                                <div style="display: flex;align-items: center;margin-bottom: 2px">
                                    <el-select 
                                        v-model="alarm.alarmType" 
                                        placeholder="请选择报警类型" 
                                        filterable
                                        style="width: 260px;margin-right: 10px;"
                                    >
                                        <el-option 
                                            v-for="item in alarmTypeList" 
                                            :key="item.alarmType"
                                            :label="item.name"
                                            :value="item.alarmType">
                                        </el-option>
                                    </el-select>
                                    <el-input 
                                        v-model="alarm.silence" 
                                        placeholder="请输入静默期"
                                        style="width: 230px"
                                    >
                                        <template slot="append">小时</template>
                                    </el-input>
                                    <div style="width: 60px;display: flex;justify-content: flex-end;margin-left: 5px;">
                                        <template v-if="index === form.alarms.length - 1">
                                            <i class="pony-iconv2 pony-xinzeng" 
                                               style="font-size: 20px;cursor:pointer;" 
                                               @click="addAlarm"></i>
                                            <i class="pony-iconv2 pony-shanchu" 
                                               style="font-size: 20px;margin-left:5px;cursor:pointer;" 
                                               @click="delAlarm"></i>
                                        </template>
                                    </div>
                                </div>
                                <el-input
                                    v-if="alarm.alarmType"
                                    type="textarea"
                                    :rows="3"
                                    readonly
                                    disabled
                                    :value="getMessageTemplate(alarm.alarmType)"
                                    placeholder="选择报警类型后显示对应短信模板"
                                    style="width:100%;margin: 5px 0 10px 0;"
                                >
                                </el-input>
                            </div>
                        </div>
                    </el-form-item>
                   
                    <el-form-item prop="phone" label="手机号:" style="width:100%">
                        <div>
                            <div v-for="(item,index) in form.phone" :key="index" style="display: flex;align-items: center;margin-bottom: 2px">
                                <el-input 
                                    v-model="item.num" 
                                    placeholder="请输入手机号"
                                    style="width: calc(100% - 50px)"
                                ></el-input>
                                <template v-if="index === form.phone.length - 1">
                                    <i class="pony-iconv2 pony-xinzeng" 
                                       style="font-size: 20px;cursor:pointer;margin-left: 5px;" 
                                       @click="addPhone"></i>
                                    <i class="pony-iconv2 pony-shanchu" 
                                       style="font-size: 20px;margin-left:5px;cursor:pointer;" 
                                       @click="delPhone"></i>
                                </template>
                            </div>
                        </div>
                    </el-form-item>
                    <el-form-item prop="gateway" label="短信网关:" >
                        <el-select v-model="form.gateway" placeholder="请选择短信网关" style="width:calc(100% - 50px);">
                            <el-option v-for="item in gatewayList" :key="item.value" :label="item.name" :value="item.value"></el-option>
                        </el-select>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
    </PonyDialog>
</template>
<script>

export default {
    name: "addCycle",
    data() {
        return {
            show: false,
            mode: "add",
            loading: false,
            addModeTitle: "新增短信下发配置",
            addModeOkText: "确认新增",
            modifyModeTitle: "修改短信下发配置",
            modifyModeOkText: "确认修改",
            form: {
                alarms: [{alarmType: '', silence: ''}],
                phone: [{num: ''}],
                gateway: ''
            },
            rules: {
                alarms: [{
                    required: true, validator: (rules, value, callback) => {
                        for (let i = 0, num = value.length; i < num; i++) {
                            if (value[i].alarmType === '') {
                                return callback(new Error('请选择报警类型'))
                            }
                            if (value[i].silence === '') {
                                return callback(new Error('请输入静默期'))
                            }
                            if (value[i].silence < 0) {
                                return callback(new Error('请输入大于0的数字'))
                            }
                            // 检查重复的报警类型
                            for (let j = 0; j < i; j++) {
                                if (value[j].alarmType === value[i].alarmType) {
                                    return callback(new Error('请不要选择重复的报警类型'))
                                }
                            }
                        }
                        return callback()
                    }
                }],
                phone: [{
                    required: true, validator: (rules, value, callback) => {
                        for (let i = 0, num = value.length; i < num; i++) {
                            if (value[i].num === '') {
                                return callback(new Error('请输入手机号'))
                            }
                        }
                        let phoneList = value.map(item => item.num)
                        phoneList.map((item, index) => {
                            if (phoneList.indexOf(item) !== index) {
                                return callback(new Error('请不要输入重复手机号'))
                            }
                        })
                        return callback()
                    }
                }],
                gateway: [{required: true, message: "请选择短信网关", trigger: "change"}]
            },
            deptSelectValue: null,
            id: null,
            alarmTypeList: [
                { name: "超速报警", alarmType: 202 },
                { name: "超速预警", alarmType: 214 },
                { name: "GNSS模块发生故障", alarmType: 205 },
                { name: "GNSS天线未接或被剪断", alarmType: 206 },
                { name: "GNSS天线短路", alarmType: 207 },
                { name: "终端主电源欠压", alarmType: 208 },
                { name: "终端主电源掉电", alarmType: 209 },
                { name: "TTS模块故障", alarmType: 211 },
                { name: "平台:超速报警", alarmType: 279 },
                { name: "平台:超时驾驶报警", alarmType: 255 },
                { name: "平台：进围栏提示", alarmType: 233 },
                { name: "平台：出围栏提示", alarmType: 234 },
                { name: "平台：围栏禁出报警", alarmType: 235 },
                { name: "平台：围栏禁入报警", alarmType: 236 },
                { name: "平台：三天未上线报警", alarmType: 237 },
                { name: "平台：七天未上线报警", alarmType: 238 },
                { name: "平台：一个月未上线报警", alarmType: 247 },
                { name: "平台：非法闯入禁入区域行驶报警", alarmType: 259 },
                { name: "存储单元故障报警", alarmType: 263 },
                { name: "进区域", alarmType: 269 },
                { name: "出区域", alarmType: 270 },
               
            ],
            gatewayList: [],
        }
    },
    computed: {
        getMessageTemplate() {
            return (alarmType) => {
                if (!alarmType) {
                    return ''
                }
                
                switch (alarmType) {
                    case 279: // 平台-超速报警
                        return `短信模版:当前XX车产生超速报警,请注意减速慢行，当前速度为65,限速值为60.`
                    case 255: // 平台-超时驾驶报警
                        return `短信模版:当前XX车已疲劳驾驶,请驾驶员注意寻找位置休息，已连续驾驶5小时，疲劳驾驶1小时.`
                    default:
                        return `短信模版:尊敬的(**单位**)管理员，您下辖的(**车牌号**)于(**时间**)在(**地点**)触发了(**报警**)，请及时处理。`
                }
            }
        }
    },
    async mounted() {
        let result = await this.$api.getSysDictByCode({ code: 'sms_gateway' })
        if(!result || !result.length) {
            this.$warning('读取必要字典出错！请刷新重试')
            return
        }
        this.gatewayList = result
    },
    methods: {
        addPhone() {
            if(this.form.phone.length === 5) {
                this.$warning('最多添加5个手机号')
                return
            }
            this.form.phone.push({num: ''})
        },
        delPhone() {
            if (this.form.phone.length <= 1) {
                return
            }
            this.form.phone = this.form.phone.slice(0,-1)
        },
        addAlarm() {
           
            this.form.alarms.push({alarmType: '', silence: ''})
        },
        delAlarm() {
            if (this.form.alarms.length <= 1) {
                return
            }
            this.form.alarms = this.form.alarms.slice(0,-1)
        },
        async add(id) {
            const res = await this.$api.insertSysConfig({
                id: id,
                config_type: 90,
                obj_id: this.deptSelectValue,
                obj_type: 1,
                config_value: JSON.stringify({
                    alarms: this.form.alarms.map(alarm => ({
                        alarmType: alarm.alarmType,
                        silence: Number(alarm.silence)
                    })),
                    phone: this.form.phone.map(item => item.num).join(','),
                    gateway: this.form.gateway
                }),
            })
            if (res.RS !== 1) {
                return this.$error(res.Reason || '提交失败！')
            }
            this.$success(`${this.mode === 'add' ? '新增' : '修改'}成功！`)
            this.show = false
            this.$emit('getList')
        },
        commit() {
            if (!this.deptSelectValue) {
                return this.$warning('请选择企业或车队！')
            }
            this.$refs.form.validate((valid) => {
                if (valid) {
                    if (this.mode === "add") {
                        this.add(null);
                    } else {
                        this.add(this.id);
                    }
                }
            })
        },
        async showModal(rowData) {
            this.clearData()
            this.show = true;
            this.params = null;
            if (rowData) {
                this.mode = "modify";
                this.form.alarms = JSON.parse(rowData.config_value).alarms.map(alarm => ({
                    alarmType: alarm.alarmType,
                    silence: Number(alarm.silence)
                }))
                this.form.phone = rowData.phone.split(',').map(item => ({num: item}))
                this.form.gateway = rowData.gateway
                this.deptSelectValue = rowData.obj_id
                this.id = rowData.id
                await this.$nextTick();
                await this.$refs['departmentTree'].waitForInit
                const $tree = this.$refs['departmentTree'].$refs['tree']
                $tree.setCurrentKey(rowData.obj_id)
                const node = $tree.getNode(rowData.obj_id)
                node.expand(null, true);
            } else {
                this.mode = "add";
            }
        },
        // 单选树节点选择
        nodeClick(data, node) {
           
            this.deptSelectValue = data.id;
        },
        clearData() {
            this.form = {
                alarms: [{alarmType: '', silence: ''}],
                phone: [{num: ''}],
                gateway: ''
            }
            this.deptSelectValue = null
            this.id = null
        }
    }
}
</script>

<style scoped lang="scss">
</style>
<template>
    <PonyDialog v-model="show" class="dealer-site-info" :has-mask="false" :insert-body="true"
                :title="mode === 'add' ? addModeTitle : modifyModeTitle"
                :ok-text="mode === 'add' ? addModeOkText : modifyModeOkText" :width="500" :loading="loading"
                content-style="min-height:150px;overflow:auto;" @confirm="commit">
        <el-row style="height: 350px">
            <el-col :span="12">
                <ElementTree type="department" ref="departmentTree" @node-click="nodeClick" node-key="id"
                             style="height:350px">
                </ElementTree>
            </el-col>
            <el-col :span="12">
                <el-form :model="form" label-width="50px" size="mini" ref="form" label-position="top"
                         style="padding:20px">
                    <el-form-item prop="alarmType" label="油耗来源:" style="width:100%">
                        <el-select v-model="form.config_value" placeholder="请选择油耗来源">
                            <el-option key="1" label="TBox转发" value="1"></el-option>
                            <el-option key="2" label="设备上报" value="2"></el-option>
                        </el-select>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
    </PonyDialog>
</template>
<script>

export default {
    name: 'addDriverScoreRuler',
    data() {
        return {
            show: false,
            mode: "add",
            loading: false,
            addModeTitle: "新增配置",
            addModeOkText: "确认新增",
            modifyModeTitle: "修改配置",
            modifyModeOkText: "确认修改",
            form: {
                config_value: '1'
            },
            deptSelectValue: {
                id: null,
                type: null,
            },
            id: null
        }
    },
    methods: {
        async commit() {
            if (!this.deptSelectValue.id) {
                return this.$warning('请选择企业！')
            }
            if (!this.form.config_value) {
                return this.$warning('请选择油耗来源！')
            }
            let params = {
                id: this.id,
                config_type: 60,
                obj_id: this.deptSelectValue.id,
                obj_type: this.deptSelectValue.type,
                config_value: this.form.config_value
            }
            const res = await this.$api.insertSysConfig(params)
            if (res.RS !== 1) {
                return this.$error(res.Reason)
            } else {
                this.$success(this.mode == 'add' ? '新增成功' : '修改成功')
                this.$emit('change')
            }
            this.$emit('refresh')
            this.show = false
        },
        async showModal(rowData) {
            this.clearData()
            this.show = true;
            if (rowData) {
                this.mode = "modify";
                Object.assign(this.form, rowData)
                this.id = rowData.id
                this.deptSelectValue.id = rowData.obj_id
                this.deptSelectValue.type = rowData.obj_type
                await this.$nextTick();
                await this.$refs['departmentTree'].waitForInit
                const $tree = this.$refs['departmentTree'].$refs['tree']
                $tree.setCurrentKey(rowData.obj_id)
                const node = $tree.getNode(rowData.obj_id)
                node.expand(null, true);
            } else {
                this.mode = "add";
            }
        },
        // 单选树节点选择
        nodeClick(data, node) {
            this.deptSelectValue = data;
        },
        clearData() {
            this.form = {
                config_value: '',
            }
            this.deptSelectValue = {
                id: null,
                type: null,
            }
            this.id = null
        }
    }
}
</script>

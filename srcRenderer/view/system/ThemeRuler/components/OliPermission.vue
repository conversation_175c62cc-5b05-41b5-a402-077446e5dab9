<template>
    <Layout :has-color="true" :contentLoading="table.loading">
        <template slot="aside">
            <div class="query-top">
                <ElementTree type="department" ref="tree" @node-click="selectNode" @mounted="treeMounted"></ElementTree>
            </div>
        </template>
        <template slot="query">
            <div class="query-item">
                <el-button size="mini" type="primary" @click="add()">新增</el-button>
            </div>
            <div class="break-item"></div>
            <div>
                <el-pagination
                 background
                 small
                 :pager-count="5"
                 :current-page.sync="pager.current"
                 layout="prev, pager, next, total"
                 :page-size="pager.size"
                 :total="this.table.data.length"
                >
                </el-pagination>
            </div>
        </template>
        <template slot="content">
            <el-table
             ref="breaktable"
             slot="content"
             class="el-table--ellipsis el-table--radius"
             border
             stripe
             highlight-current-row
             size="mini"
             :data="formatList"
             height="100%"
             style="width: 100%"
            >
                <el-table-column
                 type="index"
                 :index="(index) => index + 1 + pageStart"
                 label="序号"
                 width="70"
                >
                </el-table-column>
                <el-table-column
                 prop="dept_name"
                 label="部门"
                 min-width="120"
                 show-overflow-tooltip
                ></el-table-column>
                <el-table-column
                 prop="config_value_desc"
                 label="油耗来源"
                 min-width="120"
                 show-overflow-tooltip
                ></el-table-column>
                <el-table-column
                 prop="update_by"
                 label="更新人"
                 min-width="120"
                 show-overflow-tooltip
                ></el-table-column>
                <el-table-column
                 prop="update_time"
                 label="更新时间"
                 min-width="120"
                 show-overflow-tooltip
                ></el-table-column>
                <el-table-column label="操作" min-width="70">
                    <template slot-scope="{ row }">
                        <el-button
                         type="text"
                         title="修改"
                         size="mini"
                         @click="modify(row, true)"
                        >
                            <i class="pony-iconv2 pony-xiugai"></i>
                        </el-button>
                        <el-button
                         type="text"
                         title="删除"
                         size="mini"
                         @click="removeDealerTable(row)"
                        >
                            <i class="pony-iconv2 pony-shanchu"></i>
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
        </template>
        <AddOliPermission ref="addOliPermission" @change="search()"></AddOliPermission>
    </Layout>
</template>

<script>
import AddOliPermission from "./modal/addOliPermission";

export default {
    name: "OliPermission",
    components: {
        AddOliPermission
    },
    data() {
        return {
            selectId: '',
            table: {
                loading: false,
                data: [],
            },
            pager: {
                current: 1,
                size: 30,
                total: 0,
            },
        }
    },
    computed: {
        pageStart() {
            return (this.pager.current - 1) * this.pager.size;
        },
        formatList() {
            return this.table.data.slice(
             (this.pager.current - 1) * this.pager.size,
             this.pager.current * this.pager.size
            )
        }
    },
    methods: {
        treeMounted($tree) {
            this.getSysConfig()
        },
        add() {
            this.$refs.addOliPermission.showModal()
        },
        modify(row) {
            this.$refs.addOliPermission.showModal(row)
        },
        search() {
            this.getSysConfig(this.selectId)
        },
        async removeDealerTable(rowData) {
            await this.$confirm("确认要删除此记录吗?", "确认删除", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "error",
            });
            let res = await this.$api.delTTS({
                id: rowData.id,
            });
            if (res.RS == 1) {
                this.$success("删除成功");
                this.table.data = this.table.data.filter(
                 (item) => item.id != rowData.id
                );
            } else {
                this.$error("删除失败" + res.message);
            }
        },
        selectNode(data, node, $node) {
            this.selectId = data.id
            this.getSysConfig(data.id);
        },
        async getSysConfig(id) {
            this.table.data = []
            let res = await this.$api.getEventRulerConfig({
                obj_id: id,
                version: 'OilSource'
            });
            if (res.status !== 200) {
                return this.$error('查询失败！')
            }
            if (res.data.length === 0) {
                return this.$warning('暂无数据！')
            }
            this.table.data = res.data
            this.pager.total = res.data.length
        },
    },
}
</script>
<style lang="scss" scoped>
.query-top {
    overflow: auto
}
</style>

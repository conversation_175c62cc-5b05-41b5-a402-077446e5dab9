<template>
  <Layout :has-color="true" :contentLoading="table.loading">
    <template slot="query">
      <div class="query-item">
        <el-button size="mini" type="primary" @click="add">新增</el-button>
      </div>
      <div class="break-item"></div>
      <div class="query-item">
        <el-pagination small background :current-page.sync="table.page" :page-size="table.size"
          layout="prev, pager, next, total" :total="table.total">
        </el-pagination>
      </div>
    </template>

    <template slot="content">
      <el-table ref="table" class="el-table--ellipsis el-table--radius" border stripe highlight-current-row size="mini"
        :data="formatList" height="100%" style="width: 100%">
        <el-table-column type="index" align="center" :index="(index) => index + 1 + pageStart" label="序号"
          width="60"></el-table-column>
        <el-table-column label="操作" width="120">
          <template slot-scope="{row}">
            <el-button type="text" title="修改" size="mini" @click="edit(row)">
              <i class="pony-iconv2 pony-xiugai"></i>
            </el-button>
            <el-button type="text" :title="row.status == -1 ? '启用' : '停用'" size="mini" @click="changeStatus(row)">
              <i class="pony-iconv2 pony-zanting" v-if="row.status == 0"></i>
              <i class="pony-iconv2 pony-bofang" v-else></i>
            </el-button>
            <el-button type="text" title="删除" size="mini" @click="deleteInfo(row)">
              <i class="pony-iconv2 pony-shanchu"></i>
            </el-button>
          </template>
        </el-table-column>
        <el-table-column label="转发状态" width="100" show-overflow-tooltip>
          <template slot-scope="{row}">
            {{ row.status == -1 ? '停用' : '启用' }}
          </template>
        </el-table-column>
        <el-table-column prop="ip" label="ip" width="140" show-overflow-tooltip></el-table-column>
        <el-table-column prop="port" label="端口" width="80"></el-table-column>

        <el-table-column prop="userName" label="权限账号" show-overflow-tooltip></el-table-column>
        <el-table-column prop="protocolName" label="转发协议" show-overflow-tooltip width="130">
          <template slot-scope="{row}">
            {{ row.protocolName == 'all' ? '全部' : row.protocolName }}
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="新增时间" show-overflow-tooltip width="140">
          <template slot-scope="{row}">
            {{ TimeFormat(row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="createBy" label="新增账号" show-overflow-tooltip width="120"></el-table-column>
        <el-table-column prop="updateTime" label="修改时间" show-overflow-tooltip width="140">
          <template slot-scope="{row}">
            {{ TimeFormat(row.updateTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="updateBy" label="修改账号" show-overflow-tooltip width="120"></el-table-column>
        <el-table-column prop="remark" label="备注" show-overflow-tooltip></el-table-column>

      </el-table>
    </template>
    <addRules808 ref="addRules" @refresh="search()"></addRules808>
  </Layout>
</template>

<script>
import addRules808 from './modal/addRules808'

export default {
  name: "Rules808",
  components: {
    addRules808
  },
  data() {
    return {
      table: {
        loading: false,
        page: 1,
        size: 30,
        data: [],
        total: 0
      },
      version: ['2013版', '2019版']
    }
  },
  computed: {
    pageStart() {
      return (this.table.page - 1) * this.table.size
    },
    formatList() {
      return this.table.data.slice(this.pageStart, this.pageStart + this.table.size)
    },
  },
  async mounted() {
    await this.search()
  },
  methods: {
    async changeStatus(row) {
      this.$confirm(`是否${row.status == -1 ? '启用' : '停用'}转发?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const res = await this.$api.operateForward808Config({
          operateType: -1,
          id: row.id,
          stop: row.status == -1 ? false : true
        })
        if (res.status === 200) {
          this.$success('设置成功!')
          await this.search()
        } else {
          this.$error('设置失败!')
        }
      })
    },
    add() {
      this.$refs["addRules"].showModal();
    },
    edit(rowData) {
      this.$refs["addRules"].showModal(rowData);
    },
    async deleteInfo(rowData) {
      this.$confirm('此操作将永久删除该配置, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const res = await this.$api.operateForward808Config({
          operateType: -2,
          id: rowData.id
        })
        if (res.status === 200) {
          this.$success('删除成功!')
          await this.search()
        } else {
          this.$error('删除失败!')
        }
      })
    },
    // 搜索
    async search() {
      this.table.loading = true
      const res = await this.$api.operateForward808Config({
        operateType: 2
      })
      this.table.loading = false
      if (res.status !== 200) {
        return this.$error('查询配置失败!')
      } else if (res.data.length === 0) {
        return this.$message('暂无数据')
      }
      this.table.data = res.data
      this.table.total = res.data.length
      this.$nextTick(() => {
        this.$refs.table.doLayout()
      })
    },
  }
}
</script>

<style lang='scss' scoped></style>

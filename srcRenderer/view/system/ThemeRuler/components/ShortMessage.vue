<template>
    <Layout :has-color="true" :content-loading="loading">
        <template slot="aside">
            <div class="query-top">
                <ElementTree
                  type="department"
                  ref="departmentTree"
                  @node-click="nodeClick"
                  node-key="id"
                >
                </ElementTree>
            </div>
        </template>
        <template slot="query">
            <el-button type="primary" @click="addShortMessage">新增</el-button>
            <div class="break-item"></div>
            <el-pagination background small :current-page.sync="shortMessage.page" :page-size="shortMessage.size"
                           layout="prev, pager, next, total" :total="shortMessage.data.length">
            </el-pagination>
        </template>
        <template slot="content">
            <el-table class="el-table--radius box-shadow" ref="adasAlarmTable" :data="cycleFormatList"
                      border
                      stripe highlight-current-row height="100%" style="width: 100%">
                <el-table-column type="index" width="55" label="序号"></el-table-column>
                <el-table-column label="部门" prop="dept_name"></el-table-column>
                <el-table-column label="报警/静默时间（h）">
                    <template slot-scope="scope">
                        <span class="alarm-content">{{scope.row.alarms}}</span>
                    </template>
                </el-table-column>
                <el-table-column label="手机号">
                    <template slot-scope="scope">
                        <span class="alarm-content">{{formatPhone(scope.row.phone)}}</span>
                    </template>
                </el-table-column>
                <el-table-column label="网关" prop="gatewayView"></el-table-column>
                <el-table-column label="更新人" prop="update_by"></el-table-column>
                <el-table-column label="更新时间" prop="update_time"></el-table-column>
                <el-table-column label="操作" width="80">
                    <template slot-scope="scope">
                        <el-button type="text" size="mini" title="编辑" @click="editShortMessage(scope.row)">
                            <i class="pony-iconv2 pony-xiugai"></i>
                        </el-button>
                        <el-button type="text" size="mini" title="删除" @click="del(scope.row,0)">
                            <i class="pony-iconv2 pony-shanchu"></i>
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
        </template>
        <AddShortMessage ref="addCycle" @getList="getShortMessageList"></AddShortMessage>
    </Layout>
</template>

<script>
import AddShortMessage from "./modal/addShortMessage";

export default {
    name: "Troubleshooting",
    components: {
        AddShortMessage,
    },
    data() {
        return {
            shortMessage: {
                data: [],
                page: 1,
                size: 30,
            },
            deptSelectValue: null,
            loading: false
        }
    },
    computed: {
        cyclePageStart() {
            return (this.shortMessage.page - 1) * this.shortMessage.size
        },
        cycleFormatList() {
            return this.shortMessage.data.slice(this.cyclePageStart, this.cyclePageStart + this.shortMessage.size)
        },
    },
    mounted() {
        this.getShortMessageList()
    },
    methods: {
        // 格式化手机号，将逗号替换为换行符
        formatPhone(phone) {
            return phone ? phone.replace(/,/g, '\n') : '';
        },
        // 单选树节点选择
        nodeClick(data, node) {
            this.deptSelectValue = data.id;
            this.getShortMessageList();
        },
        // 新增短信配置
        addShortMessage() {
            this.$refs.addCycle.showModal()
        },
        editShortMessage(data) {
            this.$refs.addCycle.showModal(data)
        },
        // 查询短信配置列表
        async getShortMessageList() {
            const res = await this.$api.getEventRulerConfig({
                version: 'ERPAlarmSMSSend',
                obj_id: this.deptSelectValue
            })
            if (res.status !== 200) {
                return this.$error(res.message || '查询错误！')
            }
            this.shortMessage.data = res.data
            this.$nextTick(() => {
                this.$refs.adasAlarmTable.doLayout()
            })
        },
        del(row, type) {
            this.$confirm('此操作将永久删除该配置, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                let res = await this.$api.delTTS({
                    id: row.id,
                });
                if (res.RS === 1) {
                    this.$success("删除成功");
                    await this.getShortMessageList()
                } else {
                    this.$error("删除失败" + res.message);
                }
            })
        }
    }
}
</script>

<style scoped lang="scss">
.query-top {
    height: 100%;
}

.alarm-content {
    white-space: pre-line;
    line-height: 1.5;
    display: block;
}
</style>

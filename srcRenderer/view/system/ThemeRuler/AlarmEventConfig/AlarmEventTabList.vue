<template>
    <div class="alarm-event-tab-list">
        <el-tabs v-model="tabName" v-loading="loading">
            <el-tab-pane :label="`报警事件${item.label}配置`" :name="item.key" :key="item.key"
                         v-for="(item,index) in alarmEventTypeList"
                         style="border-top: 1px solid #ecdecd">
                <el-checkbox v-model="item.checkAll"
                             @change="checkAll(index)">全选
                </el-checkbox>
                <div style="margin: 10px 0;"></div>
                <el-checkbox-group v-model="item.checked" @change="checkCheckAll(item)">
                    <el-checkbox v-for="alarmEvent in item.list" :label="alarmEvent.value" :key="alarmEvent.value">{{alarmEvent.label}}</el-checkbox>
                </el-checkbox-group>
            </el-tab-pane>
        </el-tabs>
    </div>

</template>

<script>
    export default {
        name: "alarm-event-tab-list",
        model: {
            prop: 'value',
            event: 'change'
        },
        props: {
            value: Array,
        },
        data() {
            return {
                tabName: 'danger_event_type',
                loading: false,
                alarmEventTypeList: [
                    {label: '类型', value: 0, key: 'danger_event_type', list: [], checkAll: false, checked: [],},
                    {label: '处理类型', value: 1, key: 'event_deal_type', list: [], checkAll: false, checked: [],},
                ],

            }
        },
        watch: {
            value: 'refreshChecked'
        },
        methods: {
            completedValue() {
                let res = this.alarmEventTypeList.map(item => item.checked).flat().map(item => item);
                this.$emit('change', res);
            },
            checkAll(index) {
                let alarmEvent = this.alarmEventTypeList[index];
                if (alarmEvent.checkAll) {
                    alarmEvent.checked = alarmEvent.list.map(item => item.value);
                } else {
                    alarmEvent.checked = [];
                }
                this.completedValue();
            },
            checkCheckAll(item) {
                item.checkAll = item.list.length === item.checked.length;
                this.completedValue();
            },
            refreshChecked() {
                this.alarmEventTypeList.forEach((item, index) => {
                    item.checked = item.list.filter(item => {
                        return this.value.indexOf(item.value)!=-1;
                    }).map(item => item.value)
                    item.checkAll = item.list.length === item.checked.length;
                })
            },
          /**
           * 处理类型值在数据库中和事件类型是重复的, 这里想在一起做处理, 所以把处理类型的值统一加一下(这里统一加200)
           * @param data
           * @returns {[]|*}
           */
            handleDealValue(data) {
                if (!data) {
                    return data;
                } else {
                  let ret = [];
                  for (let i = 0; i < data.length; i++) {
                      ret[i] = data[i];
                      ret[i].value = ret[i].value + 200;
                  }
                  return ret;
                }
            }
        },
        async mounted() {
            this.loading = true;
            let res = await Promise.all(
                this.alarmEventTypeList.map(item => {
                    return 'event_deal_type' == item.key
                        ? this.$store.dispatch('dictionary/getFormatListByCode', 'event_deal_type')
                        : this.$store.dispatch('dictionary/getFormatListByCode', 'danger_event_type');
                })
            )
            this.alarmEventTypeList.forEach((item, index) => {
                item.list = 'event_deal_type' == item.key
                    ? this.handleDealValue(res[index])
                    : res[index];
            })
            this.refreshChecked()
            this.loading = false;
        }
    }
</script>

<style scoped lang="scss">
    .alarm-event-tab-list {
        /deep/ .el-tab-pane {
            padding: 7px;
             .el-checkbox {
                width: 222px;
                margin-right: 0;
                .el-checkbox__input {
                    width: 14px;
                    vertical-align: 3px;
                }
                .el-checkbox__label {
                    width: calc(100% - 14px);
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }
            }
        }
    }
</style>
<template>
    <PonyDialog v-model="show" title="批量修改车辆" @confirm="commit" :width="800" class="modify" :hasMask="true">
        <div class="modify">
            <div class="content">
            <div class="content-item">
                <el-checkbox v-model="state.deptId"></el-checkbox>
                <span>
                    车队归属
                </span>
                <SelectTreeInput v-model="data.deptGroup" type="department" ref="departmentInput" placeholder="请选择归属部门"
                                 title="请选择归属部门" :withParent="true" :condition="deptCondition"
                                 :disable="!state.deptId"></SelectTreeInput>
            </div>
            <div class="content-item">
                <el-checkbox v-model="state.plateColor"></el-checkbox>
                <span>
                    车牌颜色
                </span>
                <el-select placeholder="请选择车牌颜色" v-model="data.plateColor" :disabled="!state.plateColor">
                    <el-option v-for="(item, index) in plateColor" :key="index" :label="item.label"
                               :value="+item.value">
                    </el-option>
                </el-select>
            </div>
            <div class="content-item">
                <el-checkbox v-model="state.industryType"></el-checkbox>
                <span>
                    车辆行业
                </span>
                <el-select placeholder="请选择车辆行业" v-model="data.industryType" :disabled="!state.industryType">
                    <el-option v-for="(item, index) in industryType" :key="index" :label="item.label"
                               :value="item.value" :disabled="item.disabled">
                    </el-option>
                </el-select>
            </div>
            <div class="content-item">
                <el-checkbox v-model="state.vehicleType"></el-checkbox>
                <span>
                    车辆类型
                </span>
                <SelectTreeInput v-model="vehicleTypeSelectValue" ref="vehicleType" type="vehicleType"
                                 :withParent="true" :condition="treeDeptCondition" placeholder="请选择车辆类型" title="请选择车辆类型"
                                 :disable="!state.vehicleType">
                </SelectTreeInput>
            </div>
            <div class="content-item">
                <el-checkbox v-model="state.channelTalkback"></el-checkbox>
                <span>
                    对讲通道
                </span>
                <el-select v-model="data.channelTalkback" class="elWidth" placeholder="请选择对讲通道"
                           :disabled="!state.channelTalkback">
                    <el-option v-for="(item, index) in 60" :key="index" :label="`通道${index + 1}`" :value="index+1">
                    </el-option>
                </el-select>
            </div>
            <div class="content-item">
                <el-checkbox v-model="state.channelBroadcast"></el-checkbox>
                <span>
                    广播通道
                </span>
                <el-select v-model="data.channelBroadcast" class="elWidth" placeholder="请选择广播通道"
                           :disabled="!state.channelBroadcast">
                    <el-option v-for="(item, index) in 60" :key="index" :label="`通道${index + 1}`" :value="index+1">
                    </el-option>
                </el-select>
            </div>
            <div class="content-item">
                <el-checkbox v-model="state.inputType"></el-checkbox>
                <span>
                    接入方式
                </span>
                <el-select v-model="data.inputType" class="elWidth" placeholder="请选择广播通道"
                           :disabled="!state.inputType">
                    <el-option v-for="(item, index) in inputTypeList" :key="index" :label="item.label" :value="item.value">
                    </el-option>
                </el-select>
            </div>
            <div class="content-item">
                <el-checkbox v-model="state.videoDevice"></el-checkbox>
                <span style="width: 70px">
                    视频设备
                </span>
                <el-switch v-model="data.videoDevice" :disabled="!state.videoDevice"></el-switch>
            </div>
            <div class="content-item">
                <el-checkbox v-model="state.channelDsm"></el-checkbox>
                <span>
                    dms通道
                </span>
                <el-select v-model="data.channelDsm" class="elWidth" placeholder="请选择dms通道"
                           :disabled="!state.channelDsm">
                    <el-option v-for="(item, index) in 16" :key="index" :label="`通道${index + 1}`" :value="index+1">
                    </el-option>
                </el-select>
            </div>
            <div class="content-item">
                <el-checkbox v-model="state.channelAdasFront"></el-checkbox>
                <span>
                    adas通道
                </span>
                <el-select v-model="data.channelAdasFront" class="elWidth" placeholder="请选择adas通道"
                           :disabled="!state.channelAdasFront">
                    <el-option v-for="(item, index) in 16" :key="index" :label="`通道${index + 1}`" :value="index+1">
                    </el-option>
                </el-select>
            </div>
            <div class="content-item">
                <el-checkbox v-model="state.annualVaildDate"></el-checkbox>
                <span>
                    年审时间
                </span>
                <el-date-picker v-model="data.annualVaildDate" class="elWidth" placeholder="请选择年审时间" type="date" style="width:100%" :disabled="!state.annualVaildDate" value-format="yyyy-MM-dd"></el-date-picker>
            </div>
            <div class="content-item">
                <el-checkbox v-model="state.remark"></el-checkbox>
                <span>
                    备注
                </span>
                <el-input type="textarea" :row="4" v-model="data.remark" :disabled="!state.remark"></el-input>
            </div>
        </div>
        <div class="content bg">
            <div class="content-item">
                <el-checkbox v-model="state.channelValid"></el-checkbox>
                <span>
                    视频通道
                </span>
                <el-select v-model="channelValid" class="elWidth" multiple collapse-tags placeholder="请选择视频通道"
                        :disabled="!state.channelValid">
                    <el-option v-for="(item, index) in 16" :key="index" :label="`通道${index + 1}`" :value="index">
                    </el-option>
                </el-select>
            </div>
            <el-table border class="el-table--ellipsis channelTable" 
                :data="data.channelValidName"
                max-height="375"
                >
                <el-table-column type="index" label="序号" width="80">
                </el-table-column>
                <el-table-column label="逻辑通道" min-width="100" prop="no">
                    <template slot-scope="{row}">
                            {{ `通道${row.no}` }}
                    </template>
                </el-table-column>
                <el-table-column label="名称" min-width="100">
                    <template slot-scope="scope">
                        <el-input v-model="scope.row.name" :disabled="!state.channelValid"></el-input>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        </div>
        
    </PonyDialog>
</template>
<script>
import SelectTreeInput from "@/components/common/SelectTreeInput";
import {mapActions} from "vuex";

export default {
    name: 'multipleModify',
    components: {
        SelectTreeInput,
    },
    data() {
        const defaultData = {
            vehicleIdList: [],
            deptId: null,
            plateColor: null,
            vehicleType: null,
            industryType: null,
            channelValid: null,
            channelTalkback: null,
            channelBroadcast: null,
            remark: null,
            deptGroup: null,
            inputType: null,
            channelDsm: null,
            channelAdasFront: null,
            videoDevice: false,
            channelValidName:[],
            annualVaildDate:""
        }
        const defaultState = {
            deptId: false,
            plateColor: false,
            vehicleType: false,
            industryType: false,
            channelValid: false,
            channelTalkback: false,
            channelBroadcast: false,
            remark: false,
            inputType: false,
            channelDsm: false,
            channelAdasFront: false,
            videoDevice: false,
            annualVaildDate:false
        }
        return {
            show: false,
            data: JSON.parse(JSON.stringify(defaultData)),
            state: JSON.parse(JSON.stringify(defaultState)),
            defaultState,
            defaultData,
            vehicleType: [],
            plateColor: [],
            industryType: [],
            channelValid: [],
            vehicleTypeSelectValue: null,
            inputTypeList: [
                {
                    value: 1,
                    label: '设备直连'
                },
                {
                    value: 2,
                    label: ' 808转发'
                },
                {
                    value: 3,
                    label: '809接入'
                },
            ]
        }
    },
    watch: {
        "data.deptGroup": function (val) {
            if (!val) return;
            Object.assign(this.data, {
                deptId: val.value,
            });
        },
        vehicleTypeSelectValue: function (newVal) {
            if (!newVal) return;
            Object.assign(this.data, {
                vehicleType: newVal.value,
            });
        },
        channelValid:{
            handler(val) {
            if(val.length > 0){
                let arr = []
                val.forEach(number=>{
                    let value = number+1
                    let index = this.data.channelValidName.findIndex(it=>it.no==value)
                    if(index == -1){
                        arr.push({
                            no:value,
                            name:`通道${value}`
                        })
                    }else{
                        arr.push(this.data.channelValidName[index])
                    } 
                })
                this.data.channelValidName = arr.sort((a,b)=>a.no-b.no)
            }else{
                this.data.channelValidName =[]
            }
},
deep:true,
        }
    },
    computed: {},
    async mounted() {
        await this.initDictionary()
    },
    methods: {
        ...mapActions("dictionary", [
            "getFormatListByCode",
        ]),
        async commit() {
            if (this.state.deptId && this.data.deptId == null) {
                this.$warning("请选择归属部门")
                return
            }
            if (this.state.plateColor && this.data.plateColor == null) {
                this.$warning("请选择车牌颜色")
                return
            }
            if (this.state.vehicleType && this.data.vehicleType == null) {
                this.$warning("请选择车辆类型")
                return
            }
            if (this.state.channelValid && this.channelValid.length == 0) {
                this.$warning("请选择视频通道")
                return
            }
            if (this.state.channelTalkback && this.data.channelTalkback == null) {
                this.$warning("请选择对讲通道")
                return
            }
            if (this.state.channelBroadcast && this.data.channelBroadcast == null) {
                this.$warning("请选择广播通道")
                return
            }
            if (this.state.remark && this.data.remark == null) {
                this.$warning("请填写备注")
                return
            }
            if(!this.state.videoDevice){
                this.data.videoDevice =null
            }
            if (this.state.channelValid) {
                let channel_valid_list = [];
                for (let i = 0; i < 16; i++) {
                    if (this.channelValid.indexOf(i) <= -1) {
                        channel_valid_list[i] = "0";
                    } else {
                        channel_valid_list[i] = "1";
                    }
                }
                this.data.channelValid = channel_valid_list.join("");
            }

            let params = JSON.parse(JSON.stringify(this.data))
            await this.$confirm(`已选择${this.data.vehicleIdList.length}辆，是否修改?`, '提示', {
                type: 'warning'
            })
            try {
            let result = await this.$api.updateBatch(params)
            if (result.status == 200) {
                this.$success("批量修改成功")
                this.$emit('refresh')
                this.show = false
            } else {
                this.$warning(result.message)
            }}catch (e) {

            }

        },
        async initUserIndustryList() {
            let res = await Promise.all([
                this.$api.getUserIndustryBind({user_id: "-1"}),
                this.$api.getUserIndustryBind({user_id: "0"}),
            ]);
            this.data.industryType = res[0].data[0].value;
            return res[1].data.map((item) => {
                return {
                    label: item.name,
                    value: item.value,
                    disabled: !res[0].data.some((ci) => {
                        return ci.value === item.value;
                    }),
                };
            });
        },
        async initDictionary() {
            let [
                vehicleType,
                plateColor,
                industryType,
            ] = await Promise.all([
                this.getFormatListByCode("vehicle_type"),
                this.getFormatListByCode("plate_color"),
                this.initUserIndustryList(),
            ]);
            Object.assign(this, {
                vehicleType,
                plateColor,
                industryType,
            });
        },
        deptCondition(treeNode) {
            if (treeNode.type !== 3) {
                this.$message({
                    type: "info",
                    message: "请选择归属企业!",
                    showClose: true,
                });
                return false;
            } else {
                return true;
            }
        },
        treeDeptCondition(treeNode) {
            return true;
        },
        async showModal(list) {
            this.show = true;
            this.vehicleTypeSelectValue = null;
            this.data = JSON.parse(JSON.stringify(this.defaultData))
            this.state = JSON.parse(JSON.stringify(this.defaultState))
            this.channelValid = []
            this.data.vehicleIdList = list;
        }
    }

}
</script>
<style lang="scss" scoped>
.modify {
    display:flex;
    width:100%;
    height:100%;
    align-items:center;
    justify-content:center;
    .content {
        display: flex;
        width: 50%;
        height: 100%;
        flex-direction: column;

        .content-item {
            display: flex;
            flex-direction: row;
            align-items: center;
            margin: 5px 15px ;
            > span {
                width: 90px;
                margin-left: -15px;
            }
        }
        .channelTable{
        width: 92%;
        margin: 5px 15px ;

    }
    }

    /deep/ .el-select {
        width: 100%;
    }
    .bg{
        background-color:var(--background-color-lighter)
    }
}
</style>

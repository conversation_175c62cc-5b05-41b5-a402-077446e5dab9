<template>
    <PonyDialog v-model="show" :title="$ct('label.bindDriver')" class="driver-bind" :loading="loading"
                :width="300"  :content-max-height="500">
        <el-form :model="data" label-width="100px" size="mini">
            <el-form-item :label="$ct('label.plateNo') + ':'">
                <el-input :value="data.plateNo" readonly></el-input>
            </el-form-item>
            <el-form-item :label="$ct('label.group') + ':'">
                <el-input :value="data.companyName + '>>'+ data.deptName" readonly></el-input>
            </el-form-item>
            <el-form-item :label="$ct('label.currentBinded') + ':'">
                <el-input :value="data.driverName || $ct('label.noData')" readonly></el-input>
            </el-form-item>
            <el-form-item :label="$ct('label.unbindedTime') + ':'">
                <el-input :value="data.bindTime || $ct('label.unbound')" readonly></el-input>
            </el-form-item>
            <el-form-item :label="$ct('label.changeBinding')">
                <el-select v-model="data.driverNew" filterable clearable
                           :placeholder="$ct('label.driverPlz')" popper-class="car-modal-popper">
                    <el-option
                            v-for="item in driverList"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value">
                        <div class="option-contain" style="width: 300px;">
                            <span class="option-label">{{ item.label }}</span>
                            <span class="extra" :title="item.info">{{ item.info}}</span>
                        </div>
                    </el-option>
                </el-select>
            </el-form-item>
        </el-form>
        <template slot="footer">
            <el-button @click="unbindDriver">{{$ct('label.unbind')}}</el-button>
            <el-button type="primary" @click="bindDriver">{{$ct('label.change')}}</el-button>
            <el-button @click="show = false">{{$ct('label.cancel')}}</el-button>
        </template>
    </PonyDialog>
</template>

<script>
    /**
     * @Author: yezy
     * @Email: <EMAIL>
     * @Date: 2019/8/7 13:42
     * @LastEditors: yezy
     * @LastEditTime: 2019/8/7 13:42
     * @Description:
     */
    export default {
        name: "driverBind",
        data() {
            return {
                loading: false,
                show: false,
                driverList: [],
                data: {
                    driverId: null,
                    driverName: null,
                    bindTime: null,
                    plateNo: null,
                    deptId: null,
                    deptName: null,
                    companyId: null,
                    companyName: null,
                    vehicleId: null,
                    driverNew: null,
                },
            }
        },
        methods: {
            async getUnbindDriverList() {
                this.loading = true;
                let res = await this.$api.selectDriverInfoBind({
                    company_id: this.data.companyId,
                    dept_id: this.data.deptId,
                    page: 1,
                    count: 99999,
                    sorting: -1
                });
                this.loading = false;
                if (res.RS === 1) {
                    this.driverList = res.DriverList.filter(item => {
                        return item.audit_status === 1 && item.id !== this.data.driverId;
                    }).map(item => {
                        return {
                            label: item.name,
                            value: item.id,
                            info: item.phone
                        }
                    });
                } else {
                    this.driverList = [];
                }
            },
            async bindDriver() {
              if(!this.data.driverNew) {
                return this.$message.warning('请选择驾驶员！')
              }
              this.loading = true;
              let res = await this.$api.vehicleBindDriverInfo({
                driverId: this.data.driverNew,
                vehicleId: this.data.vehicleId
              })
              this.loading = false;
              if (res.RS === 1) {
                this.$message({type: 'success', showClose: true, message: res.Reason});
                this.$emit('refresh');
                this.show = false;
              } else {
                this.$message({type: 'error', showClose: true, message: res.Reason});
              }
            },
            async unbindDriver() {
                this.loading = true;
                let res =await this.$api.vehivelUnbindToDriver({
                    vehicleId: this.data.vehicleId,
                    driverId: this.data.driverId
                })
                this.loading = false;
                if (res.RS === 1) {
                    this.$message({type: 'success', showClose: true, message: res.Reason});
                    this.$emit('refresh');
                    this.show = false;
                } else {
                    this.$message({type: 'error', showClose: true, message: res.Reason});
                }
            },
            showModal(rowData) {
                Object.assign(this.data, {
                    deptId: rowData.dept_id,
                    deptName: rowData.dept_name,
                    plateNo: rowData.plate_no,
                    bindTime: rowData.bind_time,
                    companyId: rowData.company_id,
                    companyName: rowData.company_name,
                    driverId: rowData.driver_id,
                    driverName: rowData.driver_name,
                    vehicleId: rowData.id,
                    driverNew: null,
                })
                this.getUnbindDriverList();
                this.show = true;
            },
        }
    }
</script>

<style scoped lang="scss">
    .car-modal-popper {
        .option-contain {
            color: #6e6e6e;
            width: 220px;
            display: flex;
            justify-content: space-between;
            > .option-label {
                color: #6e6e6e;
                flex-shrink: 0;
                margin-right: 5px
            }
            > .extra {
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis
            }
        }
    }
</style>
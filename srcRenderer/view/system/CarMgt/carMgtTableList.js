// 全部表头
export let allSettingList = [
    { name: '单位', key: 'company_name', size: '180', align:"left", type: 'company_name'},
    { name: '车牌号', key: 'plate_no', size: '120', align:"center", type: 'plate_no',sort: true},
    { name: '车辆类型', key: 'trail_type_names', size: '110', align:"center", type: 'trail_type_names'},
    {name: '终端类型名/终端手机号/终端型号/主终端ICCID卡号', key: 'terminal_type_name_one', size:'580',align: 'center', type: 'terminal_type_name_one'},
    {name: '标签', key: 'sign', size:'250',align: 'center', type: 'sign'},
    { name: '绑定司机', key: 'driver_name', size: '80', align:"center", type: 'driver_name'},
    { name: '运营状态', key: 'status', size: '80', align:"center", type: 'status'},
    { name: '车牌颜色', key: 'plateColorView', size: '80', align:"center", type: 'plateColorView'},
    { name: '终端ID(平台)', key: 'device_id', size: '100', align:"center", type: 'device_id'},
    { name: '通道明细', key: 'channelView', size: '80', align:"center", type: 'channelView'},
    { name: 'VIN码', key: 'vin', size: '100', align:"center", type: 'vin'},
    { name: '别名', key: 'alias', size: '80', align:"center", type: 'alias'},
    { name: '品牌', key: 'brand', size: '80', align:"center", type: 'brand'},
    { name: '型号', key: 'vehicle_model', size: '80', align:"center", type: 'vehicle_model'},
    { name: '关联搅拌站', key: 'mixingStationView', size: '100', align:"center", type: 'mixingStationView'},
    { name: '视频通道', key: 'channel_count', size: '80', align:"center", type: 'channel_count'},
    { name: '开始时间', key: 'fee_begin', size: '100', align:"center", type: 'fee_begin'},
    { name: '到期时间', key: 'fee_end', size: '100', align:"center", type: 'fee_end'},

    { name: '吨位', key: 'weight', size: '80', align:"center", type: 'trail_tonnage'},
    { name: '黑名单', key: 'black_white', size: '80', align:"center", type: 'black_white'},
    { name: '创建人', key: 'createBy', size: '80', align:"center", type: 'createBy'},
    { name: '创建时间', key: 'createTimeView', size: '150', align:"center", type: 'createTimeView'},
    { name: '更新人', key: 'updateBy', size: '80', align:"center", type: 'updateBy'},
    { name: '更新时间', key: 'updateTimeView', size: '150', align:"center", type: 'updateTimeView'},
    { name: '发动机编号', key: 'engineNumber', size: '150', align:"center", type: 'engineNumber'},
    { name: '发动机型号', key: 'engineModel', size: '150', align:"center", type: 'engineModel'},
    { name: '变速箱型号', key: 'gearboxModel', size: '150', align:"center", type: 'gearboxModel'},
    { name: '现车后桥速比', key: 'rearAxleSpeedRatioCurrent', size: '150', align:"center", type: 'rearAxleSpeedRatioCurrent'},
    { name: '原车后桥速比', key: 'rearAxleSpeedRatioOriginal', size: '150', align:"center", type: 'rearAxleSpeedRatioOriginal'},
    { name: '主车轮胎型号', key: 'tireModelMain', size: '150', align:"center", type: 'tireModelMain'},
]
export let ocrSettingList = [
    { name: '单位', key: 'company_name', size: '180', align:"left", type: 'company_name'},
    { name: '车牌号', key: 'plate_no', size: '120', align:"center", type: 'plate_no',sort: true},
    { name: '车辆类型', key: 'register_type', size: '110', align:"center", type: 'register_type'},
    { name: '品牌型号', key: 'vehicle_model', size: '110', align:"center", type: 'vehicle_model'},
    {name: '识别代码VIN', key: 'vin', size:'140',align: 'center', type: 'vin'},
    { name: '发动机号', key: 'engineNumber', size: '140', align:"center", type: 'engineNumber'},
    { name: '注册日期', key: 'register_date', size: '140', align:"center", type: 'register_date'},
    { name: '发证日期', key: 'issue_date', size: '140', align:"center", type: 'issue_date'},
    { name: '所有人', key: 'owners_name', size: '110', align:"center", type: 'owners_name'},
    { name: '地址', key: 'address', size: '200', align:"center", type: 'address'},
    { name: '使用性质', key: 'use_nature', size: '110', align:"center", type: 'use_nature'},
    { name: '核载质量', key: 'nuclear_carry', size: '110', align:"center", type: 'nuclear_carry'},
    { name: '保险到期日期', key: 'insurance_valid_date', size: '140', align:"center", type: 'insurance_valid_date'},
]

// 默认表头
export let defaultSettingList = [
    { name: '单位', key: 'company_name', size: '180', align:"left", type: 'company_name'},
    { name: '车牌号', key: 'plate_no', size: '120', align:"center", type: 'plate_no',sort: true},
    { name: '车辆类型', key: 'trail_type_names', size: '110', align:"center", type: 'trail_type_names'},
    {name: '终端类型名/终端手机号/终端型号/主终端ICCID卡号', key: 'terminal_type_name_one', size:'580',align: 'center', type: 'terminal_type_name_one'},
    {name: '标签', key: 'sign', size:'250',align: 'center', type: 'sign'},
    { name: '绑定司机', key: 'driver_name', size: '80', align:"center", type: 'driver_name'},
    { name: '运营状态', key: 'status', size: '80', align:"center", type: 'status'},
    { name: '黑名单', key: 'black_white', size: '80', align:"center", type: 'black_white'},
]


<template>
    <el-card :body-style="{ padding: '0px',height:'100%',display:'flex',flexDirection:'column' }" class="tree-manager" shadow="hover">
        <div class="query-wrap">
            {{title}}
        </div>
        <div class="content-wrap " v-loading="this.form.state === 'saving'">
            <div class="tree">
                <el-tree
                        v-loading="areaTableTree.loading "
                        :data="areaTableTree.data"
                        node-key="id" highlight-current
                        ref="tree" :expand-on-click-node="false"
                        accordion @node-click="nodeClick">
                    <div class="custom-tree-node" slot-scope="{ node, data }">
                        <div>
                            <el-tag size="mini" :type="tree.levelTypeMap[node.level - 1]">
                                {{tree.levelLabel[node.level - 1]}}
                            </el-tag>
                            <span :class="{'new-node':!node.data.id}">{{ node.data.name }}</span>
                        </div>
                        <div>
                            <el-button
                                    type="text"
                                    size="mini"
                                    icon="pony-iconv2 pony-jia"
                                    v-if="node.level<6"
                                    @click="appendNode(node,data)"
                            >
                            </el-button>
                            <el-button
                                    type="text"
                                    size="mini"
                                    icon="pony-iconv2 pony-jian"
                                    @click="deleteNode(node)"
                            >
                            </el-button>
                        </div>
                    </div>
                </el-tree>
            </div>
            <div class="tree-node-info">
                <template v-if="currentNode">
                    <el-form label-width="80px" :model="form.data" :rules="form.rules" size="mini" ref="form"
                             :disabled="form.mode === 'saving'"
                    >
                        <el-row>
                            <el-col :span="24" :lg="{span:11}">
                                <el-form-item label="区域名称" prop="name">
                                    <el-input v-model="form.data.name"></el-input>
                                </el-form-item>
                                <el-form-item label="排序" prop="sort">
                                    <el-input v-model="form.data.sort"></el-input>
                                </el-form-item>
                                <el-form-item label="经度" prop="lng">
                                    <el-input v-model="form.data.lng"></el-input>
                                </el-form-item>
                                <el-form-item label="维度" prop="lat">
                                    <el-input v-model="form.data.lat"></el-input>
                                </el-form-item>
                                <el-form-item label="邮政编码" prop="postal_code">
                                    <el-input v-model="form.data.postal_code"></el-input>
                                </el-form-item>
                                <el-form-item label="备注">
                                    <el-input type="textarea" v-model="form.data.remark"></el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>

                    </el-form>
                    <div style="text-align: left">
                        <el-button size="mini" :disabled="form.state !== 'needSave'"
                                   :loading="form.state === 'saving'" @click="save"
                        >保存
                        </el-button>
                        <el-button size="mini" :disabled="form.state !== 'needSave'" @click="nodeClick(currentNode.data,currentNode)">取消</el-button>
                    </div>
                </template>
                <span v-else>请选择一个树节点</span>
            </div>
        </div>
    </el-card>
</template>

<script>
    /**
     * @Author: yezy
     * @Email: <EMAIL>
     * @Date: 2019-04-25 10:50:08
     * @LastEditors: yezy
     * @LastEditTime: 2019-04-25 10:50:12
     * @Description: 部门管理
     */
    import {mapState, mapActions} from 'vuex'
    import SelectTreeInput from '../../components/common/SelectTreeInput';

    export default {
        name: "areaTableTreeMgt",
        components: {
            SelectTreeInput
        },
        data() {
            return {
                title: '行政区划管理',
                tree: {
                    type: 'areaTable',
                    levelLabel: ['国家', '省级', '地级', '县级', '乡级','村落',],
                    levelTypeMap: ['', 'success', 'info', 'warning', 'danger','danger'],
                },
                menuLevel: [
                    {value: 0, label: '系统级'},
                    {value: 1, label: '代理商级别'},
                    {value: 2, label: '公司级别'},
                    {value: 3, label: '车队级别'},
                ],
                menuType: [
                    {value: 0, label: '导航'},
                    {value: 1, label: '按钮'},
                ],
                currentNode: null,
                form: {
                    data: {
                        id: null,
                        name: '',
                        remark: '',
                        sort: 1,
                        lng: '',
                        lat: '',
                        postal_code: '',
                        type: 0,
                    },
                    rules: {
                        name: [{required: true, message: '请输入区域名称', trigger: 'change'}],
                        sort: [{required: true, message: '请输入排序', trigger: 'change'}],
                        lng: [{required: true, message: '请输入经度', trigger: 'change'}],
                        lat: [{required: true, message: '请输入维度', trigger: 'change'}],
                    },
                    state: 'default',//default needSave saving

                },
            }
        },
        computed: {
            ...mapState('ztreeData', ['treeData']),
            areaTableTree: function () {
                return JSON.parse(JSON.stringify(this.treeData.areaTable));
            }
        },
        watch: {
            'form.data': {
                handler: function (value) {
                    this.form.state = 'needSave';
                },
                deep: true,
            }
        },
        methods: {
            ...mapActions('ztreeData', {
                getStateTreeData: 'getTreeData',
            }),
            nodeClick(data, node) {
                Object.assign(this.form.data, {
                    id: data.id,
                    name: data.name,
                    remark: data.remark,
                    sort: data.sort,
                    lng: data.lng,
                    lat: data.lat,
                    postal_code: data.postal_code,
                    type: data.type,
                    parent_id: data.parent_id,
                })
                this.currentNode = node;
                this.$nextTick(() => {
                    this.form.state = 'default';
                })
            },
            async save() {
                if (!await this.$refs['form'].validate()) return;
                this.form.state = 'saving';
                const data = this.form.data;
                let params = {
                    id: data.id,
                    name: data.name,
                    remark: data.remark,
                    parent_id: data.parent_id,
                    type: data.type,
                    postal_code: data.postal_code,
                    sort: Number(data.sort),
                    lng: data.lng,
                    lat: data.lat,
                }
                this.saveNode(params)
            },
            async saveNode(params) {
                if (params.id) {
                    try {
                        let res = await this.$api.operateSysArea({
                            operate_type: 1,
                            ...params
                        });
                        if (res.status === 200) {
                            this.$success('修改成功')
                            Object.assign(this.currentNode.data, this.form.data, params)
                            this.form.state = 'default';
                        } else {
                            throw new Error('修改失败')
                        }
                    } catch (e) {
                        this.$error('修改失败')
                        this.form.state = 'needSave';
                    }
                } else {
                    try {
                        delete params.id;
                        let res = await this.$api.operateSysArea({
                            operate_type: 0,
                            ...params
                        });
                        if (res.status === 200) {
                            this.$success('新增成功')
                            Object.assign(this.form.data, {id: res.id})
                            Object.assign(this.currentNode.data, this.form.data, params)
                            this.$nextTick(() => {
                                this.form.state = 'default';
                            })
                        } else {
                            throw new Error('新增失败')
                        }
                    } catch (e) {
                        this.$error('新增失败')
                        this.form.state = 'needSave';
                    }
                }
            },
            async deleteNode(node) {
                await this.$confirm('确认删除此节点?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                })
                node.loading = true;
                if (node.data.id) {
                    let res = await this.$api.operateSysArea({
                        operate_type: -2,
                        id: node.data.id,
                    });
                    if (res.status !== 200) {
                        this.$error(res.result)
                        node.loading = false;
                        return;
                    }
                }
                this.$refs['tree'].remove(node);
                this.$success('删除成功')
                node.loading = false;
                this.currentNode = null;
            },
            appendNode(node, data) {
                this.$refs['tree'].append({
                    id: null,
                    name: '<<--新增节点-->>',
                    remark: '',
                    parent_id: node.data.id,
                    type: data.type + 1,
                    sort: 1,
                    lng: '',
                    lat: '',
                    postal_code: '',
                }, node);
                node.expanded = true;
            },
            nodeCondition2(treeNode) {
                if (treeNode.type < 3) {
                    this.$message({
                        type: 'warning',
                        showClose: true,
                        message: '至少选择到市级别！'
                    })
                    return false;
                }
                return true;
            },
        },
        async created() {
            this.getStateTreeData(this.tree.type)
        }
    }
</script>

<style scoped lang="scss">
    @import "DepartmentTreeMgt";
</style>
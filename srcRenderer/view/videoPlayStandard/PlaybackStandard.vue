<template>
		<div style="height: 100%;" v-stop-mouse-move="{time:300,handler:onStop}">
				<Layout id="playback1078" :has-color="true">
						<template slot="aside">
								<ElementTree type="vehicleWithChannel" state onlineFilter onlineCountTip @node-click="playbackTreeClick"
								             @node-dbl-click="playbackTreeDblClick" class="flex-grow bg bg--light box-shadow"
								             :style="queryMore ? 'height:calc(100% - 345px)':'height:calc(100% - 158px)'"></ElementTree>
								<div class="search-wrap flex-not-shrink bg bg--light box-shadow">
										<div class="query-item">
												<label>开始时间：</label>
												<el-date-picker v-model="query.startTime" class="query-input" type="datetime">
												</el-date-picker>
										</div>
										<div class="query-item">
												<label>结束时间：</label>
												<el-date-picker v-model="query.endTime" class="query-input" type="datetime">
												</el-date-picker>
										</div>
										<transition name="fade">
												<div class="query-more-box" v-show="queryMore">
														<div class="query-item">
																<label>报警标志:</label>
																<DictionarySelect :all="true" code="mini_media_event_code" v-model="query.event_code"
																                  placeholder="选择报警标志" multiple></DictionarySelect>
														</div>
														<div class="query-item">
																<label>资源类型:</label>
																<el-select v-model="query.media_type" class="query-input" type="datetime">
																		<el-option label="音视频" :value="0"></el-option>
																		<el-option label="音频" :value="1"></el-option>
																		<el-option label="视频" :value="2"></el-option>
																		<el-option label="音视频或视频" :value="3"></el-option>
																		<el-option label="平台录制" :value="4"></el-option>

																</el-select>
														</div>
														<div class="query-item">
																<label>码流类型:</label>
																<el-select v-model="query.stream_type" class="query-input" type="datetime">
																		<el-option label="所有码流" :value="0"></el-option>
																		<el-option label="主码流" :value="1"></el-option>
																		<el-option label="子码流" :value="2"></el-option>
																</el-select>
														</div>
														<div class="query-item">
																<label>存储器类型:</label>
																<el-select v-model="query.storage_type" class="query-input" type="datetime">
																		<el-option label="所有存储器" :value="0"></el-option>
																		<el-option label="主存储器" :value="1"></el-option>
																		<el-option label="灾备存储器" :value="2"></el-option>
																</el-select>
														</div>
												</div>
										</transition>
										<div class="query-item" style="padding:0">
												<p class="query-more" @click="queryMore = !queryMore">更多<i
														:class="['pony-iconv2',queryMore?'pony-shang':'pony-xia']"></i></p>
										</div>
										<div class="query-item">
												<el-button type="primary" @click="search" class="w100">查询</el-button>
										</div>
								</div>
						</template>
						<template slot="content">
								<div class="video-right-wrap"
								     :class="{'multiple-4':chn4Mode,'multiple-9':chn9Mode,'multiple-16':chn16Mode}">
										<div :class="['video-map-wrap']">
												<div class="video-wrap"
												     :style="chn9Mode || chn4Mode || chn16Mode? 'height: calc(100% - 20px);' :''"
												     v-show="!videoUrl.length">
														<div class="video-player"
														     :class="{selected:index===window.selected,maximize:index===window.maximize}"
														     v-for="(item,index) in (chn16Mode ? 16 :chn9Mode?9:chn4Mode?4:1)" :key="index"
														     @click="selectWindow(index)" @dblclick="toggleMaximize(index)">
																<!-- <VideoPlayer
																								v-if="videoUrl[index]"
																								ref="playerVideo" @ready="eventEmitter('ready', $event)" :options="videoOption[index]" >
																								</VideoPlayer> -->
																<PlaybackPlayer ref="player" type="playBack" :vehicleId="currentVehicle.vehicle_id"
																                :terminalCode="currentVehicle.code" :level="playParams.params.level"
																                :play_code="playParams.params.playbackWays"
																                :symbol="currentVehicle.plate_no"
																                :label="$t('common.chnName') +formatVideoPlayList[index].chn"
																                :chn="formatVideoPlayList[index].chn"
																                :startTime="formatVideoPlayList[index].startTime"
																                :endTime="formatVideoPlayList[index].endTime"
																                @currentTimeChange="(time)=>currentTimeChange(index,time)"
																                @stop="onVideoStop">
																</PlaybackPlayer>

														</div>
														<div class="process-all" v-show="chn9Mode || chn4Mode || chn16Mode">
																<el-slider class="el-silder-nomargin" v-model="activeSlider"
																           @change="activeSliderChange" :show-tooltip="false">
																</el-slider>
														</div>
												</div>
												<div class="video-wrap" v-show="videoUrl.length">
														<div class="video-player"
														     :class="{selected:index===window.selected,maximize:index===window.maximize}"
														     v-for="(item,index) in videoOption" :key="item.sources[0].src">
																<VideoPlayer ref="playerVideo" @ready="eventEmitter('ready', $event)"
																             :options="item" @closeVideo="closeVideo">
																</VideoPlayer>
														</div>
														<div class="video-player"
														     :class="{selected:index===window.selected,maximize:index===window.maximize}"
														     v-for="(item,index) in noVideoUrlList" :key="item">
																<PlaybackPlayer ref="player"
																                type="playBack"
																                :vehicleId="currentVehicle.vehicle_id"
																                :terminalCode="currentVehicle.code"
																                :level="playParams.params.level"
																                :play_code="playParams.params.playbackWays"
																                :symbol="currentVehicle.plate_no"
																                :label="$t('common.chnName') +formatVideoPlayList[index].chn"
																                :chn="formatVideoPlayList[index].chn"
																                :startTime="formatVideoPlayList[index].startTime"
																                :endTime="formatVideoPlayList[index].endTime"
																                @currentTimeChange="(time)=>currentTimeChange(index,time)"
																                @stop="onVideoStop">
																</PlaybackPlayer>
														</div>
												</div>
												<!-- <div class="map-wrap">
																				<GPSRecordMap ref="recordMap" :start-time="timeToDate(currentVideo.startTime)" :end-time="timeToDate(currentVideo.endTime)" :vehicle-id="currentVehicle.vehicle_id" :current-time="currentVideo.currentTime"></GPSRecordMap>
																		</div> -->
												<div class="fixed-button">

														<template v-if="chn9Mode || chn4Mode || chn16Mode">
																<i class="pony-iconv2 pony-sanjiaoxing text text--link" :title="$ct('playAll')"
																   @click="allPlay" v-if="videoPlayList.length || videoUrl.length"></i>
																<i class="pony-iconv2 pony-tingzhi text text--link" :title="$ct('stopAll')"
																   @click="allStop" v-if="videoPlayList.length || videoUrl.length"></i>
														</template>
														<i class="pony-iconv2 pony-sifenping text text--link" title="2 x 2"
														   @click="toggleMultiple(4)"></i>
														<i class="pony-iconv2 pony-jiufenping text text--link" title="3 x 3"
														   @click="toggleMultiple(9)"></i>
														<i class="pony-iconv2 pony-shiliufenping text text--link" title="4 x 4"
														   @click="toggleMultiple(16)"></i>
														<i class="pony-iconv2 pony-shuaxin text text--link" :title="$ct('refresh')"
														   @click="search"></i>
												</div>
										</div>

										<el-tabs class="special-border-bottom video-list" v-model="tabName" type="border-card"
										         style="border: none;  user-select: none;">
												<el-tab-pane :label="$ct('timeAxis')" name="axis">
														<VideoListAxis :chn="chnList.length ? chnList : undefined" :timeData="timeData"
														               ref="videoAxis" v-loading="recordTable.loading"
														               :element-loading-text="$ct('searching')"
														               @do="cmdAdapter"></VideoListAxis>
												</el-tab-pane>
												<el-tab-pane :label="$ct('recordList')" name="table" lazy>
														<el-table :empty-text="recordTable.loading? ' ':$ct('noResult')"
														          v-loading="recordTable.loading" ref="record" :data="recordTable.data" border
														          stripe
														          height="100%" style="margin-top: -1px" highlight-current-row
														          @row-dblclick="realPlay">
																<el-table-column type="index" :label="$ct('index')" width="50">
																</el-table-column>
																<el-table-column prop="beg" :label="$ct('chn')" min-width="60" :filters="tableFilter"
																                 :filter-multiple="false" :filter-method="positionFilter">
																		<template slot-scope="scope">
																				<span>{{ toCHN(scope.row.channel_no) }}</span>
																		</template>
																</el-table-column>
																<el-table-column prop="beg" :label="$ct('startTime')" min-width="130">
																		<template slot-scope="scope">
																				<span>{{ scope.row | toStart }}</span>
																		</template>
																</el-table-column>
																<el-table-column prop="end" :label="$ct('endTime')" min-width="130">
																		<template slot-scope="scope">
																				<span>{{ scope.row | toEnd }}</span>
																		</template>
																</el-table-column>

																<el-table-column prop="end" label="资源类型" min-width="100">
																		<template slot-scope="scope">
																				<span>{{ mediaType[scope.row.media_type] }}</span>
																		</template>
																</el-table-column>
																<el-table-column prop="end" label="码流类型" min-width="120">
																		<template slot-scope="scope">
																				<span>{{ streamType[scope.row.stream_type] }}</span>
																		</template>
																</el-table-column>
																<el-table-column prop="end" label="存储器类型" min-width="130">
																		<template slot-scope="scope">
																				<span>{{ storageType[scope.row.storage_type] }}</span>
																		</template>
																</el-table-column>
																<el-table-column prop="end" label="报警标志" min-width="130">
																		<template slot-scope="scope">
																				<span>{{ scope.row.alarmType }}</span>
																		</template>
																</el-table-column>
																<el-table-column :label="$ct('timeLength')" min-width="100">
																		<template slot-scope="scope">
																				<span>{{ toTime(scope.row) }}</span>
																		</template>
																</el-table-column>
																<el-table-column prop="len" :label="`${$ct('fileSize')}(MB)`" min-width="90">
																		<template slot-scope="scope">
																				<span>{{ scope.row.size | toOmen }}</span>
																		</template>
																</el-table-column>
																<el-table-column prop="file" :label="$ct('fileName')" show-overflow-tooltip
																                 min-width="130">
																		<template slot-scope="scope" v-once>
																				<span>{{ toFileName(scope.row) }}</span>
																		</template>
																</el-table-column>
																<el-table-column :label="$ct('state')" min-width="100">
																		<template slot-scope="scope">
																				<span>{{
																								scope.row.status !== undefined ? $ct(`fileState[${scope.row.status}]`) : $ct('loading')
																						}}</span>
																				<i class="pony-iconv2 pony-shijian2" style="vertical-align:-2px"
																				   v-if="scope.row.status == 0 || scope.row.status == 4"
																				   :title="`${scope.row.upload_start} ~ ${scope.row.upload_end}`"></i>
																		</template>
																</el-table-column>
																<el-table-column :label="$ct('progress')" min-width="130">
																		<div class="video-control" slot-scope="{row,$index}">
																				<!-- loading-->
																				<span v-if="row.loading"><i
																						class="el-icon-loading"></i>{{ $ct('loading') }}</span>
																				<!--正在下载-->
																				<template v-else-if="row.status === 0">
																						<span v-if="row.progress === -1">
																								<template v-if="row.ftp == 1">
																										{{ $ct('clickHint3') }}
																								</template>
																								<template v-else>
																										{{ $ct('clickHint2') }}
																								</template>
																						</span>
																						<el-progress v-else :percentage="row.progress" :text-inside="true"
																						             style="flex-grow: 1" :stroke-width="14"></el-progress>
																						<i class="pony-iconv2 pony-guanbi" :title="$ct('cancelDownload')"
																						   @click="cancelDownload(row)"></i>
																				</template>
																				<!--下载完成-->
																				<template v-else-if="row.status === 4">
																						<span class="font-blue"
																						      style="color:#67C23A"
																						      @click="download(toFileName(row),row.url)">
																								{{ $ct('clickForDownload') }}
																								<i class="pony-iconv2 pony-xiazai" style="cursor: pointer;margin-left: 5px"></i>
																						</span>
																						<i class="pony-iconv2 pony-shuaxin" :title="$ct('reUpload')"
																						   @click="downloadSelectTime(row,true)"></i>
																				</template>
																				<!--请求上传-->
																				<span v-else class="font-blue" @click="downloadSelectTime(row)">
																						{{ $ct('clickForUpload') }}
																						<i class="pony-iconv2 pony-xiazai" style="cursor: pointer;margin-left: 5px"></i>
																				</span>
																		</div>
																</el-table-column>
																<el-table-column :label="$ct('control')" min-width="50">
																		<template slot-scope="{row}">
																				<i class="pony-iconv2 pony-sanjiaoxing" :title="$ct('play')"
																				   @click="realPlay(row)" style="margin-right: 5px;"></i>
																		</template>
																</el-table-column>
														</el-table>
												</el-tab-pane>
												<el-tab-pane :label="$ct('downloadList')" name="download" lazy>
														<el-table :empty-text="downloadTable.loading? $ct('searching') : $ct('noResult')"
														          v-loading="downloadTable.loading" ref="download" :data="downloadTable.data" border
														          stripe height="100%" style="margin-top: -1px" highlight-current-row>
																<el-table-column type="index" :label="$ct('index')" min-width="60">
																</el-table-column>
																<el-table-column :label="$ct('chn')" min-width="60" :filters="tableFilter"
																                 :filter-multiple="false" :filter-method="positionFilter">
																		<template slot-scope="scope">
																				<span>{{ toCHN(scope.row.channel_no) }}</span>
																		</template>
																</el-table-column>
																<el-table-column :label="$ct('startTime')" min-width="130">
																		<template slot-scope="scope">
																				<span>{{ scope.row | toStart }}</span>
																		</template>
																</el-table-column>
																<el-table-column :label="$ct('endTime')" min-width="130">
																		<template slot-scope="scope">
																				<span>{{ scope.row | toEnd }}</span>
																		</template>
																</el-table-column>
																<el-table-column prop="end" label="资源类型" min-width="100">
																		<template slot-scope="scope">
																				<span>{{ mediaType[scope.row.media_type] }}</span>
																		</template>
																</el-table-column>
																<el-table-column prop="end" label="码流类型" min-width="120">
																		<template slot-scope="scope">
																				<span>{{ streamType[scope.row.stream_type] }}</span>
																		</template>
																</el-table-column>
																<el-table-column prop="end" label="报警标志" min-width="130">
																		<template slot-scope="scope">
																				<span>{{ scope.row.alarmType }}</span>
																		</template>
																</el-table-column>
																<el-table-column :label="$ct('timeLength')" min-width="120">
																		<template slot-scope="scope">
																				<span>{{ toTime(scope.row) }}</span>
																		</template>
																</el-table-column>
																<el-table-column prop="file" :label="$ct('fileName')" show-overflow-tooltip>
																		<template slot-scope="scope" v-once>
																				<span>{{ toFileName(scope.row) }}</span>
																		</template>
																</el-table-column>
																<el-table-column :label="$ct('state')" min-width="120">
																		<template slot-scope="scope">
																				<span>{{
																								scope.row.status !== undefined ? $ct(`fileState[${scope.row.status}]`) : $ct('loading')
																						}}</span>
																		</template>
																</el-table-column>
																<el-table-column :label="$ct('mode')" min-width="120">
																		<template slot-scope="{row}">
																				{{ row.ftp == 1 ? 'ftp' : row.ftp == 2 ? '平台录制' : '-' }}
																		</template>
																</el-table-column>
																<el-table-column :label="$ct('progress')" min-width="150">
																		<div class="video-control" slot-scope="{row,$index}">
																				<!-- loading-->
																				<span v-if="row.loading"><i
																						class="el-icon-loading"></i>{{ $ct('loading') }}</span>
																				<!--正在下载-->
																				<template v-else-if="row.status === 0">
																						<template v-if="row.progress === -1">
												<span v-if="row.ftp == 1">
													{{ $ct('clickHint4') }}
												</span>
																								<span v-else>
													{{ $ct('clickHint2') }}
												</span>
																						</template>
																						<template v-else>
																								<el-progress :percentage="row.progress" :text-inside="true"
																								             style="flex-grow: 1" :stroke-width="14"></el-progress>
																						</template>
																						<i class="pony-iconv2 pony-guanbi" :title="$ct('cancelDownload')"
																						   @click="cancelDownload(row)"></i>
																				</template>
																				<!--下载完成-->
																				<template v-else-if="row.status === 4">
											<span class="font-blue" style="color:#67C23A"
											      @click="download(toFileName(row),row.url)">
												{{ $ct('clickForDownload') }}
												<i class="pony-iconv2 pony-xiazai"
												   style="cursor: pointer;margin-left: 5px"></i>
											</span>
																						<i class="pony-iconv2 pony-shuaxin" :title="$ct('reUpload')"
																						   @click="downloadSelectTime(row,true)"></i>
																				</template>
																				<!--请求上传-->
																				<span v-else class="font-blue" @click="downloadSelectTime(row)">
											{{ $ct('clickForUpload') }}
											<i class="pony-iconv2 pony-xiazai"
											   style="cursor: pointer;margin-left: 5px"></i>
										</span>
																		</div>
																</el-table-column>
																<el-table-column :label="$ct('control')" min-width="50">
																		<template slot-scope="{row}">
																				<i class="pony-iconv2 pony-sanjiaoxing" :title="$ct('play')"
																				   @click="realPlayDown(row)" :disabled="!row.url"
																				   style="margin-right: 5px;"></i>
																		</template>
																</el-table-column>
														</el-table>
												</el-tab-pane>
										</el-tabs>
								</div>
						</template>
						<PonyDialog v-model="timeShow.show" width="330" okText="确认上传" @close="timeShow.show = false"
						            @confirm="confirmUpload" title="上传视频">
								<div class="search-wrap ">
										<div class="query-item">
												<label>开始时间：</label>
												<el-date-picker v-model="timeShow.startTime" class="query-input" type="datetime"
												                value-format="yyyy-MM-dd HH:mm:ss" :picker-options="pickeroptions1">
												</el-date-picker>
										</div>
										<div class="query-item">
												<label>结束时间：</label>
												<el-date-picker v-model="timeShow.endTime" class="query-input" type="datetime"
												                value-format="yyyy-MM-dd HH:mm:ss" :picker-options="pickeroptions">
												</el-date-picker>
										</div>
										<div class="query-item">
												<el-checkbox v-model="ftpMode" :disabled="!currentVehicle.is_ftp_support">
														FTP{{ $ct('mode') }}
												</el-checkbox>
												<i class="pony-iconv2 pony-bangzhu" :title="$ct('messageInfo.10')" style="margin-left: -10px;
                    margin-bottom: 2px;"></i>
										</div>

								</div>
						</PonyDialog>
						<PonyDialog v-model="playParams.show" width="330" okText="播放" @close="playParams.show = false"
						            @confirm="confirmPlay" title="播放参数设置">
								<div class="search-wrap ">
										<div class="query-item">
												<label>开始时间：</label>
												<el-date-picker v-model="playParams.params.startTime" class="query-input" type="datetime"
												                value-format="yyyy-MM-dd HH:mm:ss" :picker-options="playPickeroptions1">
												</el-date-picker>
										</div>
										<div class="query-item" v-if="playParams.params.playbackWays != 4">
												<label>结束时间：</label>
												<el-date-picker v-model="playParams.params.endTime" class="query-input" type="datetime"
												                value-format="yyyy-MM-dd HH:mm:ss" :picker-options="playPickeroptions">
												</el-date-picker>
										</div>
										<div class="query-item">
												<label>回放方式：</label>
												<el-select v-model="playParams.params.playbackWays" style="width:215px">
														<el-option label="正常回放" :value="0"></el-option>
														<el-option label="快进回放" :value="1"></el-option>
														<el-option label="关键帧快退回放" :value="2"></el-option>
														<el-option label="关键帧播放" :value="3"></el-option>
														<el-option label="单帧上传" :value="4"></el-option>
												</el-select>
										</div>
										<div class="query-item"
										     v-if="playParams.params.playbackWays == 1 || playParams.params.playbackWays == 2">
												<label>倍速：</label>

												<el-select v-model="playParams.params.level" style="width:215px">
														<el-option label="X1" :value="1"></el-option>
														<el-option label="X2" :value="2"></el-option>
														<el-option label="X4" :value="3"></el-option>
														<el-option label="X8" :value="4"></el-option>
														<el-option label="X16" :value="5"></el-option>
												</el-select>
										</div>

								</div>
						</PonyDialog>
				</Layout>
		</div>
</template>

<script>
/**
 * @Author: xieyj
 * @Email:
 * @Date: 2020-05-11 15:23:45
 * @LastEditors: xieyj
 * @LastEditTime: 2020-05-11 15:23:45
 * @Description: 1078录像回放
 */
import VideoPlayer from "@/components/alarmvideo/VideoPlayer";

import GPSRecordMap from '@/view/videoPlay/component/GPSRecordMap'
// import videoWS from './component/videoPlayerV2/lib/ws';
import VideoListAxis from '@/view/videoPlay/component/VideoListAxis/VideoListAxis'

import PlaybackPlayer from "@/view/videoPlayStandard/component/videoPlayerStandard/PlaybackPlayerStandard";
import {
		mapState
} from 'vuex';

const API_URL = window.PONY.media.apiUrl;
import DictionarySelect from '@/components/common/DictionarySelect'

export default {
		name: "playbackStandard",
		_i18Name: 'playback1078',

		components: {
				PlaybackPlayer,
				GPSRecordMap,
				DictionarySelect,
				VideoListAxis,
				VideoPlayer
		},
		data() {
				return {
						videoUrl: [],
						activeSlider: 0,
						// maxVideoTime:0,
						playParams: {
								show: false,
								rowData: null,
								params: {
										playbackWays: 0,
										level: 1,
										startTime: '',
										endTime: '',
								}

						},
						timeShow: {
								show: false,
								startTime: '',
								endTime: '',
								rowData: null,
								reupload: true

								// chn:0
						}, //设置下载时间的弹框
						queryMore: false,
						query: {
								date: moment().startOf('day'),
								startTime: moment().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
								endTime: moment().endOf('day').format('YYYY-MM-DD HH:mm:ss'),
								chn: 0,
								media_type: 0,
								stream_type: 0,
								storage_type: 0,
								event_code: [null]
						},
						currentVehicle: {
								plate_no: '',
								vehicle_id: '',
								code: '',
								chnNo: 0,
						},
						mediaType: ['音视频', '音频', '视频', '视频或音视频', '平台录制'],
						streamType: ['主码流或子码流', '主码流', '子码流'],
						storageType: ['主存储器或灾备存储器', '主存储器', '灾备存储器'],

						channelNoList: [],
						currentVideo: {
								startTime: null,
								endTime: null,
								currentTime: null,
						},
						videoPlayList: [
								// {
								//     chn:0,
								//     startTime:null,
								//     endTime:null,
								// }
						],
						recordTable: {
								loading: false,
								data: []
						},
						downloadTable: {
								loading: false,
								data: []
						},
						timeInter: null,

						tabName: 'axis',
						chn4Mode: false,
						chn9Mode: false,
						chn16Mode: false,
						ftpMode: true,
						newPlayer: false,
						currentRecordAjax: null,
						ws: null,
						plateNoTemp: null,
						chnList: [1, 2, 3, 4], //标识设备的前4个通道号是什么 不一定是1,2,3,4
						timeData: undefined,
						window: {
								selected: -1,
								maximize: -1,
						},

						// completedChn: 0,
						pickeroptions: {
								disabledDate: time => {
										return time.getTime() <= new Date(this.timeShow.startTime).getTime();
								}
						},
						playPickeroptions: {
								disabledDate: time => {
										return time.getTime() <= new Date(this.playParams.params.startTime).getTime();
								}
						},

				}
		},
		filters: {
				toStart(value) {
						if (value) {
								let startTime = value.start_time;
								return moment(startTime, 'YYYYMMDDHHmmss').format('YYYY-MM-DD HH:mm:ss');
						}
				},
				toEnd(value) {
						if (value) {
								let endTime = value.end_time;
								return moment(endTime, 'YYYYMMDDHHmmss').format('YYYY-MM-DD HH:mm:ss');
						}
				},
				toOmen(value) {
						if (value) {
								return (value / 1024 / 1024).toFixed(2);
						}
				},

		},
		computed: {
				...mapState('dictionary', ['dictionary']),
				...mapState("auth", ["token"]),

				tableFilter: function () {
						return this.chnList.map(item => {
								return {
										text: this.toCHN(item),
										value: item,
								}
						})
				},
				noVideoUrlList() {
						let urlLength = this.videoUrl.length;
						let num = this.chn16Mode ? ((16 - urlLength) >= 0 ? (16 - urlLength) : 0) : this.chn9Mode ? ((9 -
								urlLength) >= 0 ? (9 - urlLength) : 0) : this.chn4Mode ? ((4 - urlLength) >= 0 ? (4 - urlLength) :
								0) : ((1 - urlLength) >= 0 ? (1 - urlLength) : 0)
						return num
				},
				videoOption() {
						return this.videoUrl.map(item => {
								return {
										sources: [{
												type: "video/mp4",
												src: item
										}]
								};

						})
				},
				formatVideoPlayList: function () {
						let arrLength = (this.chn4Mode || this.chn9Mode || this.chn16Mode) ? (this.chn16Mode ? 16 : this
								.chn4Mode ? 4 : 9) : 1;
						
						return new Array(arrLength).fill(0).map((item, index) => {
								return this.videoPlayList[index] || {}
						})
				},
				pickeroptions1: function () {
						let strStart = moment(this.timeShow.startTime).format('HH:mm:ss')
						let strEnd = moment(this.timeShow.endTime).format('HH:mm:ss')
						return {
								selectableRange: strStart + "-" + strEnd
						}
						// selectableRange:time=> {
						//     time.getTime() - new Date(moment(this.timeShow.endTime)).getTime();
						// }
				},
				playPickeroptions1: function () {
						let strStart = moment(this.playParams.params.startTime).format('HH:mm:ss')
						let strEnd = moment(this.playParams.params.endTime).format('HH:mm:ss')
						return {
								selectableRange: strStart + "-" + strEnd
						}
						// selectableRange:time=> {
						//     time.getTime() - new Date(moment(this.timeShow.endTime)).getTime();
						// }
				},
		},
		watch: {
				'query.event_code': function (newVal, oldVal) {
						let newindex = newVal.indexOf(null),
								oldindex = oldVal.indexOf(null); //获取newval和oldval里null的索引,如果没有则返回-1
						if (newindex != -1 && oldindex == -1 && newVal.length > 1) //如果新的选择里有勾选了选择所有选择所有 则 只直线勾选所有整个选项
								this.query.event_code = [null];
						else if (newindex != -1 && oldindex != -1 && newVal.length >
								1) //如果操作前有勾选了选择所有且当前也选中了勾选所有且勾选数量大于1  则移除掉勾选所有
								this.query.event_code.splice(newVal.indexOf(null), 1)
				},
				'videoPlayList': {
						handler(val) {
								if (this.videoPlayList.length) {
										this.videoUrl = []
								}
						},
						deep: true

				}
		},
		mounted() {
		},
		methods: {

				// 全局进度条
				activeSliderChange(value) {
						if (!this.$refs['player'].find(item => item.isPlaying)) {
								this.activeSlider = 0
								return
						}
						this.$refs['player'].forEach(item => {
								if (!item.isPlaying) return
								item.setProgress(value)
						})
				},
				toTime(value) {
						if (value != undefined) {
								let startTime = moment(value.start_time, 'YYYYMMDDHHmmss').toDate();
								let endTime = moment(value.end_time, 'YYYYMMDDHHmmss').toDate();
								let ms = endTime - startTime;
								return this.SecondFormat(ms)
						}

				},
				onVideoStop(chn) {
						let index = this.videoPlayList.findIndex(item => item && item.chn === chn);
						// this.$set(this.videoPlayList, index);
						this.videoPlayList.splice(index, 1)
				},
				toggleMultiple(chnNo) {
						if (chnNo == 16) {
								this[`chn16Mode`] = !this[`chn16Mode`];
								this[`chn4Mode`] = false;
								this[`chn9Mode`] = false
						} else {
								let other = chnNo === 4 ? 9 : 4;
								this[`chn${chnNo}Mode`] = !this[`chn${chnNo}Mode`];
								this[`chn${other}Mode`] = false;
								this[`chn16Mode`] = false
						}
						if (this[`chn16Mode`] === false && this[`chn4Mode`] === false && this[`chn9Mode`] === false) {
								chnNo = 1
						}
						if (this.videoUrl.length > chnNo || this[`chn16Mode`] === false || this[`chn4Mode`] === false || this[`chn9Mode`] === false) {
								this.videoUrl.splice(chnNo)
						}
						this.activeSlider = 0

				},
				toggleMaximize(index) {
						if (!this.chn4Mode && !this.chn9Mode && !this.chn16Mode) return;
						this.window.maximize = this.window.maximize === index ? -1 : index;
				},
				selectWindow: _.throttle(async function (index) {
						this.window.selected = index;

						if (!this.currentVehicle || !this.videoPlayList[index]) return;
						Object.assign(this.currentVideo, {
								startTime: this.videoPlayList[index].startTime,
								endTime: this.videoPlayList[index].endTime,
						})
						await this.$nextTick()
						// this.$refs['recordMap'].getGPSData();
				}, 500),
				toCHN(value) {
						return this.$t('common.chnName') + value;
				},
				timeToDate(str) {
						return moment(str, 'YYYYMMDDHHmmss').toDate();
				},
				toFileName(value) {
						if (value) {
								if (value.fileName) {
										return value.fileName;
								} else {
										return `${this.plateNoTemp}_${value.channel_no}_${value.start_time}`
								}
						}
				},
				async playbackTreeClick(data, node, $node) {
						if (data.type === 4) {
								try {
										this.currentVehicle = await this.$store.dispatch('vehicle/getVehicleInfo', {
												key: 'id',
												value: data.id
										});

										//设置通道号 状态树才有这个字段，普通车辆树没有
										this.currentVehicle.chnNo = data.extraInfo.channelCount;

										let result = await this.$api.getChannelNoByVehicleId({
												vehicle_id: data.id + ":V2"
										})
										this.channelNoList = []
										result.data.channel_valid_v2.forEach((item, index) => {
												if (item) {
														this.channelNoList.push(index + 1)
												}
										})
										return true;
								} catch (e) {
										this.$info(this.$ct('messageInfo[0]'))
										return false
								}
						}
						if (data.type === 5) {
								this.currentVehicle = await this.$store.dispatch('vehicle/getVehicleInfo', {
										key: 'id',
										value: node.parent.data.id
								});
								this.channelNoList = [data.chn]

						}
						return false
				},
				async playbackTreeDblClick(data, node, $node) {
						if (await this.playbackTreeClick(data, node, $node)) {
								this.search();
						}
				},
				eventEmitter(type, target) {
						if (target) target.play();
				},
				realPlayDown(row) {
						this.videoPlayList = []

						if (this.videoUrl.includes(row.url)) {
								this.$warning('已添加该视频!')
								return
						}

						this.videoUrl.push(row.url)
						if (this.videoUrl.length > 1 && this.videoUrl.length <= 4) {
								if (!this.chn9Mode && !this.chn16Mode && !this.chn4Mode) {
										this.chn4Mode = true
								}
						}
						if (this.videoUrl.length > 4 && this.videoUrl.length <= 9) {
								if (!this.chn9Mode && !this.chn16Mode) {
										this.chn9Mode = true
										this.chn4Mode = false
								}
						}
						if (this.videoUrl.length > 9 && this.videoUrl.length <= 16) {
								if (!this.chn16Mode) {
										this.chn16Mode = true
										this.chn4Mode = false
										this.chn9Mode = false
								}
						}
						// this.$nextTick(()=>{
						//     console.log(this.$refs['playerVideo']);
						//     this.$refs['playerVideo'][this.videoUrl.length - 1].initVideo()
						// })
				},
				realPlay(rowData) {
						let chnObj = this.downloadTable.data.find(item => item.channel_no == rowData.channel_no && !item.ftp &&
								item.status == 0)

						if (chnObj) {
								this.$confirm('当前有同一通道的视频正在上传，如点击播放，视频上传会失败，请确认是否继续播放？', '提示', {
										confirmButtonText: '确定',
										cancelButtonText: '取消',
										type: 'warning'
								}).then(async () => {
										this.showPlayConfig(rowData)
								}).catch(() => {
										console.log('服务器出错')
								})
						} else {
								this.showPlayConfig(rowData)

						}


				},
				// chn >=0   startTime,endTime YYYYMMDDHHmmss
				async play(chn, startTime, endTime) {
						const params = {
								chn: chn + 1,
								startTime: startTime,
								endTime: endTime
						}
						let changeIndex;
						//单窗口模式 直接替换单前视频
						if (!this.chn4Mode && !this.chn9Mode && !this.chn16Mode) {
								changeIndex = 0;
						} else {
								// changeIndex = 0;
								const arrLength = this.chn4Mode ? 4 : this.chn9Mode ? 9 : 16;
								const index = this.videoPlayList.slice(0, arrLength).findIndex(item => item === null);
								const sameChnIndex = this.videoPlayList.findIndex(item => item && item.chn === (chn + 1));
								if (sameChnIndex !== -1) {
										changeIndex = sameChnIndex
								} else if (index === -1) {
										if (this.videoPlayList.length < arrLength) { //说明非空item的length 不足4或9
												changeIndex = this.videoPlayList.length;
										} else { //或者刚好等于4或9;
												//如果处于4通道模式并且通道数量大于4的情况  则自动切换到9通道模式
												if (this.chn4Mode && this.chnList.length > 4) {
														changeIndex = 4;
														this.toggleMultiple(9);
														await this.$nextTick();
												}
												if (this.chn9Mode && this.chnList.length > 9) {
														changeIndex = 9;
														this.toggleMultiple(16);
														await this.$nextTick();
												}
												//逻辑上永远不会触发else
												// else {
												//     this.$warning('没有空余的窗口了，将替换同一通道的窗口');
												//     changeIndex = this.videoPlayList.findIndex(item => item.chn === chn);
												// }
										}
								} else {
										changeIndex = index;
								}
						}
						//找到第一个空位塞进去
						const target = this.videoPlayList[changeIndex]
						// if (target && target.chn === chn && (this.chn9Mode || this.chn4Mode)) {
						//     await this.$confirm('同一通道同时只能播放一个视频，是否替换', '提示', {
						//         confirmButtonText: '确定',
						//         cancelButtonText: '取消',
						//         type: 'warning'
						//     })
						// }
						this.$set(this.videoPlayList, changeIndex, params);
						await this.$nextTick();
						let item = this.$refs['player'][changeIndex];
						item.loadSource()
						this.selectWindow(changeIndex)
				},
				allPlay() {
						if (this.tabName == 'download') {
								this.$refs['playerVideo'].slice(0, this.videoUrl.length).forEach(item => {
										item.rePlay()
								})
						} else {
								this.$nextTick(() => {
										this.$refs['player'].slice(0, this.chnList.length).forEach(item => {
												if (item.chn) {
														item.reload()
												}
										})
								})
						}

				},
				allStop() {
						if (this.tabName == 'download') {
								this.videoUrl = []
						} else {
								this.$nextTick(() => {
										this.$refs['player'].forEach(item => {
												if (item.chn) {
														item.stop()
												}
										})
								})
						}

				},
				closeVideo(src) {
						let arr = JSON.parse(JSON.stringify(this.videoUrl))
						let num = arr.indexOf(src)
						this.videoUrl.splice(num,1)
				},
				currentTimeChange(index, time) {
						if (index === this.window.selected) {
								this.currentVideo.currentTime = moment(this.timeToDate(this.currentVideo.startTime)).add(time,
										'seconds').toDate();
						}
				},
				showPlayConfig(row) {
						this.playParams.rowData = row
						this.playParams.show = true
						this.playParams.params.startTime = moment(row.start_time, 'YYYYMMDDHHmmss').format('YYYY-MM-DD HH:mm:ss')
						this.playParams.params.endTime = moment(row.end_time, 'YYYYMMDDHHmmss').format('YYYY-MM-DD HH:mm:ss')
						this.playParams.params.level = 1
						this.playParams.params.playbackWays = 0

				},
				confirmPlay() {
						this.playParams.show = false
						this.play(this.playParams.rowData.channel_no - 1, moment(this.playParams.params.startTime,
								'YYYY-MM-DD HH:mm:ss').format('YYYYMMDDHHmmss'), moment(this.playParams.params.endTime,
								'YYYY-MM-DD HH:mm:ss').format('YYYYMMDDHHmmss'));
				},
				//从真实地址下载
				download(name, url) {
						let aLink = document.createElement('a');
						let evt = document.createEvent("HTMLEvents");
						evt.initEvent("click", true, true); //initEvent 不加后两个参数在FF下会报错  事件类型，是否冒泡，是否阻止浏览器的默认行为
						aLink.download = name;
						aLink.href = url;
						aLink.target = '_blank';
						// aLink.dispatchEvent(evt);
						//aLink.click()
						aLink.dispatchEvent(new MouseEvent('click', {
								bubbles: true,
								cancelable: true,
								view: window
						})); //兼容火狐
				},
				downloadSelectTime(row, reupload = false) {
						this.timeShow.show = true
						this.ftpMode = false;
						this.timeShow.startTime = moment(row.start_time, 'YYYYMMDDHHmmss').format('YYYY-MM-DD HH:mm:ss')
						this.timeShow.endTime = moment(row.end_time, 'YYYYMMDDHHmmss').format('YYYY-MM-DD HH:mm:ss')
						this.timeShow.rowData = row
						this.timeShow.reupload = reupload

				},
				confirmUpload() {
						this.$warning('下载' + this.timeShow.startTime + '到' + this.timeShow.endTime + '的视频')
						this.downloadQueue(this.timeShow);
						this.timeShow.show = false
				},

				//创建下载任务
				async downloadQueue(row) {
						row.rowData.loading = true;
						try {
								let res = await $.ajax({
										url: API_URL,
										type: 'post',
										data: JSON.stringify({
												cmd: 'upload',
												terminal_no: this.currentVehicle.code,
												channel_no: row.rowData.channel_no,
												start_time: moment(row.startTime, 'YYYY-MM-DD HH:mm:ss').format(
														'YYYYMMDDHHmmss'),
												end_time: moment(row.endTime, 'YYYY-MM-DD HH:mm:ss').format(
														'YYYYMMDDHHmmss'),
												media_type: row.rowData.media_type,
												event_code: row.rowData.event_code,
												stream_type: row.rowData.stream_type,
												storage_type: row.rowData.storage_type,
												ftp: this.ftpMode,
												reupload: row.reupload
										}),
										headers: {
												Authorization: 'token ' + this.token,
										},
								});
								row.rowData.status = res.code
								if (res.code === 4) {
										row.rowData.url = res.data
										this.download(this.toFileName(row.rowData), res.data)
								} else if (res.code != 0) {
										this.$error(this.$ct('messageInfo[4]'))
								} else {
										Object.assign(row.rowData, {
												status: 0,
												progress: 0,
												upload_start: row.startTime,
												upload_end: row.endTime,
												id: res.data,
												ftp: this.ftpMode,
										});

										let uploadedObj = this.downloadTable.data.find(item => item.id == res.data)
										//避免重复推入
										if (uploadedObj) {
												Object.assign(uploadedObj, {
														...JSON.parse(JSON.stringify(row.rowData)),
														loading: false,
												})
										} else {
												let downObj = JSON.parse(JSON.stringify(row.rowData))
												this.downloadTable.data.push(Object.assign(downObj, {
														start_time: moment(row.startTime, 'YYYY-MM-DD HH:mm:ss').format(
																'YYYYMMDDHHmmss'),
														end_time: moment(row.endTime, 'YYYY-MM-DD HH:mm:ss').format(
																'YYYYMMDDHHmmss'),
														loading: false,

												}))
												// this.downloadTable.data.push({
												//     ...JSON.parse(JSON.stringify(Object.assign(row.rowData,{
												//         start_time: moment(row.startTime, 'YYYY-MM-DD HH:mm:ss').format('YYYYMMDDHHmmss'),
												//         end_time: moment(row.endTime, 'YYYY-MM-DD HH:mm:ss').format('YYYYMMDDHHmmss'),
												//     }))),
												//     loading: false,
												// });
										}
										this.downloadLog(row.rowData.channel_no, row.startTime, row.endTime);
										this.$success(this.$ct('messageInfo[5]'));
										clearInterval(this.timeInter)

										this.timeInter = null

										this.intervalReflash()
										// videoWS.subscribeProgressInfo([res.data])
								}
						} finally {
								row.rowData.loading = false;
								this.$forceUpdate()

						}
				},

				async downloadLog(chn, startTime, endTime) {
						this.$api.downloadVideoLog({
								vehicleId: this.currentVehicle.vehicle_id,
								aisle: chn,
								videoStart: startTime,
								videoEnd: endTime,
						})
				},
				async search() {
						this.activeSlider = 0
						this.allStop();
						await this.getRecordList();
				},
				onProgressHandle(data) {
						const rows = [];
						const recordItem = this.recordTable.data.find(item => item.id === data.id);
						recordItem && rows.push(recordItem);
						const downloadItem = this.downloadTable.data.find(item => item.id === data.id);
						downloadItem && rows.push(downloadItem);
						if (!rows.length) {
								clearInterval(this.timeInter)
								this.timeInter = null
								return
						}
						rows.length && rows.forEach(row => {
								Object.assign(row, {
										status: 0,

										progress: Math.min(
												100,
												row.size ? (
														row.ftp ? Number((data.size / row.size * 100).toFixed(1)) :
																Number(data.percent.toFixed(1))
												) : data.percent ? Number(data.percent.toFixed(1)) : 0
										),
										currentSize: data.size,
								})

						})
				},
				onStreamSavedHandle(data) {
						const rows = [];
						const recordItem = this.recordTable.data.find(item => item.id === data.id);
						recordItem && rows.push(recordItem);
						const downloadItem = this.downloadTable.data.find(item => item.id === data.id);
						downloadItem && rows.push(downloadItem);
						rows.length && rows.forEach(row => {
								Object.assign(row, {
										status: 4,
										url: data.url,
										loading: false,
										currentSize: data.size,
								})
						});
				},
				async onStreamStopHandle(data) {
						const rows = [];
						const recordItem = this.recordTable.data.find(item => item.id === data.id);
						recordItem && rows.push(recordItem);
						const downloadItem = this.downloadTable.data.find(item => item.id === data.id);
						downloadItem && rows.push(downloadItem);
						if (rows.length) {
								let temp = rows[rows.length - 1];
								//steamStop事件有可能在uploadResult事件后
								if (temp.status === 4) return;
								//steamStop事件发生在uploadResult前并且1000ms内status没发生变化 则说明设备意外中断上传，需要显示上传失败
								await this.$utils.sleep(1000);
								if (temp.status === 4) return;
								rows.length && rows.forEach(row => {
										Object.assign(row, {
												status: 1,
												loading: false,
												currentSize: data.size,
										})
								});
						}
				},
				async getDownloadList(params) {
						this.downloadTable.loading = true
						let query = Object.assign({
										cmd: 'records'
								},
								params,
						)
						if (this.channelNoList.length == 1) {
								query.channel_no = this.channelNoList.length[0]
						}
						try {
								let res = await $.ajax({
										url: API_URL,
										type: 'post',
										data: JSON.stringify(query),
										headers: {
												Authorization: 'token ' + this.token,
										},
								});
								// const downloadList = [];
								// this.recordTable.data.forEach((item, index, array) => {
								//     let temp = res.data.find((row) => {
								//         return item.channel_no === row.channel_no &&
								//             item.start_time === row.start_time &&
								//             item.end_time === row.end_time;
								//     })
								//     Object.assign(item, {
								//         status: temp ? temp.status : 3,
								//         url: temp ? temp.url : '',
								//         progress: -1,
								//         id: temp ? temp.id : undefined,
								//         loading: false,
								//         ftp: temp && !!temp.from,
								//     });
								//     if (temp) {
								//         downloadList.push(JSON.parse(JSON.stringify(item)))
								//     }
								// });
								if(!res || res.code != 0){
									this.downloadTable.loading = false
									clearInterval(this.timeInter)
									this.timeInter = null
									return
								}
								if(res.data.length){
									res.data.forEach(item=>{
									let byteArr = parseInt(item.event_code, 16).toString(2).split('').reverse()
									let ind = byteArr.findIndex(it => it == 1)
									if (ind != -1) {
										let alarmIndex = this.dictionary['mini_media_event_code'].findIndex(it=>it.value == ind)
										if(alarmIndex != -1){
											item.alarmType = this.dictionary['mini_media_event_code'][alarmIndex].name //先转10进制，在转2进制

										}else {
											item.alarmType = ''
										}

									} else {
											item.alarmType = ''
									}
									})
									this.downloadTable.data = res.data
										.filter(item => this.channelNoList.includes(item.channel_no))
										.filter(item => {
												//包含null,则是全选
												if (this.query.event_code.includes(null)) {
														return true
												} else {
														let codeIndex = parseInt(item.event_code, 16).toString(2).split('').reverse().findIndex(it => it == 1)
														if (codeIndex == -1) {
															return false
														} else {
															return this.query.event_code.includes(codeIndex)
														}
												}
										})
										.filter(item => {
												if (this.query.media_type == 3) {
														return item.media_type == 0 || item.media_type == 2
												} else {
														return item.media_type == this.query.media_type
												}
										})
										.filter(item => {
												if (this.query.stream_type == 0) {
														return true
												} else {
														return item.stream_type == this.query.stream_type
												}
										})
										.map(item => {
												return {
														...item,
														status: item ? item.status : 3,
														url: item ? item.url : '',
														progress: -1,
														id: item ? item.id : undefined,
														loading: false,
														ftp: item.from,
												}
										});
									if (this.$refs["download"]) {
											this.$nextTick(() => {
													this.$refs["download"].doLayout();
											})
									}
								}


								this.downloadTable.loading = false
								//重新订阅进度信息
								clearInterval(this.timeInter)

								this.timeInter = null
								this.intervalReflash()
								// videoWS.subscribeProgressInfo(res.data.filter(item => item.status !== 4).map(item => item.id));
						} catch (e) {
								console.log(e);
								this.downloadTable.loading = false

								throw new Error(this.$ct('messageInfo[3]'))
						}
				},
				async getDownListStatus() {
						let ids = []
						ids = this.downloadTable.data.filter(item => item.status == 0).map(item => item.id)
						if (!ids.length) return
						let res = await $.ajax({
								type: "get",
								traditional: true,
								url: API_URL,
								data: {
										ids: ids
								},
								dataType: "json",
						})
						// let res = await $.get(API_URL_STATUS, {ids:["0341fb92-38f7-48b4-b9be-c7b02192e5f9"]})
						if (res.code != 0) {
								this.$error(res.message || '状态查询出错！')
								return
						}
						res.data.forEach(data => {
								const rows = [];
								const recordItem = this.recordTable.data.find(item => item.id === data.id);
								recordItem && rows.push(recordItem);
								const downloadItem = this.downloadTable.data.find(item => item.id === data.id);
								downloadItem && rows.push(downloadItem);
								rows.length && rows.forEach(row => {
										switch (data.status) {
												case 0: //正在上传
														Object.assign(row, {
																status: 0,

																progress: Math.min(
																		100,
																		row.size ? (
																				row.ftp ? Number((data.size / row.size * 100)
																								.toFixed(1)) :
																						Number(data.percent.toFixed(1))
																		) : data.percent ? Number(data.percent.toFixed(
																				1)) : 0
																),
																currentSize: data.size,
														})
														break;
												case 1: //失败
														Object.assign(row, {
																status: 1,
																loading: false,
																currentSize: data.size,
														})
														break;
												case 4: //上传完成
														Object.assign(row, {
																status: 4,
																url: data.url,
																loading: false,
																currentSize: data.size,
														})
														break;
										}


								})
								this.$forceUpdate()

						})


				},
				//定时器，定时刷新
				intervalReflash() {
						this.timeInter = setInterval(() => {
								this.getDownListStatus()
						}, 3500)
				},
				positionFilter(value, row) {

						return row.channel_no === value;
				},

				transforAlarmType(data) {
						// 数组转10进制
						if (data.includes(null)) return '0000000000000000'
						if (!data.length) return
						let str = []
						for (let i = 0; i < 64; i++) {
								let value = data.includes(i) ? '1' : '0'
								str.push(value)
						}
						// 二进制转10进制 先转回来! 然后数格子
						let result = str.reverse().join('')
						return parseInt(result, 2).toString(16).padStart(16, '0')
				},
				async getRecordListAll() {
						let params = {
								terminal_no: this.currentVehicle.code,
								// channel_no: 0,//0 是所有通道
								start_time: moment(this.query.startTime).format('YYYYMMDDHHmmss'),
								end_time: moment(this.query.endTime).format('YYYYMMDDHHmmss'),
						}
						this.getDownloadList(params);
						/** 根据对应通道号 并发获取通道数据  ↑*/
						/** 根据对应通道号 轮询获取通道数据  ↓*/
						let event_code = this.transforAlarmType(this.query.event_code)
						let result = []
						// this.completedChn = 0;

						// for (let chn = 0; chn < this.channelNoList.length; chn++) {
						clearTimeout(this.timeOut)
						let ajax = $.ajax({
								url: API_URL,
								type: 'post',
								data: JSON.stringify(Object.assign({
												cmd: 'index'
										},
										params, {
												media_type: this.query.media_type,
												stream_type: this.query.stream_type,
												storage_type: this.query.storage_type,
												event_code,
										}, {
												channel_no: this.channelNoList.length == 1 ? this.channelNoList[0] : 0
										},
								)),
								headers: {
										Authorization: 'token ' + this.token,
								},
						});

						this.currentRecordAjax = ajax;
						this.timeOut = setTimeout(() => {
								this.$error('获取超时！')
								this.abortRecordAjax()
								this.recordTable.loading = false;
						}, 60 * 1000)
						let res = await ajax
						if (res.code != 0) {
								this.$error(res.message || '超时')
								clearTimeout(this.timeOut)
								this.currentRecordAjax = null;

								return
						}
						if (res.code == 0) {
								this.$api.storeMiniVideoList({
										request: {
												cmd: 'index',
												terminal_no: this.currentVehicle.code,
												start_time: moment(this.query.startTime).format('YYYYMMDDHHmmss'),
												end_time: moment(this.query.endTime).format('YYYYMMDDHHmmss'),
												channel_no: this.channelNoList.length == 1 ? this.channelNoList[0] : 0
										},
										data: res.data
								})
						}

						result = res.data
						clearTimeout(this.timeOut)
						// this.completedChn = this.channelNoList[chn];
						this.currentRecordAjax = null;
						// }

						// result = result.reduce(function (acc, cur) {
						//     if (cur.data) {
						//         return acc.concat(cur.data);
						//     } else
						//         return acc.concat([])
						// }, [])
						// console.log(result);
						/** 根据对应通道号 轮询获取通道数据  ↑*/
						//并且根据通道列表过滤数据
						// console.log(result);
						this.recordTable.data = result.map(item => {
								let byteArr = parseInt(item.event_code, 16).toString(2).split('').reverse()
								let ind = byteArr.findIndex(it => it == 1)
								if (ind != -1) {
										let alarmIndex = this.dictionary['mini_media_event_code'].findIndex(it=>it.value == ind)
										if(alarmIndex != -1){
											item.alarmType = this.dictionary['mini_media_event_code'][alarmIndex].name //先转10进制，在转2进制

										}else {
											item.alarmType = ''
										}

								} else {
										item.alarmType = ''
								}
								return Object.assign(item, {
										status: 3,
										url: '',
										progress: -1,
										currentSize: -1,
										fileName: '',
										loading: false,
								})
						});
						// console.log(this.recordTable.data);
						//分析结果得出 通道数量
						// let chn = [...new Set(this.recordTable.data.map(item => item.channel_no))].sort((a, b) => a - b);
						// //裁剪
						// console.log(chn);
						// console.log(this.currentVehicle);
						// this.chnList = chn.slice(0, this.currentVehicle.chnNo);
						this.chnList = this.channelNoList
						this.recordTable.data = this.recordTable.data
								.filter(item => this.chnList.includes(item.channel_no))
								.sort((a, b) => parseInt(a.start_time) - parseInt(b.start_time));
						if (this.$refs["record"]) {
								this.$nextTick(() => {
										this.$refs["record"].doLayout();
								})
						}

						// console.log(this.chnList);
						// } else {
						//     throw new Error(this.$ct('messageInfo[2]'))
						// }
						//等待ws连接  查询下载列表相关数据
				},
				abortRecordAjax() {
						if (!this.currentRecordAjax) return;
						if (this.currentRecordAjax instanceof Array) {
								this.currentRecordAjax.forEach(ajax => ajax.abort());
						} else {
								this.currentRecordAjax.abort();
						}
				},
				async getRecordList() {
						try {
								this.abortRecordAjax();
								if (!this.currentVehicle.code) {
										this.$warning(this.$ct('messageInfo[1]'))
										return;
								}
								this.setAxisData([]);
								//查询所有通道的录像列表
								this.plateNoTemp = this.currentVehicle.plate_no;
								this.recordTable.data = [];
								this.recordTable.loading = true;
								//获取录像列表
								await this.getRecordListAll();
								//生成时间轴数据
								this.setAxisData(this.recordTable.data);
								// this.$nextTick(()=>{
								//     this.$refs["record"].doLayout();
								// })
								this.recordTable.loading = false;
						} catch (e) {
								if (e && e.readyState === 0) return; //ajax abort导致的出错就不提示
								this.$error(e)
								this.recordTable.loading = false;
						}
				},
				async cancelDownload(row) {
						let result = await $.ajax({
								url: API_URL,
								type: 'post',
								data: JSON.stringify({
										cmd: "cancelUpload",
										id: row.id,
								}),
								headers: {
										Authorization: 'token ' + this.token,
								},
						});

						if (result.code === 0) {
								let downLoadObj = this.downloadTable.data.find(it => it.id == row.id)
								let recordObj = this.recordTable.data.find(it => it.id == row.id)
								if (downLoadObj) {
										Object.assign(downLoadObj, {
												status: 3,
												url: '',
												progress: -1,
												fileName: '',
												loading: false,
												id: null,
										})
										this.$forceUpdate()
								}
								if (recordObj) {
										Object.assign(recordObj, {
												status: 3,
												url: '',
												progress: -1,
												fileName: '',
												loading: false,
												id: null,
										})
										this.$forceUpdate()
								}


								// let ids = this.videoTable.data.filter(item => item.status == 0).map(item => item.id)
								// if(!ids.length){
								//     clearInterval(this.timeInter)
								//     this.timeInter = null
								// }
						} else {
								this.$error('取消下载失败');
						}
				},
				setAxisData(tableData) {
						const startOfDay = moment(this.query.startTime).startOf('day');
						const timeData = new Array(this.chnList.length)
						// console.log(timeData);
						tableData.forEach(item => {
								const startTime = (moment(item.start_time, 'YYYYMMDDHHmmss').diff(startOfDay)) / 1000;
								const endTime = (moment(item.end_time, 'YYYYMMDDHHmmss').diff(startOfDay)) / 1000;
								const chn = item.channel_no - 1;
								if (timeData[chn]) {
										// const lastRange = timeData[chn][timeData[chn].length - 1];
										// if (startTime >= lastRange[1]) {
										timeData[chn].push([startTime, endTime]);
										// } else {
										//     //相差0s以内的默认是连续视屏
										//     lastRange[1] = endTime;
										// }
								} else {
										timeData[chn] = [
												[startTime, endTime]
										];
								}
						})
						//将时间轴约束在00:00 到 24:00
						timeData.forEach(chnData => {
								if (chnData[0][0] < 0) chnData[0][0] = 0;
								if (chnData[chnData.length - 1][1] > 86400) chnData[chnData.length - 1][1] = 86400;
						})
						// console.log(timeData);
						this.timeData = timeData;
				},
				async cmdAdapter(data) {
						if (data.cmd == 'play') {
								let chnObj = this.downloadTable.data.find(item => item.channel_no == data.chn + 1 && !item.ftp &&
										item.status == 0)
								let chnObjReload = this.recordTable.data.find(item => item.channel_no == data.chn + 1 && !item
										.ftp && item.status == 0)

								if (chnObj || chnObjReload) {
										let checkRes = await this.$confirm('当前有同一通道的视频正在上传，如点击播放，视频上传会失败，请确认是否继续播放？', '提示', {
												confirmButtonText: '确定',
												cancelButtonText: '取消',
												type: 'warning'
										})
										if (!checkRes) return
								}
						}
						if (data.cmd == 'playAll') {
								let noList = []
								data.rangeList.forEach(async (item, index) => {
										let chnObj = this.downloadTable.data.find(it => it.channel_no == index + 1 && !it
												.ftp && it.status == 0)
										let chnObjReload = this.recordTable.data.find(item => item.channel_no == data.chn +
												1 && !item.ftp && item.status == 0)

										if (chnObj) {
												noList.push(chnObj)
												return
										}
								})
								if (noList.length) {
										let checkRes = await this.$confirm('当前有同一通道的视频正在上传，如点击播放，视频上传会失败，请确认是否继续播放？', '提示', {
												confirmButtonText: '确定',
												cancelButtonText: '取消',
												type: 'warning'
										})
										if (!checkRes) return
								}

						}

						try {
								switch (data.cmd) {
										case 'playAll':
												const {
														timeStart, timeEnd
												} = data;

												let videoList = data.rangeList
														.map((item, index) => ({
																chn: index,
																range: item[0]
														}))
														.filter(item => item.range && !!item.range.length)
														.map(item => ({
																chn: item.chn + 1,
																startTime: moment(this.query.startTime).startOf('day')
																		.add(timeStart, 'seconds').format('YYYYMMDDHHmmss'),
																endTime: moment(this.query.startTime).startOf('day')
																		.add(timeEnd, 'seconds').format('YYYYMMDDHHmmss'),
														}))
												switch (true) {
														case videoList.length > 4 && videoList.length > 9:
																this.chn9Mode = false;
																this.chn16Mode = true;
																this.chn4Mode = false;
																break;
														case videoList.length > 4 && videoList.length <= 9:
																this.chn9Mode = true;
																this.chn16Mode = false;
																this.chn4Mode = false;
																break;
														default:
																if (!this.chn9Mode && !this.chn16Mode) {
																		this.chn4Mode = true;
																		this.chn9Mode = false;
																		this.chn16Mode = false;

																}
												}
												this.videoPlayList = videoList;
												await this.$nextTick();
												this.allPlay();
												break;
										case 'play': {
												const {
														chn,
														rangeList,
														timeStart,
														timeEnd
												} = data;

												if (rangeList.length === 0) {
														this.$warning(this.$ct('messageInfo.7')); // '该时间段内没有可用的录像！',
														return;
												}
												// if (!data.firstRange && rangeList.length >= 2) {
												//     this.$warning(this.$ct('messageInfo.8')) //不支持多段视频
												// }
												this.$refs['videoAxis'].selectRange(rangeList[0][0], rangeList[0][1]);
												const startTime = moment(this.query.startTime).startOf('day')
														.add(timeStart, 'seconds').format('YYYYMMDDHHmmss');
												const endTime = moment(this.query.startTime).startOf('day')
														.add(timeEnd, 'seconds').format('YYYYMMDDHHmmss');
												this.play(chn, startTime, endTime);
												break;
										}
										// case 'download': {
										//     this.timeShow.show = true
										//     const {chn, rangeList, timeStart,timeEnd} = data;

										//     if (rangeList.length === 0) {
										//         this.$warning(this.$ct('messageInfo.7'));
										//         return;
										//     }

										//     this.$refs['videoAxis'].selectRange(rangeList[0][0], rangeList[0][1]);
										//     const startTime = moment(this.query.startTime).startOf('day')
										//         .add(timeStart, 'seconds').format('YYYY-MM-DD HH:mm:ss');
										//     const endTime = moment(this.query.startTime).startOf('day')
										//         .add(timeEnd, 'seconds').format('YYYY-MM-DD HH:mm:ss');
										//     //弹框中的默认时间为拉鼠标选中的时间
										//     this.timeShow.startTime = startTime
										//     this.timeShow.endTime = endTime
										//     this.timeShow.chn = chn


										//     // await this.downloadSelect(chn, startTime, endTime);

										//     break;
										// }

								}
						} catch (e) {
								this.$error(e);
						}
				},

				onStop() {
						this.allStop();
				}
		},
		async created() {
				// let ws = videoWS.getInstance();
				// ws.on('progress', this.onProgressHandle);
				// ws.on('streamSave', this.onStreamSavedHandle);
				// ws.on('streamEnd', this.onStreamStopHandle);
				// ws.init();
				// let tryCount = 5;//剩余重试次数
				// const fn = async () => {
				//     if (!tryCount) return;
				//     await this.$utils.sleep(500);
				//     ws.init();
				//     console.log('ws重新链接');
				//     tryCount--;
				// }
				// ws.on('close', fn)
				// this.ws = ws;
		},
		beforeDestroy() {
				clearInterval(this.timeInter)

				this.timeInter = null
				// this.abortRecordAjax();
				// this.ws?.off('progress', this.onProgressHandle);
				// this.ws?.off('streamSave', this.onStreamSavedHandle);
				// this.ws?.off('streamEnd', this.onStreamStopHandle);
				// this.ws?.close();
		}
}
</script>

<style scoped lang="scss">
#playback1078 {
		.search-wrap {
				overflow: hidden;
				padding: 10px;

				.query-item {
						display: flex;
						align-items: center;
						padding: 5px;

						.query-more {
								width: 100%;
								text-align: center;
								font-size: 12px;
								line-height: 1;
								color: var(--color-primary);
								cursor: pointer;

						}

						label {
								font-size: 12px;
								line-height: 32px;
								width: 65px;
						}

						.query-input {
								font-size: 12px;
								width: calc(100% - 60px);
						}
				}
		}

		/deep/ .el-checkbox is-disabled {
				margin-right: 0px;
		}

		.video-right-wrap {
				position: relative;
				display: flex;
				flex-direction: column;
				height: 100%;

				&[class*="multiple-"] {
						.video-map-wrap .video-wrap {
								width: 100%;
								display: flex;
								flex-wrap: wrap;
								justify-content: space-between;
								padding: 1px 0 0 1px;

								.video-player {
										border: 1px solid transparent;
										margin-left: -1px;
										margin-top: -1px;
								}
						}
				}

				&.multiple-4 .video-map-wrap .video-wrap .video-player {
						height: calc(50% + 1px);
						width: calc(50% + 1px);
				}

				&.multiple-9 .video-map-wrap .video-wrap .video-player {
						height: calc(33.333333% + 1px);
						width: calc(33.333333% + 1px);
				}

				&.multiple-16 .video-map-wrap .video-wrap .video-player {
						height: calc(25% + 1px);
						width: calc(25% + 1px);
				}

				.video-list {
						position: relative;
						height: 257px;
						z-index: 0;
				}

				.video-map-wrap {
						$video-height: "100vh - 96px - 257px"; //视屏区域的高度
						position: relative;
						height: calc(#{$video-height});
						width: 100%;
						z-index: 1;

						.video-wrap {
								position: relative;
								flex-shrink: 0;
								float: left;
								height: 100%;
								width: 100%;

								// width: calc((#{$video-height}) * 16 / 9);
								.process-all {
										height: 12px;
										float: left;
										width: calc(100% - 17px);
										margin: 6px 0px 0px 8px;
								}

								.video-player {
										position: relative;
										height: 100%;
										width: 100%;
										float: left;
										z-index: 1;

										&.selected {
												z-index: 2;
												border-color: var(--color-primary);
										}

										&.maximize {
												position: absolute;
												top: 1px;
												left: 1px;
												z-index: 3;
												width: 100% !important;
												height: 100% !important;
										}
								}
						}

						.map-wrap {
								overflow: hidden;
								position: relative;
								height: 100%;
								border-radius: 0 8px 8px 0;
						}

						.fixed-button {
								position: absolute;
								right: 0;
								top: 100%;
								display: flex;
								align-items: center;

								> i {
										top: 0;
										font-size: 14px;
										width: 29px;
										height: 29px;
										line-height: 29px;
										text-align: center;
										border-bottom: 1px solid transparent;

										&:hover {
												border-bottom-color: var(--border-color-lighter);
												background: var(--background-color-light);
										}
								}
						}
				}

				.video-control {
						display: flex;
						justify-content: center;
						align-items: center;

						i[class^="el-icon-"] {
								font-size: 14px;
						}

						i {
								cursor: pointer;
								margin-left: 5px;
						}
				}
		}
}
</style>

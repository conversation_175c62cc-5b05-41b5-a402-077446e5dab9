<template>
		<div
				class="realtime-player pony-player"
				v-context-menu="contextMenuOptions"
				@mousemove="controlShow = true"
				@mouseleave="controlShow = false"
				ref="playerBox"
		>
				<canvas :id="'canvas' + chnN" style="display:none"></canvas>
				<div class="pony-player__video-wrap wrap " :id="'warp' + chnN" ref="flag">
						<div
								class="video-title"
								:class="settingValue.subtitle"
								v-show="label && sourceParams"
								:style="{ opacity: labelAlwaysShow || controlShow ? 1 : 0 }"
						>
								<div v-if="settingValue.subtitleList.includes('车牌号')">
										{{label}}
								</div>
								<div v-if="settingValue.subtitleList.includes('地点')">
										{{subtitle.place}}
								</div>
								<div v-if="settingValue.subtitleList.includes('时间')">
										{{subtitle.time}}
								</div>
								<div v-if="settingValue.subtitleList.includes('速度')">
										{{subtitle.speed}}
								</div>
								<div v-if="settingValue.subtitleList.includes('经纬度')">
										{{subtitle.latlng}}
								</div>
						</div>
						<div class="pony-player__log-wrap" v-if="showLog">
								<i class="pony-iconv2 pony-guanbi pointer" @click="showLog = false"></i>
						</div>
						<FlvVideo
								class="video"
								ref="player"
								:showDecodeInfo="showDecodeInfo"
								@decodeLog="log"
								@ready="_onPlayerMounted"
								@speedChange="_speedChange"
								@play="() => (this.isPlaying = true)"
								@pause="() => (this.isPlaying = false)"
								@ended="() => (this.isPlaying = false)"
						>
						</FlvVideo>
				</div>
				<div class="pony-player__main-wrap wrap">
						<!--控制条-->
						<div
								class="pony-player__controller"
								v-if="loaded && player && player.videoElm"
								:style="{ opacity: controlShow ? 1 : 0 }"
						>
								<span style="cursor: pointer;" @click="changeLiveStream" v-if="boxComponents.includes('stream')">
										{{ streamType ? "子码流" : "主码流" }}
								</span>
								<i :class="['pony-iconv2',pauseOrStartStatus?'pony-sanjiaoxing':'pony-zanting']"
								   :title="pauseOrStartStatus?'暂停':'播放'"
								   @click="pausedOrStart"
								></i>

<!--								<i class="pony-iconv2 pony-shuaxin" v-if="loaded && boxComponents.includes('refresh')"-->
<!--								   @click="reload"></i>-->
								<i

										class="pony-iconv2 pony-tingzhi"
										@click="stop"
								></i>
								<div class="flex-grow"></div>
								<span class="speed" v-if="boxComponents.includes('speed')">{{ flow }}</span>
								<TalkBack
										v-if="boxComponents.includes('recording')"
										:terminalId="terminalCode"
										:chn="chn"
										:inline="true"
										ref="talkBack"
										expandBackground="rgba(255,255,255,0.05)"
								>
										<i
												slot="icon"
												slot-scope="{ allowClick, title }"
												:class="['pony-iconv2 pony-yuyin', { 'is-disabled': !allowClick }]"
												style="font-size: 16px"
												:title="`${$ct('talkBack')}：` + title"
										></i>
								</TalkBack>
								<div :class="['collapse-item',{'is-disabled':!loaded}]" v-if="boxComponents.includes('sound')">
										<i :class="['pony-iconv2 ',{'is-disabled':!loaded || !hasAudio},player.videoElm.muted?'pony-jingyin':'pony-shengyin']"
										   @click="toggleSound" :title="volumeHint"></i>
										<div class="pane">
												<el-slider
														v-model="volumeProcess"
														vertical
														@change="changeVolume"
														height="100px">
												</el-slider>
										</div>
								</div>
								<i
										v-if="boxComponents.includes('tts')"
										class="pony-iconv2 pony-ziti"
										:title="$ct('tts')"
										@click="ttsSend"
								></i>
								<i
										v-if="boxComponents.includes('photo')"
										:class="['pony-iconv2 pony-paizhao', { 'is-disabled': !loaded }]"
										:title="$ct('screenShot')"
										@click="screenshot()"
								></i>
								<i class="videoTime" v-show="recordStatus">{{ videoTime }}</i>
								<i
										v-if="boxComponents.includes('video')"
										class="pony-iconv2 pony-shipin"
										:title="recordStatus ? '结束录制' : '开始录制'"
										@click="startRecord"
								></i>
								<i
										class="pony-iconv2"
										:class="[isFullScreen ? 'pony-suoxiao' : 'pony-quanping']"
										@click="toggleChange"
										v-if="enableFullScreen && boxComponents.includes('full')"
										:title="(isFullScreen ? $ct('exit') : '') + $ct('fullscreen')"
								></i>
								<el-popover
										v-if="boxComponents.includes('setting')"
										placement="top"
										width="320"
										trigger="click">
										<div class="block">
												<span class="demonstration">亮度</span>
												<el-slider v-model="settingValue.brightness" :min="0" :max="1" :step="0.1" :format-tooltip="formatter"></el-slider>
										</div>
										<div class="block">
												<span class="demonstration">对比度</span>
												<el-slider v-model="settingValue.contrast" :min="0" :max="1" :step="0.1" :format-tooltip="formatter"></el-slider>
										</div>
										<div class="block">
												<span class="demonstration">饱和度</span>
												<el-slider v-model="settingValue.saturate" :min="0" :max="1" :step="0.1" :format-tooltip="formatter"></el-slider>
										</div>
										<div class="block">
												<span class="demonstration">色度</span>
												<el-slider v-model="settingValue.rotate" :min="0" :max="360" :step="1" :format-tooltip="formatterRotate"></el-slider>
										</div>
										<div class="block">
												<div class="demonstration">字幕设置</div>
												<el-radio v-model="settingValue.subtitle" label="leftTop">左上</el-radio>
												<el-radio v-model="settingValue.subtitle" label="leftBottom">左下</el-radio>
												<el-radio v-model="settingValue.subtitle" label="rightTop">右上</el-radio>
												<el-radio v-model="settingValue.subtitle" label="rightBottom">右下</el-radio>
												<el-checkbox-group v-model="settingValue.subtitleList">
														<el-checkbox label="时间"></el-checkbox>
														<el-checkbox label="地点"></el-checkbox>
														<el-checkbox label="车牌号"></el-checkbox>
														<el-checkbox label="速度"></el-checkbox>
														<el-checkbox label="经纬度"></el-checkbox>
												</el-checkbox-group>
										</div>
										<div class="block">
												<div class="demonstration" style="line-height:26px">音视频设置</div>
												<el-button @click="videoConfigChange(1)" type="primary">关闭音频</el-button>
												<el-button @click="videoConfigChange(2)" type="primary">关闭视频</el-button>
										</div>
										<i class="pony-iconv2 pony-shezhi" slot="reference"></i>
								</el-popover>
						</div>
						<div class="pony-player__mask wrap" v-else @click="loadSource">
								<i
										class="real-video"
										style="cursor: not-allowed;"
										:title="$ct('notSupportVideo')"
										v-if="disabled"
								>点击播放实时视频
								</i>
								<i
										v-else-if="loading"
										class="el-icon-loading"
										style="font-size:40px"
								></i>
								<i
										v-else
										class="real-video"
										:title="$ct('play')"
								>{{ title }}</i>
						</div>
				</div>
		</div>
</template>

<script>
import FlvVideo from "./FlvVideoStandard";
import TalkBack from "@/view/videoPlay/component/TalkBack/TalkBack";

import strictEnd from "../mxin/strictEnd";
import fullscreen from "../mxin/fullscreen";
import log from "../mxin/log";
import BstandardUtil from "@/util/bstandard";
import {mapState} from "vuex";
import {GPS} from "../../../../../service/wsService";
import moment from "moment";

const API_URL = window.PONY.media.apiUrl;
/**
 * @Author: yezy
 * @Email: <EMAIL>
 * @Date: 2020/7/22 16:15
 * @LastEditors: xieyj
 * @LastEditTime: 2021/11/15 16:15
 * @Description:
 */
export default {
		name: "realtimePlayer",
		_i18Name: "videoPlayerPony",
		mixins: [strictEnd, fullscreen, log],
		components: {TalkBack, FlvVideo},
		props: {
				vehicleId: {
						type: [String, Number],
				},
				terminalCode: {
						type: String,
				},
				chnN: {
						type: [Number, Object],
						default: 0,
				},
				symbol: {
						type: String,
				},
				label: {
						type: String,
				},
				autoPlay: {
						type: Boolean,
						default: false,
				},
				labelAlwaysShow: {
						type: Boolean,
						default: false,
				},
				showClose: {
						type: Boolean,
						default: false,
				},
				enableFullScreen: {
						type: Boolean,
						default: false,
				},
				disabled: {
						type: Boolean,
						default: false,
				},
				boxComponents: {
						type: [String, Array],
						default: function () {
								return ['stream', 'refresh', 'stop', 'speed', 'recording', 'sound', 'tts', 'photo', 'video', 'full', 'setting']
						}
				},
				playWays:{
						type: Number,
						default: 0,
				}
		},
		data() {
				return {
						videoConfig:{
							video:false,
							voice:false
						},
						//录像的状态
						recordStatus: false,
						startTime: null,
						frameId: null,
						stream: null,
						recorder: null,
						canvasContext: null,
						videoElement: null,
						canvasElement: null,
						chunks: [],
						videoTime: "00:00:00",
						timeRecord: null,
						pauseOrStartStatus:false,//视频是否是暂停状态
						loaded: false, //当前是否有请求的加载的流
						//相关状态，不一定与内部video标签一致
						isPlaying: false,
						// 速度
						currentSpeed: 0,
						// 流量
						flow: 0,
						chn: this.chnN,
						player: null, //指向 $refs['player']
						sourceLoading: false,
						stopLoading: false,
						controlShow: false,
						loadAjax: null,
						url: "",
						hasAudio: false,
						//上一次的参数
						lastParams: {
								url: undefined,
						},
						timeOut: null,
						showLog: false,
						showDecodeInfo: false,
						contextMenuOptions: [
								{
										label: "显示日志",
										handler: () => {
												this.showLog = !this.showLog;
										},
								},
								{
										label: "显示解码信息",
										handler: () => {
												this.showDecodeInfo = !this.showDecodeInfo;
										},
								},
						],
						controlTimeout: null,
						//打断加载行为
						interruptLoadSourceHandle: null,

						streamType: 1, // 0 主码流 1 子码流
						title: '点击播放实时视频',
						settingValue: {
								brightness: 1,
								contrast: 1,
								saturate: 1,
								rotate: 0,
								subtitle: 'leftTop',
								subtitleList: ['时间','地点','车牌号','速度', '经纬度'],
						},
						volumeProcess: 0, // 音量
						source:{ // ws
								subscriptList:[],
								handlList:[],
						},
						subtitle: {
								time: '', // 时间
								place: '', // 地点
								speed: ''// 速度
						},
						timeDelay: null
				};
		},
		computed: {
				...mapState("vehicle", ["basicByVehicleId"]),
				...mapState("auth", ["token"]),


				sourceParams: function () {
						if (this.vehicleId && this.terminalCode && this.chn !== undefined) {
								return `${this.vehicleId}-${this.terminalCode}-${this.chn}`;
						} else {
								return undefined;
						}
				},
				loading: function () {
						return this.sourceLoading || this.stopLoading;
				},
				volumeHint: function () {
						if (this.loaded && !this.hasAudio) {
								return this.$ct("notSupportAudio");
						} else {
								return "";
						}
				},
		},
		watch: {

				// 对比度，色度，饱和度，亮度
				settingValue: {
						handler: function (val) {
								let box = this.$refs.player.$refs.video
								let video = box.getElementsByTagName('video')[0]
								video.style.filter = `brightness(${val.brightness}) contrast(${val.contrast}) saturate(${val.saturate}) hue-rotate(${val.rotate}deg)`
						},deep: true
				},
				showLog: function (val) {
						this.contextMenuOptions[0].label = `${val ? "隐藏" : "显示"}日志`;
				},
				chnN: function (val) {
						this.chn = val;
				},
				showDecodeInfo: function (val) {
						this.contextMenuOptions[1].label = `${val ? "隐藏" : "显示"}解码信息`;
				},
				sourceParams: {
						handler: function (val, oldV) {
								// if (oldV) {
								//     let [vehicleId, terminalCode, chn] = oldV.split('-');
								//     this.lastParams.terminalCode = terminalCode;
								//     this.lastParams.chn = parseInt(chn);
								// }
								this.title = '点击播放实时视频'

								val && this.autoPlay && this._loadSource();
						},
						// immediate: true,//用onPlayerMounted替代
				},
				url: function (val, oldV) {
						this.lastParams.url = oldV;
				},
				controlShow: function (val, oldV) {
						if (!oldV && val) {
								clearTimeout(this.controlTimeout);
								this.controlTimeout = setTimeout(() => {
										this.controlShow = false;
								}, 3000); //3s后自动隐藏
						}
				},
				disabled: function (val, oldV) {
						if (!oldV && val) {
								this.stop();
						}
				},
		},
		methods: {
				async videoConfigChange(type){
						let res = await $.ajax({
								url: API_URL,
								type: "post",
								data: JSON.stringify({
										cmd: "controlLive",
										id: this.lastParams.url
												? this.lastParams.url
														.split("/")
														[
												this.lastParams.url.split("?")[0].split("/").length - 1
														].slice(0, -4)
												: this.url
														.split("?")[0]
														.split("/")
														[this.url.split("?")[0].split("/").length - 1].slice(0, -4),
										close_type: type,
								}),
								headers: {
										Authorization: "token " + this.token,
								},
						});
						if(res.code == 0){
							// this.$refs["player"].unload()
							if(type == 1){
								this.videoConfig.voice = false
							}else {
								this.videoConfig.video = false
							}
							this.$refs["player"].load(this.url, this.videoConfig.voice,this.videoConfig.video)
						}

				},
				async pausedOrStart(){
						let code
						if(this.pauseOrStartStatus){
								code = 3
						}else {
								code = 2
						}

						let res = await $.ajax({
								url: API_URL,
								type: "post",
								data: JSON.stringify({
										cmd: "controlLive",
										id: this.lastParams.url
												? this.lastParams.url
														.split("/")
														[
												this.lastParams.url.split("?")[0].split("/").length - 1
														].slice(0, -4)
												: this.url
														.split("?")[0]
														.split("/")
														[this.url.split("?")[0].split("/").length - 1].slice(0, -4),
										control_code: code,
								}),
								headers: {
										Authorization: "token " + this.token,
								},
						});
						if(res.code == 0){
								this.pauseOrStartStatus = !this.pauseOrStartStatus
						}
				},
				changeVolume(val){
						if(val == 0){
								this.$refs.player.toggleMuted(true);
						}else {
								this.$refs.player.setVolume(val/100)
								this.$refs.player.toggleMuted(false);
						}

				},
				formatterRotate(val) {
						return val + '°'
				},
				formatter(val) {
						return val * 100 + '%'
				},
				//   每个盒子内的全屏事件
				toggleChange() {
						this.$emit("useChange");
						this.toggleFullScreen();
				},
				// 宽高比改变
				changeWidth(row, BoxWidth, BoxHeight, winNum) {
						if (winNum != 6 && winNum != 10) {
								if (row == "1") {
										this.$refs.flag.style.width = BoxWidth + "px";
										this.$refs.flag.style.height = BoxHeight + "px";
										this.$refs.flag.style.marginTop = 0 + "px";
										this.$refs.flag.style.marginLeft = 0 + "px";
								}
								if (row == "2") {
										let videoWidth = BoxWidth;
										let videoHeight = videoWidth / 2;
										let marginLeft = (BoxWidth - videoWidth) / 2;
										let marginTop = (BoxHeight - videoHeight) / 2;
										this.$refs.flag.style.width = videoWidth + "px";
										this.$refs.flag.style.height = videoHeight + "px";
										this.$refs.flag.style.marginLeft = marginLeft + "px";
										this.$refs.flag.style.marginTop = marginTop + "px";
								}
								if (row == "3") {
										let videoHeight = BoxHeight;
										let videoWidth = (videoHeight / 9) * 16;
										let marginLeft = (BoxWidth - videoWidth) / 2;
										let marginTop = (BoxHeight - videoHeight) / 2;

										this.$refs.flag.style.width = videoWidth + "px";
										this.$refs.flag.style.height = videoHeight + "px";
										this.$refs.flag.style.marginLeft = marginLeft + "px";
										this.$refs.flag.style.marginTop = marginTop + "px";
								}
								if (row == "4") {
										let videoWidth = (BoxHeight / 3) * 4;
										let videoHeight = BoxHeight;
										let marginLeft = (BoxWidth - videoWidth) / 2;
										this.$refs.flag.style.width = videoWidth + "px";
										this.$refs.flag.style.height = videoHeight + "px";
										this.$refs.flag.style.marginLeft = marginLeft + "px";
										this.$refs.flag.style.marginTop = 0 + "px";
								}
								if (row == "5") {
										let videoWidth = BoxHeight;
										let videoHeight = BoxHeight;
										let marginLeft = (BoxWidth - videoWidth) / 2;
										this.$refs.flag.style.width = videoWidth + "px";
										this.$refs.flag.style.height = videoHeight + "px";
										this.$refs.flag.style.marginLeft = marginLeft + "px";
										this.$refs.flag.style.marginTop = 0 + "px";
								}
						} else if (winNum == 10) {
								if (row == "1") {
										this.$refs.flag.style.width = BoxWidth + "px";
										this.$refs.flag.style.height = BoxHeight + "px";
										this.$refs.flag.style.marginTop = 0 + "px";
										this.$refs.flag.style.marginLeft = 0 + "px";
								}
								if (row == "2") {
										let videoWidth = BoxWidth;
										let videoHeight = videoWidth / 2;
										let marginLeft = (BoxWidth - videoWidth) / 2;
										let marginTop = (BoxHeight - videoHeight) / 2;
										this.$refs.flag.style.width = videoWidth + "px";
										this.$refs.flag.style.height = videoHeight + "px";
										this.$refs.flag.style.marginLeft = marginLeft + "px";
										this.$refs.flag.style.marginTop = marginTop + "px";
								}
								if (row == "3") {
										let videoWidth = BoxWidth;
										let videoHeight = (videoWidth / 16) * 9;

										let marginLeft = (BoxWidth - videoWidth) / 2;
										let marginTop = (BoxHeight - videoHeight) / 2;
										this.$refs.flag.style.width = videoWidth + "px";
										this.$refs.flag.style.height = videoHeight + "px";
										this.$refs.flag.style.marginLeft = marginLeft + "px";
										this.$refs.flag.style.marginTop = marginTop + "px";
								}
								if (row == "4") {
										let videoWidth = BoxWidth;
										let videoHeight = (videoWidth / 4) * 3;
										let marginLeft = (BoxWidth - videoWidth) / 2;
										let marginTop = (BoxHeight - videoHeight) / 2;
										this.$refs.flag.style.width = videoWidth + "px";
										this.$refs.flag.style.height = videoHeight + "px";
										this.$refs.flag.style.marginLeft = marginLeft + "px";
										this.$refs.flag.style.marginTop = marginTop + "px";
								}
								if (row == "5") {
										let videoWidth = BoxWidth;
										let videoHeight = BoxWidth;
										let marginLeft = (BoxWidth - videoWidth) / 2;
										let marginTop = (BoxHeight - BoxWidth) / 2;
										this.$refs.flag.style.width = videoWidth + "px";
										this.$refs.flag.style.height = videoHeight + "px";
										this.$refs.flag.style.marginLeft = marginLeft + "px";
										this.$refs.flag.style.marginTop = marginTop + "px";
								}
						} else {
								if (row == "1") {
										this.$refs.flag.style.width = BoxWidth + "px";
										this.$refs.flag.style.height = BoxHeight + "px";
										this.$refs.flag.style.marginTop = 0 + "px";
										this.$refs.flag.style.marginLeft = 0 + "px";
								}
								if (row == "2") {
										let videoWidth = BoxWidth;
										let videoHeight = videoWidth / 2;
										let marginLeft = (BoxWidth - videoWidth) / 2;
										let marginTop = (BoxHeight - videoHeight) / 2;
										this.$refs.flag.style.width = videoWidth + "px";
										this.$refs.flag.style.height = videoHeight + "px";
										this.$refs.flag.style.marginLeft = marginLeft + "px";
										this.$refs.flag.style.marginTop = marginTop + "px";
								}
								if (row == "3") {
										let videoWidth = BoxWidth;
										let videoHeight = (videoWidth / 16) * 9;
										let marginLeft = (BoxWidth - videoWidth) / 2;
										let marginTop = (BoxHeight - videoHeight) / 2;
										this.$refs.flag.style.width = videoWidth + "px";
										this.$refs.flag.style.height = videoHeight + "px";
										this.$refs.flag.style.marginLeft = marginLeft + "px";
										this.$refs.flag.style.marginTop = marginTop + "px";
								}
								if (row == "4") {
										let videoWidth = BoxWidth;
										let videoHeight = (videoWidth / 4) * 3;
										let marginLeft = (BoxWidth - videoWidth) / 2;
										let marginTop = (BoxHeight - videoHeight) / 2;
										this.$refs.flag.style.width = videoWidth + "px";
										this.$refs.flag.style.height = videoHeight + "px";
										this.$refs.flag.style.marginLeft = marginLeft + "px";
										this.$refs.flag.style.marginTop = marginTop + "px";
								}
								if (row == "5") {
										let videoWidth = BoxWidth - 30;
										let videoHeight = BoxWidth - 30;
										let marginLeft = (BoxWidth - videoWidth) / 2;
										let marginTop = (BoxHeight - BoxWidth) / 2;
										this.$refs.flag.style.width = videoWidth + "px";
										this.$refs.flag.style.height = videoHeight + "px";
										this.$refs.flag.style.marginLeft = marginLeft + "px";
										this.$refs.flag.style.marginTop = marginTop + "px";
								}
						}
				},
				//录像
				startRecord() {
						//已经在录像的
						if (this.recordStatus) {
								this.stopScreen();
						} else {
								this.startScreen();
						}
				},
				//开始录像
				async startScreen() {
						this.recordStatus = true;

						this.canvasElement = document.querySelector(`#canvas${this.chnN}`);
						this.videoElement = document
								.querySelector(`#warp${this.chnN}`)
								.querySelector("video");
						this.canvasContext = this.canvasElement.getContext("2d");
						this.chunks = [];

						this.frameId = null;
						//创建MediaRecorder，设置媒体参数
						const stream = this.canvasElement.captureStream(60);
						// MediaRecorder  API
						this.recorder = new MediaRecorder(stream, {
								mimeType: "video/webm;codecs=vp8",
								videoBitsPerSecond: 6500000, //默认是2500000，说是越高越清晰，但是感觉没什么用
						});
						//收集录制数据
						this.recorder.ondataavailable = (e) => {
								this.chunks.push(e.data);
						};
						this.startTime = new Date().getTime();
						this.recorder.start(10);
						this.drawFrame();
						this.timeDuration();
				},
				drawFrame() {
						// 捕捉录像画面放到canvas上面
						this.canvasContext.drawImage(
								this.videoElement,
								0,
								0,
								this.canvasElement.width,
								this.canvasElement.height
						);
						// 传入一个动画函数,返回一个浏览器定义的id
						//好处一堆,可以自己百度
						this.frameId = requestAnimationFrame(this.drawFrame);
				},
				stopScreen() {
						this.recorder.stop();
						this.recordStatus = false;
						cancelAnimationFrame(this.frameId);
						this.download();
						this.recorder = null;
						clearInterval(this.timeRecord);
						this.timeRecord = null;
				},
				// 录像的时间记录
				timeDuration() {
						this.timeRecord = setInterval(() => {
								let second = new Date().getTime() - this.startTime;
								this.videoTime = this.SecondFormat(second);
						}, 1000);
				},
				download() {
						let blob = new Blob(this.chunks);
						let url = window.URL.createObjectURL(blob);
						let link = document.createElement("a");
						link.href = url;
						link.download =
								this.basicByVehicleId[this.vehicleId].plate +
								"通道" +
								this.chnN +
								".mp4";
						link.style.display = "none";
						document.body.appendChild(link);
						link.click();
						link.remove();
				},
				async changeLiveStream() {
						let res = await $.ajax({
								url: API_URL,
								type: "post",
								data: JSON.stringify({
										cmd: "controlLive",
										id: this.lastParams.url
												? this.lastParams.url
														.split("/")
														[
												this.lastParams.url.split("?")[0].split("/").length - 1
														].slice(0, -4)
												: this.url
														.split("?")[0]
														.split("/")
														[this.url.split("?")[0].split("/").length - 1].slice(0, -4),
										control_code: 1,
										switch_stream:this.streamType ? 0 : 1
								}),
								headers: {
										Authorization: "token " + this.token,
								},
						});
						if(res.code == 0){
								this.streamType = this.streamType ? 0 : 1
						}

				},
				async _speedChange(speed) {
						let num = Number(this.currentSpeed) + Number(speed)
						this.flow = Number(num).toFixed(2) + 'KB'
						if (num > 1024) {
								this.flow = Number(num / 1024).toFixed(2) + 'MB'
						} else if (num > 1024 * 1024) {
								this.flow = Number(num / 1024 / 1024).toFixed(2) + 'GB'
						}
						this.currentSpeed = Number(num).toFixed(2)
				},
				async _loadSource() {
						await this._freeSource();
						await this._requestSource();
				},
				/*
						申请占用此设备通道，
						目前实时视频的推流在不同的客户端会复用,
						这是一个非常耗时的流程，需要实现其中任意阶段可暂停
						*/
				async _requestSource() {
						this.sourceLoading = true;
						try {
								this.log("正在请求视频资源...");
								if (typeof this.chn !== "number") return;
								let ajax = $.ajax({
										url: API_URL,
										type: "post",
										data: JSON.stringify({
												cmd: "live",
												terminal_no: this.terminalCode,
												channel_no: this.chn,
												stream_type: this.streamType,
												media_type: this.playWays,
										}),
										headers: {
												Authorization: "token " + this.token,
										},
								});
								this.loadAjax = ajax;
								let result
								if (this.timeOut) {
										clearTimeout(this.timeOut)
								}
								this.timeOut = setTimeout(() => {
										if (!result) {
												// this.$error("获取视频地址失败,超时!");
												if (this.label.split(' ')[1] !== undefined) {
														this.title = this.label.split(' ')[0] + '-' + this.label.split(' ')[1] + '获取视频地址失败,超时!'
												} else {
														this.title = this.label + '获取视频地址失败,超时!'
												}
												ajax.abort()
												this.loadAjax = null;
												return
										}
								}, 60 * 1000)
								result = await ajax;
								if (!result || result.code === 1) {
										clearTimeout(this.timeOut)
										// this.$error("获取视频地址失败");
										if (this.label.split(' ')[1] !== undefined) {
												this.title = this.label.split(' ')[0] + '-' + this.label.split(' ')[1] + result.message || '获取视频地址失败'
										} else {
												this.title = this.label + result.message || '获取视频地址失败'
										}
								}
								this.loadAjax = null;
								if (typeof result !== "object") {
										result = JSON.parse(result);
								}
								if (result.code == 0) {
										clearTimeout(this.timeOut)

										this.log("成功获取视频地址，开始缓冲...");
										this.url = result.data;
										this.hasAudio = result.hasAudio;
										this.videoConfig.voice = result.hasAudio
										this.videoConfig.video = result.hasVideo

										this.title = '重新加载实时视频'
										await this.$api.setVideoLog({
												uri: this.url, // 视频接口地址
												type: 501, // 日志类型(501: 实时视频; 502: 录像回放)
												vehicleId: this.vehicleId, // 车辆id
										});
										await Promise.race([
												//允许中断操作
												new Promise((resolve, reject) => {
														this.interruptLoadSourceHandle = {resolve, reject};
												}),
												Promise.all([
														//   (async () => {
														//     try {
														//       //向ws订阅流的相关信息 这一步骤不是必要的
														//       //有可能会得不到回复
														//       await this._subscribeVideoInfo(this.url);
														//     } catch (e) {
														//       //报错的话 就忽略这次 订阅推流结束事件的行为（忽略严格模式）
														//       this.videoStream._resolve();
														//     }
														//   })(),
														//加载视频
														this.$refs["player"].load(this.url, this.hasAudio),
												]),
										]);
										this.interruptLoadSourceHandle.resolve();
										this.interruptLoadSourceHandle = null;
										this.loaded = true;
										// 向父组件暴露一个方法，用于判断视频是否正在播放
										this.$emit('playing', this.loaded)
										this.log("开始播放视频.");
										this.source.subscriptList.push(
												GPS.subscribe(msg => {
														if (msg.id === this.vehicleId) {
																this.subtitle.speed = msg.gpsSpeed + 'Km/h'
																this.subtitle.place = msg.location
																this.subtitle.time = moment().format('YYYY-MM-DD HH:mm:ss')
																this.subtitle.latlng = msg.latlng
														}
												}),
										)
										this.timeDelay = setInterval(() => {
												this.subtitle.time = moment().format('YYYY-MM-DD HH:mm:ss')
										}, 1000)
								} else {
										clearTimeout(this.timeOut)
										throw {from: "api", msg: result.message};
								}
						} catch (e) {
								clearTimeout(this.timeOut)
								switch (true) {
										case e.statusText === "abort":
												break;
										case e.from === "flvjs" && e.error === "NetworkError":
												// this.$error("视频地址已失效.");
												this.title = '视频地址已失效'
												this.log(`请求视频资源失败,原因：视频地址已失效.`);
												break;
										case e.from === "api":
												// this.$error(this.$ct("messageInfo.2") + `: ${e.msg}`);
												if (this.label.split(' ')[1] !== undefined) {
														this.title = this.label.split(' ')[0] + '-' + this.label.split(' ')[1] + ` ${e.msg}`
												} else {
														this.title = this.label + ` ${e.msg}`
												}
												this.log(`请求视频资源失败,原因：${e.msg}`);
												break;
										case e.from === "interrupt":
												break;
										default:
												console.error(e);
								}
								//报错的话 就忽略这次 订阅推流结束事件的行为（忽略严格模式）
								this.videoStream._resolve instanceof Function &&
								this.videoStream._resolve();
						} finally {
								this.sourceLoading = false;
						}
				},
				/*释放设备通道资源
				 * strict= true 时采取严格模式，即明确得到设备断开提示才会结束*/
				async _freeSource(strict = true) {
						try {
								this.stopLoading = true;
								this.loadAjax && this.loadAjax.abort();
								if (this.interruptLoadSourceHandle) {
										this.interruptLoadSourceHandle.reject({
												from: "interrupt",
												msg: "人工打断",
										});
										this.interruptLoadSourceHandle = null;
								}
								if (this.loaded) {
										this.loaded = false;
										this.player.unload();
										this.log("正在释放请求的资源...");
										await $.ajax({
												url: API_URL,
												type: "post",
												data: JSON.stringify({
														cmd: "controlLive",
														id: this.lastParams.url
																? this.lastParams.url
																		.split("/")
																		[
																this.lastParams.url.split("?")[0].split("/").length - 1
																		].slice(0, -4)
																: this.url
																		.split("?")[0]
																		.split("/")
																		[this.url.split("?")[0].split("/").length - 1].slice(0, -4),
														control_code: 0,
												}),
												headers: {
														Authorization: "token " + this.token,
												},
										});
										clearInterval(this.timeDelay)
										this.$emit('playing', this.loaded)
										this.timeDelay = null
										this.url = "";
										this.hasAudio = false;
										this.videoConfig.voice = false;
										this.videoConfig.video = false


										this.lastParams = {
												url: undefined,
										};
										if (strict) {
												this.log("严格模式：等待设备停止推流...");
												let timeStart = Date.now();
												await Promise.race([
														new Promise(async (resolve) => {
																await this.videoStream.waitForEnd;
																this.log(`设备已断开,耗时${Date.now() - timeStart}ms.`);
																resolve();
														}),
														new Promise(async (resolve) => {
																await this.$utils.sleep(500);
																if (this.videoStream.waitForEnd) {
																		this.log(`等待断开时间超过500ms,已忽略`);
																		this.videoStream._reject("忽略等待");
																}
																resolve();
														}),
												]);
										}
								}
								this.$refs["talkBack"]?.clearWs();
						} catch (e) {
								throw e;
						} finally {
								this.stopLoading = false;
								if (this.source.subscriptList.length) {
										this.source.subscriptList.forEach(item => item.unsubscribe())
										this.source.subscriptList = []
								}
						}
				},
				//确保首次执行时$refs['player']已存在
				_onPlayerMounted() {
						this.player = this.$refs["player"];
						this.sourceParams && this.autoPlay && this._loadSource();
						this.log("播放器初始化完成");
				},

				loadSource() {
						if (typeof this.chn !== "number") {
								this.chn = this.chn.value;
						}

						if (this.sourceParams) {
								this._loadSource();
						}
				},
				reload() {
						//   console.log(this.chnN);
						//   this.chn=this.chnN
						return this._loadSource();
				},
				stop() {
						this.isPlaying = false;
						return this._freeSource();
				},
				toggleSound() {
						if (!this.loaded || !this.hasAudio) {
								return;
						}
						this.player.toggleMuted();
				},
				screenshot() {
						if (this.loaded) {
								let data = this.player.screenShotOnLine(this.symbol);
								if (data) {
										this.$emit('photo',data,this.vehicleId, this.chnN, this.terminalCode)
								}
						}
				},
				async ttsSend() {
						let value = "";
						if (this.$parent.showModal) {
								value = await this.$parent.showModal();
						} else if (this.$parent.$parent.showModal) {
								value = await this.$parent.$parent.showModal();
						} else if (this.$parent.$parent.$parent.showModal) {
								value = await this.$parent.$parent.$parent.showModalTTS();
						} else {
								return
						}
						// let {value} = await this.$prompt(this.$ct('ttsTip'), this.$ct('tts'))
						await BstandardUtil.init();
						let bits = Array.from({length: 6}).fill(0);
						bits[0] = 0;
						bits[2] = 1;
						bits[3] = 1;
						bits[4] = 0;
						bits[5] = 0;
						bits = bits.reverse();
						let res = await this.$api.sendMiniStandardCommon({
								vehicle_terminal_list: [
										{
												vehicle_id: this.vehicleId,
												terminal_no: this.terminalCode,
										},
								],
								operate_key: "SendSMS",
								cmd_sms: {
										type: parseInt(bits.join(""), 2),
										text: value,
								},
						});
						let wsRes = await BstandardUtil.waitForResponse(res.data);
						if (wsRes && wsRes.task_state === 0) {
								this.$success(this.$ct("messageInfo.1"));
								return true;
						} else {
								this.$message(this.$ct("messageInfo.0"));
								return false;
						}
						await BstandardUtil.destroy();
				},
		},
		created() {
				window.addEventListener("beforeunload", this.stop);
				this.$nextTick(() => {
						this.changeWidth(
								this.$parent.$parent.value,
								this.$refs["playerBox"].offsetWidth,
								this.$refs["playerBox"].offsetHeight,
								this.$parent.$parent.windowNum
						);
				});
		},
		beforeDestroy() {
				window.removeEventListener("beforeunload", this.stop);
				this._freeSource(false);
				clearTimeout(this.controlTimeout);
				if (this.source.subscriptList.length) {
						this.source.subscriptList.forEach(item => item.unsubscribe())
						this.source.subscriptList = []
				}
				if (this.timeDelay) {
						clearInterval(this.timeDelay)
						this.timeDelay = null
				}
		},
		deactivated() {
				let isPlaying = !this.player.videoElm.paused;
				if (isPlaying) {
						this.log("切换至后台，断开设备链接");
						this._freeSource(false);
						this.$once("hook:activated", () => {
								this._loadSource();
						});
				}
		},
};
</script>

<style lang="scss">
@import "../player";
</style>
<style scoped lang="scss">
.realtime-player {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		flex-grow: 1;

		#canvas {
				width: 100%;
				height: 100%;
		}

		.real-video {
				font-size: 13px;
				font-weight: bold;
				font-style: normal;
		}

		.videoTime {
				position: absolute;
				right: 0;
				top: -5px;
				background-color: black;
				color: red;
				display: inline-block;
				line-height: 20px;
				font-style: normal;
				font-size: 12px;
				padding: 3px 8px;
				border-radius: 3px;
		}

		.video {
				height: 100%;
		}

		.pony-player__controller > span {
				margin: 0;
		}
}

.el-radio {
		margin-right: 18px;
}

.el-checkbox {
		margin-right: 18px;
}
</style>

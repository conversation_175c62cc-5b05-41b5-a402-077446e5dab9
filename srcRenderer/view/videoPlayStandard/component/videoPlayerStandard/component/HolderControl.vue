<template>
    <div>
        <el-collapse v-model="activeNames" @change="handleChange">
            <el-collapse-item title="视频控制" name="1">
                <el-tabs v-model="activeTab" @tab-click="handleClick" style="height: 100%">
                    <el-tab-pane label="云台" name="first" :disabled="!(listenStatus == null && talkStatus == null)">
                        <div class="holder">
                            <div class="left">
                                <div class="box">
                                    <div class="one">
                                        <i class="play_icon pony-iconv2 pony-jiantoushang"
                                           @click="change('PTZRotate',1)"></i>
                                    </div>
                                    <div class="two">
                                        <i class="play_icon pony-iconv2 pony-jiantouzuo"
                                           @click="change('PTZRotate',3)"></i>
                                        <i class="play_icon pony-iconv2 pony-guanbiyuan"
                                           @click="change('PTZRotate',0)"></i>
                                        <i class="play_icon pony-iconv2 pony-jiantouyou"
                                           @click="change('PTZRotate',4)"></i>
                                    </div>
                                    <div class="three">
                                        <i class="play_icon pony-iconv2 pony-jiantouxia"
                                           @click="change('PTZRotate',2)"></i>
                                    </div>
                                </div>
                                <div class="block">
                                    <el-slider v-model="speed" :min="0" :max="255"></el-slider>
                                </div>
                            </div>
                            <div class="right">
                                <div class="item" v-for="(item,index) in list" :key="index"
                                     @click="change(item.id,item.change)">{{ item.label }}
                                </div>
                            </div>
                        </div>
                    </el-tab-pane>
                    <el-tab-pane label="监听" name="second" :disabled="!(listenStatus == null && talkStatus == null)">
                        <div class="jianting">
                            <p>
                                <i class="pony-iconv2 pony-erji"
                                   :style="!listenStatus?'':'color:var(--color-primary)'"></i>
                            </p>
                            <p>{{ resDes }}</p>
                            <div class="button">
                                <el-button type="primary" @click="!listenStatus?startCommand():endCommand()"
                                           :disabled="!currentVehicle || inputType == 3">{{ listenStatus ? '关闭' : '打开' }}
                                </el-button>
                            </div>
                        </div>
                    </el-tab-pane>
                    <el-tab-pane label="广播" name="third" :disabled="!(listenStatus == null && talkStatus == null)">
                        <div class="jianting">
                            <div v-if="!talkList.length">
                                <p>
                                    <i class="pony-iconv2 pony-tongzhi"
                                       :style="!talkStatus?'':'color:var(--color-primary)'"></i>
                                </p>
                            </div>
                            <el-table ref="talkList" :data="talkList" border stripe height="200px" highlight-current-row
                                      v-else>
                                <el-table-column type="index" label="序号" min-width="50"></el-table-column>
                                <el-table-column prop="plateNo" label="车牌号" min-width="80"
                                                 show-overflow-tooltip></el-table-column>
                                <el-table-column prop="chn" label="通道号" min-width="60"></el-table-column>
                                <el-table-column prop="des" label="状态" min-width="80"
                                                 show-overflow-tooltip></el-table-column>
                            </el-table>
                            <div class="button">
                                <el-button type="primary" @click="broadcast">
                                    {{ talkStatus ? '关闭' : '打开' }}
                                </el-button>
                            </div>
                        </div>
                    </el-tab-pane>
                    <el-tab-pane label="对讲" name="fourth" :disabled="!(listenStatus == null && talkStatus == null)">
                        <div class="jianting">
                            <p>
                                <i class="pony-iconv2 pony-yuyin"
                                   :style="!listenStatus?'':'color:var(--color-primary)'"></i>
                            </p>
                            <p>{{ resDes }}</p>
                            <div class="button">
                                <el-button type="primary" @click="!listenStatus?startTalking():endTalking()"
                                           :disabled="!currentVehicle || this.inputType == 3">{{ listenStatus ? '关闭' : '打开' }}
                                </el-button>
                            </div>
                        </div>
                    </el-tab-pane>
                </el-tabs>

            </el-collapse-item>
        </el-collapse>
    </div>
</template>

<script>
import {TaskCmdResponse} from '@/service/wsService'
import Recorder from '@/view/videoPlay/component/TalkBack/Recoder'
import {mapState} from 'vuex'


const BASE_URL_API = window.PONY.media.apiUrl;

export default {
    name: "HolderControl",
    props: {
        inputType:{
            default: 1
        },
        terminal_no: {
            required: true
        },
        vehicle_id: {
            required: true
        },
        clickItemNum: {
            required: true,
            default: 0
        },
        currentVehicle: {
            default: null,

        },
        selectNodesList: {
            default: [],
        }
    },
    data() {
        return {
            resDes: '',
            ws: '',
            wsList: [],
            wsUrl: '',
            id:'',
            recorder: null,
            linked: false,
            temp: [],
            replyBuffer: [],
            audioCtx: null,
            delayTemp: [],
            isPlaying: false,
            timeInter: null,
            currentRecordAjax: null,
            listenStatus: null,

            // 广播的操作
            talkStatus: null,
            talkList: [],
            maike: false,
            talkingStatus: null,
            // 是否折叠
            activeNames: [],
            // 控制选项列表
            list: [
                {label: '焦距变小', id: 'PTZFocus', change: 1},
                {label: '焦距变大', id: 'PTZFocus', change: 0},
                {label: '光圈缩小', id: 'PTZAperture', change: 1},
                {label: '光圈放大', id: 'PTZAperture', change: 0},
                {label: '变倍调小', id: 'PTZZoom', change: 1},
                {label: '变倍调大', id: 'PTZZoom', change: 0},
                {label: '补光打开', id: 'InfraredLight', change: 1},
                {label: '补光关闭', id: 'InfraredLight', change: 0},
                {label: '雨刷打开', id: 'PTZWiper', change: 1},
                {label: '雨刷关闭', id: 'PTZWiper', change: 0},
            ],
            // ws
            source: {
                timeOut: 15,
                currentTaskId: '',
                subscriptList: [],
                handlList: [],
                handleTimer: null,
                handleInterval: null,
            },
            // 移动大小
            speed: 0,
            activeTab: 'first',
            initialized: false
        }
    },
    computed:{
		    ...mapState("auth", ["token"]),

    },
    mounted() {
        // 订阅，进来就打开
        this.source.subscriptList.push(
          TaskCmdResponse.subscribe(msg => this.source.handlList.push(msg)),
          // 先不处理，先放数组
        )
    },
    methods: {
        broadcast() {
            if (!this.talkStatus) {
                this.startTalk()
                this.$emit('changHeight', 300)
            } else {
                this.endTalk()
                this.$emit('changHeight', 190)
            }
        },
        async initDevice() {
            let stream;
            try {
                // if(this.maike)return
                if (!(navigator.getUserMedia || navigator.webkitGetUserMedia)) {
                    this.$warning('获取不到麦克风权限!')
                    return;
                }
                // this.maike = true
                stream = await navigator.mediaDevices.getUserMedia({audio: true});
                this.recorder = new Recorder(stream);
                this.recorder.onAudioData = this.onAudioDataHandle.bind(this);
                // this.step = StepState.GET_MICROPHONE_PERMISSION_SUCCESS;
                this.initialized = true;
                // this.setStateDelay(StepState.CLICK_TO_CONNECT_DEVICE, 500);
            } catch (e) {
                console.error(e);
                // this.step = StepState.GET_MICROPHONE_PERMISSION_FAILURE;
                this.$error('申请麦克风权限失败，请确认开启麦克风权限或检查音频输入设备。')
            }
        },

        async startTalkBack() {
            if (this.recorder) {
                this.recorder.start();
            }
        },
        stopTalkBack() {
            if (this.recorder) {
                this.recorder.stop();
            }
        },
        onAudioDataHandle(audioData) {
            if (this.linked) {
                if (this.activeTab == 'third') {
                    this.wsList.forEach(item => {
                        if (!item.ws) return
                        item.ws.send(audioData);
                    })
                } else {
                    this.ws.send(audioData);
                }
                // this.ws.send(new ArrayBuffer(1364));
            }
        },

        playReply() {
            this.isPlaying = false;
            let buffer = this.replyBuffer.shift();
            if (buffer) {
                let source = this.audioCtx.createBufferSource();
                source.buffer = buffer;
                source.connect(this.audioCtx.destination);
                source.onended = this.playReply;
                source.start();
                this.isPlaying = true;
            }
        },
        async endCommand() {
            this.abortRecordAjax()
            this.resDes = ''
            this.listenStatus = null
            this.clearWs()
            this.stop()
        },
        async startTalking() {
            if (!this.currentVehicle || this.inputType == 3) return
            if (!this.audioCtx) {
                this.audioCtx = new AudioContext();
            }
            this.initDevice()
            this.listenStatus = 'ing'
            let ajax = $.ajax({
                url: BASE_URL_API,
                type: "post",
                data: JSON.stringify({
                    cmd: "live",
                    terminal_no: this.currentVehicle.terminal_no.toString(),
                    vehicle_id: this.currentVehicle.vehicle_id.toString(),
                    media_type: 2,
                    //2020-06-28 18:01:03 因设备原因固定成通道1
                    channel_no: this.currentVehicle.channelTalkback,
                }),
                headers: {
                    Authorization: "token " + this.token,
                },
            });
            if (this.timeInter) {
                clearTimeout(this.timeInter)
            }
            this.currentRecordAjax = ajax;
            //请求状态为请求中
            this.listenStatus = 'ing'
            this.resDes = `${this.currentVehicle.plateNo}(通道${this.currentVehicle.channelTalkback}):请求中`
            this.timeInter = setTimeout(() => {
                this.resDes = `${this.currentVehicle.plateNo}(通道${this.currentVehicle.channelTalkback}):设备没有响应,请稍后再试`
                this.abortRecordAjax()
                this.listenStatus = null
            }, 20 * 1000)

            let res = await ajax

            if (res.code == 0) {
                //请求状态结束并开始监听
                this.listenStatus = 'start'
                this.resDes = `${this.currentVehicle.plateNo}(通道${this.currentVehicle.channelTalkback}):成功`
                this.wsUrl = res.data
                this.id = res.id
                let ws = new WebSocket(res.data);
                ws.binaryType = 'arraybuffer'  //WebSocket.binaryType 返回websocket连接所传输二进制数据的类型。(Blob 类型的数据/ArrayBuffer 类型的数据(ArrayBuffer 对象用来表示通用的、固定长度的原始二进制数据缓冲区。))
                ws.onopen = this.onWsOpen.bind(this);
                ws.onmessage = this.onWsMessage.bind(this);
                ws.onclose = this.onWsClose.bind(this);
                ws.onerror = this.onWsError.bind(this);
                this.ws = ws;
            } else {
                this.listenStatus = null
                this.resDes = `${this.currentVehicle.plateNo}(通道${this.currentVehicle.channelTalkback}):${res.message}`
            }
            clearTimeout(this.timeInter)
            this.currentRecordAjax = null;

        },
        endTalking() {
            this.abortRecordAjax()
            this.resDes = ''
            this.listenStatus = null
            this.clearWs()
            this.stop(4)
        },
        //广播
        async startTalk() {
            if (!this.selectNodesList.length) {
                this.$warning('请选择需要广播的车辆!')
                return
            }
            if (!this.audioCtx) {
                this.audioCtx = new AudioContext();
            }
            this.initDevice()
            this.talkList = this.selectNodesList.map(item => {
                return {
                    id: item.id,
                    plateNo: item.name,
                    chn: item.channelBroadcast,
                    des: '请求中',
                    iconSkin: item.iconSkin,
                    terminal_no: item.terminalNo
                }
            })
            this.talkStatus = 'ing'
            this.wsList = []
            this.$nextTick(() => {
                this.$refs.talkList.doLayout()
            })
            await this.talkList.forEach(async (item, index) => {
                // if(item.iconSkin.split('_')[1] == 0){
                //     item.des = '设备离线'
                //     return
                // }    
                item.des = '请求中'
                let ajax = $.ajax({
                    url: BASE_URL_API,
                    type: "post",
                    data: JSON.stringify({
                        cmd: "live",
                        terminal_no: item.terminal_no,
                        vehicle_id: item.id,
                        media_type: 4,
                        channel_no: item.chn,
                    }),
                    headers: {
                        Authorization: "token " + this.token,
                    },
                });
                let currentAjax = ajax
                let timeInter = setTimeout(() => {
                    item.des = `设备没有响应,请稍后再试`
                    if (currentAjax) {
                        currentAjax.abort()
                    }
                    let startList = this.talkList.filter(item => item.des == '请求中' || item.des == '设备请求成功')
                    if (!startList.length) {
                        this.talkStatus = null
                    } else {
                        this.talkStatus = 'start'
                    }
                }, 20 * 1000)
                let res = await ajax
                if (res.code != 0) {
                    item.des = '设备请求失败' + res.message
                } else {

                    item.des = '设备请求成功'
                    let ws = new WebSocket(res.data);
                    ws.binaryType = 'arraybuffer'  //WebSocket.binaryType 返回websocket连接所传输二进制数据的类型。(Blob 类型的数据/ArrayBuffer 类型的数据(ArrayBuffer 对象用来表示通用的、固定长度的原始二进制数据缓冲区。))
                    ws.onopen = this.onWsOpen.bind(this);
                    ws.onmessage = this.onWsMessage.bind(this);
                    ws.onclose = this.onWsClose.bind(this);
                    ws.onerror = this.onWsError.bind(this);
                    this.wsList.push({
                        id: item.id,
                        ws,
                        wsUrl: res.id,
                    })
                    // this.ws = ws;
                }
                clearTimeout(timeInter)
                let startList = this.talkList.filter(item => item.des == '请求中' || item.des == '设备请求成功')
                if (!startList.length) {
                    this.talkStatus = null
                } else {
                    this.talkStatus = 'start'
                }
            })

        },
        async endTalk() {
            this.abortRecordAjax()
            this.talkStatus = null
            this.stopList()
            this.clearWs()
            this.talkList = []
        },
        //监听
        async startCommand() {
            if (!this.currentVehicle || this.inputType == 3) return
            // if(!this.currentVehicle.status){
            //     this.resDes = `${this.currentVehicle.plateNo}(通道${this.currentVehicle.chn}):设备离线`
            //     return
            // }
            if (!this.audioCtx) {
                this.audioCtx = new AudioContext();
            }
            clearTimeout(this.timeInter)
            let ajax = $.ajax({
                url: BASE_URL_API,
                type: "post",
                data: JSON.stringify({
                    cmd: "live",
                    terminal_no: this.currentVehicle.terminal_no.toString(),
                    vehicle_id: this.currentVehicle.vehicle_id.toString(),
                    media_type: 3,
                    //2020-06-28 18:01:03 因设备原因固定成通道1
                    channel_no: this.currentVehicle.chn,
                }),
                headers: {
                    Authorization: "token " + this.token,
                },
            });
            this.currentRecordAjax = ajax;
            //请求状态为请求中
            this.listenStatus = 'ing'
            this.resDes = `${this.currentVehicle.plateNo}(通道${this.currentVehicle.chn}):请求中`
            this.timeInter = setTimeout(() => {
                this.resDes = `${this.currentVehicle.plateNo}(通道${this.currentVehicle.chn}):设备没有响应,请稍后再试`
                this.abortRecordAjax()
                this.listenStatus = null
            }, 20 * 1000)

            let res = await ajax

            if (res.code == 0) {
                //请求状态结束并开始监听
                this.listenStatus = 'start'
                this.resDes = `${this.currentVehicle.plateNo}(通道${this.currentVehicle.chn}):成功`
                this.wsUrl = res.data
                this.id = res.id
                let ws = new WebSocket(res.data);
                ws.binaryType = 'arraybuffer'  //WebSocket.binaryType 返回websocket连接所传输二进制数据的类型。(Blob 类型的数据/ArrayBuffer 类型的数据(ArrayBuffer 对象用来表示通用的、固定长度的原始二进制数据缓冲区。))
                ws.onopen = this.onWsOpen.bind(this);
                ws.onmessage = this.onWsMessage.bind(this);
                ws.onclose = this.onWsClose.bind(this);
                ws.onerror = this.onWsError.bind(this);
                this.ws = ws;
            } else {
                this.listenStatus = null
                this.resDes = `${this.currentVehicle.plateNo}(通道${this.currentVehicle.chn}):${res.message}`
            }
            clearTimeout(this.timeInter)
            this.currentRecordAjax = null;

        },
        abortRecordAjax() {
            if (!this.currentRecordAjax) return;
            if (this.currentRecordAjax instanceof Array) {
                this.currentRecordAjax.forEach(ajax => ajax.abort());
            } else {
                this.currentRecordAjax.abort();
            }
        },
        handleClick(tab, event) {
            if (this.activeTab === 'third') {
                this.$emit('changeTree', true)
            } else {
                this.$emit('changeTree', false)
            }
            this.$emit('changHeight', 190)
        },
        handleChange(val) {
            if (val.length > 0) {
                this.$emit('changHeight', 190)
            } else {
                this.$emit('changHeight', 32)
            }
        },

        clearWs() {
            this.stopTalkBack();
            this.linked = false;
            if (!this.ws) return;
            let ws = this.ws;
            ws.onopen = null;
            ws.onmessage = null;
            ws.onclose = null;
            ws.onerror = null;
            this.ws = null;
        },
        onWsClose() {
            console.log('ws已断开' + Date.now());
            this.clearWs();
        },
        onWsOpen() {
            console.log('ws已连接' + Date.now());
            this.linked = true;
            this.startTalkBack();
        },
        onWsError(err) {
            console.error(err);
            this.clearWs();
        },
        async onWsMessage(msg) {

            //pcm
            //0.04s*10 一个了
            if (this.temp.length === 20) {
                this.temp = this.temp.map(item => new Int16Array(item));
                let size = this.temp.reduce((a, b) => a + b.length, 0)
                let totalArray = new Int16Array(size);
                let offset = 0;
                for (let i = 0; i < this.temp.length; i++) {
                    totalArray.set(this.temp[i], offset)
                    offset += this.temp[i].length;
                }
                this.temp = [];
                let audioBuffer = await this.audioCtx.decodeAudioData(this.buildWAV(totalArray))
                this.replyBuffer.push(audioBuffer);
                if (!this.isPlaying) {
                    this.playReply()
                }
            } else {
                this.temp.push(msg.data)
            }

        },
        /**
         * @param {Int16Array} input*/
        buildWAV(input) {
            let sampleRate = 8000;
            let sampleBits = 16;
            let bytes = input;
            let dataLength = bytes.length * 2;
            let buffer = new ArrayBuffer(44 + dataLength);
            let data = new DataView(buffer);
            let channelCount = 1;//单声道
            let offset = 0;

            let writeString = function (str) {
                for (let i = 0; i < str.length; i++) {
                    data.setUint8(offset + i, str.charCodeAt(i));
                }
            };

            // 资源交换文件标识符
            writeString('RIFF');
            offset += 4;
            // 下个地址开始到文件尾总字节数,即文件大小-8
            data.setUint32(offset, 36 + dataLength, true);
            offset += 4;
            // WAV文件标志
            writeString('WAVE');
            offset += 4;
            // 波形格式标志
            writeString('fmt ');
            offset += 4;
            // 过滤字节,一般为 0x10 = 16
            data.setUint32(offset, 16, true);
            offset += 4;
            // 格式类别 (PCM形式采样数据)
            data.setUint16(offset, 1, true);
            offset += 2;
            // 通道数
            data.setUint16(offset, channelCount, true);
            offset += 2;
            // 采样率,每秒样本数,表示每个通道的播放速度
            data.setUint32(offset, sampleRate, true);
            offset += 4;
            // 波形数据传输率 (每秒平均字节数) 单声道×每秒数据位数×每样本数据位/8
            data.setUint32(offset, channelCount * sampleRate * (sampleBits / 8), true);
            offset += 4;
            // 快数据调整数 采样一次占用字节数 单声道×每样本的数据位数/8
            data.setUint16(offset, channelCount * (sampleBits / 8), true);
            offset += 2;
            // 每样本数据位数
            data.setUint16(offset, sampleBits, true);
            offset += 2;
            // 数据标识符
            writeString('data');
            offset += 4;
            // 采样数据总数,即数据总大小-44
            data.setUint32(offset, dataLength, true);
            offset += 4;
            for (let i = 0; i < bytes.length; i++, offset += 2) {
                data.setInt16(offset, bytes[i], true);
            }
            return data.buffer;
        },
        async change(flag, value) {
            if (!this.terminal_no || !this.vehicle_id) {
                return this.$warning('请先选择车辆!')
            }

            // // 通道
            // if (typeof(this.clickItemNum)==="undefined") {
            //     return this.$warning('请选择通道！')
            // }

            let arr = {}
            arr.vehicle_terminal_list = [
                {
                    terminal_no: this.terminal_no,
                    vehicle_id: this.vehicle_id
                }
            ]
            arr.operate_key = flag

            if (flag === 'PTZRotate') {
                arr.cmd_ptz_rotate = {
                    channel_id: this.clickItemNum,
                    rotate: value,
                    speed: this.speed
                }
            } else if (flag === 'PTZFocus') {
                arr.cmd_ptz_focus = {
                    channel_id: this.clickItemNum,
                    rotate: value
                }
            } else {
                arr.cmd_ptz_adjust = {
                    channel_id: this.clickItemNum,
                    adjust: value
                }
            }
            let result = await this.$api.sendMiniStandardCommon(arr)
            this.source.currentTaskId = result.data
            await this.handleCommandResult()
        },
        //处理终端的回应
        async handleCommandResult() {
            // 超时了，没找到结果
            this.source.handleTimer = setTimeout(() => {
                this.$message({type: 'error', showClose: true, message: '服务器响应超时'})
                if (this.source.handleInterval) {
                    clearInterval(this.source.handleInterval)
                    this.source.handleInterval = null
                }
            }, 1000 * this.source.timeOut)
            // 半s找一次
            this.source.handleInterval = setInterval(() => {
                if (!this.source.handlList.length) return
                this.source.handlList.forEach(item => {
                    if (this.source.currentTaskId == item.task_id) {
                        if (item.task_state == 0) {
                            this.$success('操作成功！')
                        } else {
                            this.$message({type: 'error', showClose: true, message: `${item.message}`})
                        }
                        clearTimeout(this.source.handleTimer)
                    }
                    this.source.handlList.remove(item)
                })
            }, 500)
        },
        stop(type = 0) {
            this.ws && this.ws.close();
            this.linked = false
            if (!this.id) return
            $.ajax({
                url: BASE_URL_API,
                type: "post",
                data: JSON.stringify({
                    cmd: "controlLive",
                    control_code: type,
                    id: this.id,
                }),
                headers: {
                    Authorization: "token " + this.token,
                },
            });
        },
        stopList() {
            this.linked = false
            if (!this.wsList.length) return
            this.wsList.forEach(item => {
                if (!item.ws) return
                item.ws && item.ws.close();
                item.ws.onopen = null;
                item.ws.onmessage = null;
                item.ws.onclose = null;
                item.ws.onerror = null;
                item.ws = null;
                $.ajax({
                    url: BASE_URL_API,
                    type: "post",
                    data: JSON.stringify({
                        cmd: "controlLive",
                        control_code: 0,
                        id: item.wsUrl,
                    }),
                    headers: {
                        Authorization: "token " + this.token,
                    },
                });
            })
        }

    },
    async beforeDestroy() {
        this.stop();
        this.clearWs();
        this.recorder = null;
        this.initialized = false;
        this.audioCtx = null;
        if (this.source.subscriptList.length) {
            this.source.subscriptList.forEach(item => item.unsubscribe())
            this.source.subscriptList = []
        }
        if (this.source.handleTimer) {
            clearTimeout(this.source.handleTimer)
            this.source.handleTimer = null
        }
        if (this.source.handleInterval) {
            clearInterval(this.source.handleInterval)
            this.source.handleInterval = null
        }
    }
}
</script>

<style scoped lang="scss">
.heightMax {
    height: 100%;
}

.holder {
    display: flex;
    align-items: center;
    border-top: 1px solid var(--border-color-base);
    border-bottom: 1px solid var(--border-color-base);

    .left {
        width: 35%;
        height: 100px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;

        .box {
            height: 85%;


            i {
                font-size: 30px;
                color: var(--color-text-secondary);

                &:hover {
                    color: #2880E2;
                }
            }

            .one {
                height: 33%;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .two {
                height: 33%;
                display: flex;
                align-items: center;
                justify-content: space-around;
            }

            .three {
                height: 33%;
                display: flex;
                align-items: center;
                justify-content: center;
            }
        }

        .block {
            height: 10px;
            width: 80%;
        }
    }

    .right {
        width: 65%;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-wrap: wrap;

        .item {
            width: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 1px solid var(--border-color-base);
            cursor: pointer;

            &:hover {
                background-color: var(--color-text-hover);
                color: #2880E2;
            }
        }
    }
}

/deep/ .el-collapse-item__header {
    height: 32px;
    padding: 0 10px;
    color: var(--color-text-regular);
}

/deep/ .el-collapse-item__content {
    padding: 0;
}

/deep/ .el-tabs__header {
    padding: 0 10px;
}

.jianting {
    height: 100%;

    p {
        text-align: center;

        i {
            font-size: 50px;
        }
    }

    .button {
        text-align: center;

        .el-button {
            margin: 2px 0;
        }
    }

}

/deep/ .el-collapse {
    height: 100% !important;
}

/deep/ .el-collapse-item {
    height: 100% !important;
}

/deep/ .el-collapse-item__wrap {
    height: 100%;
}
</style>

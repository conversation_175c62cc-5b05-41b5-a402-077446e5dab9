<template>
    <div class="playback-player pony-player"
         v-context-menu="contextMenuOptions"
         @mousemove="controlShow = true"
         @mouseleave="controlShow = false"
    >
        <canvas id="canvas" style="display:none"></canvas>
        <div class="pony-player__video-wrap wrap " id="warp">
            <FlvVideo ref="player"
                      :showDecodeInfo="showDecodeInfo"
                      @decodeLog="log"
                      @ready="_onPlayerMounted"
                      @speedChange="_speedChange"
                      @play="()=>this.isPlaying = true"
                      @ended="_onEnded"
                      @canplay="_onCanPlay"
                      @timeupdate="_onTimeUpdate"
                      @progress="_setBufferProgress"
            >
            </FlvVideo>
        </div>
        <div class="pony-player__main-wrap wrap">
            <!--控制条-->
            <div class="pony-player__controller" v-if="loaded && player && player.videoElm" :style="{opacity:controlShow?1:0}">
                <i class="pony-iconv2 pony-sanjiaoxing" :title="$ct('play')" v-if="player.videoElm.paused" @click="videoPlayer"></i>
                <i class="pony-iconv2 pony-zanting" :title="$ct('pause')" v-else @click="videoPause"></i>
                <i class="pony-iconv2 pony-tingzhi" :title="$ct('stop')" @click="stop()"></i>
                <i class="pony-iconv2 pony-guanjianzhenbofang" title="关键帧播放" @click="guanjianzhen()"></i>
                <div :class="['collapse-item',{'is-disabled':!loaded}]">
                    <i class="pony-iconv2 pony-guanjianzhenkuaitui" title="关键帧快退"></i>
                    <ul class="pane">
                        <li v-for="(item,index) in playBack.speedValue" :key="item"
                            :class="['text text--link',{'text--brand':playBack.backSpeed === index}]" @click="backForward(index)">
                            X{{item}}
                        </li>
                    </ul>
                </div>
                <CustomSlider :percentage="playBack.progress" :subPercentage="playBack.bufferedProgress"
                              :disabled="loaded" :format-tooltip="formatTip" :offset="playBack.offset"
                              @change="setProgress" class="progress" rangeLimit>
                </CustomSlider>
                <span style="width: 65px;text-align: right">{{currentSpeed}}KB/s</span>
                <span class="time-tip">
                    {{formatTime(playBack.currentTime) + ' / ' + formatTime(playBack.totalTime)}}
                </span>
                <div :class="['collapse-item',{'is-disabled':!loaded}]">
                    <span>X{{playBack.speedValue[playBack.speed]}}</span>
                    <ul class="pane">
                        <li v-for="(item,index) in playBack.speedValue" :key="item"
                            :class="['text text--link',{'text--brand':playBack.speed === index}]" @click="fastForward(index)">
                            X{{item}}
                        </li>
                    </ul>
                </div>
                <i :class="['pony-iconv2 pony-paizhao',{'is-disabled':!loaded }]" :title="$ct('screenShot')"
                   @click="screenshot()"></i>
                <div :class="['collapse-item',{'is-disabled':!loaded}]">
                    <i :class="['pony-iconv2 ',{'is-disabled':!loaded || !hasAudio},player.videoElm.muted?'pony-jingyin':'pony-shengyin']"
                   @click="toggleSound" :title="volumeHint"></i>
                   <div class="pane" v-if="!(!loaded || !hasAudio)">
                       <el-slider
                        v-model="playBack.volumeProcess"
                        vertical
                        @change="changeVolume"
                        height="100px">
                        </el-slider>
                    </div>
                    
                <!-- <span>X{{playBack.speedValue[playBack.speed]}}</span>
                <ul class="pane">
                    <li v-for="(item,index) in playBack.speedValue" :key="item"
                        :class="['text text--link',{'text--brand':playBack.speed === index}]" @click="fastForward(index)">
                        X{{item}}
                    </li>
                </ul> -->
                </div>
                
                <i class="videoTime" v-show="recordStatus">{{videoTime}}</i>

                <i class="pony-iconv2 pony-shipin" :title="recordStatus?'结束录制':'开始录制'" @click="startRecord"></i>

                <i class="pony-iconv2" :class="[isFullScreen?'pony-suoxiao':'pony-quanping']" @click="toggleFullScreen"
                   v-if="enableFullScreen"
                   :title="(isFullScreen?$ct('exit'):'') + $ct('fullscreen')"></i>
            </div>
            <div class="pony-player__mask wrap" v-else>
                <i class="pony-iconv2 pony-sanjiaoxing" style="cursor: not-allowed;font-size:40px" :title="$ct('notSupportVideo')" v-if="disabled"></i>
                <i v-else-if="loading" class="el-icon-loading" ></i>
                <i v-else :class="['pony-iconv2',firstLoad?'pony-sanjiaoxing':'pony-shuaxin']" style="font-size:40px" :title="$ct('play')"
                   @click="loadSource()"></i>
            </div>
        </div>
        <div class="pony-player__tip-wrap wrap">
            <div class="video-title" v-show="label && sourceParams" :style="{opacity: (labelAlwaysShow || controlShow)?1:0}">
                {{label}}
            </div>
        </div>
        <div class="pony-player__log-wrap" v-if="showLog">
            <i class="pony-iconv2 pony-guanbi pointer" @click="showLog = false"></i>
            <div>
                <p v-for="row in logArr" :key="row">{{row}}</p>
            </div>
        </div>
    </div>
</template>

<script>
    import strictEnd from "@/view/videoPlayStandard/component/videoPlayerStandard/mxin/strictEnd";
    import fullscreen from "@/view/videoPlayStandard/component/videoPlayerStandard/mxin/fullscreen";
    import log from "@/view/videoPlayStandard/component/videoPlayerStandard/mxin/log";
    import CustomSlider from "@/components/common/CustomSlider";
    import FlvVideo from "@/view/videoPlayStandard/component/videoPlayerStandard/component/FlvVideoStandard";
    import {mapState} from 'vuex';


    const API_URL = window.PONY.media.apiUrl;
    /**
     * @Author: xieyj
     * @Email: 
     * @Date: 2022/3/16 09:54
     * @LastEditors: 
     * @LastEditTime: 
     * @Description:
     */
    export default {
        name: "playbackPlayerStandard",
        _i18Name: 'videoPlayerPony',
        mixins: [strictEnd, fullscreen, log],
        components: {
            FlvVideo,
            CustomSlider
        },
        props: {
            vehicleId: {type: [String, Number],},
            terminalCode: {type: String,},
            chn: {type: Number, default: 0},// start at 0
            startTime: {type: String}, // YYYYMMDDHHmmss 格式
            endTime: {type: String},// YYYYMMDDHHmmss 格式
            play_code:{type:Number,default:0},
            level:{type:Number,default:0},

            label: {type: String,},
            autoPlay: {type: Boolean, default: false,},
            labelAlwaysShow: {type: Boolean, default: false,},
            showClose: {type: Boolean, default: false,},
            enableFullScreen: {type: Boolean, default: false,},
            disabled: {type: Boolean, default: false,}
        },
        data() {
            return {
                
                //录像的状态
                recordStatus:false,
                startTime2:null,
                frameId:null,
                stream:null,
                recorder:null,
                canvasContext:null,
                videoElement:null,
                canvasElement:null,
                chunks:[],
                videoTime:'00:00:00',
                timeRecord:null,
                timeOut:null,
                firstLoad: true,//该参数是否第一次加载，决定图标是三角按钮还是刷新按钮
                loaded: false,//当前是否有请求的加载的流
                //相关状态，不一定与内部video标签一致
                isPlaying: false,

                currentSpeed: 0,
                playBack: {
                    offset: 0,
                    progress: 0,
                    bufferedProgress: 0,
                    currentTime: 0,
                    totalTime: 0,
                    loading: false,
                    timer: null,
                    backSpeed:null,
                    volumeProcess:0,
                    speed: 0,
                    speedValue: [1, 2, 4, 8, 16],
                },

                player: null,//指向 $refs['player']
                sourceLoading: false,
                stopLoading: false,
                controlShow: false,
                loadAjax: null,
                url: '',
                hasAudio: false,
                //上一次的参数
                lastParams: {
                    url:undefined
                    // terminalCode: undefined,
                    // chn: undefined,
                },

                showLog: false,
                showDecodeInfo: false,
                contextMenuOptions: [
                    {
                        label: '显示日志',
                        handler: () => {
                            this.showLog = !this.showLog;
                        }
                    },
                    {
                        label: '显示解码信息',
                        handler: () => {
                            this.showDecodeInfo = !this.showDecodeInfo;
                        }
                    }
                ],
                controlTimeout: null,
                //打断加载行为
                interruptLoadSourceHandle: null

            }
        },
        computed: {
            
            ...mapState('vehicle', ['basicByVehicleId']),
		    ...mapState("auth", ["token"]),

            startDate: function () {
                return moment(this.startTime, 'YYYYMMDDHHmmss').toDate();
            },
            endDate: function () {
                return moment(this.endTime, 'YYYYMMDDHHmmss').toDate();
            },
            diffTime: function () {
                return this.endDate - this.startDate;
            },
            offsetTime: function () {
                return this.diffTime * this.playBack.offset / 100 / 1000;
            },
            sourceParams: function () {
                if (this.vehicleId && this.terminalCode && this.chn !== undefined && this.startTime && this.endTime) {
                    return `${this.vehicleId}-${this.terminalCode}-${this.chn}-${this.startTime}-${this.endTime}`;
                } else {
                    return '';
                }
            },
            loading: function () {
                return this.sourceLoading || this.stopLoading
            },
            volumeHint: function () {
                if (this.loaded && !this.hasAudio) {
                    return this.$ct('notSupportAudio')
                } else {
                    return ''
                }
            },
        },
        watch: {
            showLog: function (val) {
                this.contextMenuOptions[0].label = `${val ? '隐藏' : '显示'}日志`;
            },
            showDecodeInfo:function(val){
                this.contextMenuOptions[1].label = `${val ? '隐藏' : '显示'}解码信息`;
            },
            url:function(val,oldV){
                this.lastParams.url = oldV
            },
            sourceParams: {
                handler: function (val, oldV) {
                    // if (oldV) {
                    //     let [vehicleId, terminalCode, chn] = oldV.split('-');
                    //     this.lastParams.terminalCode = terminalCode;
                    //     this.lastParams.chn = parseInt(chn);
                    // }
                    this.firstLoad = true;
                    val && this.autoPlay && this._loadSource();
                },
                // immediate: true,//用onPlayerMounted替代
            },
            controlShow: function (val, oldV) {
                if (!oldV && val) {
                    clearTimeout(this.controlTimeout);
                    this.controlTimeout = setTimeout(() => {
                        this.controlShow = false;
                    }, 3000) //3s后自动隐藏
                }
            }
        },
        mounted(){
        },
        methods: {
            changeVolume(val){
                if (!this.loaded || !this.hasAudio) {
                    return;
                }
                if(val == 0){
                    this.player.toggleMuted(true);
                }else {
                    this.player.setVolume(val/100)
                    this.player.toggleMuted(false);
                }

            },
            //录像
            startRecord(){
                //已经在录像的
                if(this.recordStatus){
                    this.stopScreen()
                }else{
                    this.startScreen()
                }
            },
            //开始录像
            async startScreen(){
                this.recordStatus = true

                this.canvasElement = document.querySelector('#canvas');
                this.videoElement = document.querySelector('#warp').querySelector("video");
                this.canvasContext = this.canvasElement.getContext("2d");
                // this.canvasContext.fillStyle = "deepskyblue";
                // this.canvasContext.fillRect(0, 0, this.canvasElement.width, this.canvasElement.height);
                this.chunks = [];
        
                this.frameId = null;
                //创建MediaRecorder，设置媒体参数
                const stream = this.canvasElement.captureStream(60);
    8
                this.recorder = new MediaRecorder(stream, {
                    mimeType: 'video/webm;codecs=vp8',
                    videoBitsPerSecond : 6500000
                });
                //收集录制数据
                this.recorder.ondataavailable = e => {
                    this.chunks.push(e.data);
                    // this.recorder.blobs.push(e.data);
                };
                // this.recorder.blobs = []
                this.startTime2 = new Date().getTime()
                this.recorder.start(10);
                this.drawFrame()
                this.timeDuration()
            

            },
            drawFrame() {
                this.canvasContext.drawImage(this.videoElement, 0, 0, this.canvasElement.width, this.canvasElement.height);
                this.frameId = requestAnimationFrame(this.drawFrame);
            },
            stopScreen(){
                this.recorder.stop()
                this.recordStatus = false
                cancelAnimationFrame(this.frameId)
                this.download()
                this.recorder = null
                clearInterval(this.timeRecord)
                this.timeRecord = null
            },
            timeDuration(){
                this.timeRecord = setInterval(()=>{
                    let second = (new Date().getTime() - this.startTime2)
                    this.videoTime = this.SecondFormat(second)
                },1000)
            },
            download(){
                // let recorderFile = null
                let blob = new Blob(this.chunks);
                // console.log(blob);
                // ysFixWebmDuration(blob,this.videoTime,(fixedBlob)=>{
                //     console.log(fixedBlob);
                //     recorderFile = fixedBlob
                // })
                    let url = window.URL.createObjectURL(blob);
                    let link = document.createElement("a");
                    link.href = url;
                    link.download =  this.basicByVehicleId[this.vehicleId].plate + '通道'+this.chn+ ".mp4";
                    link.style.display = "none";
                    document.body.appendChild(link);
                    link.click();
                    link.remove();
            },
            _onEnded() {
                this.isPlaying = false
                this.currentSpeed = 0;
                this.playBack.currentTime = this.offsetTime;
                this.playBack.progress = this.playBack.offset;
                // this.playBack.offset = 0;
                // this.playBack.bufferedProgress = 0;
            },
            _onCanPlay() {
                this.playBack.progress = 0;
                this.playBack.currentTime = 0;
            },
            _onTimeUpdate(time, event) {
                this.playBack.currentTime = Math.min((time + this.offsetTime), this.playBack.totalTime);
                this.playBack.progress = (time + this.offsetTime) / this.playBack.totalTime * 100;
                this.$emit('currentTimeChange', this.playBack.currentTime);
            },
            _setBufferProgress(time, event) {
                // this.playBack.bufferedTime = Math.min((time + this.offsetTime), this.playBack.totalTime);
                this.playBack.bufferedProgress = (time + this.offsetTime) / this.playBack.totalTime * 100;
            },
            async _speedChange(speed) {
                this.currentSpeed = speed;
            },
            //确保首次执行时$refs['player']已存在
            _onPlayerMounted() {

                this.player = this.$refs['player'];
                this.sourceParams && this.autoPlay && this._loadSource();
                this.log('播放器初始化完成');
            },
            async _loadSource() {

                await this._freeSource();
                await this._requestSource();


            },
            /*
            申请占用此设备通道，
            目前实时视频的推流在不同的客户端会复用,
            这是一个非常耗时的流程，需要实现其中任意阶段可暂停
            */
            async _requestSource() {

                this.sourceLoading = true;
                try {
                    this.log('正在请求视频资源...');
                    const startTime = moment(this.startTime, 'YYYYMMDDHHmmss'),
                          offsetStartTime = !this.playBack.offset ?
                              this.startTime : startTime
                                  .add(moment(this.endTime, 'YYYYMMDDHHmmss').diff(startTime, 'seconds') * this.playBack.offset / 100, 'seconds')
                                  .format('YYYYMMDDHHmmss');



                //    let ajax = $.post(API_URL, JSON.stringify({
                //         cmd: "playback",
                //         terminal_no: this.terminalCode,
                //         channel_no: this.chn,
                //         play_code: 0,
                //         level: 0,
                //         start_time: offsetStartTime,
                //         end_time: this.endTime,
                //     }));
                    let ajax = $.ajax({
                        url:API_URL,
                        type: 'post',
                        data:JSON.stringify({
                            cmd: "playback",
                            terminal_no: this.terminalCode,
                            channel_no: this.chn,
                            play_code: this.play_code,
                            level: this.level,
                            start_time: offsetStartTime,
                            end_time: this.endTime,
                        }),
                        headers: {
                            Authorization:'token '+ this.token,
                        },
                    });
                    this.loadAjax = ajax;
                    let result
                    if(this.timeOut){
                        clearTimeout(this.timeOut)
                    }
                    this.timeOut = setTimeout(()=>{
                        if(!result){
                            this.$error("获取视频地址失败,超时!");
                            ajax.abort()
                            this.loadAjax = null;
                            return
                        }
                    },60*1000)
                    result = await ajax;
                    this.loadAjax = null;
                    if (typeof result !== 'object') {
                        result = JSON.parse(result)
                    }
                    if (result.code == 0) {
                        clearTimeout(this.timeOut)
                        this.log('成功获取视频地址，开始缓冲...');
                        this.url = result.data;
                        this.hasAudio = result.hasAudio;
                        this.firstLoad = false;
                        await this.$api.setVideoLog({
                            uri: this.url, // 视频接口地址
                            type: 502, // 日志类型(501: 实时视频; 502: 录像回放)
                            vehicleId: this.vehicleId // 车辆id
                        })
                        await Promise.race([
                            //允许中断操作
                            new Promise((resolve, reject) => {
                                this.interruptLoadSourceHandle = {resolve, reject};
                            }),
                            Promise.all([
                                // (async () => {
                                //     try {
                                //         //向ws订阅流的相关信息 这一步骤不是必要的
                                //         //有可能会得不到回复
                                //         await this._subscribeVideoInfo(this.url);
                                //     } catch (e) {
                                //         //报错的话 就忽略这次 订阅推流结束事件的行为（忽略严格模式）
                                //         this.videoStream._resolve()
                                //     }
                                // })(),
                                //加载视频
                                this.$refs['player'].load(this.url, this.hasAudio),
                            ]),
                        ])
                        this.interruptLoadSourceHandle.resolve();
                        this.interruptLoadSourceHandle = null;
                        this.loaded = true;
                        this.log('开始播放视频.');
                        this.playBack.totalTime = Math.ceil(this.diffTime / 1000);
                        this.playBack.speed = 0;
                        this.playBack.backSpeed = 0;
                        if(this.play_code == 1){
                            this.playBack.speed = this.level - 1
                        }else {
                            this.playBack.backSpeed = this.level - 1

                        }

                    } else {
                        clearTimeout(this.timeOut)
                        throw {from: 'api', msg: result.message}
                    }
                } catch (e) {
                    clearTimeout(this.timeOut)
                    switch (true) {
                        case e.statusText === 'abort':
                            break;
                        case e.from === 'flvjs' && e.error === 'NetworkError':
                            this.$error('视频地址已失效.')
                            this.log(`请求视频资源失败,原因：视频地址已失效.`);
                            break;
                        case e.from === 'api':
                            this.$error(this.$ct('messageInfo.2') + `: ${e.msg}`);
                            this.log(`请求视频资源失败,原因：${e.msg}`);
                            break;
                        case e.from === 'interrupt':
                            break;
                        default:
                            console.error(e);
                    }
                    //报错的话 就忽略这次 订阅推流结束事件的行为（忽略严格模式）
                    this.videoStream._resolve instanceof Function && this.videoStream._resolve();
                } finally {
                    this.sourceLoading = false;
                }
            },
            /*释放设备通道资源
            * strict= true 时采取严格模式，即明确得到设备断开提示才会结束*/
            async _freeSource(strict = true) {
                try {
                    this.stopLoading = true;

                    this.loadAjax && this.loadAjax.abort();
                    if (this.interruptLoadSourceHandle) {
                        this.interruptLoadSourceHandle.reject({from: 'interrupt', msg: '人工打断'});
                        this.interruptLoadSourceHandle = null;
                    }
                    if (this.loaded) {
                        this.loaded = false;
                        this.player.unload();
                        this.log('正在释放请求的资源...')

                        //1078原来的版本，先注释掉，，，，，
                        // await $.post(API_URL, JSON.stringify({
                        //     cmd: "control",
                        //     terminal_no: this.lastParams.terminalCode || this.terminalCode,
                        //     channel_no: this.lastParams.chn === undefined ? this.chn : this.lastParams.chn,
                        //     control_code: 2,
                        //     time: '',
                        //     level: 1,
                        // }));
                        // await $.post(API_URL, JSON.stringify({
                        //     cmd: "controlPlayback",
                        //     id:this.lastParams.url? this.lastParams.url.split('/')[this.lastParams.url.split('/').length-1].slice(0,-4): this.url.split('/')[this.url.split('/').length-1].slice(0,-4),
                        //     // terminal_no: this.lastParams.terminalCode || this.terminalCode,
                        //     // channel_no: this.lastParams.chn === undefined ? this.chn : this.lastParams.chn,
                        //     control_code: 2,
                        //     // time: '',
                        //     // level: 1,
                        // }));

                        await $.ajax({
                            url:API_URL,
                            type: 'post',
                            data:JSON.stringify({
                                cmd: "controlPlayback",
                                id:this.lastParams.url? this.lastParams.url.split('?')[0].split('/')[this.lastParams.url.split('?')[0].split('/').length-1].slice(0,-4): this.url.split('?')[0].split('/')[this.url.split('?')[0].split('/').length-1].slice(0,-4),
                                control_code: 2,
                            }),
                            headers: {
                                Authorization:'token '+ this.token,
                            },
                        });

                        this.stopLoading = false;

                        this.url = '';
                        this.hasAudio = false;
                        this.lastParams = {
                            url:undefined
                            // terminalCode: undefined,
                            // chn: undefined,
                        }
                        if (strict) {
                            this.log('严格模式：等待设备停止推流...')
                            let timeStart = Date.now();
                            await this.videoStream.waitForEnd;
                            this.log(`设备已断开,耗时${Date.now() - timeStart}ms.`)
                        }
                    }
                } catch (e) {
                    throw e
                } finally {
                    this.stopLoading = false;

                }
            },

            //特殊处理特殊实现
            //在缓冲区内就拖动跳转，在外就重新请求响应流地址
            setProgress(percent) {
                const playBack      = this.playBack,
                      originPercent = percent;
                if (playBack.offset) {
                    percent = (percent - playBack.offset) / (100 - playBack.offset) * 100
                }
                playBack.progress = originPercent;
                // if (percent >= 0 && originPercent <= playBack.bufferedProgress) {
                    //这是缓存内的进度条
                //     let second = Math.ceil(this.diffTime * (originPercent - playBack.offset) / 100 / 1000);
                //     this.player.seekTo(second);
                // } else {
                    // this.reload(originPercent);
                // }
                this.processReload(originPercent)
            },


            //格式化进度条文字
            formatTip(percent) {
                // return this.formatTime(Math.round(percent * this.playBack.totalTime / 100));
                return moment(this.startDate).add(this.diffTime * percent / 100, 'ms').format('HH:mm:ss');
            },
            //秒数格式化成 HH:mm:ss
            formatTime(second) {
                // let mTemp = Math.floor(second / 60);
                // let m = ('0' + mTemp).slice(-(mTemp > 10 ? mTemp.toString(10).length : 2));
                // let s = ('0' + Math.round(second) % 3600 % 60).slice(-2);
                // return `${m}:${s}`
                return moment(this.startDate).add(second, 'seconds').format('HH:mm:ss');
            },

            loadSource(offset = 0) {
                this.playBack.offset = offset;
                if (this.sourceParams) {
                    this._loadSource();
                }
            },
            async processReload(offset = 0){
                this.playBack.offset = offset;
                const startTime = moment(this.startTime, 'YYYYMMDDHHmmss'),
                          offsetStartTime = !this.playBack.offset ?
                              this.startTime : startTime
                                  .add(moment(this.endTime, 'YYYYMMDDHHmmss').diff(startTime, 'seconds') * this.playBack.offset / 100, 'seconds')
                                  .format('YYYYMMDDHHmmss');
                let result = await this.sendVideoCommand(5,offsetStartTime)
                // if (result.code === 0) {
                //     // this.player.play()
                // }
                
            },
            reload(offset = 0) {
                this.playBack.offset = offset;
                return this._loadSource();
            },
            async stop() {
                this.isPlaying = false;
                this.$emit('stop', this.chn);
                await this._freeSource();
            },
            async guanjianzhen(){
                this.sourceLoading = true
                try {
                    let result = await this.sendVideoCommand(6)
                    if(result.code == 0){
                        // this.videoPlayer()
                        this.$refs['player'].load(this.url, false)

                    }
                }catch(e){
                    this.$error(e)
                }finally{
                    this.sourceLoading = false
                }
            },
            async videoPause(){
                let result = await this.sendVideoCommand(1)
                if (result.code === 0) {
                    this.player.pause()
                } 
            },
            //只需要控制码的控制命令
            async sendVideoCommand(type,time,level){
                let params = {
                    cmd: "controlPlayback",
                    id:this.url.split('?')[0].split('/')[this.url.split('?')[0].split('/').length-1].slice(0,-4),
                    control_code: type,
                }
                if(time && time !== 0){
                    params.time = time
                }
                if(level && level !== 0){
                    params.level = level
                }
                return await $.ajax({
                        url:API_URL,
                        type: 'post',
                        data:JSON.stringify(params),
                        headers: {
                            Authorization:'token '+ this.token,
                        },
                    });
            },
            async videoPlayer(){
                let result = await this.sendVideoCommand(0)
                if (result.code === 0) {
                    this.player.play()
                    this.$refs['player'].load(this.url, this.hasAudio)

                } 
            },
            async guanjianzhentui(){
                this.sourceLoading = true

                try {
                    
                    let result = await this.sendVideoCommand(4)
                    if (result.code === 0) {
                        this.videoPlayer()
                        this.$refs['player'].load(this.url, this.hasAudio)

                        // this.player.play()
                    }
                }catch(e){

                }finally{
                    this.sourceLoading = false
                }
            },
            toggleSound() {
                if (!this.loaded || !this.hasAudio) {
                    return;
                }
                this.player.toggleMuted();
            },
            screenshot() {
                if (this.loaded) {
                    this.player.screenShot(this.symbol);
                }
            },
            //目前api只接受简单请求，故用jquery提交请求
            // //todo 去除jquery依赖
            // async sendCommand(ctr, time, level) {
                
            //         return await $.ajax({
            //             url:API_URL,
            //             type: 'post',
            //             data:JSON.stringify({
            //                 cmd: "controlPlayback",
            //                 id:this.url.split('?')[0].split('/')[this.url.split('?')[0].split('/').length-1].slice(0,-4),
            //                 control_code: ctr,
            //                 time:time,
            //                 level,
            //             }),
            //             headers: {
            //                 Authorization:'token '+ this.token,
            //             },
            //         });
                    
               
            //     // return await $.post(API_URL, JSON.stringify({
            //     //     cmd: "control",
            //     //     terminal_no: this.videoInfo.terminalCode,
            //     //     channel_no: this.chn + 1,
            //     //     control_code: ctr,
            //     //     time,
            //     //     level,
            //     // }));
            // },
            //倍数播放
            async fastForward(speed) {
                // if (this.isStopped) return;
                //本地实现倍速
                // if (speed === 5) return;
                // this.player.setPlayBackRate(this.playBack.speedValue[speed])
                // this.playBack.speed = speed;
                // //简单低端机cpu性能不够 拖动后不能够继续播放的bug
                // //ps: 目前能触发的机器有 任意配置的win7下的chrome  cpu <= i5-6400的win10（i5-7400就可以完美跳转）
                // //高分辨率视频源下全机型都能触发
                // await this.$utils.sleep(200);
                // this.player.play();
                let speedValue = speed+1
                this.sourceLoading = true

                //推流实现倍速
                let oldValue = this.playBack.speed;
                let result = await this.sendVideoCommand(3, '', speedValue)
                if (result.code === 0) {
                    // this.videoPlayer()
                    if(speedValue>1){
                        this.$refs['player'].load(this.url, false)
                        this.player.play()
                    }else {
                        this.$refs['player'].load(this.url, this.hasAudio)
                        this.player.play()
                    }
                    
                    
                    this.playBack.speed = speed;
                } else {
                    this.playBack.speed = oldValue;
                    this.$error(result.message)
                }
                this.sourceLoading = false

            },
            async backForward(backSpeed){
                let speedValue = backSpeed+1
                this.sourceLoading = true

                //推流实现倍速
                let oldValue = this.playBack.backSpeed;
                let result = await this.sendVideoCommand(4, '', speedValue)
                if (result.code === 0) {
                    // this.videoPlayer()
                    
                    this.$refs['player'].load(this.url, false)

                    this.playBack.backSpeed = backSpeed;
                    // this.player.play();
                } else {
                    this.playBack.backSpeed = oldValue;
                    this.$error(result.message)
                }
                this.sourceLoading = false
            }
        },
        created() {
            window.addEventListener("beforeunload", this.stop);
        },
        beforeDestroy() {
            window.removeEventListener("beforeunload", this.stop);
            this._freeSource(false);
            clearTimeout(this.controlTimeout);
        },
    }
</script>

<style lang="scss">
    @import "./player";
</style>
<style scoped lang="scss">
   #canvas {
        width: 100%;
        height: 100%;
    }
    .videoTime {
        position: absolute;
        right: 0;
        top: -5px;
        background-color: black;
        color: red;
        display: inline-block;
        line-height: 20px;
        font-style: normal;
        font-size: 12px;
        padding: 3px 8px;
        border-radius: 3px;

    }
</style>
<template>
    <Layout class="videoFileDown" :content-loading="loading" :has-color="true">
        <template slot="aside">
            <div class="query-top">
                <ElementTree type="vehicleWithChannel" state onlineFilter onlineCountTip @node-click="playbackTreeClick" @node-dbl-click="playbackTreeDblClick" class="flex-grow bg bg--light box-shadow"></ElementTree>
            </div>
            <div class="query-bottom bg bg--light box-shadow">
                <StartEndTime itemHeight="35"
                              v-model="selectStartEndTime" valueFormat="timestamp" timeType="datetime" :isLimit="true">
                </StartEndTime>
                <div class="query-item">
                    <label>报警标志:</label>
                    <DictionarySelect :all="true" code="mini_media_event_code" v-model="query.eventCodeList" placeholder="选择报警标志" multiple ></DictionarySelect>
                </div>
                <div class="query-item">
                    <label>资源类型:</label>
                    <el-select v-model="query.media_type" class="query-input" type="datetime">
                        <el-option label="音视频" :value="0"></el-option>
                        <el-option label="音频" :value="1"></el-option>
                        <el-option label="视频" :value="2"></el-option>
                        <el-option label="音频或视频" :value="3"></el-option>
                    </el-select>
                </div>
                <div class="query-item">
                    <label>码流类型:</label>
                    <el-select v-model="query.stream_type" class="query-input" type="datetime">
                        <el-option label="所有码流" :value="0"></el-option>
                        <el-option label="主码流" :value="1"></el-option>
                        <el-option label="子码流" :value="2"></el-option>
                    </el-select>
                </div>
                <div class="query-item">
                    <label>存储器类型:</label>
                    <el-select v-model="query.storage_type" class="query-input" type="datetime">
                        <el-option label="所有存储器" :value="0"></el-option>
                        <el-option label="主存储器" :value="1"></el-option>
                        <el-option label="灾备存储器" :value="2"></el-option>
                    </el-select>
                </div>

                <div class="query-item">
                    <el-button type="primary" style="width: 100%;" @click="getRecordListAll()">查询</el-button>
                </div>
            </div>
        </template>
        <template slot="content">
            <el-table empty-text="暂无数据" v-loading="loading" ref="videoTable" :data="videoTable.data" border stripe height="100%" highlight-current-row>
                <el-table-column type="index" label="序号" width="60">
                </el-table-column>
                <el-table-column label="通道号" min-width="70" :filters="tableFilter" :filter-multiple="false" :filter-method="positionFilter">
                    <template slot-scope="{row}">
                        <span>{{ '通道'+ row.channel_no }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="开始时间" min-width="130">
                    <template slot-scope="scope">
                        <span>{{ scope.row | toStart }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="结束时间" min-width="130">
                    <template slot-scope="scope">
                        <span>{{ scope.row | toEnd }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="end" label="资源类型" min-width="100">
                    <template slot-scope="scope">
                        <span>{{ mediaType[scope.row.media_type] }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="end" label="码流类型" min-width="100">
                    <template slot-scope="scope">
                        <span>{{ streamType[scope.row.stream_type] }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="end" label="存储器类型" min-width="100">
                    <template slot-scope="scope">
                        <span>{{ storageType[scope.row.storage_type] }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="end" label="报警标志" min-width="100">
                    <template slot-scope="scope">
                        <span>{{ scope.row.alarmType }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="len" label="文件大小(MB)" min-width="90">
                    <template slot-scope="scope">
                        <span>{{ scope.row.size | toOmen }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="时长" min-width="100">
                    <template slot-scope="scope">
                        <span>{{ toTime(scope.row) }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="file" label="名称" show-overflow-tooltip min-width="140">
                    <template slot-scope="scope">
                        <span>{{ toFileName(scope.row) }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="状态" min-width="120">
                    <template slot-scope="scope">
                        <span>{{ scope.row.status !== undefined ?fileState[scope.row.status] : '正在加载' }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="进度" min-width="150">
                    <div class="video-control" slot-scope="{row,$index}">
                        <!-- loading-->
                        <span v-if="row.loading"><i class="el-icon-loading"></i>正在加载</span>
                        <!--正在下载-->
                        <template v-else-if="row.status === 0 || row.status === 5">
                            <template v-if="row.progress === -1">
                                <!-- <span v-if="row.ftp==1"></span>
                                <span v-else>正在获取进度信息</span> -->
                                <span>正在获取进度信息</span>
                            </template>
                            <template v-else>
                                <el-progress :percentage="row.progress" :text-inside="true" style="flex-grow: 1" :stroke-width="14"></el-progress>
                            </template>
                            <i :class="['pony-iconv2',row.status == 5 ? 'pony-bofang' : 'pony-zanting']" title="暂停下载" @click="suspendDownload(row)" v-if="row.status == 0 || row.status == 5"></i>
                            <i class="pony-iconv2 pony-guanbi" title="取消下载" @click="cancelDownload(row)"></i>
                        </template>
                        <!--下载完成-->
                        <template v-else-if="row.status === 4">
                            <span class="font-blue" style="color:#67C23A" @click="download(toFileName(row),row.url)">
                                点击请求上传
                                <i class="pony-iconv2 pony-xiazai" style="cursor: pointer;margin-left: 5px"></i>
                            </span>
                            <i class="pony-iconv2 pony-shuaxin" title="重新上传" @click="downloadSelectTime(row,true)"></i>
                        </template>
                        <!--请求上传-->
                        <span v-else class="font-blue" @click="downloadSelectTime(row)">
                            点击请求上传
                            <i class="pony-iconv2 pony-xiazai" style="cursor: pointer;margin-left: 5px"></i>
                        </span>
                    </div>
                </el-table-column>
            </el-table>

        </template>
        <PonyDialog v-model="timeShow.show" width="330" okText="确认上传" @close="timeShow.show = false" @confirm="confirmUpload" title="上传视频">
                <div class="search-wrap ">
                    <div class="query-item">
                        <label>开始时间：</label>
                        <el-date-picker v-model="timeShow.startTime" class="query-input" type="datetime"  value-format="yyyy-MM-dd HH:mm:ss"
                        :picker-options="pickeroptions1">
                        </el-date-picker>
                    </div>
                    <div class="query-item">
                        <label>结束时间：</label>
                        <el-date-picker v-model="timeShow.endTime" class="query-input" type="datetime"  value-format="yyyy-MM-dd HH:mm:ss"
                        :picker-options="pickeroptions2"
                        >
                        </el-date-picker>
                    </div>
                    <div class="query-item">
                        <label>下载环境:</label>
                        <el-select v-model="timeShow.ftpMethod" multiple style="width:215px" collapse-tags>
                            <el-option  label="WI-FI下可下载" :value="1"></el-option>
                            <el-option  label="LAN连接时可下载" :value="2"></el-option>
                            <el-option  label="3G/4G连接时可下载" :value="3"></el-option>
                        </el-select>
                    </div>

                </div>
            </PonyDialog>
        <PonyDialog :show="videoShow" :title="title"
                :hasFooter="false"
                @close="closeVideo"
                :isFullscreen="true"
                :content-style="'min-height: 550px'"
                :width="900"
        >
            <PlaybackPopup
                v-if="!params.type"
                ref="player"
                :vehicleId="playVideo.vehicleId"
                :terminalCode="playVideo.terminalCode"
                :chn="playVideo.chn"
                :startTime="playVideo.startTime"
                :endTime="playVideo.endTime"
                >

            </PlaybackPopup>
            <VideoPlayer
                v-else
                ref="player"
                @ready="eventEmitter('ready', $event)"
                :options="videoOption"
            >
            </VideoPlayer>
        </PonyDialog>

    </Layout>
</template>
<script src="html5plus://ready"/>
<script>
import VideoPlayer from "@/components/alarmvideo/VideoPlayer";
import PlaybackPopup from '@/view/videoPlay/component/videoPlayerV2/component/PlayBackPopup'
import DictionarySelect from '@/components/common/DictionarySelect'
import {mapState} from 'vuex'
import StartEndTime from "@/components/common/StartEedTime";
import moment from "moment";

const API_URL = window.PONY.media.apiUrl;
export default {
    name: 'videoFileDown',
    components: {DictionarySelect ,PlaybackPopup ,VideoPlayer,StartEndTime},
    data () {
        const defaultPlayVideo = {
            vehicleId:'',
            terminalCode:'',
            chn:0,
            startTime:'',
            endTime:''
        }
        return {
            wifiStatus:null,
            timeShow: {
                show: false,
                startTime: '',
                endTime: '',
                ftpMethod:[1],
                rowData: null,
                reupload: false

                // chn:0
            }, //设置下载时间的弹框
            fileState: {
                0: "正在上传",
                4: "上传完成",
                1: "上传失败",
                3: "待上传至服务器",
                5: '暂停上传',
            },
            currentVehicle:null,
            currentVehicleQuery:null,

            channelNoList:[],
            videoShow:false,
            currentRecordAjax:null,
            title:'',
            loading:false,
            currentTaskId:null,
            statusDes:[
                '成功',
                '超时'
            ],
            //本地播放时
            videoObj:{
                url:'',
            },
            timeInter:null,
            playVideo:JSON.parse(JSON.stringify(defaultPlayVideo)),
            defaultPlayVideo,
            params:{
                start: moment().startOf('days').format("YYYY-MM-DD HH:mm:ss"),
                end: moment().endOf('days').format("YYYY-MM-DD HH:mm:ss"),
                type:0,

                eventCodeList:[null],
                version:2,
                vehicleIdList:[]
            },
            // fromType:['推流','ftp','平台录制'],
            mediaType:['音视频','音频','视频','视频或音频'],
            streamType:['主码流或子码流','主码流','子码流'],
            storageType:['主存储器或灾备存储器','主存储器','灾备存储器'],


            videoTable:{
                data:[],
                page:1,
                size:30,
                show:true

            },
            timeOut:null,
            query:{
                start: moment().startOf('days').format("YYYY-MM-DD HH:mm:ss"),
                end: moment().endOf('days').format("YYYY-MM-DD HH:mm:ss"),
                type:0,
                media_type:0,
                stream_type:0,
                storage_type:0,
                eventCodeList:[null]
            },
            selectStartEndTime: [
                moment().startOf("day").valueOf(),
                moment().endOf("day").valueOf(),
            ],
        };
    },
    filters: {
        toStart(value) {
            if (value) {
                let startTime = value.start_time;
                return moment(startTime, 'YYYYMMDDHHmmss').format('YYYY-MM-DD HH:mm:ss');
            }
        },
        toEnd(value) {
            if (value) {
                let endTime = value.end_time;
                return moment(endTime, 'YYYYMMDDHHmmss').format('YYYY-MM-DD HH:mm:ss');
            }
        },
        toOmen(value) {
            if (value) {
                return (value / 1024 / 1024).toFixed(2);
            }
        },

    },
    computed: {
        ...mapState('vehicle', ['basicByVehicleId']),
        ...mapState('dictionary', ['dictionary']),
		...mapState("auth", ["token"]),

         videoOption() {
            return { sources: [{ type: "video/mp4", src: this.videoObj.url }] };
        },
        pickeroptions1: function(){
            let strStart = moment(this.timeShow.startTime).format('HH:mm:ss')
            let strEnd = moment(this.timeShow.endTime).format('HH:mm:ss')
            return{
            selectableRange: strStart +"-" + strEnd
            }

            // selectableRange:time=> {
            //     time.getTime() - new Date(moment(this.timeShow.endTime)).getTime();
            // }
        },
        tableFilter: function () {
            return this.channelNoList.map(item => {
                return {
                    text: '通道'+ item,
                    value: item,
                }
            })
        },


    },
    watch:{
        'query.eventCodeList':function(newVal,oldVal){
            let newindex =  newVal.indexOf(null),oldindex =  oldVal.indexOf(null);   //获取newval和oldval里null的索引,如果没有则返回-1
            if(newindex != -1 && oldindex == -1 && newVal.length > 1)                       //如果新的选择里有勾选了选择所有选择所有 则 只直线勾选所有整个选项
                this.query.eventCodeList = [null];
            else if(newindex != -1 && oldindex != -1 && newVal.length > 1)                 //如果操作前有勾选了选择所有且当前也选中了勾选所有且勾选数量大于1  则移除掉勾选所有
                this.query.eventCodeList.splice(newVal.indexOf(null),1)
        },
        'selectStartEndTime':function (newVal, oldVal){
            this.query.start = moment(newVal[0]).format("YYYY-MM-DD HH:mm:ss")
            this.query.end = moment(newVal[1]).format("YYYY-MM-DD HH:mm:ss")
        }
    },
    async mounted() {

        // this.wifiStatus = plus.networkinfo.getCurrentType()
        // console.log(this.wifiStatus,'this.wifiStatus');

        // document.addEventListener("plusready", ()=>{
        //     console.log(999);
        //     console.log(plus.networkinfo.getCurrentType());
        //     document.addEventListener("netchange", ()=>{
        //         var nt = plus.networkinfo.getCurrentType();
        //         console.log(nt);
        //     }, false);

        // }, false);

        // function onNetChange(){
        //     var nt = plus.networkinfo.getCurrentType();
        //     switch (nt){
        //         case plus.networkinfo.CONNECTION_ETHERNET:
        //         case plus.networkinfo.CONNECTION_WIFI:
        //         alert("Switch to Wifi networks!");
        //         break;
        //         case plus.networkinfo.CONNECTION_CELL2G:
        //         case plus.networkinfo.CONNECTION_CELL3G:
        //         case plus.networkinfo.CONNECTION_CELL4G:
        //         alert("Switch to Cellular networks!");
        //         break;
        //         default:
        //         alert("Not networks!");
        //         break;
        //     }

    },

    methods: {
        toTime(value) {
            if (value != undefined) {
                let startTime = moment(value.start_time, 'YYYYMMDDHHmmss').toDate();
                let endTime = moment(value.end_time, 'YYYYMMDDHHmmss').toDate();
                let ms = endTime - startTime;
                return this.SecondFormat(ms)
            }

        },
        positionFilter(value, row) {
            return row.channel_no === value;
        },

        downloadSelectTime(row, reupload = false) {
            this.timeShow.show = true
            this.timeShow.startTime = moment(row.start_time, 'YYYYMMDDHHmmss').format('YYYY-MM-DD HH:mm:ss')
            this.timeShow.endTime = moment(row.end_time, 'YYYYMMDDHHmmss').format('YYYY-MM-DD HH:mm:ss')
            this.timeShow.rowData = row
            this.timeShow.reupload = reupload

        },
        confirmUpload() {
            this.$warning('下载' + this.timeShow.startTime + '到' + this.timeShow.endTime + '的视频')
            this.downloadQueue(this.timeShow,this.timeShow.reupload);
            this.timeShow.show = false
        },
        toFileName(value) {
            if (value) {
                if (value.fileName) {
                    return value.fileName;
                } else {
                    return `${this.currentVehicleQuery.plate_no}_${value.channel_no}_${value.start_time}`
                }
            }
        },
        transforAlarmType(data){
            // 数组转10进制
            if(data.includes(null)) return '0000000000000000'
            if(!data.length) return
            let str = []
            for(let i = 0; i < 64; i++) {
                let value = data.includes(i)?'1':'0'
                str.push(value)
            }
            // 二进制转10进制 先转回来! 然后数格子
            let result = str.reverse().join('')
            return parseInt(result,2).toString(16).padStart(16,'0')
        },
        async getRecordListAll() {
            this.currentVehicleQuery = this.currentVehicle
            this.loading = true
            let params = {
                terminal_no: this.currentVehicleQuery.code,
                vehicle_id:this.currentVehicleQuery.vehicle_id,
                start_time: moment(this.query.start).format('YYYYMMDDHHmmss'),
                end_time: moment(this.query.end).format('YYYYMMDDHHmmss'),
            }
            /** 根据对应通道号 轮询获取通道数据  ↓*/
            let event_code = this.transforAlarmType(this.query.eventCodeList)
            let result = []
            // for (let chn = 0; chn < this.channelNoList.length; chn++) {
            if(this.timeOut){
                clearTimeout(this.timeOut)
            }
            let ajax = $.ajax({
                url: API_URL,
                type: 'post',
                data: JSON.stringify(Object.assign(
                    { cmd: 'index' },
                    params,
                    {
                        media_type:this.query.media_type,
                        stream_type:this.query.stream_type,
                        storage_type:this.query.storage_type,
                        event_code,
                    },
                    { channel_no: this.channelNoList.length>1 ? 0 : this.channelNoList[0]},
                )),
                headers: {
                    Authorization: 'token ' + this.token,
                },
            });

            this.currentRecordAjax = ajax;
            this.timeOut = setTimeout(() => {
                this.$error('获取超时！')
                this.abortRecordAjax()
                this.loading = false;
            }, 60 * 1000)
            let res = await ajax
            if (res.code != 0) {
                this.$error(res.message || '超时')
                this.loading = false;
                clearTimeout(this.timeOut)
                this.currentRecordAjax = null;
                return
            }
            this.loading = false;
            result.push(res);
            clearTimeout(this.timeOut)
            this.currentRecordAjax = null;
            // }

            result = result.reduce(function (acc, cur) {
                if (cur.data) {
                    return acc.concat(cur.data);
                } else
                    return acc.concat([])
            }, [])
            // console.log(result);
            /** 根据对应通道号 轮询获取通道数据  ↑*/
            //并且根据通道列表过滤数据
            // console.log(result);
            this.videoTable.data = result.map(item => {
                let byteArr = parseInt(item.event_code,16).toString(2).split('').reverse()
                let ind = byteArr.findIndex(it=>it == 1)
                if(ind != -1){
                    item.alarmType = this.dictionary['mini_media_event_code'][ind].name //先转10进制，在转2进制

                }else {
                    item.alarmType = ''
                }
                return Object.assign(item, {
                    status: 3,
                    url: '',
                    progress: -1,
                    currentSize: -1,
                    fileName: '',
                    loading: false,
                })
            });
            this.videoTable.data = this.videoTable.data.sort((a, b) => parseInt(a.start_time) - parseInt(b.start_time));
            this.$nextTick(() => {
                this.$refs["videoTable"].doLayout();
            });
        },
        abortRecordAjax() {
            if (!this.currentRecordAjax) return;
            if (this.currentRecordAjax instanceof Array) {
                this.currentRecordAjax.forEach(ajax => ajax.abort());
            } else {
                this.currentRecordAjax.abort();
            }
        },
        async playbackTreeClick(data, node, $node) {
            if (data.type === 4) {
                try {
                    this.currentVehicle = await this.$store.dispatch('vehicle/getVehicleInfo', {
                        key: 'id',
                        value: data.id
                    });

                    //设置通道号 状态树才有这个字段，普通车辆树没有
                    this.currentVehicle.chnNo = data.extraInfo.channelCount;

                    let result = await this.$api.getChannelNoByVehicleId({
                        vehicle_id: data.id + ":V2"
                    })
                    this.channelNoList = []
                    result.data.channel_valid_v2.forEach((item, index) => {
                        if (item) {
                            this.channelNoList.push(index + 1)
                        }
                    })
                    return true;
                } catch (e) {
                    this.$info('该车辆不支持视频相关服务，请刷新页面或咨询运维')
                    return false
                }
            }
            if(data.type === 5){
                this.currentVehicle = await this.$store.dispatch('vehicle/getVehicleInfo', {
                        key: 'id',
                        value: node.parent.data.id
                    });
                this.channelNoList = [data.chn]

            }
            return false
        },
        async playbackTreeDblClick(data, node, $node) {
            if (await this.playbackTreeClick(data, node, $node)) {
                this.getRecordListAll();
            }
        },

        toDelete(videoRow){
            this.$confirm('此操作将永久删除该文件, 是否继续?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
            }).then(async () => {
                this.loading = true
                let res = await this.$api.mediaQuery({
                    version:2,
                    type:2,
                    id:videoRow.id
                })
                if(!res || res.status !==200){
                    this.$error(res.message || '删除失败！')
                    return
                }
                this.$success('删除成功')
                this.loading = false
                // 表格里的删掉
                let deviceTableObj = this.deviceTable.data.find(it=>it.vehicle_id == videoRow.vehicleId)
                if(deviceTableObj){
                    let index = deviceTableObj.list.findIndex(it=>it.id == videoRow.id)
                    if(index != -1){
                        deviceTableObj.list.splice(index,1)
                        let itemList = deviceTableObj.list
                        deviceTableObj.mediaCount = itemList.length,
                        deviceTableObj.mediaFileSize = itemList.length?this.fileSize(itemList.map(item=>item.e).reduce((prev,cur)=>prev+cur)):'',
                        deviceTableObj.mediaFileTime = itemList.length?this.MillisecondFormat(itemList.map(item=>item.c - item.b).reduce((prev,cur)=>prev+cur)):''
                    }
                }
            }).catch((err) => {
                this.$message({type: 'error', message: err});
            });
        },

        closeVideo(){
            if(this.params.type){
                this.videoShow = false
                return
            }
            this.playVideo = JSON.parse(JSON.stringify(this.defaultPlayVideo))
            this.$refs['player'].stop()
            this.videoShow = false
        },

        eventEmitter(type, target) {
            if (target) target.play();
        },
        //播放视频
        async toPlayVideo(row) {
            this.title =  `${this.basicByVehicleId[row.vehicleId].plate}-${Number(row.a)}-${moment(row.b).format('YYYY-MM-DD HH:mm:ss')}-${moment(row.c).format('YYYY-MM-DD HH:mm:ss')}`
            this.videoShow = true

            if(this.params.type){
                this.videoObj.url = row.url
                return
            }
            let {vehicleId,terminalCode} = row
            this.playVideo = {
                vehicleId,
                terminalCode,
                chn:Number(row.a),
                startTime:moment(row.b).format('YYYYMMDDHHmmss'),
                endTime:moment(row.c).format('YYYYMMDDHHmmss'),
            }
            // this.videoShow = true
        },
        // 单位的转换
        fileSize(bytes){
            if (isNaN(bytes)) {
                return '';
            }
            let symbols = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
            let exp = Math.floor(Math.log(bytes)/Math.log(2));
            if (exp < 1) {
                exp = 0;
            }
            let i = Math.floor(exp / 10);
            bytes = bytes / Math.pow(2, 10 * i);
            if (bytes.toString().length > bytes.toFixed(2).toString().length) {
                bytes = bytes.toFixed(2);
            }
            return bytes + ' ' + symbols[i];
        },
        getTenSupport(erjinzhi, num){
            if(!num) return
            let str = []
            for(let i = 0; i < erjinzhi; i++) {
                let value = num.includes(i+1)?'1':'0'
                str.push(value)
            }
            // 二进制转10进制 先转回来! 然后数格子
            let result = str.reverse().join('')
            return parseInt(result,2)
        },
        //创建下载任务
        async downloadQueue(row,reupload = false) {

            row.rowData.loading = true;
            try {
                let ftpMethod = this.getTenSupport(3,row.ftpMethod)
                let res = await $.ajax({
                    url: API_URL,
                    type: 'post',
                    data: JSON.stringify({
                        cmd: 'upload',
                        terminal_no: this.currentVehicleQuery.code,
                        vehicle_id:this.currentVehicleQuery.vehicle_id,
                        channel_no: row.rowData.channel_no,
                        start_time: moment(row.startTime, 'YYYY-MM-DD HH:mm:ss').format('YYYYMMDDHHmmss'),
                        end_time: moment(row.endTime, 'YYYY-MM-DD HH:mm:ss').format('YYYYMMDDHHmmss'),
                        media_type: row.rowData.media_type,
                        event_code: row.rowData.event_code,
                        stream_type: row.rowData.stream_type,
                        storage_type: row.rowData.storage_type,
                        ftpMethod:ftpMethod,
                        ftp: true,
                        reupload: reupload
                    }),
                    headers: {
                        Authorization: 'token ' + this.token,
                    },
                });
                row.rowData.status = res.code
                if (res.code === 4) {
                    row.rowData.url = res.data
                    this.download(this.toFileName(row.rowData), res.data)
                } else if (res.code != 0) {
                    this.$error('请求上传失败')
                } else {
                    Object.assign(row.rowData, {
                        status: 0,
                        progress: 0,
                        upload_start: row.startTime,
                        upload_end: row.endTime,
                        id: res.data,
                        ftp: true,
                    });

                    // let uploadedObj = this.downloadTable.data.find(item => item.id == res.data)
                    // //避免重复推入
                    // if (uploadedObj) {
                    //     Object.assign(uploadedObj, {
                    //         ...JSON.parse(JSON.stringify(row.rowData)),
                    //         loading: false,
                    //     })
                    // } else {
                    //     let downObj = JSON.parse(JSON.stringify(row.rowData))
                    //     this.downloadTable.data.push(Object.assign(downObj,{
                    //         start_time: moment(row.startTime, 'YYYY-MM-DD HH:mm:ss').format('YYYYMMDDHHmmss'),
                    //         end_time: moment(row.endTime, 'YYYY-MM-DD HH:mm:ss').format('YYYYMMDDHHmmss'),
                    //         loading: false,

                    //     }))
                    //     // this.downloadTable.data.push({
                    //     //     ...JSON.parse(JSON.stringify(Object.assign(row.rowData,{
                    //     //         start_time: moment(row.startTime, 'YYYY-MM-DD HH:mm:ss').format('YYYYMMDDHHmmss'),
                    //     //         end_time: moment(row.endTime, 'YYYY-MM-DD HH:mm:ss').format('YYYYMMDDHHmmss'),
                    //     //     }))),
                    //     //     loading: false,
                    //     // });
                    // }
                    this.downloadLog(row);
                    this.$success('开始从设备上传视频');
                    clearInterval(this.timeInter)

                    this.timeInter = null

                    this.intervalReflash()
                    // videoWS.subscribeProgressInfo([res.data])
                }
            } finally {
                row.rowData.loading = false;
                this.$forceUpdate()

            }
        },
        //从真实地址下载
        download(name, url) {
            let aLink = document.createElement('a');
            let evt = document.createEvent("HTMLEvents");
            evt.initEvent("click", true, true);//initEvent 不加后两个参数在FF下会报错  事件类型，是否冒泡，是否阻止浏览器的默认行为
            aLink.download = name;
            aLink.href = url;
            aLink.target = '_blank';
            aLink.dispatchEvent(new MouseEvent('click', {bubbles: true, cancelable: true, view: window}));//兼容火狐
        },

        async downloadLog(row) {
            this.$api.downloadVideoLog({
                vehicleId: this.currentVehicleQuery.vehicle_id,
                aisle: row.rowData.channel_no,
                videoStart: row.startTime,
                videoEnd: row.endTime,
            })
        },
        async suspendDownload(row){
            //5是暂停状态,点击继续
            let control_code
            switch (row.status) {
                case 0:
                    control_code = 0
                    break;
                case 5:
                    control_code = 1
                    break;
            }
            row.loading = true
           
            let result = await $.ajax({
                url:API_URL,
                type: 'post',
                data:JSON.stringify({
                    cmd: "controlFTP",
                    terminal_no:this.currentVehicleQuery.code,
                    vehicle_id:this.currentVehicleQuery.vehicle_id,
                    id: row.id,
                    control_code,
                }),
                headers: {
                    Authorization:'token '+this.token,
                },
            });
            if(result.code == 0){
                switch (row.status) {
                case 0:
                    row.status = 5
                    let ids = this.videoTable.data.filter(item => item.status == 0).map(item => item.id)
                    if(!ids.length){
                        clearInterval(this.timeInter)
                        this.timeInter = null
                    }
                    break;
                case 5:
                    row.status = 0
                    if(!this.timeInter){
                        this.intervalReflash()
                    }
                    break;
                }
            }
            row.loading = false

        },
        async cancelDownload(row) {
           
            let result = await $.ajax({
                    url:API_URL,
                    type: 'post',
                    data:JSON.stringify({
                        cmd: "cancelUpload",
                        id: row.id,
                    }),
                    headers: {
                        Authorization:'token '+this.token,
                    },
                 });
            if (result.code === 0) {
                Object.assign(row, {
                    status: 3,
                    url: '',
                    progress: -1,
                    fileName: '',
                    loading: false,
                    id: null,
                })
                this.$forceUpdate()
                let ids = this.videoTable.data.filter(item => item.status == 0).map(item => item.id)
                if(!ids.length){
                    clearInterval(this.timeInter)
                    this.timeInter = null
                }
            } else {
                this.$error('取消下载失败');
            }
        },
        //定时器，定时刷新
        intervalReflash(){
            this.timeInter = setInterval(()=>{
                this.getDownListStatus()
            },3500)
        },
        async getDownListStatus(){
            let ids = []
            if(!this.videoTable.data.length)return
            ids = this.videoTable.data.filter(item => item.status == 0).map(item => item.id)
            if(!ids.length){
                clearInterval(this.timeInter)
                this.timeInter = null
                return
            }
            let params = {
                cmd: 'status',
                id:ids
            }
            let res = await $.ajax({
                type: "POST",
                url: API_URL,
                data: JSON.stringify(params),
                headers: {
                    Authorization: 'token ' + this.token,
                },
            })
            if(res.code != 0){
                this.$error(res.message || '状态查询出错！')
                return
            }
            res.data.forEach(data=>{
                const rows = [];
                const recordItem = this.videoTable.data.find(item => item.id === data.id);
                recordItem && rows.push(recordItem);
                rows.length && rows.forEach(row => {
                    switch (data.status) {
                        case 0: //正在上传
                            Object.assign(row, {
                                status: 0,
                                progress: Math.min(
                                    100,
                                    row.size ? (
                                        row.ftp ? Number((data.size / row.size * 100).toFixed(1)) :
                                            Number(data.percent.toFixed(1))
                                    ) : data.percent ? Number(data.percent.toFixed(1)) : 0

                                ),
                                // progress: Math.min(
                                //     100,
                                //     row.size ? (Number(data.percent.toFixed(1))) : data.percent ? Number(data.percent.toFixed(1)) : 0

                                // ),
                                currentSize: data.size,
                            })
                            break;
                        case 1:  //失败
                            Object.assign(row, {
                                status: 1,
                                loading: false,
                                currentSize: data.size,
                            })
                            break;
                        case 4: //上传完成
                            Object.assign(row, {
                                status: 4,
                                url: data.url,
                                loading: false,
                                currentSize: data.size,
                            })
                            break;
                    }
                })
                this.$forceUpdate()
            })


        },
        dealVideoList(list,itemObj){
            list.forEach(item=>{
                item.b = moment(item.b,'YYYYMMDDHHmmss').valueOf()
                item.c = moment(item.c,'YYYYMMDDHHmmss').valueOf()

                item.plateNo = this.basicByVehicleId[itemObj.vehicle_id].plate
                item.vehicleId = itemObj.vehicle_id
                item.terminalCode = itemObj.terminal_no

                let byteArr = parseInt(item.d,16).toString(2).split('')
                let ind = byteArr.findIndex(it=>it == 1)
                if(ind != -1){
                    item.alarmType = this.dictionary['mini_media_event_code'][ind].name //先转10进制，在转2进制

                }else {
                    item.alarmType = ''
                }

            })
            return list
        },

    },
    beforeDestroy() {
        if(this.timeInter){
            clearInterval(this.timeInter)
            this.timeInter = null
        }

    }
}

</script>

<style lang='scss' scoped>
.videoFileDown {
    .query-top {
            height: calc(100% - 300px);
        }
        .query-bottom {
            margin-top: 5px;
            height: 300px;
            padding: 10px;
            .query-item {
                display: flex;
                height: 40px;
                line-height: 40px;
                justify-content: space-between;
                align-items: center;
                >div {
                    flex-grow: 1;
                }
                >span {
                    font-size: 12px;
                    white-space: nowrap;
                    margin-right: 5px;
                }
            }
        }

        .video-control {
            cursor: pointer;
            display: flex;
            justify-content: center;
            align-items: center;
            i[class^=el-icon-] {
                font-size: 14px;
            }
            i {
                cursor: pointer;
                margin-left: 5px;
            }
        }
        .search-wrap {
            overflow: hidden;
            padding: 10px;

            .query-item {
                display: flex;
                align-items: center;
                padding: 5px;
                .query-more {
                    width: 100%;
                    text-align: center;
                    font-size: 12px;
                    line-height: 1;
                    color: var(--color-primary);
                    cursor: pointer;

                }

                label {
                    font-size: 12px;
                    line-height: 32px;
                    width: 65px;
                }

                .query-input {
                    font-size: 12px;
                    width: calc(100% - 60px);
                }
            }
    }
}
</style>

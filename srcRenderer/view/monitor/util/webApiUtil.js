/*
 * @Description:
 * @Version: 2.0
 * @Autor: wuchuang
 * @Date: 2019-09-23 18:17:15
 * @LastEditors: wuchuang
 * @LastEditTime: 2019-10-10 19:23:45
 */

import PinyinMatch from 'pinyin-match'

// import PinyinMatch from 'pinyin-match'
import {geo2coord} from './webGeoTrans'
import {transSecondToHMS} from './monitorUtil'
import {gcj02towgs84,changeNowHistoryFun,bd09towgs84} from '@/view/monitor/util/mapUtil'

import gaodeCityCode from './gaodeCityCode.json'
import baiduCityCode from './baiduCityCode.json'
import { resolve } from 'path'
// import BMap from 'BMap'
// /baidu 或者 gaode/
let mapType = 'gaode'  
let gaodekey = ''
let baidukey = ''
    mapType = window.PONY.map && window.PONY.map.mapType ? window.PONY.map.mapType : 'baidu'
try{
    gaodekey = window.PONY.map && window.PONY.map.a_api ? window.atob(window.PONY.map.a_api) : '11f9cd2b339bd89714714053b6d051ca'
}catch(e){
    gaodekey = '11f9cd2b339bd89714714053b6d051ca'
}
try{

    baidukey  = window.PONY.map && window.PONY.map.b ? window.atob(window.PONY.map.b) : 'CMK0Dqtg1vgwccAZfC8v7ThZ1wO7iOQo'


}catch(e){
    baidukey = 'CMK0Dqtg1vgwccAZfC8v7ThZ1wO7iOQo'
}

// if(mapType=='baidu'){
//    const loadJScript  = ()=>{
//         return new Promise((resolve,reject) =>{
//             if(typeof BMap !=='undefined'){
//                 resolve(BMap)
//                 return true
//             }
//             window.init = function(){
//                 resolve(BMap)
//             }
//             let dom = document.createElement('script')
//         dom.type = 'text/javascript'
//         dom.chartset = 'utf-8'
//         dom.src = 'https://api.map.baidu.com/api?v=3.0&ak='+ baidukey +'&callback=init'
//         document.body.appendChild(dom)
//         })
        
//     }
//     const initMap =  ()=>{
//         let map =  loadJScript()
//     }
//     window.onload = initMap()
// }

/**
 * 通过高德js Api逆地理编码 传入经纬度,获得地理位置
 *@param {经纬度}
 */
 const getAddressByLatLng = (lnglat)=>{
    return new Promise((resolve, reject) => {
        let address = ''
        AMap.plugin('AMap.Geocoder', function() {
            let geocoder = new AMap.Geocoder({
            // city 指定进行编码查询的城市，支持传入城市名、adcode 和 citycode
            city: '全国',
            extensions:'all'
            })
            // let lnglat = [116.396574, 39.992706]
            // let lnglat = latLng
            geocoder.getAddress(lnglat, function(status, result) {
            if (status === 'complete' && result.info === 'OK') {
                // result为对应的地理位置详细信息
                let poisDes = (result.regeocode.pois && result.regeocode.pois.length) ? result.regeocode.pois[0].direction + result.regeocode.pois[0].distance + '米':''
                address = result.regeocode.formattedAddress + poisDes

                resolve(address)
            }else {
                resolve('')
            }
            })
        })
    })

}
/**
 * 通过高德地图接口根据IP地址查询所在地
 * @param {ip地址} ip
 */
const getUserAddressByIp = (ip) => {
    return new Promise((resolve, reject) => {
        let interPath = 'https://restapi.amap.com/v3/ip?ip=' + ip + '&output=json&key=' + gaodekey
        $.ajax({
            url: interPath,
            type: "GET",
            dataType: "jsonp",
            success: (res) => {
                if(!res || !+res.status) {
                    resolve({})
                    return
                }
                resolve(res)
            }
        }).catch(e => {
            reject(new Error(e))
        })
    })
}
//根据地理位置信息获取经纬度点,判断是哪个地图的api
//type: 'gaode'  'baidu'
//data是参数
const getLatLngByAddressOrder = (address, city = '') => {

    if(mapType == 'gaode'){
        return getLatLngByAddressOrderGaode(address, city)
    }else {
        return getLatLngByAddressOrderBaidu(address, city)
    }
}

/**
 * @instance https://lbs.amap.com/api/webservice/guide/api/georegeo
 * @description 根据地理位置信息获取经纬度点等详情
 * @param { 地址名字 } address
 * @returns { gcj02坐标系的经纬度集合 } [lat, lng]
 */
const getLatLngByAddressOrderGaode = (address, city = '') => {
    return new Promise((resolve, reject) => {

        let interPath = 'https://restapi.amap.com/v3/geocode/geo?address=' + address + '&city=' + city + '&output=json&key=' + gaodekey
        $.ajax({
            url: interPath,
            type: "GET",
            dataType: "jsonp",
            success: (res) => {
                if(!res || !+res.status) {
                    resolve([])
                    return
                }
                let resultList = []
                res.geocodes.forEach(item => {
                    let lnglat = item.location.split(',')
                    let latLng = gcj02towgs84(+lnglat[0],+lnglat[1])
                    resultList.push({
                        latlng: latLng,
                        name: item.formatted_address,
                        province: item.province
                    })
                })
                resolve(resultList)
            }
        }).catch(e => {
            reject(new Error(e))
        })

    })
}
/**
 * @instance https://lbsyun.baidu.com/faq/api?title=webapi/guide/webservice-geocoding-base
 * @description 根据地理位置信息获取经纬度点等详情
 * @param { 地址名字 } address
 * @returns { gcj02坐标系的经纬度集合 } [lat, lng]
 */
const getLatLngByAddressOrderBaidu = (address, city = '') => {
    return new Promise((resolve, reject) => {
        let interPath = 'https://coldchain.superfleet.com.cn/baidu/geocoding/v3/?address=' + address +'&city=' + city + '&output=json&ak=' + baidukey
        // var myGeo = new BMap.Geocoder();      
        // // 将地址解析结果显示在地图上，并调整地图视野    
        // myGeo.getPoint(address, function(point){      
        //     if (point) {
        //         let resultList = []
        //             let resultlatlng = changeNowHistoryFun(point.lng,point.lat,'bd09','wgs84')
        //                 resultList.push({
        //                     latlng: resultlatlng,
        //                     name: address,
        //                     province: ''
        //                 })
        //                 resolve(resultList)
        //         }      
        //     }, 
        // city);
        $.ajax({
            url: interPath,
            type: "GET",
            dataType: "jsonp",
            success: (res) => {
                if(!res || res.status != 0) {
                    resolve([])
                    return
                }
                let resultList = []
              let resultlatlng = changeNowHistoryFun(res.result.location.lng,res.result.location.lat,'bd09','wgs84')
                resultList.push({
                    latlng: [resultlatlng[1],resultlatlng[0]],
                    name: address,
                    province: ''
                })
                resolve(resultList)
            }
        }).catch(e => {
            // reject(new Error(e))
        })


    })
}



/**
 * @instance https://lbs.amap.com/api/webservice/guide/api/search
 * @description 根据地理位置信息获取poi 详细信息
 * @param { 地址名字 } address  city
 * @returns { gcj02坐标系的经纬度集合 } [lat, lng]
 */
 const getPoiListByLatLngOrder = (address, city = '中国')=>{
    if(mapType == 'gaode'){
        return getPoiListByLatLngOrderGaode(address, city )
    }else{
        return getPoiListByLatLngOrderBaidu(address, city)
    }
 }
const getPoiListByLatLngOrderGaode = (address, city ) => {
    return new Promise((resolve, reject) => {

        let interPath = 'https://restapi.amap.com/v3/place/text?keywords=' + address + '&city=' + city +
        '&children=1&offset=20&page=1' +
        '&output=json&key=' + gaodekey

        $.ajax({
            url: interPath,
            type: "GET",
            dataType: "jsonp",
            success: (res) => {
                if(!res || !+res.status) {
                    resolve([])
                    return
                }
                let resultList = []
                res.pois.forEach(item => {
                    let lnglat = item.location.split(',')
                    let resLngLat = changeNowHistoryFun(lnglat[0],lnglat[1],'gcj02','wgs84')
                    resultList.push({
                        latlng: [+resLngLat[1], +resLngLat[0]],
                        name: item.name,
                        address: item.address,
                        pname: item.pname,
                        cityname: item.cityname,
                        adname: item.adname,
                    })
                })
                resolve(resultList)
            }
        }).catch(e => {
            reject(new Error(e))
        })

    })
}
const getPoiListByLatLngOrderBaidu = (address, city ) => {
    return new Promise((resolve, reject) => {
        let resultList=[]

        let interPath = 'https://coldchain.superfleet.com.cn/baidu/place/v2/search?query=' + address + '&region=' + city +
        '&page_size=20' +
        '&output=json&ak=' + baidukey
            $.ajax({
                url: interPath,
                type: "GET",
                dataType: "jsonp",
                success: (res) => {
                    if(!res || res.status != 0) {
                        resolve([])
                        return
                    }
                    let resultList = []
                    res.results.forEach(item => {
                        let lngLatArr = changeNowHistoryFun(item.location.lng,item.location.lat,'bd09','wgs84')
                        resultList.push({
                            latlng: [lngLatArr[1],lngLatArr[0]],
                            name: item.name,
                            address: item.address,
                            pname: item.province,
                            cityname: item.city,
                            adname: item.area,
                        })
                    })
                    resolve(resultList)
                }
            }).catch(e => {
                resolve(null)
            })

        // let local = new BMap.LocalSearch(city,{
        //     onSearchComplete:function(){
        //         let piont = local.getResults().Vr
        //         piont.forEach(item => {
        //             let lngLatArr = changeNowHistoryFun(item.point.lng,item.point.lat,'bd09','wgs84')
        //             resultList.push({
        //                 latlng: [lngLatArr[1],lngLatArr[0]],
        //                 name: item.title,
        //                 address: item.address,
        //                 pname: item.province,
        //                 cityname: item.city,
        //                 adname: '',
        //             })
        //         })
        //     resolve(resultList)
        //     }
        // })
        // local.search(address)
    })
}




/**
 * @description 根据经纬度信息获取详细地理位置坐标
 * @param {} { latlng: [ lat, lng ](传入坐标系为gcj02), detail: false(是否需要详情) } 
 */
const getAddressByLatLngOrder = (param) =>{
    if(mapType == 'gaode'){
      let latlng = changeNowHistoryFun(param.latlng[1],param.latlng[0],'wgs84','gcj02')
        param.latlng = latlng
        return getAddressByLatLngOrderGaode(param)
    }else {
        
        let latlng = changeNowHistoryFun(param.latlng[1],param.latlng[0],'wgs84','bd09')
        param.latlng = latlng
        return getAddressByLatLngOrderBD(param)
    }

}
const getAddressByLatLngOrderGaode = (param) => {
    return new Promise((resolve, reject) => {

        let lnglat = param.latlng[1] + ',' + param.latlng[0]
        let interPath = 'https://restapi.amap.com/v3/geocode/regeo?output=json&location=' +
            lnglat + '&key=' + gaodekey + '&radius=' + param.radius + '&extensions=all'

        $.ajax({
            url: interPath,
            type: "GET",
            dataType: "jsonp",
            success: (res) => {
                if(!res || !+res.status) {
                    resolve([])
                    return
                }
                res.regeocode.addressComponent.formatted_address = res.regeocode.formatted_address
                let result = res.regeocode.addressComponent
                if(!param.detail) {
                    result = res.regeocode.formatted_address
                }
                resolve(result)
            }
        }).catch(e => {
            reject(new Error(e))
        })

    })
}
const getAddressByLatLngOrderBD = (param) => {
    return new Promise((resolve, reject) => {

        let lnglat = `${param.latlng[1]}, ${param.latlng[0]}`
        let interPath = 'https://coldchain.superfleet.com.cn/baidu/reverse_geocoding/v3/?output=json&location=' +
            lnglat + '&ak='+ baidukey + '&radius=' + param.radius
            $.ajax({
                url: interPath,
                type: "GET",
                dataType: "jsonp",
                success: (res) => {

                    if(!res || res.status!=0) {
                        resolve([])
                        return
                    }
                    res.result.addressComponent.formatted_address = res.result.formatted_address
                    let result = res.result.addressComponent
                    if(!param.detail) {
                        result = res.result.formatted_address
                    }
                    resolve(result)
                }
            }).catch(e => {
                resolve(null)
            })
            // var myGeo = new BMap.Geocoder({extensions_town: true}); 
            // myGeo.getLocation(new BMap.Point(param.latlng[1],param.latlng[0]), function(result){      
            //     if (result) {
            //     let res = result.addressComponents
            //     let formatted_address  = `${result.address},${result.addressComponents.town},${result.addressComponents.street}`;
            //     res.formatted_address = formatted_address
            //     if(param.detail) {
            //         res.district = result.addressComponents.town
            //     }else{
            //         res =  formatted_address
            //     }
            //     resolve(res)


            //     }      
            // })

    })
}


/**
 * @instance https://lbs.amap.com/api/webservice/guide/api/direction#distance
 * @description 查询   “ 驾车路径 ”   导航信息
 * @param {
 *      origin: '', 起点
 *      waypoints: '', 途径点，
 *      destination: '' 终点，
 *      strategy: 默认 0  速度优先，不考虑当时路况，此路线不一定距离最短
 * } obj
 * @returns {
 *      lnglatlist: [[ lat, lng ],[ lat, lng] ],    该线的全部经纬度
 *      distance: 100                               该线路距离
 *      lineStep: [                                 行走步骤
 *          {
 *              detail: '',         详情
 *              road: '',
 *              state: '',          路况
 *              distance: '',       距离
 *          }
 *      ]
 * }
 */
 const getDrivingLatLngOrder = (obj,type = 'gcj02')=>{
    if(mapType == 'gaode'){
        return getDrivingLatLngOrderGaode(obj)
    }else {
        let params = {}
        let strategyList = [0,4,3,1,2] //高德 strategy参数对应百度的参数
        params.tactics = strategyList[obj.strategy]
        let originalLatLng = obj.origin.split(',')
        let destinationLatLng = obj.destination.split(',')
        params.origin = changeNowHistoryFun(originalLatLng[0],originalLatLng[1],type,'bd09').reverse()
        params.destination = changeNowHistoryFun(destinationLatLng[0],destinationLatLng[1],type,'bd09').reverse()
        let waypoints = obj.waypoints ? obj.waypoints.split('|') : []
        if(waypoints.length){
          let  waypointsList = waypoints.map(item => {
            let latlng = item.split(',')
            return changeNowHistoryFun(latlng[1],latlng[0],type,'bd09').reverse().join(',')
          })
          params.waypoints = waypointsList.join('|')
        }else {
          params.waypoints = ''
        }
        return  getDrivingLatLngOrderBaidu(params)
    }
}
const getDrivingLatLngOrderGaode = (obj) => {

    return new Promise((resolve, reject) => {

        let interPath = 'https://restapi.amap.com/v3/direction/driving?' +
            'origin=' + obj.origin +
            '&destination=' + obj.destination +
            '&waypoints=' + obj.waypoints +
            '&strategy=' + obj.strategy +
            '&extensions=all&output=json&key=' + gaodekey

        $.ajax({
            url: interPath,
            type: "GET",
            dataType: "jsonp",
            success: (res) => {
                if(!res || !+res.status) {
                    resolve({})
                    return
                }
                let result = {}
                let lnglatlist = []
                let lineStep = []
                let route = res.route.paths[0].steps
                if(route.length) {
                    route.forEach(item => {
                        let lnglat =  item.polyline.split(';')
                        lnglat.forEach(child => {
                            let childlng = child.split(',')
                            // let lngLatArr = [+childlng[1], +childlng[0]]
                            let lngLatArr = gcj02towgs84(+childlng[0], +childlng[1])
                            lnglatlist.push([lngLatArr[1],lngLatArr[0]])
                        })

                        lineStep.push({
                            detail: item.instruction,
                            road: item.road,
                            state: item.tmcs[0].status,
                            distance: item.distance
                        })
                    })
                }
                result = {
                    lnglatlist: lnglatlist,
                    distance: res.route.paths[0].distance,
                    duration: transSecondToHMS(res.route.paths[0].duration),
                    lineStep: lineStep,
                    durationSecond: res.route.paths[0].duration
                }
                resolve(result)
            }
        }).catch(e => {
            reject(new Error(e))
        })
    })

}
const getDrivingLatLngOrderBaidu = (obj) => {

    return new Promise((resolve, reject) => {

        let interPath = 'https://coldchain.superfleet.com.cn/baidu/directionlite/v1/driving?' +
        'origin=' + obj.origin +
        '&destination=' + obj.destination +
        '&waypoints=' + obj.waypoints +
        '&tactics=' + obj.tactics +
        '&ret_coordtype=gcj02'+
        '&ak='+ baidukey 

        $.ajax({
            url: interPath,
            type: "GET",
            dataType: "jsonp",
            success: (res) => {
                if(!res || res.status != 0) {
                    resolve([])
                    return
                }
                let result = {}
                let lnglatlist = []
                let lineStep = []
                let statusDes = ["无路况","畅通","缓行","拥堵","严重拥堵"]
                let route = res.result.routes[0].steps
                if(route.length) {
                    route.forEach(item=>{
                        let lnglat =  item.path.split(';')
                        lnglat.forEach(child => {
                            let childlng = child.split(',')
                            // let lngLatArr = [+childlng[1], +childlng[0]]
                            let lngLatArr = gcj02towgs84(+childlng[0], +childlng[1])
                            lnglatlist.push([lngLatArr[1],lngLatArr[0]])
                        })
                        let regex1 = new RegExp('<b>',"g")
                        let regex2 = new RegExp('</b>',"g")
                        let instruction = (item.instruction.replace(regex1,"")).replace(regex2,"")
                        lineStep.push({
                            detail: instruction,
                            road: item.road,
                            state: statusDes[item.status],
                            distance: item.distance
                        })
                    })
                }
                result = {
                    lnglatlist: lnglatlist,
                    distance: res.result.routes[0].distance,
                    duration: transSecondToHMS(res.result.routes[0].duration),
                    lineStep: lineStep,
                    durationSecond: res.result.routes[0].duration
                }
                resolve(result)
            }
        }).catch(e => {
            reject(new Error(e))
        })
        // var start = new BMap.Point(obj.origin[1], obj.origin[0]);
        // var end = new BMap.Point(obj.destination[1],obj.destination[0]);
        // let result = {}
        // let lnglatlist = []
        // let lineStep = []
        // var driving = new BMap.DrivingRoute(start, { 
        //     onSearchComplete: function(results){
        //         if (driving.getStatus() == 0 ){
        //         var plan = results.getPlan(0); 
        //             var distance = plan.getDistance(false)
        //             var duration = plan.getDuration(false)
        //             var route = plan.getRoute(0); 
        //             let path = route.getPath()
        //             if(path.length){
        //                 path.forEach(lnglat=>{
        //                     let lngLatArr = changeNowHistoryFun(lnglat.lng,lnglat.lat,'bd09','wgs84')
        //                     lnglatlist.push([lngLatArr[1],lngLatArr[0]])
        //                 })
        //             }
        //                   // 获取每个关键步骤，并输出到页面 
        //                 for (var i = 0; i < route.getNumSteps(); i ++) { 
        //                     var step = route.getStep(i); 
        //                     let stepDistance = step.getDistance(false)
        //                     lineStep.push({
        //                     detail: '',
        //                     road: '',
        //                     state: '无',
        //                     distance: stepDistance
        //                 })
        //                 } 
        //                 result = {
        //                                 lnglatlist: lnglatlist,
        //                                 distance: distance,
        //                                 duration: transSecondToHMS(duration),
        //                                 lineStep: lineStep,
        //                                 durationSecond: duration
        //                             }
        //             resolve(result)
        //         }
        //     } 
        // });
        //     driving.search(start, end);
    })

}
/**
 * 写个简易版的,只取每个小段的开头和结尾的经纬度
 * @instance https://lbs.amap.com/api/webservice/guide/api/direction#distance
 * @description 查询   “ 驾车路径 ”   导航信息
 * @param {
 *      origin: '', 起点
 *      waypoints: '', 终点
 *      destination: '' 途径点，
 *      strategy: 默认 0  速度优先，不考虑当时路况，此路线不一定距离最短
 * } obj
 * @returns {
 *      lnglatlist: [[ lat, lng ],[ lat, lng] ],    该线的全部经纬度
 *      distance: 100                               该线路距离
 *      lineStep: [                                 行走步骤
 *          {
 *              detail: '',         详情
 *              road: '',
 *              state: '',          路况
 *              distance: '',       距离
 *          }
 *      ]
 * }
 */
const getDrivingLatLngOrderGaodeSimple = (obj) => {

    return new Promise((resolve, reject) => {

        let interPath = 'https://restapi.amap.com/v3/direction/driving?' +
            'origin=' + obj.origin +
            '&destination=' + obj.destination +
            '&waypoints=' + obj.waypoints +
            '&strategy=' + obj.strategy +
            '&extensions=all&output=json&key=' + gaodekey

        $.ajax({
            url: interPath,
            type: "GET",
            dataType: "jsonp",
            success: (res) => {
                if(!res || !+res.status) {
                    resolve({})
                    return
                }
                let result = {}
                let lnglatlist = []
                let lineStep = []
                let route = res.route.paths[0].steps
                if(route.length) {
                    route.forEach(item => {
                        let lnglat =  item.polyline.split(';')
                        let simpleList = [lnglat[0],lnglat[lnglat.length-1]]
                        simpleList.forEach(child => {
                            let childlng = child.split(',')

                            lnglatlist.push([+childlng[1], +childlng[0]])
                        })

                        lineStep.push({
                            detail: item.instruction,
                            road: item.road,
                            state: item.tmcs[0].status,
                            distance: item.distance
                        })
                    })
                }
                result = {
                    lnglatlist: lnglatlist,
                    distance: res.route.paths[0].distance,
                    duration: transSecondToHMS(res.route.paths[0].duration),
                    lineStep: lineStep,
                }
                resolve(result)
            }
        }).catch(e => {
            reject(new Error(e))
        })
    })

}


/**
 * @description 筛选城市大区的对应码
 * @param {城市名字} keyword
 * @returns {
 *      chinese_name: '',
 *      adcode: '',
 *      cityCode: ''
 * } list
 */
const getCityCodeOrderGaode = (keyword) => {
    if(mapType=='gaode'){
        if(!keyword || !gaodeCityCode.length) return []
        let currentArray = []
        gaodeCityCode.forEach(item => {
            if(PinyinMatch.match(item.chinese_name, keyword)) currentArray.push(item)
        })
        return currentArray
    }else{
        if(!keyword || !baiduCityCode.length) return []
        let currentArray = []
        baiduCityCode.forEach(item => {
            if(PinyinMatch.match(item.chinese_name, keyword)) currentArray.push(item)
        })
        return currentArray
    }
  
}



/**
 * @instance https://www.amap.com/service/poiInfo?query_type=TQUERY&city=330700&keywords=B5%E8%B7%AF
 * @description 翻译高德查询公交线路详情的基本信息
 * @param {*} result 见 @instance
 * @returns {
 *      line_name: '',
 *      total_distance: '',
 *      start_end: '',
 *      start_time: '',
 *      end_time: '',
 *      station_names: '',
 *      station_points: '',
 *      latlnglist: ''
 * }
 */
const transLateGaodeBusLineResult = (result) => {
    if(!+result.status || !result.data.busline_list.length) return []
    let lineAllResult = []
    result.data.busline_list.forEach(item => {
        let latlnglist = []

        let station_names = []
        let station_points = []

        item.stations.forEach(station => {
            let lnglatList = station.xy_coords.split(';')
            station_names.push(station.name)
            station_points.push([+lnglatList[1], +lnglatList[0]])
        })

        let lngList = item.xs.split(',')
        let latList = item.ys.split(',')
        for(var i = 0; i < lngList.length; i++) {
            latlnglist.push([+latList[i], +lngList[i]])
        }
        let obj = {
            line_name: item.name.split('(')[0],
            total_distance: item['length'],
            start_end: item.front_name + '-' + item.terminal_name,
            start_time: item.start_time?item.start_time.slice(0,2) + ':' + item.start_time.slice(2,4):'',
            end_time: item.end_time?item.end_time.slice(0,2) + ':' + item.end_time.slice(2,4):'',
            station_names: station_names,
            station_points: station_points,
            point: latlnglist,
            active: false
        }
        lineAllResult.push(obj)
    })
    return lineAllResult
}


export {
    getUserAddressByIp,
    getLatLngByAddressOrderGaode,
    getLatLngByAddressOrderBaidu,
    getPoiListByLatLngOrderGaode,
    getPoiListByLatLngOrderBaidu,
    getPoiListByLatLngOrder,
    getAddressByLatLngOrderGaode,
    getAddressByLatLngOrder,
    getLatLngByAddressOrder,
    getDrivingLatLngOrderGaode,
    getDrivingLatLngOrderGaodeSimple,
    getDrivingLatLngOrderBaidu,
    getDrivingLatLngOrder,
    getCityCodeOrderGaode,
    transLateGaodeBusLineResult,
}





/*****************高德在上面*************************        ********************百度在下面*********************/












/**
 * @description 筛选城市大区的对应码
 * @param {城市名字} dataType
 * @returns {
 *      chinese_name: '',
 *      adcode: '',
 *      cityCode: ''
 * } list
 */
const getCityCodeOrderBaiDu = (dataType) => {
    if(!dataType || !baiduCityCode.length) return
    let result
    if(dataType == 'obj') {
        result = {}
        baiduCityCode.forEach(item => {
            result[item.name] = item.citycode
        })
    } else {
        result = baiduCityCode.filter(item => PinyinMatch.match(item.chinese_name, dataType))
    }
    return result
}



/**
 *
 * @param {百度地图的城市代码} cityCode
 * @param {线路名砸} lineName
 */
const getBaiduBusLineUidList = (cityCode, lineName) => {

    return new Promise((resolve, reject) => {
        let path = 'http://api.map.baidu.com/?qt=bl&c= ' + cityCode +'&wd= ' + lineName + '&ie=utf-8&oue=1&fromproduct=jsapi&res=api'
        $.ajax({
            url: path,
            type: "GET",
            dataType: "jsonp",
            success: (res) => {
                if(!res || !res.content ||!res.content.length || !res.content[0].uid) {
                    resolve([])
                    return
                }
                let linelist = []
                res.content.forEach(item => {
                    let linename = item.name.split('(')
                    linelist.push({
                        line_name: linename[0],
                        start_end: linename[1].slice(0, linename[1].length - 1),
                        uid: item.uid,
                        active: false
                    })
                })
                resolve(linelist)
            }
        }).catch(e => {
            reject(new Error(e))
        })
    })

}



/**
 *
 * @param {城市代码} cityCode
 * @param {线录代码} uid
 */
const getBusDetailByBaidu = (cityCode, uid) => {
    return new Promise((resolve, reject) => {
        let path = 'http://api.map.baidu.com/?qt=bsl&c=' + cityCode +'&uid=' + uid + '&ie=utf-8&oue=1&fromproduct=jsapi'
        $.ajax({
            url: path,
            type: "GET",
            dataType: "jsonp",
            success: (res) => {
                if(!res || !res.content || !res.content.length || !res.content[0].uid) {
                    resolve({})
                    return
                }

                let lineDetail = res.content[0]

                let station_names = []
                let station_points = []

                lineDetail.stations.forEach(item => {
                    let lnglat = geo2coord(item.geo)[0]
                    station_names.push(item.name)
                    station_points.push([+lnglat[1].toFixed(6), +lnglat[0].toFixed(6)])
                })

                let baiduGeos = lineDetail.geo.split('|')[2]
                let lineRouteList = geo2coord(baiduGeos).map(item => {
                    return [+item[1].toFixed(6), +item[0].toFixed(6)]
                })

                let lineResult = {
                    station_names: station_names,
                    station_points: station_points,
                    point: lineRouteList,
                    start_time: lineDetail.startTime,
                    end_time: lineDetail.endTime,
                }

                resolve(lineResult)
            }
        }).catch(e => {
            reject(new Error(e))
        })
    })
}


/**
 *
 * @param {查询点名字} value
 * @param {城市名字} city
 * @returns {
 *      name: 查询的名字,
 *      province: 那个省的,
 *      latlng: [ lat, lng ],
 *      location: 详细地址,
 *      num: 数量,
 * }
 */
const getPoiDetailOrderBaiDu = (value, city) => {
    return new Promise((resolve, reject) => {

        let data = {
            output: 'json',
            query: value,
            scope: 2,
            page_size: 20,
            ak: baidukey,
            region: city
        }

        let path = 'http://api.map.baidu.com/place/v2/search'

        $.ajax({
            url: path,
            type: "GET",
            data: data,
            dataType: "jsonp",
            success: (res) => {
                if(!res || res.status != 0) {
                    resolve(1)
                    return
                }
                let poiList = []

                if(!res.results || !res.results.length) {
                    resolve(poiList)
                    return
                }

                if(res.results[0].num) {
                    poiList = res.results
                } else {
                    res.results.forEach(item => {
                        let obj = {
                            name: item.name,
                            province: item.province,
                            latlng: [ +item.location.lat.toFixed(6), +item.location.lng.toFixed(6) ],
                            location: item.address,
                            num: item.province,
                        }

                        poiList.push(obj)
                    })
                }

                resolve(poiList);
            }
        }).catch(e => {
            reject(new Error(e))
        })
    })
}





/**
 * @description 根据IP地址查询用户所在地
 * @param {IP地址} ip
 */
const getUserLockCity = (ip) => {
    return new Promise((resolve, reject) => {
        let data = {
            ip: ip,
            output: 'json',
            ak: baidukey,
            coor: 'gcj02'
        }
        let path = 'http://api.map.baidu.com/location/ip'

        $.ajax({
            url: path,
            type: "GET",
            data: data,
            dataType: "jsonp",
            success: (res) => {
                if(!res || res.status != 0) {
                    resolve()
                    return
                }
                resolve(res.content)
            }
        }).catch(e => {
            reject(new Error(e))
        })

    })
}


export {
    getCityCodeOrderBaiDu,
    getBaiduBusLineUidList,
    getBusDetailByBaidu,
    getUserLockCity,
    getPoiDetailOrderBaiDu,
    getAddressByLatLng,
    getAddressByLatLngOrderBD
}




















// 默认配置
const DEFAULT_CONFIG = {
  timeout: 90000,
  headers: {
    'X-Requested-With': 'XMLHttpRequest',
    'Content-Type': 'application/json; charset=UTF-8',
    'RtApiKey': 'Hsds%s5Ko32#nLwnvs',
    'Authorization': 'Basic YTMyMDFjNDU2OTkzNzdjOWIzNDFkMmUwMmIwYjEyOmoxZ2VyRHNhUExmM092bHVSelZVdFkvZmRmSkptMw==',
    'Accept': '*/*'
  }
};

// API配置
const API_CONFIG = {
  baseURL: '/rtgwapi'
};

// Token 缓存对象
const tokenManager = {
  token: null,
  expireTime: null,
  // token 有效期设置为 28 分钟，留 2 分钟余量
  TOKEN_VALID_DURATION: 28 * 60 * 1000,

  /**
   * 检查 token 是否需要刷新
   * @returns {boolean} 是否需要刷新
   */
  needRefresh() {
    if (!this.token || !this.expireTime) return true;
    // 当前时间距离过期时间小于 2 分钟时刷新
    return Date.now() >= (this.expireTime - 2 * 60 * 1000);
  },

  /**
   * 设置 token
   * @param {string} newToken token字符串
   */
  setToken(newToken) {
    this.token = newToken;
    this.expireTime = Date.now() + this.TOKEN_VALID_DURATION;
  },

  /**
   * 清除 token
   */
  clearToken() {
    this.token = null;
    this.expireTime = null;
  }
};

/**
 * 发送ajax请求
 * @param {Object} options 请求配置
 * @returns {Promise} 返回Promise
 */
function ajax(options) {
  // 合并headers
  options.headers = {
    ...DEFAULT_CONFIG.headers,
    ...options.headers
  };
  
  return new Promise((resolve, reject) => {
    const xhr = new XMLHttpRequest();
    
    xhr.timeout = DEFAULT_CONFIG.timeout;
    
    xhr.open(options.method, options.url, true);
    
    Object.keys(options.headers).forEach(key => {
      xhr.setRequestHeader(key, options.headers[key]);
    });

    xhr.onreadystatechange = function() {
      if (xhr.readyState === 4) {
        if (xhr.status >= 200 && xhr.status < 300) {
          try {
            const response = xhr.responseText ? JSON.parse(xhr.responseText) : null;
            resolve(response);
          } catch (e) {
            reject(new Error('解析响应数据失败'));
          }
        } else {
          reject(new Error(`请求失败: ${xhr.status}`));
        }
      }
    };

    xhr.ontimeout = () => {
      reject(new Error('请求超时'));
    };

    xhr.onerror = () => {
      reject(new Error('网络错误'));
    };

    xhr.send(options.data ? JSON.stringify(options.data) : null);
  });
}

/**
 * 获取验证token
 * @returns {Promise} 返回请求结果
 */
async function getThirdToken(data = {}) {
  return await ajax({
    method: 'POST',
    url: `${API_CONFIG.baseURL}/cswy/v1/thirdToken`,
    data: {
      ...data,
      _t: Date.now()
    }
  });
}

/**
 * 确保有效 token
 * @returns {Promise<string>} 返回有效的 token
 */
async function ensureValidToken() {
  if (tokenManager.needRefresh()) {
    try {
      const tokenResult = await getThirdToken();
      if (tokenResult && tokenResult.result) {
        tokenManager.setToken(tokenResult.result.accessToken);
      } else {
        throw new Error('获取token失败');
      }
    } catch (error) {
      tokenManager.clearToken();
      throw error;
    }
  }
  return tokenManager.token;
}

/**
 * 获取车辆位置点
 * @param {Object} params 请求参数
 * @returns {Promise} 返回请求结果
 */
async function getCarPoints(params = {}) {
  const token = await ensureValidToken();

  return await ajax({
    method: 'POST',
    url: `${API_CONFIG.baseURL}/cswy/v1/getCarPoints`,
    headers: {
      'Authorization': `bearer ${token}`
    },
    data: {
      projectId: "1782355111520366593",
      businessLine: "hwzy",
      ...params,
      _t: Date.now()
    }
  });
}

/**
 * 获取人员位置点
 * @param {Object} params 请求参数
 * @returns {Promise} 返回请求结果
 */
async function getPersonPoints(params = {}) {
  const token = await ensureValidToken();
  
  return await ajax({
    method: 'POST',
    url: `${API_CONFIG.baseURL}/cswy/v1/getPersonPoints`,
    headers: {
      'Authorization': `bearer ${token}`
    },
    data: {
      projectId: "1782355111520366593",
      businessLine: "hwzy",
      ...params,
      _t: Date.now()
    }
  });
}

/**
 * 获取智能预警列表
 * @param {Object} params 请求参数
 * @returns {Promise} 返回请求结果
 */
async function getSmartEarlyWarnList(params = {}) {
  const token = await ensureValidToken();

  return await ajax({
    method: 'POST',
    url: `${API_CONFIG.baseURL}/cswy/v1/smartEarlyWarnList`,
    headers: {
      'Authorization': `bearer ${token}`
    },
    data: {
      businessLine: "hwzy",
      isEarlyWarning: 1,
      pageSize: 10000,
      pageIndex: 1,
      orderBy: "",
      ...params,
      _t: Date.now()
    }
  });
}

/**
 * 获取人员当前类型统计
 * @returns {Promise} 返回请求结果
 */
async function getPersonCurrentTypeStatistics() {
  const token = await ensureValidToken();

  return await ajax({
    method: 'GET',
    url: `${API_CONFIG.baseURL}/cswy/v1/personCurrentTypeStatistics`,
    headers: {
      'Authorization': `bearer ${token}`
    }
  });
}

/**
 * 获取车辆里程统计
 * @param {Object} params 请求参数
 * @returns {Promise} 返回请求结果
 */
async function getCarMilStatistics(params = {}) {
  const token = await ensureValidToken();

  return await ajax({
    method: 'POST',
    url: `${API_CONFIG.baseURL}/cswy/v1/carMilStatistics`,
    headers: {
      'Authorization': `bearer ${token}`
    },
    data: {
      businessLine: "hwzy",
      activePage: false,
      ...params,
      _t: Date.now()
    }
  });
}

/**
 * 获取工单分页数据
 * @param {Object} params 请求参数
 * @returns {Promise} 返回请求结果
 */
async function getWorkOrderPage(params = {}) {
  const token = await ensureValidToken();

  return await ajax({
    method: 'POST',
    url: `${API_CONFIG.baseURL}/cswy/v1/workOrderPage`,
    headers: {
      'Authorization': `bearer ${token}`
    },
    data: {
      status: "0",
      orders: [
        {
          asc: false,
          column: "no"
        }
      ],
      pageSize: 10000,
      pageIndex: 1,
      orderBy: "",
      ...params,
      _t: Date.now()
    }
  });
}

// 创建人员类型和车辆类型的映射对象
const PERSON_TYPE_MAP = {
  'xmjl': '项目经理',
  'xcgly': '巡查管理员', 
  'wy': '文员',
  'zj': '总监',
  'xcy': '巡查员',
  'xcdz': '巡查队长',
  'ljzycsj': '垃圾转运车司机',
  'yg': '员工',
  'gcbj': '公厕保洁',
  'gcg': '驾车工',
  'zly': '资料员',
  'bz': '班长',
  'bay': '保安员',
  'bjg': '保洁工',
  'bjzg': '保洁主管',
  'cly': '材料员',
  'dg': '电工',
  'fz': '副总',
  'gybz': '管养班长',
  'jdzg': '机动主管',
  'jxy': '机械员',
  'jsfzr': '技术负责人',
  'jsy': '技术员',
  'jszg': '技术主管',
  'jl': '经理',
  'lhg': '绿化工',
  'pg': '普工',
  'sgy': '施工员',
  'scg': '水车工',
  'sj': '司机',
  'wxry': '维修人员',
  'xcwxry': '现场修复人员',
  'xczg': '现场主管',
  'zg': '主管',
  'zlye': '质量员',
  'csgj': '城市管家',
  'SYBJY': '水域保洁员',
  'LJJPBJ': '垃圾精品保洁',
  'KSBJY': '快速保洁员',
  'SLLJQYG': '三轮垃圾清运工',
  'SLLJQZG': '三轮垃圾清障工',
  'FZG': '辅助工',
  'XFY': '信访员',
  'DCY': '督察员',
  'ZZGLY': '中转站管理员',
  'SDG': '水电工',
  'YY': '运营',
  'DLBJY': '道路保洁员',
  'HGCSJ': '哈高车司机',
  'JDSJ': '机动司机',
  'LJSCCSJ': '垃圾收集车司机',
  'LMYHCSJ': '路面养护车司机',
  'QXCSJ': '清洗车司机',
  'SSCSJ': '洒水车司机',
  'SLCXJSY': '三轮冲洗车驾驶员',
  'SLXJJSY': '四轮小机扫驾驶员',
  'TZLJSJ': '桶装垃圾车司机',
  'WPCSJ': '雾炮车司机',
  'XWCSJ': '吸污车司机',
  'XSCSJ': '洗扫车司机',
  'YSCSJ': '压缩车司机',
  'YSLJCSJ': '压缩垃圾车司机',
  'ZXSLJCSJ': '自卸式垃圾车司机',
  'XGYJSJ': '小高压机司机',
  'YHCSJ': '养护车司机',
  'DLYHCSJ': '道路养护车司机'
};


const VEHICLE_TYPE_MAP = {
  'cdddgdxsc': '纯电动多功能洗扫车',
  'cddgyqxc': '纯电动高压清洗车',
  'dgnycc': '多功能抑尘车',
  'cddxsc': '纯电动洒水车',
  'hlqxc': '护栏清洗车',
  'zxchhc': '自卸车或货车',
  'cddtzljc': '纯电动桶装垃圾车',
  'hbxljbzt': '环保型垃圾标准桶',
  'kfsxcc': '开放式巡查车',
  'xccl': '巡查车辆',
  'xcddc': '巡查电动车',
  'xxlmqxj': '小型路面清洗机',
  'xxslj': '小型扫路机',
  'ksbjc': '快速保洁车',
  'xxslgtsc': '小型三轮高压水车',
  'cyj': '吹叶机',
  'dgnmdj': '多功能磨地机',
  'wrsdjqr': '无人扫地机器人',
  'ldljqyc': '绿化垃圾清运车',
  'ssc': '洒水车',
  'glyc': '管理用车',
  'fzhchc': '防撞缓冲保护车',
  'gkzyc': '高空作业车',
  'tjhc': '应急货车(含皮卡)',
  'xqc': '小汽车',
  'qt': '其他(车辆)',
  'DXXCYC': '大型高压冲洗车',
  'DXSSC': '大型洒水车',
  'DXXSC': '大型洗扫车',
  'DLYHC': '道路养护车',
  'LJTQXC': '垃圾桶清洗车',
  'LJYSC': '垃圾压缩车',
  'PTSLC': '普通扫路车',
  'SLLJTJSC': '三轮垃圾桶收集车',
  'TZLJC': '桶装垃圾车',
  'WPC': '雾炮车',
  'syqxship': '水域清洁船'
};

export default {
  getThirdToken,
  getCarPoints,
  getPersonPoints,
  getSmartEarlyWarnList,
  getPersonCurrentTypeStatistics,
  getCarMilStatistics,
  getWorkOrderPage,
  PERSON_TYPE_MAP,
  VEHICLE_TYPE_MAP
};
<template>
  <div class="mapLayer">
    <OnlineMap ref="onlineMap" @mapReady="generateMapObj"></OnlineMap>
    <ToiletPupop 
      ref="toiletPupop"
      v-show="showToiletPupop"
      @closeToiletPupop="closeToiletPupop"
    />
    <CommonInfoWindow
      v-show="showCommonInfoWindow"
      ref="commonInfoWindow"
      :title="infoWindowData.title"
      :details="infoWindowData.details"
      @close="closeCommonInfoWindow"
    />
  </div>
</template>
<script>
import OnlineMap from "@/view/demonstration/demo/zwy/component/GaodeOnlineMap";
import {  wgs84togcj02,bd09togcj02 } from "@/view/monitor/util/mapUtil";
import {
  getVehicleState,
  checkCarRunState,
  getMapBasicIconRequire,
  getVehicleMapIcon,
  checkCarOffLine,
  getVehicleOnlineMapIcon,
  getOnlineMapBasicIconRequire,
  getFenseLayerByCustomOnline,
  transSecondToHMSCN,
} from "@/view/monitor/util/monitorUtil.js";
import ToiletPupop from './toiletPupop.vue';
import CommonInfoWindow from './commonInfoWindow.vue'
import api from './ajaxApi';

export default {
  name: "mapLayerPT",
  components: {
    OnlineMap,
    ToiletPupop,
    CommonInfoWindow
  },
  data() {
    return {
      _map: null,
      startLocationLayer: null,
      endLocationLayer: null,
      markerLayer: null,
      vehicleJudgeObj: {},
      _vehicleMarkerBind: {},
      _vehicleInfoBind: {},
      markerList: [],
      onLineVehicleList: [],
      totalVehicleList: [],
      vehicleInfo: {},
      source: {
        subscriptList: [],
        interVaList: [],
        gpsList: [],
        terminalLinkList: [],
      },
      startLayerList: [],
      endLayerList: [],
      workerPiontList: [
        {
          name: "张天",
          point: "120.547436,27.574436",
          status: "工作中"
        },
        {
          name: "李为",
          point: "120.542123,27.568845", 
          status: "工作中"
        },
        {
          name: "王兴",
          point: "120.547789,27.572234",
          status: "工作中"
        },
        {
          name: "赵磊",
          point: "120.549234,27.570567",
          status: "工作中"
        },
        {
          name: "孙浩",
          point: "120.546345,27.571678",
          status: "工作中"
        }
      ],
      workerLayerList: null,
      pointList: [
        {
          fenseType: 36,
          fenseName: "龙港垃圾焚烧发电厂",
          point: "120.552234,27.578345"
        },
        {
          fenseType: 37,
          fenseName: "龙港生活垃圾中转站",
          point: "120.548567,27.572789"
        },
        {
          fenseType: 36,
          fenseName: "新城垃圾中转站",
          point: "120.556345,27.569456"
        },
        {
          fenseType: 37,
          fenseName: "老城垃圾中转站",
          point: "120.543876,27.567234"
        }
      ],
      toiletLayerList: null,
      toiletPoints: [
        {
          name: "龙港公园公厕",
          point: "120.549234,27.570567",
          type: "AAA级"
        },
        {
          name: "市民广场公厕",
          point: "120.547678,27.572123",
          type: "AAA级"
        },
        {
          name: "市政府公厕",
          point: "120.547456,27.574345",
          type: "AA级"
        },
        {
          name: "商业街公厕",
          point: "120.546789,27.571678",
          type: "AA级"
        }
      ],
      routeLayerList: null,
      // 龙港市域范围经纬度边界 (WGS84)
      bounds: {
        minLng: 120.542,
        maxLng: 120.556,
        minLat: 27.567,
        maxLat: 27.578
      },
      areaLayerList: null,
      // 龙港市域范围的经纬度边界点 (WGS84)
      areaPoints: [
        [120.56511137938271, 27.57691337602106],
        [120.56194934379016, 27.577938027067376],
        [120.55921849486931, 27.579346906462433],
        [120.55620018816734, 27.581011922169647],
        [120.55361306813707, 27.583957656615095],
        [120.5516008636691, 27.587671729551936],
        [120.55088221921623, 27.591129545243454],
        [120.54829509918596, 27.594331128170975],
        [120.54484560581227, 27.594587250723034],
        [120.542258485782, 27.592154062061],
        [120.53981509464231, 27.590489217319046],
        [120.53837780573662, 27.588952414880207],
        [120.53593441459692, 27.587543660187677],
        [120.53248492122322, 27.58613488720365],
        [120.5291791567401, 27.586391029106686],
        [120.523861187789, 27.58703138121891],
        [120.520842881087, 27.588568210869035],
        [120.51696220104162, 27.588568210869035],
        [120.51279406321507, 27.58792786782695],
        [120.50948829873197, 27.5845980230877],
        [120.50948829873197, 27.581396152936158],
        [120.51236287654338, 27.575888715302387],
        [120.51279406321507, 27.573967450385577],
        [120.51193168987166, 27.570765266631483],
        [120.50963202762254, 27.568843911045953],
        [120.50488897423372, 27.566922521462534],
        [120.49942727639204, 27.565129193845703],
        [120.49612151190892, 27.563207738537418],
        [120.49324693409751, 27.561798649706624],
        [120.4893662540521, 27.559877135477585],
        [120.48807269403697, 27.557571273535277],
        [120.48591676067842, 27.55270318204254],
        [120.48519811622556, 27.549884713593297],
        [120.48390455621045, 27.546938054782224],
        [120.485054387335, 27.544631918245965],
        [120.48634794735014, 27.542069486931503],
        [120.48950998294266, 27.541300745758605],
        [120.49640896969007, 27.54066012396197],
        [120.50201439642231, 27.540531999149643],
        [120.50532016090541, 27.53835385423753],
        [120.51566864102648, 27.535278751839055],
        [120.51566864102648, 27.530665935184615],
        [120.51638728547934, 27.525924780858077],
        [120.51681847215104, 27.524515208639816],
        [120.51868694772845, 27.522080449972155],
        [120.51940559218131, 27.51849228525453],
        [120.52026796552475, 27.5152884667722],
        [120.52098660997758, 27.511443760095037],
        [120.5232862722267, 27.507214425896418],
        [120.52400491667956, 27.504394778481448],
        [120.524867290023, 27.503369434044263],
        [120.52616085003811, 27.49952430641393],
        [120.5301852589741, 27.49824256703307],
        [120.5389527212989, 27.49606357546669],
        [120.54182729911028, 27.493115458668246],
        [120.5510259481068, 27.488372668576467],
        [120.5559127303862, 27.486321668375655],
        [120.5622368015713, 27.48811629566267],
        [120.56338663269585, 27.489910893391063],
        [120.56712358385069, 27.48760354802537],
        [120.57201036613007, 27.486193479581253],
        [120.57646596173775, 27.48555253334728],
        [120.5786218950963, 27.488757226816084],
        [120.58192765957942, 27.49196182602766],
        [120.58422732182855, 27.494653616518054],
        [120.5863832551871, 27.498114392265318],
        [120.58782054409282, 27.502600419380048],
        [120.59012020634194, 27.50823973412398],
        [120.59285105526278, 27.513366130409977],
        [120.59385715749679, 27.515032157219007],
        [120.59816902421389, 27.517210769180156],
        [120.59960631311958, 27.520158233588695],
        [120.59285105526278, 27.521824156416454],
        [120.58868291743624, 27.527334334811822],
        [120.58681444185882, 27.53156288707979],
        [120.58566461073427, 27.53579127494487],
        [120.58868291743624, 27.537328829767848],
        [120.59486325973076, 27.537841343210605],
        [120.59788156643275, 27.54066012396197],
        [120.59500698862134, 27.548987912847533],
        [120.59457580194962, 27.554496714811226],
        [120.59356969971564, 27.558980417204925],
        [120.5927073263722, 27.56781917416555],
        [120.59256359748164, 27.57255850116962],
        [120.59227613970049, 27.57742570275329],
        [120.59213241080992, 27.58331728636358],
        [120.59184495302878, 27.587671729551936],
        [120.59141376635706, 27.589208550131765],
        [120.58580833962483, 27.589208550131765],
        [120.58393986404741, 27.586262958230737],
        [120.57890935287745, 27.583573434917657],
        [120.57689714840947, 27.580627690042814],
        [120.57244155280179, 27.57870650900447],
        [120.56927951720924, 27.57832226871604],
        [120.56626121050725, 27.577809946215556],
        [120.56511137938271, 27.57691337602106], // 闭合回到起始点
      ],
      assessmentLayerList: null,
      assessmentPoints: [
        {
          name: "龙港大道社区考核点",
          point: "120.549334,27.570667",
          projectName: "龙港大道社区",
          inspector: "张天",
          status: "待整改", 
          createTime: "2024-03-20 09:30:00"
        },
        {
          name: "新城街道考核点",
          point: "120.547778,27.572223", 
          projectName: "新城街道",
          inspector: "李为",
          status: "待整改",
          createTime: "2024-03-20 10:15:00"
        },
        {
          name: "滨海社区考核点",
          point: "120.547556,27.574445",
          projectName: "滨海社区", 
          inspector: "王兴",
          status: "待整改",
          createTime: "2024-03-20 11:00:00"
        },
        {
          name: "河口街道考核点",
          point: "120.546889,27.571778",
          projectName: "河口街道",
          inspector: "赵磊", 
          status: "待整改",
          createTime: "2024-03-20 11:30:00"
        },
        {
          name: "城东社区考核点",
          point: "120.546789,27.571678",
          projectName: "城东社区",
          inspector: "孙浩",
          status: "待整改", 
          createTime: "2024-03-20 12:00:00"
        }
      ],
      showToiletPupop: false,
      currentToiletInfo: null,
      infoWindow: null,
      workerInfoWindow: null,
      deviceInfoWindow: null,
      assessmentInfoWindow: null,
      showCommonInfoWindow: false,
      infoWindowData: {
        title: '',
        details: []
      },
      commonInfoWindow: null,
      district: null,
      polygons: [],
      workerCluster: null, // 人员点位聚合管理器
    };
  },

  async mounted() {
    await this.$refs["onlineMap"].waitForInit;
    this.generateVehiclePoint();
    this.startTimer();
    if (this.$refs.toiletPupop && this.currentToiletInfo) {
      this.$refs.toiletPupop.init(this.currentToiletInfo);
    }
  },
  methods: {
    startTimer() {
      this.timer = setInterval(() => {
        this.generateVehiclePoint();
      }, 10 * 60 * 1000)
    },
    //处理工作人员点位
    async handleWorkerPoint() {
      try {
        // 清除已有的点位图层
        if (this.workerLayerList) {
          this._map.remove(this.workerLayerList);
        }
        if (this.workerCluster) {
          this.workerCluster.setMap(null);
          this.workerCluster = null;
        }

        // 获取人员点位数据
        const res = await api.getPersonPoints();
        if(!res || !res.result) {
          console.error('获取人员点位数据失败');
          return;
        }

        // 创建聚合管理器实例
        this.workerCluster = new AMap.MarkerClusterer(this._map, [], {
          gridSize: 80, // 聚合网格大小
          maxZoom: 18,  // 最大聚合级别
          minClusterSize: 2, // 最小聚合数量
        });

        // 处理每个人员点位
        const markers = res.result.map(item => {
          if(!item.point?.lat || !item.point?.lng) return null;

          let gcj02 = bd09togcj02(item.point.lng, item.point.lat);
          let icon = new AMap.Icon({
            size: new AMap.Size(14, 30),
            image: "static/imgNewVersion/demonstration/worker_icon.png",
            imageSize: new AMap.Size(14, 30),
          });

          let marker = new AMap.Marker({
            position: gcj02,
            offset: new AMap.Pixel(-15, -15),
            icon: icon,
            extData: item // 存储完整的人员信息
          });
          
          // 添加点击事件
          marker.on('click', () => {
            const details = [
              { label: '人员名称', value: item.name || '未知' },
              { label: '人员类型', value: api.PERSON_TYPE_MAP[item.personType] || '未知' },
              { label: '工作状态', value: item.status || '工作中' },
              { label: '设备编号', value: item.deviceNo || '未知' }
            ];
            
            this.createCommonInfoWindow('人员信息', details, marker.getPosition());
          });

          return marker;
        }).filter(Boolean); // 过滤掉无效的点位

        // 添加点位到聚合管理器
        this.workerCluster.setMarkers(markers);

      } catch(error) {
        console.error('生成人员点位失败:', error);
      }
    },

    async generateMapObj(bmap) {
      this._map = bmap;
    
    },
    async handleMapPoint(list) {
      if (!list.length) return;
      this.endLayerList = [];
      this.startLayerList = [];
      if (this.startLocationLayer) this._map.remove(this.startLocationLayer);
      if (this.endLocationLayer) this._map.remove(this.endLocationLayer);
      list.forEach((item) => {
        if (item.fenseType == 36) {
          this.handleStartLocation(item);
        } else {
          this.handleEndLocation(item);
        }
      });
      await this.$refs["onlineMap"].waitForInit;
      this.startLocationLayer = new AMap.OverlayGroup(this.startLayerList);
      this.endLocationLayer = new AMap.OverlayGroup(this.endLayerList);
      this._map.add(this.endLocationLayer);
      this._map.add(this.startLocationLayer);
    },
    handleStartLocation(start) {
      let point = start.point.split(",");
      let gcj02 = wgs84togcj02(point[0], point[1]);
      let icon = new AMap.Icon({
        size: new AMap.Size(26, 26), // 图标尺寸
        image: "static/imgNewVersion/demonstration/start_icon_PT.png",
        imageSize: new AMap.Size(26, 26), // Icon的图像
      });
      let marker = new AMap.Marker({
        position: gcj02,
        offset: new AMap.Pixel(-15, -15),
        icon: icon, // 添加 Icon 实例 ，icon也可以是url链接,
        // title: fence.name
      });
      let content = "<div class='startLocation'>" + start.fenseName + "</div>";
      let loctionMarker = new AMap.Marker({
        position: gcj02,
        offset: new AMap.Pixel(0, -35),
        content: content,
        anchor: "center",
        // title: fence.name
      });
      this.startLayerList.push(marker);
      this.startLayerList.push(loctionMarker);
      
      // 添加点击事件
      marker.on('click', () => {
        const details = [
          { label: '设备类型', value: '焚烧发电厂' },
          { label: '设备名称', value: start.fenseName }
        ];
        
        this.createCommonInfoWindow('设备信息', details, marker.getPosition());
      });
    },
    handleEndLocation(end) {
      let point = end.point.split(",");
      let gcj02 = wgs84togcj02(point[0], point[1]);
      let icon = new AMap.Icon({
        size: new AMap.Size(26, 26), // 图标尺寸
        image: "static/imgNewVersion/demonstration/destination_icon_PT.png",
        imageSize: new AMap.Size(26, 26), // Icon的图像
      });
      let marker = new AMap.Marker({
        position: gcj02,
        offset: new AMap.Pixel(-15, -15),
        icon: icon, // 添加 Icon 实例 ，icon也可以是url链接,
        // title: fence.name
      });
      let content = "<div class='endLocation'>" + end.fenseName + "</div>";
      let loctionMarker = new AMap.Marker({
        position: gcj02,
        offset: new AMap.Pixel(0, -35),
        content: content,
        anchor: "center",

        // title: fence.name
      });
      this.endLayerList.push(marker);
      this.endLayerList.push(loctionMarker);
      
      // 添加点击事件
      marker.on('click', () => {
        const details = [
          { label: '设备类型', value: '垃圾中转站' },
          { label: '设备名称', value: end.fenseName }
        ];
        
        this.createCommonInfoWindow('设备信息', details, marker.getPosition());
      });
    },
    async generateVehiclePoint() {
      try {
        this._vehicleMarkerBind = {};
        this.markerList = [];
        this.totalVehicleList = [];
        this.onLineVehicleList = [];
        this._vehicleInfoBind = {}

        // 获取车辆点位数据
        const res = await api.getCarPoints();
        if(!res || !res.result) {
          console.error('获取车辆点位数据失败');
          return;
        }
        res.result.forEach((item) => {
          if (!item.point.lat || !item.point.lng) return;

          this.totalVehicleList.push(item.carId);

          let gcj02 = bd09togcj02(item.point.lng, item.point.lat);
          let icon = new AMap.Icon({
            size: new AMap.Size(26, 26),
            image: getVehicleOnlineMapIcon(item.status=='online'?'vehicle_2_0_0':'vehicle_0_0_0'),
          });

          let marker = new AMap.Marker({
            position: gcj02,
            offset: new AMap.Pixel(-13, -13),
            icon: icon,
            extData: { id: item.carId }
          });

          if (item.status == 'online') {
            this.markerList.push(marker);
            this.onLineVehicleList.push(item.carId);
          }

          this._vehicleMarkerBind[item.carId] = marker;
          this._vehicleInfoBind[item.carId] = item;
          marker.on('click', () => {
            this.openVehiclePupop(marker);
          });
        });

        this.markerLayer = new AMap.OverlayGroup(this.markerList);
        this._map.add(this.markerLayer);
        this._map.setFitView(this.markerList);

      } catch(error) {
        console.error('生成车辆点位失败:', error);
      }
    },

    // 处理公厕点位展示
    handleToiletPoint() {
      if (this.toiletLayerList) {
        this._map.remove(this.toiletLayerList);
      }

      let layerList = [];
      this.toiletPoints.forEach((item) => {
        let point = item.point.split(",");
        let gcj02 = wgs84togcj02(point[0], point[1]);

        let icon = new AMap.Icon({
          size: new AMap.Size(24, 24),
          image: "static/imgNewVersion/demonstration/toilet_icon.png", 
          imageSize: new AMap.Size(24, 24),
        });

        let marker = new AMap.Marker({
          position: gcj02,
          offset: new AMap.Pixel(-12, -12),
          icon: icon,
        });

        // 添加点击事件
        marker.on('click', () => {
          this.openToiletPupop(marker, item);
        });

        layerList.push(marker);
      });

      this.toiletLayerList = new AMap.OverlayGroup(layerList);
      this._map.add(this.toiletLayerList);
    },
    openToiletPupop(marker, info) {
      this.currentToiletInfo = info;
      this.showToiletPupop = true;
      
      this.infoWindow = new AMap.InfoWindow({
        isCustom: true,
        content: this.$refs.toiletPupop.$el,
        offset: new AMap.Pixel(0, 0)
      });

      if(this.$refs.toiletPupop) {
        this.$refs.toiletPupop.init(info);
      }

      this.infoWindow.open(this._map, marker.getPosition());
    },
    closeToiletPupop() {
      this.showToiletPupop = false;
      if(this.infoWindow) {
        this.infoWindow.close();
      }
    },
    // 生成连续路线
    handleContinuousRoutes() {
      if (this.routeLayerList) {
        this._map.remove(this.routeLayerList);
      }

      const routes = [
        {
          color: "#2196F3", // 蓝色路线 - 主干道路线
          points: [
            "27.574515208639816,120.547537105387",
            "27.574290956830217,120.547658082716",
            "27.573986614349280,120.548190963821",
            "27.573834442789520,120.548577715006",
            "27.573650234827144,120.549435325760",
            "27.573345890553800,120.549926825194",
            "27.573137654507792,120.550394352689",
            "27.572937427164617,120.550151810186",
            "27.572929418063224,120.550831851698",
            "27.573225754422032,120.551689462453",
            "27.573490053736772,120.552011490094",
            "27.573754352409420,120.552943684339",
            "27.574050686521267,120.553486045187",
            "27.574347019825957,120.553894182394",
            "27.574651361298670,120.554655516979",
          ],
        },
        {
          color: "#FFC107", // 黄色路线 - 商业区环线
          points: [
            "27.572647691757410,120.547832322776",
            "27.572591623764115,120.548096666157",
            "27.572439439065296,120.548507969341",
            "27.572231185974708,120.548665255407",
            "27.571958854408838,120.548601644151",
            "27.571622443886685,120.548754356097",
            "27.571406179430483,120.548880118876",
            "27.571253993074112,120.549096898600",
            "27.571101806504920,120.549316444351",
            "27.570909570534433,120.549472377710",
            "27.570637235661700,120.549619102216",
            "27.570388929742680,120.549893277280",
          ],
        },
        {
          color: "#F44336", // 红色路线 - 居民区连接线
          points: [
            "27.573847934354805,120.546996062867",
            "27.573671720888547,120.547298419899",
            "27.573375361233714,120.547397233511",
            "27.573119049530112,120.547514013234",
            "27.572822688370368,120.547612826847",
            "27.572486277427330,120.547765538793",
            "27.572189914544590,120.547846386294",
            "27.571925590211594,120.547963166017",
            "27.571685294806403,120.548232657687",
            "27.571733353929870,120.548571088047",
          ],
        },
      ];

      let layerList = [];

      routes.forEach((route) => {
        let path = route.points.map((point) => {
          let latlng = point.split(",");
          let gcj02 = wgs84togcj02(latlng[1], latlng[0]);
          return gcj02;
        });

        // 创建折线
        let polyline = new AMap.Polyline({
          path: path,
          strokeColor: route.color,
          strokeWeight: 4,
          strokeOpacity: 0.8,
          strokeStyle: "solid",
          lineJoin: "round",
          lineCap: "round",
        });

        layerList.push(polyline);
      });

      this.routeLayerList = new AMap.OverlayGroup(layerList);
      this._map.add(this.routeLayerList);
    },
    // 处理区域显示
    handleAreaLayer(isShow) {
      if (this.areaLayerList) {
        this._map.remove(this.areaLayerList);
      }

      if (!isShow) return;

      // 转换坐标点为高德坐标系
      const path = this.areaPoints.map(point => {
        return wgs84togcj02(point[0], point[1]);
      });

      // 创建多边形
      const polygon = new AMap.Polygon({
        path: path,
        strokeColor: "#2acbfd",  // 边框颜色
        strokeWeight: 2,         // 边框宽度
        strokeOpacity: 1,        // 边框透明度
        fillColor: '#052241',    // 填充颜色
        fillOpacity: 0.6,        // 填充透明度
      });

      this.areaLayerList = new AMap.OverlayGroup([polygon]);
      this._map.add(this.areaLayerList);
    },
    // 处理考核点位展示
    handleAssessmentPoint(isShow) {
      if (this.assessmentLayerList) {
        this._map.remove(this.assessmentLayerList);
      }

      if (!isShow) return;

      let layerList = [];
      this.assessmentPoints.forEach((item) => {
        let point = item.point.split(",");
        let gcj02 = wgs84togcj02(point[0], point[1]);

        // 创建图标
        let icon = new AMap.Icon({
          size: new AMap.Size(28, 36),
          image: "static/imgNewVersion/demonstration/longgang_zheng_icon.png", // 确保这个路径是正的
          imageSize: new AMap.Size(28, 36),
        });

        // 创建标记
        let marker = new AMap.Marker({
          position: gcj02,
          offset: new AMap.Pixel(-10, -10),
          icon: icon,
        });

        // 添加点击事件
        marker.on('click', () => {
          const details = [
            { label: '项目名称', value: item.projectName },
            { label: '巡检员', value: item.inspector },
            { label: '考核状态', value: item.status },
            { label: '创建时间', value: item.createTime }
          ];
          
          this.createCommonInfoWindow('考核信息', details, marker.getPosition());
        });

        layerList.push(marker);
      });

      this.assessmentLayerList = new AMap.OverlayGroup(layerList);
      this._map.add(this.assessmentLayerList);
    },
    // 打开车辆信息口
    openVehiclePupop(marker) {
      try {
        const carInfo = this._vehicleInfoBind[marker.getExtData().id];
        if(!carInfo) return;

        const details = [
          { label: '车牌号', value: carInfo.carNo || '未知' },
          { label: '车辆类型', value: api.VEHICLE_TYPE_MAP[carInfo.carType] || '未知' },
          { label: '在线状态', value: carInfo.status == 'online' ? '在线' : '离线' },
          { label: '速度', value: carInfo.rate || '未知' }
        ];

        this.createCommonInfoWindow('车辆信息', details, marker.getPosition());

      } catch(error) {
        console.error('打开车辆信息窗口失败:', error);
      }
    },

    // 创建统一的信息窗口
    createCommonInfoWindow(title, details, position) {
      if(this.commonInfoWindow) {
        this.commonInfoWindow.close();
      }

      this.infoWindowData = {
        title,
        details
      };
      this.showCommonInfoWindow = true;

      this.commonInfoWindow = new AMap.InfoWindow({
        isCustom: true,
        content: this.$refs.commonInfoWindow.$el,
        offset: new AMap.Pixel(0, 0),
        autoMove: true,
        closeWhenClickMap: true
      });

      this.commonInfoWindow.open(this._map, position);
    },

    closeCommonInfoWindow() {
      this.showCommonInfoWindow = false;
      if(this.commonInfoWindow) {
        this.commonInfoWindow.close();
      }
    },

   

  },
  beforeDestroy() {
    if(this.infoWindow) {
      this.infoWindow.close();
    }
    if(this.workerInfoWindow) {
      this.workerInfoWindow.close();
    }
    if(this.deviceInfoWindow) {
      this.deviceInfoWindow.close();
    }
    if(this.assessmentInfoWindow) {
      this.assessmentInfoWindow.close();
    }
    if(this.commonInfoWindow) {
      this.commonInfoWindow.close();
    }
    if(this.timer) {
      clearInterval(this.timer);
      this.timer = null;
    }
  },
};
</script>
<style lang="scss" demonstration>
.mapLayer {
  width: 100%;
  height: 100%;
  position: relative;
}
.startLocation {
  background: url("../../../../../../static/imgNewVersion/demonstration/start_bg_PT.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  text-align: center;
  width: 300px;
  height: 100px;
  line-height: 100px;
  font-weight: bold;
  overflow: hidden;
  white-space: nowrap;
  font-size: 35px;
  text-overflow: ellipsis;
}
.endLocation {
  background: url("../../../../../../static/imgNewVersion/demonstration/destination_bg_PT.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  text-align: center;
  width: 300px;
  height: 100px;
  line-height: 100px;
  font-weight: bold;
  font-size: 35px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.toilet-info {
  padding: 10px;

  .toilet-name {
    font-size: 14px;
    font-weight: bold;
    margin-bottom: 5px;
  }

  .toilet-type {
    font-size: 12px;
    color: #666;
  }
}

</style>

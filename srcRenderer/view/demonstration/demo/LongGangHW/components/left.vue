<template>
  <div class="left-content">
    <!-- 系统派单模块 -->
    <div class="dispatch-overview">
      <div class="icon">
        <div class="pie-wrapper">
          <div ref="pieChart" class="pie-chart"></div>
        </div>
      </div>
      <!-- 添加列表部分 -->
      <div class="list-container">
        <div class="list-header">
          <span>考核单号</span>
          <span>巡检人</span>
        </div>
        <div class="list-content">
          <vue-seamless-scroll 
            ref="seamlessScroll"
            :data="inspectionList" 
            :class-option="classOption"
            class="scroll-wrapper">
            <div class="list-item" v-for="(item, index) in inspectionList" :key="index">
              <span :title="item.orderNo">{{item.orderNo}}</span>
              <span :title="item.createUser">{{item.createUser}}</span>
            </div>
          </vue-seamless-scroll>
        </div>
      </div>
    </div>
    
    <!-- 添加人员出勤数模块 -->
    <div class="attendance">
      <!-- 添加滚动列表 -->
      <vue-seamless-scroll 
        :data="attendance.list" 
        :class-option="attendanceClassOption"
        class="attendance-scroll">
        <div class="attendance-item" v-for="(item, index) in attendance.list" :key="index">
          <span class="label">{{item.typeName}}</span>
          <div class="icons-container">
            <i class="pony-iconv2 pony-renyuan" 
               v-for="index in 8" 
               :key="index"
               :style="{ color: index <= (item.attendanceCount / item.onDutyCount) * 8 ? '#1ffffe' : '#ffffff', fontSize: '20px' }">
            </i>
          </div>
          <div class="count">
            <span class="present">{{item.attendanceCount}}</span>
            <span class="separator">/</span>
            <span class="total">{{item.onDutyCount}}</span>
          </div>
        </div>
      </vue-seamless-scroll>
    </div>

    <!-- 添加平台设备数模块 -->
    <div class="equipment-stats">
      <div class="equipment-item" v-for="(item, index) in equipmentList" :key="index">
        <span class="name">{{item.name}}</span>
        <span class="count">{{item.count}}</span>
        <span class="unit">个</span>
      </div>
    </div>

    <!-- 添加其他数据模块 -->
    <div class="other-data">
      <div ref="barChart" class="bar-chart"></div>
    </div>
  </div>
</template>

<script>
import vueSeamlessScroll from "vue-seamless-scroll";
import * as echarts from 'echarts'
import ajaxApi from './ajaxApi'

export default {
  name: "left",
  components: {
    vueSeamlessScroll,
  },
  data() {
    return {
      scrollStyle: {},
      classOption: {
        step: 0.5,
        hoverStop: true,
        limitMoveNum: 4,
        direction: 1,
        autoPlay: true,
        switchDelay: 3000,
        singleHeight: 35,
        waitTime: 1000,
      },
      overview: {
        milega: 0,
        driveTime: 0,
        trips: 0,
      },
      inspectionList: [
     
      ],
      pieChart: null,
      scrollHeight: 0,
      attendance: {
        list: [], // 用于滚动显示的列表数据
        summary: {
          drivers: {
            present: 0,
            total: 0
          },
          cleaners: {
            present: 0,
            total: 0
          },
          inspectors: {
            present: 0,
            total: 0
          }
        }
      },
      equipmentList: [
        { name: '中转站', count: 12 },
        { name: '收集点', count: 156 },
        { name: '清洁屋', count: 45 },
        { name: '垃圾桶', count: 2360 },
        { name: '垃圾房', count: 89 },
        { name: '焚烧厂', count: 3 },
        { name: '填埋场', count: 2 },
        { name: '处置点', count: 18 },
        { name: '智能垃圾房', count: 65 }
      ],
      barChart: null,
      vehicleData: [
        { name: '清扫车', value: 85 },
        { name: '洒水车', value: 65 },
        { name: '垃圾转运车', value: 92 },
        { name: '路面养护车', value: 78 },
        { name: '抑尘车', value: 55 },
        { name: '压缩车', value: 72 }
      ],
      timer: null,
      attendanceClassOption: {
        step: 0.5,
        limitMoveNum: 3,
        hoverStop: true,
        direction: 1,
        autoPlay: true,
        switchDelay: 3000
      },
      vehicleTypeCategories: {
        '清扫类': [
          'cdddgdxsc', // 纯电动多功能洗扫车
          'PTSLC',     // 普通扫路车
          'xxslj',     // 小型扫路机
          'wrsdjqr'    // 无人扫地机器人
        ],
        '垃圾运输类': [
          'TZLJC',     // 桶装垃圾车
          'LJYSC',     // 垃圾压缩车
          'SLLJTJSC',  // 三轮垃圾桶收集车
          'cddtzljc',  // 纯电动桶装垃圾车
          'ldljqyc',   // 绿化垃圾清运车
          'zxchhc'     // 自卸车或货车
        ],
        '养护类': [
          'DLYHC',     // 道路养护车
          'LMYFHCSJ',  // 路面养护车司机
          'gkzyc',     // 高空作业车
          'xxlmqxj'    // 小型路面清洗机
        ],
        '洒水类': [
          'DXSSC',     // 大型洒水车
          'ssc',       // 洒水车
          'cddxsc',    // 纯电动洒水车
          'xxslgtsc'   // 小型三轮高压水车
        ],
        '清洗类': [
          'DXXCYC',    // 大型高压冲洗车
          'LJTQXC',    // 垃圾桶清洗车
          'hlqxc',     // 护栏清洗车
          'cddgyqxc'   // 纯电动高压清洗车
        ],
        '巡查类': [
          'kfsxcc',    // 开放式巡查车
          'xccl',      // 巡查车辆
          'xcddc'      // 巡查电动车
        ],
        '应急类': [
          'WPC',       // 雾炮车
          'fzhchc',    // 防撞缓冲保护车
          'tjhc',      // 应急货车(含皮卡)
          'dgnycc'     // 多功能抑尘车
        ],
        '管理类': [
          'glyc',      // 管理用车
          'xqc',       // 小汽车
          'qt'         // 其他(车辆)
        ],
        '特殊作业类': [
          'cyj',       // 吹叶机
          'dgnmdj',    // 多功能磨地机
          'syqxship'   // 水域清洁船
        ]
      },
      expandedCategory: '',
    };
  },
  methods: {
    bgColor(index) {
      if (index % 2 == 0) {
        return {
          backgroundColor: "#1a354d",
        };
      }
    },
    initData(data) {
      this.overview = {
        trips: 260,
        milega: this.formatUnit(data.milega, 1000), // 米转公里
        driveTime: this.formatUnit(data.driveTime, 3600), // 秒转小时
      };
    },
    formatUnit(value, divisor) {
      if (!value) return 0;
      return parseFloat((value / divisor).toFixed(2));
    },
    initPieChart() {
      if (this.pieChart) {
        this.pieChart.dispose()
      }
      this.pieChart = echarts.init(this.$refs.pieChart)

      // 计算工单完成比例
      const total = this.inspectionList.length
      const completed = this.inspectionList.filter(item => item.status === '4').length // 状态4为完成
      const completionRate = total > 0 ? Math.round((completed / total) * 100) : 0

      const option = {
        series: [
          // 外层虚线边框
          {
            type: 'pie',
            radius: ['95%', '96%'],
            silent: true,
            label: {
              show: false
            },
            data: [
              {
                value: 1,
                itemStyle: {
                  color: 'transparent',
                  borderType: 'dashed',
                  borderColor: '#ffe854',
                  borderWidth: 1
                }
              }
            ],
            animation: false
          },
          // 主要饼图
          {
            type: 'pie',
            radius: ['75%', '90%'],
            clockwise: false,
            label: {
              show: true,
              position: 'center',
              formatter: `${completionRate}%`,
              fontSize: 24,
              color: '#ffffff'
            },
            data: [
              { 
                value: completed, 
                name: '已完成',
                label: {
                  show: true,
                  position: 'center'
                }
              },
              { 
                value: total - completed, 
                name: '未完成',
                label: {
                  show: false
                }
              }
            ],
            color: ['#ffe854', '#1a354d']
          }
        ]
      }
      this.pieChart.setOption(option)
    },
    handleResize() {
      if (this.pieChart) {
        this.pieChart.resize()
      }
      this.updateScrollSize()
    },
    updateScrollSize() {
      this.$nextTick(() => {
        const listContent = this.$el.querySelector('.list-content')
        if (listContent) {
          this.scrollHeight = listContent.clientHeight
          this.$refs.seamlessScroll && this.$refs.seamlessScroll.reset()
        }
      })
    },
    getAttendanceRatio(present, total) {
      return present / total;
    },
    initBarChart() {
      if (this.barChart) {
        this.barChart.dispose()
      }
      this.barChart = echarts.init(this.$refs.barChart)
      
      const option = {
        backgroundColor: 'transparent',
        grid: {
          top: '15%',
          bottom: '15%',
          left: '4%',
          right: '4%',
          containLabel: true
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'none'
          },
          backgroundColor: 'rgba(0,0,0,0.7)',
          borderColor: '#00cfdf',
          borderWidth: 1,
          textStyle: {
            color: '#fff'
          },
          formatter: function(params) {
            const barData = params[0]
            return `${barData.name}<br/>里程：${barData.value} km`
          },
          position: function(point, params, dom, rect, size) {
            return [point[0], point[1] - size.contentSize[1]]
          }
        },
        xAxis: {
          type: 'category',
          data: this.vehicleData.map(item => {
            // 如果是展开的分类，显示其子项
            if (item.name === this.expandedCategory) {
              return item.children.map(child => child.name)
            }
            return item.name
          }).flat(),
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            color: '#8fc6d4',
            fontSize: 10,
            interval: 0,
            margin: 20,
            rotate: 45,
            formatter: function(value) {
              if(value.length > 6) {
                return value.substring(0, 6) + '...'
              }
              return value
            }
          }
        },
        yAxis: {
          type: 'value',
          name: '当日车辆里程(公里)',
          nameTextStyle: {
            color: '#8fc6d4',
            fontSize: 12,
            padding: [0, 0, 0, 60]
          },
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            color: '#8fc6d4',
            fontSize: 12
          },
          splitLine: {
            show: false
          }
        },
        series: [
          {
            data: this.vehicleData.map(item => {
              if (item.name === this.expandedCategory) {
                return item.children.map(child => child.value)
              }
              return item.value
            }).flat(),
            type: 'pictorialBar',
            symbol: 'triangle',
            symbolSize: ['30', '100%'],
            symbolOffset: [0, 0],
            symbolPosition: 'start',
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#00cfdf' },
                { offset: 1, color: '#3d7ed4' }
              ])
            }
          },
          {
            data: this.vehicleData.map(item => {
              if (item.name === this.expandedCategory) {
                return item.children.map(child => child.value)
              }
              return item.value
            }).flat(),
            type: 'line',
            smooth: true,
            symbol: 'circle',
            symbolSize: 6,
            lineStyle: {
              color: '#00cfdf',
              width: 2
            },
            itemStyle: {
              color: '#00cfdf'
            }
          }
        ]
      }
      
      this.barChart.setOption(option)
      
      // 添加点击事件
      this.barChart.on('click', (params) => {
        const clickedCategory = this.vehicleData.find(item => 
          item.name === params.name || item.children.some(child => child.name === params.name)
        )
        if (clickedCategory) {
          this.expandedCategory = this.expandedCategory === clickedCategory.name ? '' : clickedCategory.name
          this.initBarChart()
        }
      })
    },
    async fetchWorkOrders() {
      try {
        const res = await ajaxApi.getWorkOrderPage()
        if(res && res.result && res.result.records) {
          this.inspectionList = res.result.records.map(item => ({
            orderNo: item.no,
            createUser: item.createUser,
            status: item.status
          }))
          // 更新饼图
          this.initPieChart()
        }
      } catch(err) {
        console.error('获取考核单号失败:', err)
      }
    },
    async fetchAttendance() {
      try {
        const res = await ajaxApi.getPersonCurrentTypeStatistics()
        if(res && res.result) {
          // 处理人员类型数据
          this.attendance.list = res.result.map(item => ({
            ...item,
            typeName: ajaxApi.PERSON_TYPE_MAP[item.personType] || item.personType
          })).filter(item => item.onDutyCount > 0) // 只显示有人数的类型
    
        }
      } catch(err) {
        console.error('获取人员通勤数据失败:', err) 
      }
    },
    async fetchVehicleData() {
      try {
        const res = await ajaxApi.getCarMilStatistics({
          workDateStart: moment().format('YYYY-MM-DD'),
          workDateEnd: moment().format('YYYY-MM-DD')
        })
        if(res && res.result) {
          // 按车辆类型分组并统计里程
          const mileageByType = {}
          res.result.forEach(item => {
            if (!mileageByType[item.carType]) {
              mileageByType[item.carType] = 0
            }
            mileageByType[item.carType] += item.effectiveWorkMileage || 0
          })

          // 计算分类汇总数据
          const categoryData = []
          Object.entries(this.vehicleTypeCategories).forEach(([category, types]) => {
            // 计算分类总里程
            const totalMileage = types.reduce((sum, type) => sum + (mileageByType[type] || 0), 0)
            if(totalMileage > 0) { // 只添加有里程数据的分类
              categoryData.push({
                name: category,
                value: this.formatMileage(totalMileage),
                children: types
                  .filter(type => mileageByType[type] > 0) // 只包含有里程的车辆类型
                  .map(type => ({
                    name: ajaxApi.VEHICLE_TYPE_MAP[type] || type,
                    value: this.formatMileage(mileageByType[type])
                  }))
                  .sort((a, b) => b.value - a.value) // 按里程降序排序
              })
            }
          })

          this.vehicleData = categoryData.sort((a, b) => b.value - a.value) // 按总里程降序排序
          this.initBarChart()
        }
      } catch(err) {
        console.error('获取车辆里程数据失败:', err)
      }
    },
    formatMileage(mileage) {
      // 将里程转换为公里并保留2位小数
      return parseFloat((mileage / 1000).toFixed(2))
    },
    async refreshData() {
      await Promise.all([
        this.fetchWorkOrders(),
        this.fetchAttendance(), 
        this.fetchVehicleData()
      ])
    },
    startTimer() {
      // 每5分钟刷新一次数据
      this.timer = setInterval(() => {
        this.refreshData()
      }, 10 * 60 * 1000)
    }
  },
  mounted() {
    this.$nextTick(async () => {
      this.initPieChart()
      this.updateScrollSize()
      window.addEventListener('resize', this.handleResize)
      
      // 初始化数据
      await this.refreshData()
      // 启动定时刷新
      this.startTimer()
    })
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize)
    if(this.pieChart) {
      this.pieChart.dispose()
    }
    if(this.barChart) {
      this.barChart.dispose() 
    }
    // 清理定时器
    if(this.timer) {
      clearInterval(this.timer)
      this.timer = null
    }
  }
};
</script>
<style lang="scss" scoped demonstration>
.left-content {
  width: 100%;
  height: 100%;
  background: url("../../../../../../static/imgNewVersion/demonstration/cangnan_left.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  .dispatch-overview {
    width: 94%;
    height: 22%;
    margin-top: 12%;
    display: flex;
    align-items: center;
    padding: 4% 0 0 0;
    .icon {
      width: 40%;
      height: 100%;
      display: flex;
      align-items: flex-start;
      flex-direction: column;
      justify-content: flex-start;
      padding: 0 4%;
      .pie-wrapper {
        position: relative;
        width: 100%;
        height: 100%;
        
        .pie-chart {
          width: 100% !important;
          height: 100% !important;
        }
        
    
      }
    }
  
    
    .list-container {
      flex: 1;
      height: 100%;
      display: flex;
      flex-direction: column;
      
      .list-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 70px;
        padding: 0 15px;
        font-weight: 500;
        background-color: #1a354d;
        
        span {
          flex: 1;
          text-align: center;
          &:last-child {
            text-align: center;
          }
        }
      }
      
      .list-content {
        flex: 1;
        min-height: 0;
        position: relative;
        overflow: hidden;
        
        .scroll-wrapper {
          height: 100%;
          width: 100%;
          
          .list-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 65px;
            min-height: 65px;
            padding: 0 15px;
            color: #ffffff;
            
            &:nth-child(even) {
              background-color: #1a354d;
            }
            
            span {
              flex: 1;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              text-align: center;
              width: 100%;
              padding: 0 10px;
              box-sizing: border-box;
              
              &:last-child {
                text-align: center;
              }
            }
          }
        }
      }
    }
  }
  .pre-alarm {
    width: 94%;
    height: 60%;
    margin-top: 22%;
    .alarmList {
      width: 100%;
      height: 95%;
      .alarmInfo {
        width: 100%;
        height: 100%;
        padding:1% 6%;
        li {
          display: flex;
          align-items: center;
          justify-content: space-around;
          line-height: 80px;
          padding: 0 2%;
          cursor: pointer;
          span {
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
          .plateNo {
            width: 30%;
          }
          .alarmName {
            width: 30%;
            display: flex;
            align-items: center;
            padding-right: 5px;

            span {
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
            }
          }
          .alarmNum {
            width: 40%;
          }
        }
      }
    }
  }
  .attendance {
    width: 94%;
    margin-top: 10%;
    
    .attendance-scroll {
      height: 14vh; // 调整滚动区域高度
      overflow: hidden;
    }
    
    .attendance-item {
      display: flex;
      align-items: center;
      padding: 15px 20px;
      border-radius: 4px;
      justify-content: space-between;
      margin-bottom: 10px;
      
      .label {
        color: #ffffff;
        width: 240px;
      }
      
      .icons-container {
        width: 300px;
        display: flex;
        gap: 4px;
        justify-content: center;
        align-items: center;
        margin: 0 20px;
      }
      
      .count {
        min-width: 100px;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        .present {
          color: #1ffffe;
        }
        .separator, .total {
          color: #ffffff;
        }
      }
    }
    
  }
  .equipment-stats {
    width: 94%;
    margin-top: 13%;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
    padding: 1% 2%;
    .equipment-item {
      height: 70px;
      padding: 8px;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-image: url('../../../../../../static/imgNewVersion/demonstration/longgang_items_bg.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      
      .name {
        color: #ffffff;
        margin-right: 6px;
      }
      
      .count {
        color: #1ffffe;
        margin-right: 3px;
      }
      
      .unit {
        color: #ffffff;
      }
    }
  }
  .other-data {
    width: 94%;
    margin-top: 13%;
    height: 25%;
    .bar-chart {
      height: 100%;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
     
    }
  }
}
</style>

